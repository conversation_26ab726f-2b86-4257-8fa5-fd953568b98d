<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ef4444;
            --secondary-color: #dc2626;
            --accent-color: #f87171;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .controls-section {
            background: white;
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: rgba(239, 68, 68, 0.8);
            border: 1px solid rgba(239, 68, 68, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(239, 68, 68, 0.9);
            border-color: rgba(239, 68, 68, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: white;
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .countdown-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .countdown-card {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .countdown-card.urgent {
            border-color: rgba(245, 158, 11, 0.8);
            background: rgba(245, 158, 11, 0.1);
        }

        .countdown-card.critical {
            border-color: rgba(239, 68, 68, 0.8);
            background: rgba(239, 68, 68, 0.1);
        }

        .countdown-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: white;
        }

        .exam-subject {
            color: #333;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .exam-date {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        .countdown-timer {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .countdown-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        .exams-section {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .exam-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .exam-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .exam-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .exam-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .exam-title {
            color: #333;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .exam-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-upcoming {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
        }

        .status-urgent {
            background: rgba(245, 158, 11, 0.8);
            color: #333;
        }

        .status-critical {
            background: rgba(239, 68, 68, 0.8);
            color: #333;
        }

        .exam-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .exam-detail {
            text-align: center;
        }

        .exam-detail-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .exam-detail-value {
            color: #333;
            font-weight: 600;
        }

        .exam-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .review-plan-section {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .plan-placeholder {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .countdown-section {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .exam-details {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
            
            .countdown-timer {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-clipboard-check me-2"></i>
                考试管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">考试管理</h1>
            <p class="page-description">考试日程安排、倒计时提醒、复习计划制定</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="action-buttons">
                <button class="btn btn-success" onclick="addExam()">
                    <i class="fas fa-plus me-1"></i>
                    添加考试
                </button>
                <button class="btn btn-primary" onclick="createReviewPlan()">
                    <i class="fas fa-calendar-plus me-1"></i>
                    制定复习计划
                </button>
                <button class="btn btn-primary" onclick="exportSchedule()">
                    <i class="fas fa-download me-1"></i>
                    导出考试安排
                </button>
            </div>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 考试倒计时 -->
            <div class="countdown-section">
                <div class="countdown-card critical">
                    <div class="exam-subject">高等数学</div>
                    <div class="exam-date">2024年1月18日 14:00</div>
                    <div class="countdown-timer" id="countdown1">3天</div>
                    <div class="countdown-label">距离考试</div>
                </div>
                <div class="countdown-card urgent">
                    <div class="exam-subject">数据结构</div>
                    <div class="exam-date">2024年1月25日 09:00</div>
                    <div class="countdown-timer" id="countdown2">10天</div>
                    <div class="countdown-label">距离考试</div>
                </div>
                <div class="countdown-card">
                    <div class="exam-subject">英语听力</div>
                    <div class="exam-date">2024年2月1日 10:00</div>
                    <div class="countdown-timer" id="countdown3">17天</div>
                    <div class="countdown-label">距离考试</div>
                </div>
            </div>

            <!-- 考试列表 -->
            <div class="exams-section">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    考试安排
                </h2>
                <div class="exam-list">
                    <div class="exam-item">
                        <div class="exam-header">
                            <div class="exam-title">高等数学期中考试</div>
                            <div class="exam-status status-critical">紧急</div>
                        </div>
                        <div class="exam-details">
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试时间</div>
                                <div class="exam-detail-value">1月18日 14:00</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试地点</div>
                                <div class="exam-detail-value">教学楼A101</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试时长</div>
                                <div class="exam-detail-value">120分钟</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">复习进度</div>
                                <div class="exam-detail-value">60%</div>
                            </div>
                        </div>
                        <div class="exam-actions">
                            <button class="btn btn-primary btn-sm">查看复习计划</button>
                            <button class="btn btn-success btn-sm">编辑</button>
                        </div>
                    </div>

                    <div class="exam-item">
                        <div class="exam-header">
                            <div class="exam-title">数据结构课程考试</div>
                            <div class="exam-status status-urgent">即将到来</div>
                        </div>
                        <div class="exam-details">
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试时间</div>
                                <div class="exam-detail-value">1月25日 09:00</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试地点</div>
                                <div class="exam-detail-value">计算机楼B205</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">考试时长</div>
                                <div class="exam-detail-value">90分钟</div>
                            </div>
                            <div class="exam-detail">
                                <div class="exam-detail-label">复习进度</div>
                                <div class="exam-detail-value">40%</div>
                            </div>
                        </div>
                        <div class="exam-actions">
                            <button class="btn btn-primary btn-sm">查看复习计划</button>
                            <button class="btn btn-success btn-sm">编辑</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 复习计划 -->
            <div class="review-plan-section">
                <h2 class="section-title">
                    <i class="fas fa-calendar-alt"></i>
                    复习计划
                </h2>
                <div class="plan-placeholder">
                    <i class="fas fa-calendar-plus me-2"></i>
                    复习计划功能正在开发中...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addExam() {
            alert('添加考试功能正在开发中...');
        }

        function createReviewPlan() {
            alert('制定复习计划功能正在开发中...');
        }

        function exportSchedule() {
            alert('导出考试安排功能正在开发中...');
        }

        // 更新倒计时
        function updateCountdowns() {
            // 这里可以添加实际的倒计时逻辑
            console.log('更新倒计时...');
        }

        // 每分钟更新一次倒计时
        setInterval(updateCountdowns, 60000);
    </script>
</body>
</html>
