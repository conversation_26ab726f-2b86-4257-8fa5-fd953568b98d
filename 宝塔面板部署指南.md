# 🚀 比特熊智慧系统 - 宝塔面板部署指南

## 📋 服务器信息
- **IP地址**: *************
- **用户名**: root
- **密码**: ZbDX7%=]?H2(LAUz
- **项目目录**: /www/wwwroot/www.bitbear.top
- **数据库名**: bitbear_website
- **数据库密码**: 309290133q

## 🎯 第一步：访问宝塔面板

### 1.1 获取宝塔面板地址
通常宝塔面板的访问地址为：
- **HTTP**: http://*************:8888
- **HTTPS**: https://*************:8888

### 1.2 如果不知道面板地址
SSH连接到服务器后执行：
```bash
bt default
```
这个命令会显示宝塔面板的访问地址、用户名和密码。

### 1.3 登录宝塔面板
1. 在浏览器中访问面板地址
2. 输入用户名和密码登录
3. 如果是首次登录，可能需要绑定宝塔账号

## 🗄️ 第二步：配置数据库

### 2.1 创建数据库
1. 在宝塔面板左侧菜单点击 **"数据库"**
2. 点击 **"添加数据库"**
3. 填写数据库信息：
   - **数据库名**: `bitbear_website`
   - **用户名**: `bitbear_user` (或使用root)
   - **密码**: `309290133q`
   - **访问权限**: 本地服务器
4. 点击 **"提交"** 创建数据库

### 2.2 导入数据库结构
1. 点击数据库后面的 **"管理"** 按钮进入phpMyAdmin
2. 选择 `bitbear_website` 数据库
3. 点击 **"导入"** 选项卡
4. 上传并导入以下SQL文件（按顺序）：
   - `database/init.sql`
   - `database/create_community_tables.sql`
   - `database/homepage_content_tables.sql`

## 📁 第三步：上传项目文件

### 3.1 使用宝塔文件管理器
1. 在宝塔面板左侧菜单点击 **"文件"**
2. 导航到 `/www/wwwroot/www.bitbear.top`
3. 如果目录不存在，先创建该目录

### 3.2 上传方式选择

**方式A：直接上传压缩包（推荐）**
1. 将本地项目打包为zip文件
2. 在宝塔文件管理器中点击 **"上传"**
3. 选择zip文件上传
4. 上传完成后右键解压

**方式B：使用FTP上传**
1. 在宝塔面板中创建FTP账户
2. 使用FTP工具（如FileZilla）上传文件

**方式C：使用Git同步**
1. 在服务器上安装Git
2. 克隆项目仓库到指定目录

### 3.3 设置文件权限
在宝塔文件管理器中：
1. 选中项目根目录
2. 右键选择 **"权限"**
3. 设置权限为 `755`
4. 勾选 **"应用到子目录"**
5. 特别设置 `uploads` 目录权限为 `777`

## 🌐 第四步：配置网站

### 4.1 添加网站
1. 在宝塔面板左侧菜单点击 **"网站"**
2. 点击 **"添加站点"**
3. 填写网站信息：
   - **域名**: `www.bitbear.top` 和 `*************`
   - **根目录**: `/www/wwwroot/www.bitbear.top`
   - **FTP**: 可选创建
   - **数据库**: 选择已创建的数据库
   - **PHP版本**: 选择 PHP 8.1 或更高版本

### 4.2 配置PHP
1. 点击网站后面的 **"设置"**
2. 选择 **"PHP版本"** 选项卡
3. 确保选择 PHP 8.1+ 版本
4. 在 **"配置文件"** 中检查以下扩展是否启用：
   - `pdo_mysql`
   - `mysqli`
   - `gd`
   - `curl`
   - `json`

### 4.3 配置伪静态（如需要）
1. 在网站设置中选择 **"伪静态"** 选项卡
2. 如果项目需要URL重写，添加相应规则

## ⚙️ 第五步：修改配置文件

### 5.1 更新数据库配置
编辑 `config/database.php` 文件，确保服务器环境检测正确：

```php
private function isServerEnvironment() {
    return isset($_SERVER['SERVER_NAME']) &&
           (strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false ||
            strpos($_SERVER['SERVER_NAME'], '*************') !== false ||
            (isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] === '*************'));
}
```

### 5.2 检查文件路径
确保所有文件路径在Linux环境下正确（注意大小写敏感）。

## 🧪 第六步：测试部署

### 6.1 访问网站
在浏览器中访问：
- http://*************
- http://www.bitbear.top（如果域名已解析）

### 6.2 功能测试
1. **首页加载**: 检查页面是否正常显示
2. **数据库连接**: 查看是否有数据库错误
3. **图片加载**: 检查图片资源是否正常
4. **用户功能**: 测试注册、登录功能
5. **管理后台**: 访问 `/admin.php` 测试后台功能

### 6.3 查看错误日志
如果有问题，在宝塔面板中：
1. 点击 **"日志"** 菜单
2. 查看 **"网站日志"** 和 **"PHP错误日志"**
3. 根据错误信息进行调试

## 🔒 第七步：安全配置

### 7.1 配置SSL证书
1. 在网站设置中选择 **"SSL"** 选项卡
2. 选择 **"Let's Encrypt"** 免费证书
3. 填写邮箱地址申请证书
4. 开启 **"强制HTTPS"**

### 7.2 安全设置
1. 修改宝塔面板默认端口
2. 设置面板访问IP白名单
3. 定期更新系统和软件

## 📊 第八步：性能优化

### 8.1 开启缓存
1. 安装 **"Redis"** 或 **"Memcached"**
2. 配置PHP OPcache
3. 开启网站压缩

### 8.2 数据库优化
1. 定期备份数据库
2. 优化数据库查询
3. 监控数据库性能

## 🚨 常见问题解决

### 问题1：网站无法访问
- 检查防火墙设置
- 确认域名解析
- 查看Nginx配置

### 问题2：数据库连接失败
- 检查数据库服务状态
- 验证数据库用户权限
- 确认配置文件中的数据库信息

### 问题3：文件上传失败
- 检查目录权限
- 确认PHP上传限制
- 查看磁盘空间

### 问题4：PHP错误
- 检查PHP版本兼容性
- 确认必要扩展已安装
- 查看PHP错误日志

## ✅ 部署完成检查清单

- [ ] 宝塔面板正常访问
- [ ] 数据库创建并导入成功
- [ ] 项目文件上传完成
- [ ] 网站配置正确
- [ ] 首页正常访问
- [ ] 数据库连接正常
- [ ] 用户功能正常
- [ ] 管理后台可访问
- [ ] SSL证书配置（可选）
- [ ] 性能优化完成

---

**准备好了吗？让我们开始第一步：访问宝塔面板！** 🎯
