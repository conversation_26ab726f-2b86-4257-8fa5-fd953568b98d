<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

require_once '../config/database.php';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = trim($_POST['name']);
                $url = trim($_POST['url']);
                $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                $sort_order = (int)$_POST['sort_order'];
                
                if (!empty($name)) {
                    $stmt = $pdo->prepare("INSERT INTO navbar_items (name, url, parent_id, sort_order, is_active) VALUES (?, ?, ?, ?, 1)");
                    $stmt->execute([$name, $url, $parent_id, $sort_order]);
                    $success = "菜单项添加成功！";
                }
                break;
                
            case 'edit':
                $id = (int)$_POST['id'];
                $name = trim($_POST['name']);
                $url = trim($_POST['url']);
                $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
                $sort_order = (int)$_POST['sort_order'];
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                if (!empty($name)) {
                    $stmt = $pdo->prepare("UPDATE navbar_items SET name = ?, url = ?, parent_id = ?, sort_order = ?, is_active = ? WHERE id = ?");
                    $stmt->execute([$name, $url, $parent_id, $sort_order, $is_active, $id]);
                    $success = "菜单项更新成功！";
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                // 先删除子菜单
                $stmt = $pdo->prepare("DELETE FROM navbar_items WHERE parent_id = ?");
                $stmt->execute([$id]);
                // 再删除主菜单
                $stmt = $pdo->prepare("DELETE FROM navbar_items WHERE id = ?");
                $stmt->execute([$id]);
                $success = "菜单项删除成功！";
                break;
        }
    }
}

// 获取所有导航栏项目
$stmt = $pdo->query("SELECT * FROM navbar_items ORDER BY parent_id ASC, sort_order ASC");
$allItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 组织数据结构
$mainItems = [];
$subItems = [];
foreach ($allItems as $item) {
    if ($item['parent_id'] === null) {
        $mainItems[] = $item;
    } else {
        $subItems[$item['parent_id']][] = $item;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏管理 - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            font-size: 1.125rem;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table th {
            background: #f8fafc;
            font-weight: 600;
        }
        
        .sub-item {
            padding-left: 2rem;
            background: #f8fafc;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>导航栏管理</h1>
            <div>
                <button class="btn btn-primary" onclick="showAddModal()">添加菜单项</button>
                <a href="index.php" class="btn btn-secondary">返回后台</a>
            </div>
        </div>

        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                当前导航栏结构
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>菜单名称</th>
                            <th>链接地址</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($mainItems as $item): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($item['name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($item['url']); ?></td>
                                <td><?php echo $item['sort_order']; ?></td>
                                <td>
                                    <span class="status-badge <?php echo $item['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo $item['is_active'] ? '启用' : '禁用'; ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-primary" onclick="editItem(<?php echo htmlspecialchars(json_encode($item)); ?>)">编辑</button>
                                    <button class="btn btn-danger" onclick="deleteItem(<?php echo $item['id']; ?>)">删除</button>
                                </td>
                            </tr>
                            <?php if (isset($subItems[$item['id']])): ?>
                                <?php foreach ($subItems[$item['id']] as $subItem): ?>
                                    <tr class="sub-item">
                                        <td>└─ <?php echo htmlspecialchars($subItem['name']); ?></td>
                                        <td><?php echo htmlspecialchars($subItem['url']); ?></td>
                                        <td><?php echo $subItem['sort_order']; ?></td>
                                        <td>
                                            <span class="status-badge <?php echo $subItem['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                                <?php echo $subItem['is_active'] ? '启用' : '禁用'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-primary" onclick="editItem(<?php echo htmlspecialchars(json_encode($subItem)); ?>)">编辑</button>
                                            <button class="btn btn-danger" onclick="deleteItem(<?php echo $subItem['id']; ?>)">删除</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 添加/编辑模态框 -->
    <div id="itemModal" class="modal">
        <div class="modal-content">
            <h2 id="modalTitle">添加菜单项</h2>
            <form id="itemForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="add">
                <input type="hidden" name="id" id="itemId">
                
                <div class="form-group">
                    <label class="form-label">菜单名称</label>
                    <input type="text" name="name" id="itemName" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">链接地址</label>
                    <input type="text" name="url" id="itemUrl" class="form-input" placeholder="例如: index.php 或 #">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">父级菜单</label>
                        <select name="parent_id" id="itemParent" class="form-input">
                            <option value="">顶级菜单</option>
                            <?php foreach ($mainItems as $item): ?>
                                <option value="<?php echo $item['id']; ?>"><?php echo htmlspecialchars($item['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">排序</label>
                        <input type="number" name="sort_order" id="itemSort" class="form-input" value="0">
                    </div>
                </div>
                
                <div class="form-group" id="statusGroup" style="display: none;">
                    <label>
                        <input type="checkbox" name="is_active" id="itemActive"> 启用此菜单项
                    </label>
                </div>
                
                <div style="margin-top: 2rem; text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '添加菜单项';
            document.getElementById('formAction').value = 'add';
            document.getElementById('itemForm').reset();
            document.getElementById('statusGroup').style.display = 'none';
            document.getElementById('itemModal').style.display = 'block';
        }

        function editItem(item) {
            document.getElementById('modalTitle').textContent = '编辑菜单项';
            document.getElementById('formAction').value = 'edit';
            document.getElementById('itemId').value = item.id;
            document.getElementById('itemName').value = item.name;
            document.getElementById('itemUrl').value = item.url;
            document.getElementById('itemParent').value = item.parent_id || '';
            document.getElementById('itemSort').value = item.sort_order;
            document.getElementById('itemActive').checked = item.is_active == 1;
            document.getElementById('statusGroup').style.display = 'block';
            document.getElementById('itemModal').style.display = 'block';
        }

        function deleteItem(id) {
            if (confirm('确定要删除这个菜单项吗？这将同时删除其所有子菜单。')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function closeModal() {
            document.getElementById('itemModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('itemModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
