# 比特熊智慧系统服务器部署命令

## 1. 连接服务器
ssh root@*************
# 密码: ZbDX7%=]?H2(LAUz

## 2. 检查项目文件
cd /www/wwwroot/www.bitbear.top
ls -la

## 3. 设置文件权限
chown -R www-data:www-data /www/wwwroot/www.bitbear.top
find /www/wwwroot/www.bitbear.top -type d -exec chmod 755 {} \;
find /www/wwwroot/www.bitbear.top -type f -exec chmod 644 {} \;
chmod -R 777 /www/wwwroot/www.bitbear.top/uploads

## 4. 创建必要目录
mkdir -p /www/wwwroot/www.bitbear.top/uploads/avatars
mkdir -p /www/wwwroot/www.bitbear.top/uploads/posts
mkdir -p /www/wwwroot/www.bitbear.top/uploads/videos
chmod -R 777 /www/wwwroot/www.bitbear.top/uploads

## 5. 配置数据库
mysql -u root -p
# 输入密码: 309290133q

# 在MySQL中执行:
CREATE DATABASE IF NOT EXISTS bitbear_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bitbear_website;
SHOW TABLES;
EXIT;

## 6. 导入数据库结构
cd /www/wwwroot/www.bitbear.top
mysql -u root -p309290133q bitbear_website < database/init.sql

## 7. 导入其他表结构（如果存在）
mysql -u root -p309290133q bitbear_website < database/create_community_tables.sql
mysql -u root -p309290133q bitbear_website < database/homepage_content_tables.sql

## 8. 配置Nginx
nano /etc/nginx/sites-available/www.bitbear.top

# 复制以下内容到文件中:
server {
    listen 80;
    server_name www.bitbear.top *************;
    root /www/wwwroot/www.bitbear.top;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
    
    client_max_body_size 100M;
}

## 9. 启用站点
ln -s /etc/nginx/sites-available/www.bitbear.top /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx

## 10. 重启服务
systemctl restart nginx
systemctl restart php8.1-fpm
systemctl restart mysql

## 11. 测试部署
cd /www/wwwroot/www.bitbear.top
php test_server_db.php

## 12. 检查服务状态
systemctl status nginx
systemctl status php8.1-fpm
systemctl status mysql

## 13. 查看日志（如果有问题）
tail -f /var/log/nginx/error.log
tail -f /var/log/php8.1-fpm.log

## 14. 测试网站访问
curl -I http://localhost
curl -I http://*************

## 完成后访问地址:
# http://*************
# http://www.bitbear.top (如果域名已解析)

## 管理后台:
# http://*************/admin/
# 默认账号: admin
# 默认密码: admin123

## 如果需要导入本地数据:
# 1. 在本地运行: php export_local_data.php
# 2. 将生成的 database_export/import_to_server.sql 上传到服务器
# 3. 在服务器执行: mysql -u root -p309290133q bitbear_website < import_to_server.sql
