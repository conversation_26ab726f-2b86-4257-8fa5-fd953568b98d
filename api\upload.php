<?php
session_start();
header('Content-Type: application/json');

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 引入数据库配置
try {
    require_once '../config/database.php';
    $db = db();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '数据库连接失败']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    exit;
}

try {
    $uploadType = $_POST['type'] ?? '';
    
    switch ($uploadType) {
        case 'hero_image':
            handleHeroImageUpload($db);
            break;
        case 'expert_image':
            handleExpertImageUpload($db);
            break;
        case 'video':
            handleVideoUpload($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的上传类型']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

// 处理英雄区域图片上传
function handleHeroImageUpload($db) {
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('图片上传失败');
    }
    
    $file = $_FILES['image'];
    $uploadResult = uploadImage($file, 'hero');
    
    if ($uploadResult['success']) {
        // 更新数据库中的英雄图片路径
        $sql = "UPDATE homepage_hero SET hero_image = ?, updated_at = CURRENT_TIMESTAMP WHERE is_active = 1";
        $result = $db->execute($sql, [$uploadResult['path']]);
        
        if ($result !== false) {
            echo json_encode([
                'success' => true,
                'message' => '英雄图片上传成功',
                'path' => $uploadResult['path']
            ]);
        } else {
            throw new Exception('更新数据库失败');
        }
    } else {
        throw new Exception($uploadResult['error']);
    }
}

// 处理专家头像上传
function handleExpertImageUpload($db) {
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('图片上传失败');
    }
    
    $expertId = $_POST['expert_id'] ?? null;
    if (!$expertId) {
        throw new Exception('专家ID不能为空');
    }
    
    $file = $_FILES['image'];
    $uploadResult = uploadImage($file, 'expert');
    
    if ($uploadResult['success']) {
        // 更新数据库中的专家头像路径
        $sql = "UPDATE homepage_experts SET image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $result = $db->execute($sql, [$uploadResult['path'], $expertId]);
        
        if ($result !== false) {
            echo json_encode([
                'success' => true,
                'message' => '专家头像上传成功',
                'path' => $uploadResult['path']
            ]);
        } else {
            throw new Exception('更新数据库失败');
        }
    } else {
        throw new Exception($uploadResult['error']);
    }
}

// 处理视频上传
function handleVideoUpload($db) {
    if (!isset($_FILES['video']) || $_FILES['video']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('视频上传失败');
    }
    
    $file = $_FILES['video'];
    $uploadResult = uploadVideo($file);
    
    if ($uploadResult['success']) {
        // 记录上传的视频信息
        $sql = "INSERT INTO homepage_uploads (file_type, file_path, original_name, file_size) VALUES (?, ?, ?, ?)";
        $params = ['video', $uploadResult['path'], $file['name'], $file['size']];
        $result = $db->execute($sql, $params);
        
        if ($result !== false) {
            echo json_encode([
                'success' => true,
                'message' => '视频上传成功',
                'path' => $uploadResult['path']
            ]);
        } else {
            throw new Exception('记录上传信息失败');
        }
    } else {
        throw new Exception($uploadResult['error']);
    }
}

// 上传图片文件
function uploadImage($file, $category = 'general') {
    // 检查文件类型
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'error' => '不支持的图片格式'];
    }
    
    // 检查文件大小 (最大5MB)
    $maxSize = 5 * 1024 * 1024;
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'error' => '图片文件过大，最大支持5MB'];
    }
    
    // 创建上传目录
    $uploadDir = '../uploads/' . $category . '/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'error' => '创建上传目录失败'];
        }
    }
    
    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // 移动上传的文件
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // 返回相对于网站根目录的路径
        $relativePath = 'uploads/' . $category . '/' . $filename;
        return ['success' => true, 'path' => $relativePath];
    } else {
        return ['success' => false, 'error' => '文件保存失败'];
    }
}

// 上传视频文件
function uploadVideo($file) {
    // 检查文件类型
    $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'error' => '不支持的视频格式'];
    }
    
    // 检查文件大小 (最大100MB)
    $maxSize = 100 * 1024 * 1024;
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'error' => '视频文件过大，最大支持100MB'];
    }
    
    // 创建上传目录
    $uploadDir = '../uploads/videos/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'error' => '创建上传目录失败'];
        }
    }
    
    // 生成唯一文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // 移动上传的文件
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // 返回相对于网站根目录的路径
        $relativePath = 'uploads/videos/' . $filename;
        return ['success' => true, 'path' => $relativePath];
    } else {
        return ['success' => false, 'error' => '文件保存失败'];
    }
}

// 验证图片文件
function validateImage($file) {
    // 使用getimagesize验证是否为真实图片
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        return false;
    }
    
    // 检查图片尺寸
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // 最小尺寸要求
    if ($width < 100 || $height < 100) {
        return false;
    }
    
    // 最大尺寸要求
    if ($width > 4000 || $height > 4000) {
        return false;
    }
    
    return true;
}

// 生成缩略图
function generateThumbnail($sourcePath, $thumbnailPath, $maxWidth = 300, $maxHeight = 300) {
    $imageInfo = getimagesize($sourcePath);
    if ($imageInfo === false) {
        return false;
    }
    
    $sourceWidth = $imageInfo[0];
    $sourceHeight = $imageInfo[1];
    $sourceType = $imageInfo[2];
    
    // 计算缩略图尺寸
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    $thumbnailWidth = intval($sourceWidth * $ratio);
    $thumbnailHeight = intval($sourceHeight * $ratio);
    
    // 创建源图像资源
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    // 创建缩略图资源
    $thumbnailImage = imagecreatetruecolor($thumbnailWidth, $thumbnailHeight);
    
    // 保持透明度
    if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
        imagealphablending($thumbnailImage, false);
        imagesavealpha($thumbnailImage, true);
        $transparent = imagecolorallocatealpha($thumbnailImage, 255, 255, 255, 127);
        imagefilledrectangle($thumbnailImage, 0, 0, $thumbnailWidth, $thumbnailHeight, $transparent);
    }
    
    // 缩放图像
    imagecopyresampled(
        $thumbnailImage, $sourceImage,
        0, 0, 0, 0,
        $thumbnailWidth, $thumbnailHeight,
        $sourceWidth, $sourceHeight
    );
    
    // 保存缩略图
    $result = false;
    switch ($sourceType) {
        case IMAGETYPE_JPEG:
            $result = imagejpeg($thumbnailImage, $thumbnailPath, 85);
            break;
        case IMAGETYPE_PNG:
            $result = imagepng($thumbnailImage, $thumbnailPath);
            break;
        case IMAGETYPE_GIF:
            $result = imagegif($thumbnailImage, $thumbnailPath);
            break;
    }
    
    // 释放资源
    imagedestroy($sourceImage);
    imagedestroy($thumbnailImage);
    
    return $result;
}
?>
