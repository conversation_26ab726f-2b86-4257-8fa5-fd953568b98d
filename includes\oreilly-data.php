<?php
/**
 * O<PERSON><PERSON>区域数据获取函数
 * 严格区分主标题(h1/h2)和副标题(h3)的层级
 */

require_once __DIR__ . '/../config/database.php';

/**
 * 获取所有O'Reilly区域数据
 * @return array 包含所有区域数据的数组
 */
function getOreillyData() {
    static $cache = null;
    
    if ($cache !== null) {
        return $cache;
    }
    
    try {
        $db = db();
        
        // 获取O'Reilly英雄区域数据
        $hero = $db->fetchOne("SELECT * FROM oreilly_hero_section WHERE is_active = 1");
        
        // 获取专家课程区域数据
        $liveCourses = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'live_courses' AND is_active = 1");
        $aiAnswers = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'ai_answers' AND is_active = 1");
        
        // 获取专家展示区域数据
        $experts = $db->fetchOne("SELECT * FROM oreilly_experts_section WHERE is_active = 1");
        
        // 获取推荐区域数据
        $testimonial = $db->fetchOne("SELECT * FROM oreilly_testimonial_section WHERE is_active = 1");
        
        // 获取行动号召区域数据
        $cta = $db->fetchOne("SELECT * FROM oreilly_cta_section WHERE is_active = 1");
        
        $cache = [
            'hero' => $hero ?: getDefaultHeroData(),
            'courses' => [
                'live_courses' => $liveCourses ?: getDefaultLiveCoursesData(),
                'ai_answers' => $aiAnswers ?: getDefaultAiAnswersData()
            ],
            'experts' => $experts ?: getDefaultExpertsData(),
            'testimonial' => $testimonial ?: getDefaultTestimonialData(),
            'cta' => $cta ?: getDefaultCtaData()
        ];
        
        return $cache;
        
    } catch (Exception $e) {
        error_log("获取O'Reilly数据失败: " . $e->getMessage());
        return getDefaultOreillyData();
    }
}

/**
 * 获取O'Reilly英雄区域数据
 */
function getOreillyHeroData() {
    $data = getOreillyData();
    return $data['hero'];
}

/**
 * 获取专家课程区域数据
 */
function getOreillyCoursesData() {
    $data = getOreillyData();
    return $data['courses'];
}

/**
 * 获取专家展示区域数据
 */
function getOreillyExpertsData() {
    $data = getOreillyData();
    return $data['experts'];
}

/**
 * 获取推荐区域数据
 */
function getOreillyTestimonialData() {
    $data = getOreillyData();
    return $data['testimonial'];
}

/**
 * 获取行动号召区域数据
 */
function getOreillyCtaData() {
    $data = getOreillyData();
    return $data['cta'];
}

// 默认数据函数
function getDefaultHeroData() {
    return [
        'main_title' => 'Build the skills your teams need',
        'description' => 'Give your teams the O\'Reilly learning platform and equip them with the resources that drive business outcomes. <strong>Click on a feature below to explore.</strong>',
        'primary_button_text' => 'Request a demo ›',
        'primary_button_url' => '#',
        'secondary_button_text' => 'Try it free ›',
        'secondary_button_url' => '#'
    ];
}

function getDefaultLiveCoursesData() {
    return [
        'title' => 'Level up with<br>expert-led live courses',
        'description' => 'Reserve your seat for interactive workshops to gain hands-on experience—and ask questions along the way.',
        'button_text' => 'Pick your events ›',
        'button_url' => '#'
    ];
}

function getDefaultAiAnswersData() {
    return [
        'title' => 'O\'Reilly AI-powered Answers<br>just got even smarter',
        'description' => 'O\'Reilly Answers instantly generates information teams can trust, sourced from thousands of titles on our learning platform.',
        'button_text' => 'Discover Answers ›',
        'button_url' => '#'
    ];
}

function getDefaultExpertsData() {
    return [
        'main_title' => 'We share the knowledge of<br>innovators. You put it to work.',
        'description' => 'Tech teams love tapping into the minds of innovators through our expert-led courses, renowned text-based content, and bite-size online Superstream tech conferences. In fact, in a recent survey, one-third of tech practitioners rated O\'Reilly content a five out of five (excellent)—better than Pluralsight, LinkedIn Learning, Udacity, or Skillsoft.'
    ];
}

function getDefaultTestimonialData() {
    return [
        'title' => 'Why Jose uses O\'Reilly every day',
        'description' => 'As a principal software engineer, I rely on O\'Reilly\'s platform to keep my team updated with the latest technologies and best practices.',
        'stat1_number' => '5+',
        'stat1_label' => 'Years using',
        'stat2_number' => '200+',
        'stat2_label' => 'Books read',
        'stat3_number' => '50+',
        'stat3_label' => 'Courses completed',
        'button_text' => 'View more testimonials ›',
        'button_url' => '#'
    ];
}

function getDefaultCtaData() {
    return [
        'main_title' => 'See how O\'Reilly can help your tech teams stay ahead',
        'primary_button_text' => 'Request a demo',
        'primary_button_url' => '#',
        'secondary_button_text' => 'Try it free',
        'secondary_button_url' => '#'
    ];
}

function getDefaultOreillyData() {
    return [
        'hero' => getDefaultHeroData(),
        'courses' => [
            'live_courses' => getDefaultLiveCoursesData(),
            'ai_answers' => getDefaultAiAnswersData()
        ],
        'experts' => getDefaultExpertsData(),
        'testimonial' => getDefaultTestimonialData(),
        'cta' => getDefaultCtaData()
    ];
}
?>
