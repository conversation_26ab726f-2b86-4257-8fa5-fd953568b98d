<?php
session_start();

// 如果用户已登录，重定向到社区页面
require_once 'classes/Auth.php';
$auth = new Auth();
if ($auth->isLoggedIn()) {
    header('Location: community.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/register.css">
    <link rel="stylesheet" href="assets/css/toast.css">
</head>
<body>
    <!-- 背景渐变 -->
    <div class="background-gradient"></div>
    
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="image/bitlogo.png" alt="比特熊" class="brand-logo">
                <span class="brand-text">比特熊智慧系统</span>
            </div>
            <div class="nav-links">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="login.php" class="nav-link">登录</a>
            </div>
        </div>
    </nav>

    <!-- 注册表单容器 -->
    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1 class="register-title">创建账户</h1>
                <p class="register-subtitle">加入比特熊智慧系统，开启您的学习之旅</p>
            </div>

            <form id="registerForm" class="register-form" enctype="multipart/form-data">
                <!-- 头像上传区域 -->
                <div class="avatar-upload-section">
                    <div class="avatar-preview" id="avatarPreview">
                        <img src="assets/images/default-avatar.png" alt="头像预览" id="avatarImage">
                        <div class="avatar-overlay">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.1 3.89 23 5 23H19C20.1 23 21 22.1 21 21V9M20 9H15V4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            <span>点击上传头像</span>
                        </div>
                    </div>
                    <input type="file" id="avatarInput" name="avatar" accept="image/*" style="display: none;">
                    <p class="avatar-hint">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                </div>

                <!-- 表单字段 -->
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-container">
                        <input type="text" id="username" name="username" class="form-input" required>
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>
                    <div class="field-error" id="usernameError"></div>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">邮箱地址</label>
                    <div class="input-container">
                        <input type="email" id="email" name="email" class="form-input" required>
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2"/>
                                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>
                    <div class="field-error" id="emailError"></div>
                </div>

                <div class="form-group">
                    <label for="nickname" class="form-label">昵称</label>
                    <div class="input-container">
                        <input type="text" id="nickname" name="nickname" class="form-input" required>
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" stroke="currentColor" stroke-width="2"/>
                                <path d="M12 14C16.4183 14 20 17.5817 20 22H4C4 17.5817 7.58172 14 12 14Z" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>
                    <div class="field-error" id="nicknameError"></div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-container">
                        <input type="password" id="password" name="password" class="form-input" required>
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                                <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                    <div class="field-error" id="passwordError"></div>
                    <div class="password-strength" id="passwordStrength"></div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认密码</label>
                    <div class="input-container">
                        <input type="password" id="confirmPassword" name="confirmPassword" class="form-input" required>
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                                <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>
                    <div class="field-error" id="confirmPasswordError"></div>
                </div>

                <!-- 服务条款 -->
                <div class="form-group">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
                        <span class="checkmark"></span>
                        <span class="checkbox-text">我已阅读并同意 <a href="#" class="terms-link">服务条款</a> 和 <a href="#" class="terms-link">隐私政策</a></span>
                    </label>
                </div>

                <!-- 提交按钮 -->
                <button type="submit" class="register-button" id="registerButton">
                    <span class="button-text">创建账户</span>
                    <div class="button-loader" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>

            <!-- 登录链接 -->
            <div class="register-footer">
                <p class="login-prompt">已有账户？ <a href="login.php" class="login-link">立即登录</a></p>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="assets/js/register.js"></script>
</body>
</html>
