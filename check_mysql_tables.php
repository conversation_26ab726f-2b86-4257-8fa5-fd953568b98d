<?php
// 检查MySQL数据库表结构
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>MySQL数据库表结构检查</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    echo "<p class='success'>✅ 数据库连接成功!</p>\n";
    
    // 检查当前数据库
    $currentDb = $connection->query("SELECT DATABASE() as db")->fetch();
    echo "<p class='info'>当前数据库: " . ($currentDb['db'] ?? '未选择') . "</p>\n";
    
    // 检查所有表
    $tables = $connection->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p class='error'>❌ 数据库中没有表，需要初始化</p>\n";
    } else {
        echo "<p class='success'>✅ 找到 " . count($tables) . " 个表:</p>\n";
        echo "<ul>\n";
        foreach ($tables as $table) {
            echo "<li class='info'>{$table}</li>\n";
        }
        echo "</ul>\n";
        
        // 检查关键表的结构
        $keyTables = ['users', 'user_roles'];
        foreach ($keyTables as $table) {
            if (in_array($table, $tables)) {
                echo "<h3>表 '{$table}' 结构:</h3>\n";
                $columns = $connection->query("DESCRIBE {$table}")->fetchAll();
                echo "<table border='1' style='border-collapse:collapse;'>\n";
                echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>\n";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>{$column['Field']}</td>";
                    echo "<td>{$column['Type']}</td>";
                    echo "<td>{$column['Null']}</td>";
                    echo "<td>{$column['Key']}</td>";
                    echo "<td>{$column['Default']}</td>";
                    echo "<td>{$column['Extra']}</td>";
                    echo "</tr>\n";
                }
                echo "</table>\n";
                
                // 检查表中的数据
                $count = $connection->query("SELECT COUNT(*) as count FROM {$table}")->fetch();
                echo "<p class='info'>表 '{$table}' 中有 {$count['count']} 条记录</p>\n";
                
                if ($table === 'users' && $count['count'] > 0) {
                    $users = $connection->query("SELECT id, username, email, real_name, status FROM users LIMIT 5")->fetchAll();
                    echo "<h4>用户示例:</h4>\n";
                    echo "<table border='1' style='border-collapse:collapse;'>\n";
                    echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>真实姓名</th><th>状态</th></tr>\n";
                    foreach ($users as $user) {
                        echo "<tr>";
                        echo "<td>{$user['id']}</td>";
                        echo "<td>{$user['username']}</td>";
                        echo "<td>{$user['email']}</td>";
                        echo "<td>{$user['real_name']}</td>";
                        echo "<td>{$user['status']}</td>";
                        echo "</tr>\n";
                    }
                    echo "</table>\n";
                }
            } else {
                echo "<p class='error'>❌ 关键表 '{$table}' 不存在</p>\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 检查失败: " . $e->getMessage() . "</p>\n";
}
?>
