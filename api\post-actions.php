<?php
// 开启错误报告用于调试
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误，避免破坏JSON输出
ini_set('log_errors', 1);

// 开启输出缓冲，防止意外输出
ob_start();

session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// 清理任何意外的输出
ob_clean();
header('Content-Type: application/json');

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

// 读取输入数据
$rawInput = file_get_contents('php://input');
error_log("帖子API原始输入: " . $rawInput);

$input = json_decode($rawInput, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("帖子API JSON解析错误: " . json_last_error_msg());
    ob_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'JSON格式错误']);
    ob_end_flush();
    exit;
}

$action = $input['action'] ?? '';
$postId = intval($input['post_id'] ?? 0);

error_log("帖子API解析后: action={$action}, post_id={$postId}");

if (!$postId) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '无效的帖子ID']);
    ob_end_flush();
    exit;
}

try {
    $db = db();
    
    // 验证帖子是否存在
    $post = $db->fetchOne("SELECT * FROM posts WHERE id = ? AND status = 'published'", [$postId]);
    if (!$post) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '帖子不存在']);
        exit;
    }
    
    switch ($action) {
        case 'like':
        case 'dislike':
            handleLikeDislike($db, $currentUser['id'], $postId, $action);
            break;
            
        case 'bookmark':
            handleBookmark($db, $currentUser['id'], $postId);
            break;
            
        case 'share':
            handleShare($db, $currentUser['id'], $postId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            exit;
    }
    
} catch (Exception $e) {
    error_log("帖子操作错误: " . $e->getMessage());
    ob_clean(); // 清理任何之前的输出
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误']);
    ob_end_flush();
}

function handleLikeDislike($db, $userId, $postId, $action) {
    try {
        $db->query("START TRANSACTION");
        
        // 检查用户是否已经点赞/反对过
        $existingLike = $db->fetchOne(
            "SELECT * FROM likes WHERE user_id = ? AND target_type = 'post' AND target_id = ?",
            [$userId, $postId]
        );
        
        if ($existingLike) {
            if ($existingLike['type'] === $action) {
                // 取消点赞/反对
                $db->execute(
                    "DELETE FROM likes WHERE user_id = ? AND target_type = 'post' AND target_id = ?",
                    [$userId, $postId]
                );
                
                // 更新帖子统计
                $column = $action === 'like' ? 'like_count' : 'dislike_count';
                $db->execute("UPDATE posts SET {$column} = {$column} - 1 WHERE id = ?", [$postId]);
                
            } else {
                // 切换点赞/反对类型
                $db->execute(
                    "UPDATE likes SET type = ? WHERE user_id = ? AND target_type = 'post' AND target_id = ?",
                    [$action, $userId, $postId]
                );
                
                // 更新帖子统计
                if ($action === 'like') {
                    $db->execute("UPDATE posts SET like_count = like_count + 1, dislike_count = dislike_count - 1 WHERE id = ?", [$postId]);
                } else {
                    $db->execute("UPDATE posts SET dislike_count = dislike_count + 1, like_count = like_count - 1 WHERE id = ?", [$postId]);
                }
            }
        } else {
            // 新增点赞/反对
            $currentTime = date('Y-m-d H:i:s');
            $db->execute(
                "INSERT INTO likes (user_id, target_type, target_id, type, ip_address, created_at) VALUES (?, 'post', ?, ?, ?, ?)",
                [$userId, $postId, $action, $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '', $currentTime]
            );
            
            // 更新帖子统计
            $column = $action === 'like' ? 'like_count' : 'dislike_count';
            $db->execute("UPDATE posts SET {$column} = {$column} + 1 WHERE id = ?", [$postId]);
        }
        
        $db->query("COMMIT");
        
        // 获取更新后的数据
        $post = $db->fetchOne("SELECT like_count, dislike_count FROM posts WHERE id = ?", [$postId]);
        $userLike = $db->fetchOne(
            "SELECT type FROM likes WHERE user_id = ? AND target_type = 'post' AND target_id = ?",
            [$userId, $postId]
        );
        
        echo json_encode([
            'success' => true,
            'data' => [
                'like_count' => intval($post['like_count']),
                'dislike_count' => intval($post['dislike_count']),
                'user_liked' => $userLike && $userLike['type'] === 'like',
                'user_disliked' => $userLike && $userLike['type'] === 'dislike'
            ]
        ]);
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

function handleBookmark($db, $userId, $postId) {
    // 检查是否已收藏
    $existing = $db->fetchOne(
        "SELECT * FROM bookmarks WHERE user_id = ? AND post_id = ?",
        [$userId, $postId]
    );
    
    if ($existing) {
        // 取消收藏
        $db->execute(
            "DELETE FROM bookmarks WHERE user_id = ? AND post_id = ?",
            [$userId, $postId]
        );
        
        echo json_encode([
            'success' => true,
            'data' => ['bookmarked' => false]
        ]);
    } else {
        // 添加收藏
        $db->execute(
            "INSERT INTO bookmarks (user_id, post_id, created_at) VALUES (?, ?, NOW())",
            [$userId, $postId]
        );
        
        echo json_encode([
            'success' => true,
            'data' => ['bookmarked' => true]
        ]);
    }
}

function handleShare($db, $userId, $postId) {
    // 记录分享行为
    $db->execute(
        "INSERT INTO shares (user_id, post_id, share_type, ip_address, created_at) VALUES (?, ?, 'link', ?, NOW())",
        [$userId, $postId, $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '']
    );
    
    // 更新帖子分享数
    $db->execute("UPDATE posts SET share_count = share_count + 1 WHERE id = ?", [$postId]);
    
    // 获取更新后的分享数
    $post = $db->fetchOne("SELECT share_count FROM posts WHERE id = ?", [$postId]);
    
    echo json_encode([
        'success' => true,
        'data' => ['share_count' => intval($post['share_count'])]
    ]);
}

// 结束输出缓冲并发送内容
ob_end_flush();
?>
