<?php
/**
 * 环境检测验证脚本
 * 专门验证服务器环境是否被正确识别
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>环境检测验证</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f0f2f5; }
    .container { max-width: 900px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .header { text-align: center; margin-bottom: 30px; color: #333; }
    .section { margin: 20px 0; padding: 20px; border-radius: 8px; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f8f9fa; font-weight: bold; }
    .highlight { background-color: #fff3cd; font-weight: bold; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    .btn:hover { background: #0056b3; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; border: 1px solid #e9ecef; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔍 环境检测验证</h1>";
echo "<p>验证服务器环境是否被正确识别</p>";
echo "</div>";

// 1. 显示所有相关的服务器变量
echo "<div class='section info'>";
echo "<h3>📊 服务器环境变量</h3>";
echo "<table>";
echo "<tr><th>变量名</th><th>值</th><th>说明</th></tr>";

$serverVars = [
    'SERVER_NAME' => '服务器名称',
    'HTTP_HOST' => 'HTTP主机头',
    'SERVER_ADDR' => '服务器IP地址',
    'SERVER_SOFTWARE' => '服务器软件',
    'DOCUMENT_ROOT' => '文档根目录',
    'REQUEST_URI' => '请求URI',
    'HTTP_USER_AGENT' => '用户代理',
    'REMOTE_ADDR' => '客户端IP',
    'SERVER_PORT' => '服务器端口'
];

foreach ($serverVars as $var => $desc) {
    $value = $_SERVER[$var] ?? '未设置';
    $isImportant = in_array($var, ['SERVER_NAME', 'HTTP_HOST', 'SERVER_ADDR']);
    $class = $isImportant ? 'highlight' : '';
    echo "<tr class='{$class}'><td><strong>{$var}</strong></td><td>{$value}</td><td>{$desc}</td></tr>";
}
echo "</table>";
echo "</div>";

// 2. 环境检测逻辑测试
echo "<div class='section info'>";
echo "<h3>🧪 环境检测逻辑测试</h3>";

$serverName = $_SERVER['SERVER_NAME'] ?? '';
$serverAddr = $_SERVER['SERVER_ADDR'] ?? '';
$httpHost = $_SERVER['HTTP_HOST'] ?? '';

echo "<table>";
echo "<tr><th>检测条件</th><th>检测值</th><th>结果</th><th>说明</th></tr>";

// 检测条件1: SERVER_NAME包含bitbear.top
$condition1 = strpos($serverName, 'bitbear.top') !== false;
echo "<tr>";
echo "<td>SERVER_NAME包含'bitbear.top'</td>";
echo "<td>'{$serverName}'</td>";
echo "<td>" . ($condition1 ? '✅ 匹配' : '❌ 不匹配') . "</td>";
echo "<td>检查服务器名称是否包含域名</td>";
echo "</tr>";

// 检测条件2: SERVER_ADDR是目标IP
$condition2 = $serverAddr === '*************';
echo "<tr>";
echo "<td>SERVER_ADDR是'*************'</td>";
echo "<td>'{$serverAddr}'</td>";
echo "<td>" . ($condition2 ? '✅ 匹配' : '❌ 不匹配') . "</td>";
echo "<td>检查服务器IP是否为目标IP</td>";
echo "</tr>";

// 检测条件3: HTTP_HOST包含bitbear.top
$condition3 = strpos($httpHost, 'bitbear.top') !== false;
echo "<tr>";
echo "<td>HTTP_HOST包含'bitbear.top'</td>";
echo "<td>'{$httpHost}'</td>";
echo "<td>" . ($condition3 ? '✅ 匹配' : '❌ 不匹配') . "</td>";
echo "<td>检查HTTP主机头是否包含域名</td>";
echo "</tr>";

// 最终结果
$isServerEnvironment = $condition1 || $condition2 || $condition3;
echo "<tr class='highlight'>";
echo "<td><strong>最终环境判断</strong></td>";
echo "<td>-</td>";
echo "<td><strong>" . ($isServerEnvironment ? '🌐 服务器环境' : '💻 本地环境') . "</strong></td>";
echo "<td>基于以上条件的逻辑OR运算结果</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

// 3. 数据库配置预览
echo "<div class='section info'>";
echo "<h3>🗄️ 数据库配置预览</h3>";

if ($isServerEnvironment) {
    $dbConfig = [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '309290133q',
        'database' => 'bitbear_website',
        'charset' => 'utf8mb4',
        'ports' => [3306]
    ];
    echo "<div class='success'>✅ 使用服务器环境配置</div>";
} else {
    $dbConfig = [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '',
        'database' => 'bitbear_system',
        'charset' => 'utf8mb4',
        'ports' => [3307, 3306]
    ];
    echo "<div class='warning'>⚠️ 使用本地环境配置</div>";
}

echo "<table>";
echo "<tr><th>配置项</th><th>值</th></tr>";
echo "<tr><td>主机</td><td>{$dbConfig['host']}</td></tr>";
echo "<tr><td>用户名</td><td>{$dbConfig['username']}</td></tr>";
echo "<tr><td>密码</td><td>" . (empty($dbConfig['password']) ? '(空密码)' : '***已设置***') . "</td></tr>";
echo "<tr><td>数据库名</td><td>{$dbConfig['database']}</td></tr>";
echo "<tr><td>字符集</td><td>{$dbConfig['charset']}</td></tr>";
echo "<tr><td>端口</td><td>" . implode(', ', $dbConfig['ports']) . "</td></tr>";
echo "</table>";
echo "</div>";

// 4. 实际数据库连接测试
echo "<div class='section info'>";
echo "<h3>🔌 实际数据库连接测试</h3>";

try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 获取数据库信息
    $dbInfo = $connection->query("SELECT DATABASE() as current_db, USER() as current_user, VERSION() as version")->fetch();
    
    echo "<table>";
    echo "<tr><th>数据库信息</th><th>值</th></tr>";
    echo "<tr><td>当前数据库</td><td>{$dbInfo['current_db']}</td></tr>";
    echo "<tr><td>当前用户</td><td>{$dbInfo['current_user']}</td></tr>";
    echo "<tr><td>MySQL版本</td><td>{$dbInfo['version']}</td></tr>";
    echo "</table>";
    
    // 检查关键表是否存在
    $tables = ['users', 'user_roles', 'user_profiles'];
    echo "<h4>📋 关键表检查</h4>";
    echo "<table>";
    echo "<tr><th>表名</th><th>状态</th><th>记录数</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $count = $connection->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
            echo "<tr><td>{$table}</td><td>✅ 存在</td><td>{$count}</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>{$table}</td><td>❌ 不存在或无法访问</td><td>-</td></tr>";
        }
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败</div>";
    echo "<pre>错误信息: " . $e->getMessage() . "</pre>";
}

echo "</div>";

// 5. 建议和解决方案
echo "<div class='section info'>";
echo "<h3>💡 建议和解决方案</h3>";

if (!$isServerEnvironment) {
    echo "<div class='warning'>";
    echo "<h4>⚠️ 环境检测问题</h4>";
    echo "<p>当前环境被识别为本地环境，但您可能在云服务器上运行。可能的原因：</p>";
    echo "<ul>";
    echo "<li>域名配置问题：确保域名正确解析到服务器</li>";
    echo "<li>服务器配置问题：检查Web服务器的虚拟主机配置</li>";
    echo "<li>代理或CDN：如果使用了代理，可能影响环境变量</li>";
    echo "</ul>";
    echo "<p><strong>建议的解决方案：</strong></p>";
    echo "<ol>";
    echo "<li>检查域名DNS解析是否正确指向服务器IP</li>";
    echo "<li>检查Web服务器配置，确保正确设置了ServerName</li>";
    echo "<li>如果确认在云服务器上，可以手动修改环境检测逻辑</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<h4>✅ 环境检测正常</h4>";
    echo "<p>服务器环境被正确识别，数据库配置应该是正确的。</p>";
    echo "</div>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='快速修复云服务器注册问题.php' class='btn'>快速修复</a>";
echo "<a href='test_register_api_direct.php' class='btn'>API测试</a>";
echo "<a href='register.php' class='btn'>测试注册</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
