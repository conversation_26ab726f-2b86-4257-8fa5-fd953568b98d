-- 修复navbar_items表结构 - 添加缺失的icon字段
-- 请在宝塔面板的phpMyAdmin中执行此SQL

-- 1. 检查并添加icon字段（如果不存在）
ALTER TABLE `navbar_items` 
ADD COLUMN IF NOT EXISTS `icon` VARCHAR(255) NULL AFTER `parent_id`;

-- 2. 检查并添加索引（如果不存在）
ALTER TABLE `navbar_items` 
ADD INDEX IF NOT EXISTS `idx_parent` (`parent_id`),
ADD INDEX IF NOT EXISTS `idx_order` (`sort_order`);

-- 3. 更新现有菜单项的图标（可选）
UPDATE `navbar_items` SET `icon` = 'fas fa-home' WHERE `name` = '首页' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-info-circle' WHERE `name` LIKE '%关于%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-cogs' WHERE `name` LIKE '%服务%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-envelope' WHERE `name` LIKE '%联系%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-newspaper' WHERE `name` LIKE '%新闻%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-graduation-cap' WHERE `name` LIKE '%课程%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-gamepad' WHERE `name` LIKE '%游戏%' AND `icon` IS NULL;
UPDATE `navbar_items` SET `icon` = 'fas fa-users' WHERE `name` LIKE '%团队%' AND `icon` IS NULL;

-- 4. 验证修复结果
SELECT 'navbar_items表结构修复完成' as status;
SHOW COLUMNS FROM `navbar_items`;
