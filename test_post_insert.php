<?php
require_once 'config/database.php';

try {
    $db = db();
    
    // 检查用户表
    $users = $db->fetchAll("SELECT id, username FROM users LIMIT 3");
    echo "用户列表:\n";
    foreach ($users as $user) {
        echo "- ID: {$user['id']}, 用户名: {$user['username']}\n";
    }
    
    // 尝试插入帖子
    $userId = $users[0]['id'];
    $sql = "INSERT INTO posts (user_id, title, content, status, created_at) VALUES (?, ?, ?, 'published', NOW())";
    $params = [$userId, "测试帖子标题", "这是测试帖子的内容"];
    
    $result = $db->execute($sql, $params);
    $postId = $db->lastInsertId();
    
    echo "\n✓ 帖子插入成功，ID: {$postId}\n";
    
    // 验证插入结果
    $post = $db->fetchOne("SELECT * FROM posts WHERE id = ?", [$postId]);
    echo "帖子标题: {$post['title']}\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
