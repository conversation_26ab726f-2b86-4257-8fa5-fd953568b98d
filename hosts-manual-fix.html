<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hosts文件手动修复指南 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        
        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }
        
        .alert-success {
            background: #dcfce7;
            border-color: #16a34a;
            color: #15803d;
        }
        
        .step {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
            position: relative;
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-button:hover {
            background: #2563eb;
        }
        
        .test-button {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .file-path {
            background: #f1f5f9;
            padding: 0.5rem;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #cbd5e1;
        }
        
        .hosts-content {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-line;
            margin: 1rem 0;
        }
        
        .warning-box {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #991b1b;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .success-box {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Hosts文件手动修复指南</h1>
        
        <div class="alert alert-info">
            <strong>🎯 目标：</strong> 通过修改Hosts文件，将服务器IP直接解析到本地，避免DNS解析延迟问题。
        </div>
        
        <div class="alert alert-warning">
            <strong>⚠️ 重要提醒：</strong> 修改Hosts文件需要管理员权限，请确保以管理员身份操作。修改前建议备份原文件。
        </div>
        
        <!-- 步骤1：备份Hosts文件 -->
        <div class="step">
            <h3><span class="step-number">1</span>备份Hosts文件</h3>
            <p>首先备份现有的Hosts文件，以防出现问题时可以恢复：</p>
            
            <div class="file-path">
                Hosts文件位置：<code>C:\Windows\System32\drivers\etc\hosts</code>
            </div>
            
            <p><strong>操作步骤：</strong></p>
            <ol>
                <li>按 <kbd>Win + R</kbd> 打开运行对话框</li>
                <li>输入：<code>C:\Windows\System32\drivers\etc</code></li>
                <li>找到 <code>hosts</code> 文件</li>
                <li>右键复制，粘贴为 <code>hosts.backup</code></li>
            </ol>
        </div>
        
        <!-- 步骤2：以管理员身份打开记事本 -->
        <div class="step">
            <h3><span class="step-number">2</span>以管理员身份打开记事本</h3>
            <p>修改Hosts文件需要管理员权限：</p>
            
            <ol>
                <li>按 <kbd>Win</kbd> 键，搜索"记事本"</li>
                <li>右键点击"记事本"</li>
                <li>选择"以管理员身份运行"</li>
                <li>在记事本中，点击"文件" → "打开"</li>
                <li>导航到：<code>C:\Windows\System32\drivers\etc\</code></li>
                <li>将文件类型改为"所有文件(*.*)"</li>
                <li>选择并打开 <code>hosts</code> 文件</li>
            </ol>
        </div>
        
        <!-- 步骤3：添加Hosts条目 -->
        <div class="step">
            <h3><span class="step-number">3</span>添加Hosts条目</h3>
            <p>在Hosts文件末尾添加以下内容：</p>
            
            <div class="code-block">
                <button class="copy-button" onclick="copyToClipboard('hosts-content')">复制</button>
                <div id="hosts-content"># BitBear System - DNS Fix
************* bitbear.panel
************* bitbear.local
************* panel.bitbear.com
************* tencentcloud.panel
# End BitBear System</div>
            </div>
            
            <div class="success-box">
                <strong>✅ 说明：</strong> 这些条目将服务器IP直接映射到本地域名，避免DNS查询延迟。
            </div>
        </div>
        
        <!-- 步骤4：保存文件 -->
        <div class="step">
            <h3><span class="step-number">4</span>保存文件</h3>
            <p>保存修改后的Hosts文件：</p>
            
            <ol>
                <li>在记事本中按 <kbd>Ctrl + S</kbd> 保存</li>
                <li>如果提示权限不足，确保以管理员身份运行</li>
                <li>保存成功后关闭记事本</li>
            </ol>
        </div>
        
        <!-- 步骤5：刷新DNS缓存 -->
        <div class="step">
            <h3><span class="step-number">5</span>刷新DNS缓存</h3>
            <p>清除系统DNS缓存，使新的Hosts条目生效：</p>
            
            <ol>
                <li>按 <kbd>Win + R</kbd> 打开运行对话框</li>
                <li>输入 <code>cmd</code> 并按 <kbd>Ctrl + Shift + Enter</kbd>（以管理员身份运行）</li>
                <li>在命令提示符中输入以下命令：</li>
            </ol>
            
            <div class="code-block">
                <button class="copy-button" onclick="copyToClipboard('dns-flush')">复制</button>
                <div id="dns-flush">ipconfig /flushdns</div>
            </div>
            
            <p>看到"已成功刷新 DNS 解析缓存"消息即表示成功。</p>
        </div>
        
        <!-- 步骤6：测试修复效果 -->
        <div class="step">
            <h3><span class="step-number">6</span>测试修复效果</h3>
            <p>现在可以测试不同的访问方式：</p>
            
            <div style="margin: 1rem 0;">
                <a href="http://*************:8888/tencentcloud" class="test-button" target="_blank">
                    🔗 直接IP访问
                </a>
                <a href="http://bitbear.panel:8888/tencentcloud" class="test-button" target="_blank">
                    🌐 域名访问1
                </a>
                <a href="http://bitbear.local:8888/tencentcloud" class="test-button" target="_blank">
                    🏠 本地域名访问
                </a>
            </div>
            
            <div class="success-box">
                <strong>✅ 预期效果：</strong> 如果修复成功，域名访问应该和直接IP访问一样快，不会有DNS解析延迟。
            </div>
        </div>
        
        <!-- 高级DNS优化 -->
        <div class="step">
            <h3><span class="step-number">7</span>高级DNS优化（可选）</h3>
            <p>如果仍有DNS问题，可以优化系统DNS设置：</p>
            
            <h4>推荐的DNS服务器：</h4>
            <ul>
                <li><strong>主DNS：</strong> ********* (阿里DNS)</li>
                <li><strong>备DNS：</strong> ******* (Google DNS)</li>
            </ul>
            
            <h4>修改方法：</h4>
            <ol>
                <li>打开"设置" → "网络和Internet"</li>
                <li>点击"更改适配器选项"</li>
                <li>右键点击当前网络连接 → "属性"</li>
                <li>选择"Internet协议版本4(TCP/IPv4)" → "属性"</li>
                <li>选择"使用下面的DNS服务器地址"</li>
                <li>输入推荐的DNS地址</li>
            </ol>
        </div>
        
        <!-- 故障排除 -->
        <div class="step">
            <h3><span class="step-number">8</span>故障排除</h3>
            
            <h4>如果修复后仍无法访问：</h4>
            <ul>
                <li><strong>清除浏览器缓存：</strong> 按 <kbd>Ctrl + Shift + Delete</kbd></li>
                <li><strong>尝试隐私模式：</strong> 按 <kbd>Ctrl + Shift + N</kbd></li>
                <li><strong>重启浏览器：</strong> 完全关闭后重新打开</li>
                <li><strong>检查防火墙：</strong> 确保没有阻止8888端口</li>
            </ul>
            
            <h4>如果需要恢复原始设置：</h4>
            <ol>
                <li>删除添加的Hosts条目</li>
                <li>或者用备份文件替换当前Hosts文件</li>
                <li>重新刷新DNS缓存</li>
            </ol>
            
            <div class="warning-box">
                <strong>⚠️ 注意：</strong> 如果出现任何问题，可以随时恢复备份的Hosts文件。
            </div>
        </div>
        
        <!-- 自动化脚本选项 -->
        <div class="alert alert-info">
            <h4>🚀 自动化选项</h4>
            <p>如果您不想手动操作，可以运行我创建的PowerShell脚本：</p>
            <ol>
                <li>以管理员身份打开PowerShell</li>
                <li>运行：<code>.\dns-hosts-fix.ps1</code></li>
                <li>按照提示操作即可</li>
            </ol>
        </div>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                const button = element.parentNode.querySelector('.copy-button');
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#16a34a';
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.style.background = '#3b82f6';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制内容');
            });
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🛠️ Hosts文件修复指南已加载');
            console.log('📋 目标服务器: *************:8888');
            
            // 检测是否为管理员权限（简单检测）
            if (navigator.userAgent.indexOf('Windows') !== -1) {
                console.log('💡 提示: 请确保以管理员身份运行相关操作');
            }
        });
    </script>
</body>
</html>
