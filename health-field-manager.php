<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once 'config/database.php';

// 检查登录状态 - 兼容两种认证方式
$isAuthenticated = false;
$currentUser = null;

// 方式1: 新的Auth类认证
if (class_exists('Auth')) {
    require_once 'classes/Auth.php';
    $auth = new Auth();
    if ($auth->isLoggedIn() && $auth->isAdmin()) {
        $isAuthenticated = true;
        $currentUser = $auth->getCurrentUser();
    }
}

// 方式2: 管理后台认证（回退方案）
if (!$isAuthenticated && isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $isAuthenticated = true;
    $currentUser = [
        'id' => $_SESSION['user_id'] ?? 1,
        'username' => $_SESSION['admin_user'] ?? 'admin',
        'role_code' => $_SESSION['user_type'] ?? 'admin',
        'full_name' => '管理员'
    ];
}

if (!$isAuthenticated) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康字段管理 - 比特熊智慧系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #10b981;
            --secondary-color: #059669;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --success-color: #10b981;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
        }

        body {
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-title {
            margin: 0;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-description {
            margin: 0.5rem 0 0 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .fields-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .field-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .field-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .field-info {
            flex: 1;
        }

        .field-name {
            font-weight: 600;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            margin-bottom: 0.25rem;
        }

        .field-details {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .field-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.375rem 0.75rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: rgba(16, 185, 129, 0.3);
            border-color: rgba(16, 185, 129, 0.5);
        }

        .btn-primary:hover {
            background: rgba(16, 185, 129, 0.5);
        }

        .btn-secondary {
            background: rgba(107, 114, 128, 0.3);
            border-color: rgba(107, 114, 128, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.5);
        }

        .btn-danger {
            background: rgba(239, 68, 68, 0.3);
            border-color: rgba(239, 68, 68, 0.5);
        }

        .btn-danger:hover {
            background: rgba(239, 68, 68, 0.5);
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .system-field {
            background: rgba(251, 191, 36, 0.2);
            border-color: rgba(251, 191, 36, 0.5);
        }

        .system-badge {
            background: var(--warning-color);
            color: white;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            margin: 5% auto;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-title {
            margin: 0;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.8);
            color: #64748b;
        }

        .close-btn:hover {
            color: white;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            font-size: 0.875rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
        }

        .form-control:focus {
            outline: none;
            border-color: rgba(16, 185, 129, 0.5);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #64748b;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">健康字段管理</h1>
            <p class="page-description">管理健康日志的字段定义，支持添加自定义字段</p>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="showAddFieldModal()">
                    <i class="fas fa-plus me-1"></i>
                    添加字段
                </button>
                <button class="btn btn-secondary" onclick="loadFields()">
                    <i class="fas fa-sync-alt me-1"></i>
                    刷新
                </button>
                <button class="btn btn-secondary" onclick="window.close()">
                    <i class="fas fa-times me-1"></i>
                    关闭
                </button>
            </div>
        </div>

        <!-- 字段列表 -->
        <div class="fields-container">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin me-2"></i>
                正在加载字段列表...
            </div>
            
            <div id="fieldsContainer" style="display: none;">
                <!-- 字段列表将通过JavaScript生成 -->
            </div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-list-alt"></i>
                <h5>暂无字段</h5>
                <p>点击"添加字段"按钮创建第一个自定义字段</p>
            </div>
        </div>
    </div>

    <!-- 添加/编辑字段模态框 -->
    <div id="fieldModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加字段</h3>
                <button class="close-btn" onclick="closeFieldModal()">&times;</button>
            </div>
            
            <form id="fieldForm">
                <input type="hidden" id="fieldId" name="id">
                
                <div class="form-group">
                    <label class="form-label" for="fieldName">字段名称 *</label>
                    <input type="text" class="form-control" id="fieldName" name="field_name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="fieldType">字段类型</label>
                    <select class="form-control" id="fieldType" name="field_type">
                        <option value="number">数字</option>
                        <option value="text">文本</option>
                        <option value="boolean">是/否</option>
                        <option value="select">选择项</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="fieldUnit">单位</label>
                    <input type="text" class="form-control" id="fieldUnit" name="field_unit" placeholder="如：bpm、%、次等">
                </div>
                
                <div class="form-group" id="optionsGroup" style="display: none;">
                    <label class="form-label" for="fieldOptions">选择项 (每行一个)</label>
                    <textarea class="form-control" id="fieldOptions" name="field_options" rows="3" placeholder="选项1&#10;选项2&#10;选项3"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="displayOrder">显示顺序</label>
                    <input type="number" class="form-control" id="displayOrder" name="display_order" value="0">
                </div>
                
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-secondary" onclick="closeFieldModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let fields = [];
        let editingFieldId = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadFields();
            
            // 绑定表单提交事件
            document.getElementById('fieldForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveField();
            });
            
            // 绑定字段类型变化事件
            document.getElementById('fieldType').addEventListener('change', function() {
                toggleOptionsGroup();
            });
        });

        function loadFields() {
            showLoading(true);
            
            fetch('api/health-management.php?action=get_fields')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        fields = data.fields;
                        renderFields();
                    } else {
                        console.error('加载字段失败:', data.error);
                        alert('加载字段失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    alert('网络错误，请稍后重试');
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function renderFields() {
            const container = document.getElementById('fieldsContainer');
            const emptyState = document.getElementById('emptyState');
            
            if (fields.length === 0) {
                container.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            container.style.display = 'block';
            emptyState.style.display = 'none';
            
            container.innerHTML = '';
            
            fields.forEach(field => {
                const fieldItem = document.createElement('div');
                fieldItem.className = `field-item ${field.is_system == 1 ? 'system-field' : ''}`;
                
                fieldItem.innerHTML = `
                    <div class="field-info">
                        <div class="field-name">
                            ${field.field_name}
                            ${field.is_system == 1 ? '<span class="system-badge ms-2">系统字段</span>' : ''}
                        </div>
                        <div class="field-details">
                            类型: ${getFieldTypeText(field.field_type)} | 
                            单位: ${field.field_unit || '无'} | 
                            顺序: ${field.display_order}
                        </div>
                    </div>
                    <div class="field-actions">
                        ${field.is_system == 0 ? `
                            <button class="btn btn-sm btn-secondary" onclick="editField(${field.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteField(${field.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        ` : `
                            <span class="text-muted small">系统字段不可编辑</span>
                        `}
                    </div>
                `;
                
                container.appendChild(fieldItem);
            });
        }

        function getFieldTypeText(type) {
            const types = {
                'number': '数字',
                'text': '文本',
                'boolean': '是/否',
                'select': '选择项'
            };
            return types[type] || type;
        }

        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const fieldsContainer = document.getElementById('fieldsContainer');
            const emptyState = document.getElementById('emptyState');
            
            if (show) {
                loadingIndicator.style.display = 'block';
                fieldsContainer.style.display = 'none';
                emptyState.style.display = 'none';
            } else {
                loadingIndicator.style.display = 'none';
            }
        }

        function showAddFieldModal() {
            editingFieldId = null;
            document.getElementById('modalTitle').textContent = '添加字段';
            document.getElementById('fieldForm').reset();
            document.getElementById('fieldId').value = '';
            toggleOptionsGroup();
            document.getElementById('fieldModal').style.display = 'block';
        }

        function editField(fieldId) {
            const field = fields.find(f => f.id == fieldId);
            if (!field) return;
            
            if (field.is_system == 1) {
                alert('系统字段不能编辑');
                return;
            }
            
            editingFieldId = fieldId;
            document.getElementById('modalTitle').textContent = '编辑字段';
            document.getElementById('fieldId').value = field.id;
            document.getElementById('fieldName').value = field.field_name;
            document.getElementById('fieldType').value = field.field_type;
            document.getElementById('fieldUnit').value = field.field_unit || '';
            document.getElementById('displayOrder').value = field.display_order;
            
            if (field.field_options) {
                document.getElementById('fieldOptions').value = field.field_options.replace(/,/g, '\n');
            }
            
            toggleOptionsGroup();
            document.getElementById('fieldModal').style.display = 'block';
        }

        function deleteField(fieldId) {
            const field = fields.find(f => f.id == fieldId);
            if (!field) return;
            
            if (field.is_system == 1) {
                alert('系统字段不能删除');
                return;
            }
            
            if (!confirm(`确定要删除字段"${field.field_name}"吗？\n删除后相关的历史数据将无法显示。`)) {
                return;
            }
            
            fetch('api/health-management.php?action=delete_field', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: fieldId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('字段删除成功');
                    loadFields();
                } else {
                    alert('删除失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('删除失败:', error);
                alert('网络错误，删除失败');
            });
        }

        function saveField() {
            const formData = new FormData(document.getElementById('fieldForm'));
            const fieldData = {};
            
            for (let [key, value] of formData.entries()) {
                fieldData[key] = value;
            }
            
            // 处理选择项
            if (fieldData.field_type === 'select' && fieldData.field_options) {
                fieldData.field_options = fieldData.field_options.split('\n').filter(opt => opt.trim()).join(',');
            }
            
            fetch('api/health-management.php?action=save_field', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(fieldData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    closeFieldModal();
                    loadFields();
                } else {
                    alert('保存失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('保存失败:', error);
                alert('网络错误，保存失败');
            });
        }

        function closeFieldModal() {
            document.getElementById('fieldModal').style.display = 'none';
            editingFieldId = null;
        }

        function toggleOptionsGroup() {
            const fieldType = document.getElementById('fieldType').value;
            const optionsGroup = document.getElementById('optionsGroup');
            
            if (fieldType === 'select') {
                optionsGroup.style.display = 'block';
            } else {
                optionsGroup.style.display = 'none';
            }
        }

        // 点击模态框外部关闭
        document.getElementById('fieldModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFieldModal();
            }
        });
    </script>
</body>
</html>
