<?php
/**
 * 修复用户注册问题诊断脚本
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复用户注册问题 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .step.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .step.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .step.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 12px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 修复用户注册问题</h1>
            <p>诊断并修复用户注册功能的数据库问题</p>
        </div>
        
        <div class="content">
            <?php
            
            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<div class='step success'>";
                echo "<h3>✅ 数据库连接成功</h3>";
                echo "<p>已成功连接到数据库</p>";
                echo "</div>";
                
                // 1. 检查必要的表是否存在
                echo "<div class='step info'>";
                echo "<h3>🔍 检查数据库表结构</h3>";
                
                $requiredTables = ['user_roles', 'users', 'user_profiles'];
                $existingTables = [];
                
                foreach ($requiredTables as $table) {
                    try {
                        $result = $db->query("SHOW TABLES LIKE '{$table}'")->fetch();
                        if ($result) {
                            $existingTables[] = $table;
                            echo "<p>✅ {$table} 表存在</p>";
                        } else {
                            echo "<p>❌ {$table} 表不存在</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p>❌ 检查 {$table} 表时出错: " . $e->getMessage() . "</p>";
                    }
                }
                echo "</div>";
                
                // 2. 检查用户角色数据
                if (in_array('user_roles', $existingTables)) {
                    echo "<div class='step info'>";
                    echo "<h3>🔍 检查用户角色数据</h3>";
                    
                    $roles = $db->query("SELECT * FROM user_roles")->fetchAll();
                    if (empty($roles)) {
                        echo "<p>⚠️ 用户角色表为空，正在插入默认角色...</p>";
                        
                        try {
                            $db->exec("
                                INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                                ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                                ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                                ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                            ");
                            echo "<p>✅ 默认角色插入成功</p>";
                        } catch (Exception $e) {
                            echo "<p>❌ 插入默认角色失败: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p>✅ 用户角色数据正常，共 " . count($roles) . " 个角色</p>";
                        foreach ($roles as $role) {
                            echo "<p>- {$role['role_name']} ({$role['role_code']})</p>";
                        }
                    }
                    echo "</div>";
                }
                
                // 3. 检查用户表结构
                if (in_array('users', $existingTables)) {
                    echo "<div class='step info'>";
                    echo "<h3>🔍 检查用户表结构</h3>";
                    
                    $columns = $db->query("SHOW COLUMNS FROM users")->fetchAll();
                    $requiredColumns = ['id', 'username', 'email', 'password_hash', 'full_name', 'role_id', 'status'];
                    $existingColumns = array_column($columns, 'Field');
                    
                    echo "<div class='code-block'>";
                    echo "用户表字段:\n";
                    foreach ($columns as $column) {
                        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
                    }
                    echo "</div>";
                    
                    $missingColumns = array_diff($requiredColumns, $existingColumns);
                    if (!empty($missingColumns)) {
                        echo "<p>⚠️ 缺少字段: " . implode(', ', $missingColumns) . "</p>";
                    } else {
                        echo "<p>✅ 用户表结构完整</p>";
                    }
                    echo "</div>";
                }
                
                // 4. 检查用户资料表结构
                if (in_array('user_profiles', $existingTables)) {
                    echo "<div class='step info'>";
                    echo "<h3>🔍 检查用户资料表结构</h3>";
                    
                    $columns = $db->query("SHOW COLUMNS FROM user_profiles")->fetchAll();
                    echo "<div class='code-block'>";
                    echo "用户资料表字段:\n";
                    foreach ($columns as $column) {
                        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
                    }
                    echo "</div>";
                    echo "</div>";
                }
                
                // 5. 测试注册功能
                echo "<div class='step info'>";
                echo "<h3>🧪 测试注册功能</h3>";
                
                try {
                    // 模拟注册数据
                    $testUsername = 'test_user_' . time();
                    $testEmail = 'test_' . time() . '@example.com';
                    $testNickname = '测试用户';
                    $testPassword = 'test123456';
                    
                    // 开始事务
                    $db->exec("BEGIN");
                    
                    // 获取普通用户角色ID
                    $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
                    $roleId = $userRole ? $userRole['id'] : 3;
                    
                    echo "<p>使用角色ID: {$roleId}</p>";
                    
                    // 创建用户记录
                    $passwordHash = password_hash($testPassword, PASSWORD_DEFAULT);
                    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
                    
                    $stmt = $db->prepare($sql);
                    $result = $stmt->execute([
                        $testUsername,
                        $testEmail,
                        $passwordHash,
                        $testNickname,
                        $roleId
                    ]);
                    
                    if ($result) {
                        $userId = $db->lastInsertId();
                        echo "<p>✅ 用户创建成功，ID: {$userId}</p>";
                        
                        // 创建用户资料
                        if (in_array('user_profiles', $existingTables)) {
                            $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                           VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                            
                            $profileStmt = $db->prepare($profileSql);
                            $profileResult = $profileStmt->execute([
                                $userId,
                                $testNickname,
                                'assets/images/default-avatar.png'
                            ]);
                            
                            if ($profileResult) {
                                echo "<p>✅ 用户资料创建成功</p>";
                            } else {
                                echo "<p>❌ 用户资料创建失败</p>";
                            }
                        }
                        
                        // 清理测试数据
                        $db->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                        $db->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
                        echo "<p>✅ 测试数据清理完成</p>";
                        
                    } else {
                        echo "<p>❌ 用户创建失败</p>";
                    }
                    
                    // 提交事务
                    $db->exec("COMMIT");
                    
                } catch (Exception $e) {
                    $db->exec("ROLLBACK");
                    echo "<p>❌ 注册测试失败: " . $e->getMessage() . "</p>";
                    echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                }
                echo "</div>";
                
                // 6. 检查上传目录
                echo "<div class='step info'>";
                echo "<h3>🔍 检查上传目录</h3>";
                
                $uploadDir = 'uploads/avatars/';
                if (!is_dir($uploadDir)) {
                    if (mkdir($uploadDir, 0755, true)) {
                        echo "<p>✅ 创建上传目录成功: {$uploadDir}</p>";
                    } else {
                        echo "<p>❌ 创建上传目录失败: {$uploadDir}</p>";
                    }
                } else {
                    echo "<p>✅ 上传目录已存在: {$uploadDir}</p>";
                }
                
                if (is_writable($uploadDir)) {
                    echo "<p>✅ 上传目录可写</p>";
                } else {
                    echo "<p>⚠️ 上传目录不可写，请检查权限</p>";
                }
                echo "</div>";
                
                echo "<div class='step success'>";
                echo "<h3>🎉 诊断完成</h3>";
                echo "<p>如果所有检查都通过，注册功能应该可以正常工作了。</p>";
                echo "<p>如果仍有问题，请检查PHP错误日志获取详细信息。</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ 诊断失败</h3>";
                echo "<p>错误信息: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                echo "</div>";
            }
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="register.php" class="btn">测试注册</a>
                <a href="admin-dashboard.php" class="btn">返回管理后台</a>
                <a href="云服务器兼容性检查.php" class="btn">兼容性检查</a>
            </div>
        </div>
    </div>
</body>
</html>
