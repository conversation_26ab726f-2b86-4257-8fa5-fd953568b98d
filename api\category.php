<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// 检查用户是否登录
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$currentUser = $auth->getCurrentUser();

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $db = db();
    
    if ($action === 'get') {
        // 获取单个分类信息
        $id = intval($_GET['id'] ?? 0);
        
        if ($id <= 0) {
            throw new Exception('无效的分类ID');
        }
        
        $category = $db->fetchOne("SELECT * FROM categories WHERE id = ?", [$id]);
        if (!$category) {
            throw new Exception('分类不存在');
        }
        
        echo json_encode(['success' => true, 'data' => $category]);
        
    } elseif ($action === 'list') {
        // 获取分类列表
        $parentId = $_GET['parent_id'] ?? null;
        
        if ($parentId === null) {
            // 获取所有分类
            $categories = $db->fetchAll("SELECT * FROM categories ORDER BY parent_id, sort_order, name");
        } else {
            // 获取指定父分类下的子分类
            $parentId = intval($parentId);
            $categories = $db->fetchAll("SELECT * FROM categories WHERE parent_id = ? ORDER BY sort_order, name", [$parentId]);
        }
        
        echo json_encode(['success' => true, 'data' => $categories]);
        
    } elseif ($action === 'tree') {
        // 获取分类树
        $allCategories = $db->fetchAll("SELECT * FROM categories ORDER BY parent_id, sort_order, name");
        
        // 构建树形结构
        $categoryTree = [];
        $categoryMap = [];
        
        foreach ($allCategories as $category) {
            $categoryMap[$category['id']] = $category;
            $categoryMap[$category['id']]['children'] = [];
        }
        
        foreach ($allCategories as $category) {
            if ($category['parent_id']) {
                if (isset($categoryMap[$category['parent_id']])) {
                    $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$category['id']];
                }
            } else {
                $categoryTree[] = &$categoryMap[$category['id']];
            }
        }
        
        echo json_encode(['success' => true, 'data' => $categoryTree]);
        
    } else {
        throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
