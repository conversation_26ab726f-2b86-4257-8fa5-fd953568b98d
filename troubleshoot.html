<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排除 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .problem-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .problem-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .solution-steps {
            list-style: none;
            padding: 0;
        }
        
        .solution-steps li {
            background: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        
        .solution-steps li strong {
            color: #10b981;
        }
        
        .command-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        
        .copy-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: block;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-ok { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 故障排除指南</h1>
        
        <!-- 问题1: 无法访问网站 -->
        <div class="problem-section">
            <div class="problem-title">
                🌐 问题1: 无法访问网站
            </div>
            <ul class="solution-steps">
                <li>
                    <strong>步骤1:</strong> 检查PHP服务器是否运行
                    <div class="command-box">
                        php -S localhost:8000
                        <button class="copy-btn" onclick="copyToClipboard('php -S localhost:8000')">复制</button>
                    </div>
                </li>
                <li>
                    <strong>步骤2:</strong> 使用启动脚本
                    <div class="command-box">
                        start_server.bat
                        <button class="copy-btn" onclick="copyToClipboard('start_server.bat')">复制</button>
                    </div>
                    或者
                    <div class="command-box">
                        PowerShell -ExecutionPolicy Bypass -File start_server.ps1
                        <button class="copy-btn" onclick="copyToClipboard('PowerShell -ExecutionPolicy Bypass -File start_server.ps1')">复制</button>
                    </div>
                </li>
                <li>
                    <strong>步骤3:</strong> 检查端口是否被占用
                    <div class="command-box">
                        netstat -an | findstr :8000
                        <button class="copy-btn" onclick="copyToClipboard('netstat -an | findstr :8000')">复制</button>
                    </div>
                </li>
            </ul>
        </div>
        
        <!-- 问题2: 页面加载缓慢 -->
        <div class="problem-section">
            <div class="problem-title">
                ⏱️ 问题2: 页面加载缓慢
            </div>
            <ul class="solution-steps">
                <li>
                    <strong>解决方案1:</strong> 使用轻量版页面
                    <br>访问: <a href="index_lite.php" style="color: #60a5fa;">index_lite.php</a>
                </li>
                <li>
                    <strong>解决方案2:</strong> 检查数据库连接
                    <br>访问: <a href="diagnostic.php" style="color: #60a5fa;">diagnostic.php</a>
                </li>
                <li>
                    <strong>解决方案3:</strong> 重启PHP服务器
                    <div class="command-box">
                        taskkill /f /im php.exe && php -S localhost:8000
                        <button class="copy-btn" onclick="copyToClipboard('taskkill /f /im php.exe && php -S localhost:8000')">复制</button>
                    </div>
                </li>
            </ul>
        </div>
        
        <!-- 问题3: 数据库连接失败 -->
        <div class="problem-section">
            <div class="problem-title">
                🗄️ 问题3: 数据库连接失败
            </div>
            <ul class="solution-steps">
                <li>
                    <strong>步骤1:</strong> 启动MySQL服务
                    <div class="command-box">
                        net start mysql
                        <button class="copy-btn" onclick="copyToClipboard('net start mysql')">复制</button>
                    </div>
                </li>
                <li>
                    <strong>步骤2:</strong> 检查MySQL端口
                    <div class="command-box">
                        netstat -an | findstr :3306
                        <button class="copy-btn" onclick="copyToClipboard('netstat -an | findstr :3306')">复制</button>
                    </div>
                    或
                    <div class="command-box">
                        netstat -an | findstr :3307
                        <button class="copy-btn" onclick="copyToClipboard('netstat -an | findstr :3307')">复制</button>
                    </div>
                </li>
                <li>
                    <strong>步骤3:</strong> 初始化数据库
                    <div class="command-box">
                        php init_db.php
                        <button class="copy-btn" onclick="copyToClipboard('php init_db.php')">复制</button>
                    </div>
                </li>
            </ul>
        </div>
        
        <!-- 问题4: 权限错误 -->
        <div class="problem-section">
            <div class="problem-title">
                🔒 问题4: 权限错误
            </div>
            <ul class="solution-steps">
                <li>
                    <strong>解决方案1:</strong> 以管理员身份运行命令提示符
                </li>
                <li>
                    <strong>解决方案2:</strong> 设置PowerShell执行策略
                    <div class="command-box">
                        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
                        <button class="copy-btn" onclick="copyToClipboard('Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser')">复制</button>
                    </div>
                </li>
                <li>
                    <strong>解决方案3:</strong> 检查文件权限
                    <br>确保项目文件夹有读写权限
                </li>
            </ul>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
            <a href="test.html" class="action-btn">
                🧪 基础测试
            </a>
            <a href="quick_start.php" class="action-btn">
                🚀 快速启动
            </a>
            <a href="diagnostic.php" class="action-btn">
                🔍 系统诊断
            </a>
            <a href="index_lite.php" class="action-btn">
                🏠 轻量首页
            </a>
        </div>
        
        <!-- 实时状态检查 -->
        <div class="problem-section">
            <div class="problem-title">
                📊 实时状态检查
            </div>
            <div id="statusCheck">
                <p><span class="status-indicator status-warning"></span>正在检查系统状态...</p>
            </div>
        </div>
    </div>
    
    <script>
        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // 显示复制成功提示
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #10b981;
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 1000;
                    animation: slideIn 0.3s ease;
                `;
                notification.textContent = '已复制到剪贴板';
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
            });
        }
        
        // 检查系统状态
        async function checkSystemStatus() {
            const statusDiv = document.getElementById('statusCheck');
            let statusHTML = '';
            
            // 检查基本连接
            statusHTML += '<p><span class="status-indicator status-ok"></span>HTML页面加载正常</p>';
            
            // 检查PHP服务器
            try {
                const response = await fetch('test.php');
                if (response.ok) {
                    statusHTML += '<p><span class="status-indicator status-ok"></span>PHP服务器运行正常</p>';
                } else {
                    statusHTML += '<p><span class="status-indicator status-error"></span>PHP服务器响应异常</p>';
                }
            } catch (error) {
                statusHTML += '<p><span class="status-indicator status-error"></span>无法连接PHP服务器</p>';
            }
            
            // 检查数据库
            try {
                const response = await fetch('api/navbar.php');
                const data = await response.json();
                if (data.success) {
                    statusHTML += '<p><span class="status-indicator status-ok"></span>数据库连接正常</p>';
                } else {
                    statusHTML += '<p><span class="status-indicator status-error"></span>数据库连接失败</p>';
                }
            } catch (error) {
                statusHTML += '<p><span class="status-indicator status-warning"></span>数据库状态未知</p>';
            }
            
            statusDiv.innerHTML = statusHTML;
        }
        
        // 页面加载完成后检查状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkSystemStatus, 1000);
            
            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
