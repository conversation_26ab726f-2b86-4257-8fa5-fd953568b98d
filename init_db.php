<?php
/**
 * 数据库初始化脚本
 */

require_once 'config/database.php';

try {
    echo "开始初始化数据库...\n";

    $db = db();
    $db->initializeDatabase();

    echo "数据库初始化成功！\n";
    echo "默认管理员账号: admin\n";
    echo "默认密码: admin123\n";

    // 检查导航栏数据
    echo "\n检查导航栏数据...\n";
    $items = $db->fetchAll('SELECT * FROM navbar_items WHERE visible = 1 ORDER BY sort_order ASC, id ASC');
    echo "导航栏项目数量: " . count($items) . "\n";

    foreach ($items as $item) {
        $parentInfo = $item['parent_id'] ? " (父级: {$item['parent_id']})" : "";
        echo sprintf("- ID: %d, 名称: %s, URL: %s, 类型: %s%s\n",
            $item['id'], $item['name'], $item['url'], $item['type'], $parentInfo);
    }

} catch (Exception $e) {
    echo "数据库初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
