<?php
/**
 * 强制服务器配置修复脚本
 * 如果环境检测有问题，强制使用服务器数据库配置
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>强制服务器配置修复</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #ee5a24; }
    .btn.success { background: #28a745; }
    .btn.success:hover { background: #1e7e34; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🚨 强制服务器配置修复</h1>";
echo "<p>强制使用云服务器数据库配置，绕过环境检测</p>";
echo "</div>";

// 1. 显示当前环境检测结果
echo "<div class='info'>";
echo "<h3>🔍 当前环境检测结果</h3>";

$serverName = $_SERVER['SERVER_NAME'] ?? '';
$serverAddr = $_SERVER['SERVER_ADDR'] ?? '';
$httpHost = $_SERVER['HTTP_HOST'] ?? '';

$isServerEnv = (strpos($serverName, 'bitbear.top') !== false) ||
               ($serverAddr === '*************') ||
               (strpos($httpHost, 'bitbear.top') !== false);

echo "<p>SERVER_NAME: {$serverName}</p>";
echo "<p>SERVER_ADDR: {$serverAddr}</p>";
echo "<p>HTTP_HOST: {$httpHost}</p>";
echo "<p>环境检测结果: " . ($isServerEnv ? '服务器环境' : '本地环境') . "</p>";

if (!$isServerEnv) {
    echo "<div class='warning'>⚠️ 环境检测显示为本地环境，但您可能在云服务器上运行</div>";
}
echo "</div>";

// 2. 强制使用服务器配置进行数据库连接测试
echo "<div class='info'>";
echo "<h3>🔧 强制服务器配置测试</h3>";

$serverConfig = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '309290133q',
    'database' => 'bitbear_website',
    'charset' => 'utf8mb4',
    'port' => 3306
];

echo "<p>使用以下服务器配置进行连接测试：</p>";
echo "<table>";
echo "<tr><th>配置项</th><th>值</th></tr>";
foreach ($serverConfig as $key => $value) {
    $displayValue = ($key === 'password') ? '***' : $value;
    echo "<tr><td>{$key}</td><td>{$displayValue}</td></tr>";
}
echo "</table>";

try {
    // 直接使用服务器配置连接数据库
    $dsn = "mysql:host={$serverConfig['host']};port={$serverConfig['port']};dbname={$serverConfig['database']};charset={$serverConfig['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $serverConfig['username'], $serverConfig['password'], $options);
    
    echo "<div class='success'>✅ 强制服务器配置连接成功！</div>";
    
    // 获取数据库信息
    $dbInfo = $pdo->query("SELECT DATABASE() as current_db, USER() as current_user, VERSION() as version")->fetch();
    
    echo "<p>连接信息：</p>";
    echo "<ul>";
    echo "<li>当前数据库: {$dbInfo['current_db']}</li>";
    echo "<li>当前用户: {$dbInfo['current_user']}</li>";
    echo "<li>MySQL版本: {$dbInfo['version']}</li>";
    echo "</ul>";
    
    // 检查关键表
    echo "<h4>📋 关键表检查</h4>";
    $tables = ['users', 'user_roles', 'user_profiles'];
    $allTablesExist = true;
    
    echo "<table>";
    echo "<tr><th>表名</th><th>状态</th><th>记录数</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
            echo "<tr><td>{$table}</td><td>✅ 存在</td><td>{$count}</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>{$table}</td><td>❌ 不存在</td><td>-</td></tr>";
            $allTablesExist = false;
        }
    }
    echo "</table>";
    
    if (!$allTablesExist) {
        echo "<div class='warning'>⚠️ 部分表不存在，需要创建</div>";
        
        // 创建缺失的表
        echo "<h4>🔧 创建缺失的表</h4>";
        
        try {
            // 创建user_roles表
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS user_roles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    role_code VARCHAR(50) NOT NULL UNIQUE,
                    role_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    permissions TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            // 插入默认角色
            $pdo->exec("
                INSERT IGNORE INTO user_roles (role_code, role_name, description, permissions) VALUES
                ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
            ");
            
            echo "<div class='success'>✅ user_roles表创建成功</div>";
            
            // 创建users表
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email VARCHAR(100) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    avatar VARCHAR(255),
                    role_id INT DEFAULT 3,
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    last_login TIMESTAMP NULL,
                    login_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
                )
            ");
            
            echo "<div class='success'>✅ users表创建成功</div>";
            
            // 创建user_profiles表
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS user_profiles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    nickname VARCHAR(100),
                    bio TEXT,
                    signature VARCHAR(500),
                    avatar_url VARCHAR(255),
                    location VARCHAR(100),
                    website VARCHAR(255),
                    social_links JSON,
                    post_count INT DEFAULT 0,
                    comment_count INT DEFAULT 0,
                    like_received_count INT DEFAULT 0,
                    reputation_score INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_id (user_id)
                )
            ");
            
            echo "<div class='success'>✅ user_profiles表创建成功</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ 创建表失败: " . $e->getMessage() . "</div>";
        }
    }
    
    // 3. 测试注册功能
    echo "<div class='info'>";
    echo "<h3>🧪 测试注册功能</h3>";
    
    try {
        $testData = [
            'username' => 'forcetest_' . time(),
            'email' => 'forcetest_' . time() . '@example.com',
            'nickname' => '强制测试用户',
            'password' => 'test123456'
        ];
        
        echo "<p>测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "</p>";
        
        // 开始事务
        $pdo->beginTransaction();
        
        // 获取普通用户角色ID
        $userRole = $pdo->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
        $roleId = $userRole ? $userRole['id'] : 3;
        
        // 创建用户记录
        $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            $testData['username'],
            $testData['email'],
            $passwordHash,
            $testData['nickname'],
            $roleId
        ]);
        
        if ($result) {
            $userId = $pdo->lastInsertId();
            echo "<div class='success'>✅ 用户创建成功，ID: {$userId}</div>";
            
            // 创建用户资料
            $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                           VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
            
            $profileStmt = $pdo->prepare($profileSql);
            $profileResult = $profileStmt->execute([
                $userId,
                $testData['nickname'],
                'assets/images/default-avatar.png'
            ]);
            
            if ($profileResult) {
                echo "<div class='success'>✅ 用户资料创建成功</div>";
                
                // 清理测试数据
                $pdo->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
                echo "<div class='success'>✅ 测试数据清理完成</div>";
                
                echo "<div class='success'><strong>🎉 注册功能测试完全成功！</strong></div>";
                
            } else {
                echo "<div class='error'>❌ 用户资料创建失败</div>";
            }
        } else {
            echo "<div class='error'>❌ 用户创建失败</div>";
        }
        
        // 提交事务
        $pdo->commit();
        
    } catch (Exception $e) {
        $pdo->rollback();
        echo "<div class='error'>❌ 注册功能测试失败: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 强制服务器配置连接失败</div>";
    echo "<pre>错误信息: " . $e->getMessage() . "</pre>";
    
    echo "<div class='warning'>";
    echo "<h4>可能的问题：</h4>";
    echo "<ul>";
    echo "<li>数据库服务未启动</li>";
    echo "<li>数据库用户名或密码错误</li>";
    echo "<li>数据库不存在</li>";
    echo "<li>网络连接问题</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' class='btn success'>测试注册页面</a>";
echo "<a href='环境检测验证.php' class='btn'>环境检测</a>";
echo "<a href='快速修复云服务器注册问题.php' class='btn'>快速修复</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
