<?php
/**
 * 数据库连接测试文件
 * 请将此文件上传到服务器根目录，然后访问测试数据库连接
 */

echo "<h2>🔍 数据库连接测试</h2>";
echo "<hr>";

// 测试数据库连接参数
$configs = [
    'localhost_3306' => [
        'host' => 'localhost',
        'port' => 3306,
        'username' => 'root',
        'password' => '309290133q',
        'database' => 'bitbear_website'
    ],
    'localhost_3307' => [
        'host' => 'localhost', 
        'port' => 3307,
        'username' => 'root',
        'password' => '309290133q',
        'database' => 'bitbear_website'
    ],
    '127.0.0.1_3306' => [
        'host' => '127.0.0.1',
        'port' => 3306,
        'username' => 'root',
        'password' => '309290133q',
        'database' => 'bitbear_website'
    ]
];

echo "<h3>📊 服务器环境信息</h3>";
echo "<ul>";
echo "<li><strong>SERVER_NAME:</strong> " . ($_SERVER['SERVER_NAME'] ?? '未设置') . "</li>";
echo "<li><strong>HTTP_HOST:</strong> " . ($_SERVER['HTTP_HOST'] ?? '未设置') . "</li>";
echo "<li><strong>SERVER_ADDR:</strong> " . ($_SERVER['SERVER_ADDR'] ?? '未设置') . "</li>";
echo "<li><strong>DOCUMENT_ROOT:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? '未设置') . "</li>";
echo "<li><strong>PHP版本:</strong> " . PHP_VERSION . "</li>";
echo "</ul>";

echo "<h3>🔌 数据库连接测试</h3>";

foreach ($configs as $name => $config) {
    echo "<h4>测试配置: {$name}</h4>";
    
    try {
        $dsn = "mysql:host={$config['host']};port={$config['port']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        echo "<span style='color: green;'>✅ 连接成功</span><br>";
        
        // 测试数据库是否存在
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
        if ($stmt->rowCount() > 0) {
            echo "<span style='color: green;'>✅ 数据库 {$config['database']} 存在</span><br>";
            
            // 连接到具体数据库
            $dsn_db = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset=utf8mb4";
            $pdo_db = new PDO($dsn_db, $config['username'], $config['password']);
            
            // 检查表
            $stmt = $pdo_db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<span style='color: blue;'>📋 数据库表数量: " . count($tables) . "</span><br>";
            
            if (count($tables) > 0) {
                echo "<span style='color: blue;'>📋 表列表: " . implode(', ', array_slice($tables, 0, 5)) . (count($tables) > 5 ? '...' : '') . "</span><br>";
            }
            
        } else {
            echo "<span style='color: red;'>❌ 数据库 {$config['database']} 不存在</span><br>";
        }
        
    } catch (PDOException $e) {
        echo "<span style='color: red;'>❌ 连接失败: " . $e->getMessage() . "</span><br>";
    }
    
    echo "<br>";
}

echo "<h3>💡 建议的修复步骤</h3>";
echo "<ol>";
echo "<li>确认哪个配置连接成功</li>";
echo "<li>更新 config/database.php 中的服务器配置</li>";
echo "<li>确保数据库 bitbear_website 存在且有数据表</li>";
echo "<li>检查数据库用户权限</li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>测试完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
