<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试分类模态框</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
    </style>
</head>
<body>
    <h1>测试分类管理功能</h1>
    <p>点击下面的按钮测试添加分类模态框：</p>
    
    <button class="btn btn-primary" onclick="showAddCategoryModal()">
        添加分类
    </button>
    
    <div id="console-output" style="margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px;">
        <h3>控制台输出：</h3>
        <div id="log"></div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logDiv = document.getElementById('log');
            const logEntry = document.createElement('div');
            logEntry.textContent = args.join(' ');
            logEntry.style.marginBottom = '5px';
            logDiv.appendChild(logEntry);
        };
        
        function showAddCategoryModal() {
            console.log('showAddCategoryModal 函数被调用');
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>添加分类</h3>
                        <button class="modal-close" onclick="this.closest('.modal-overlay').remove()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="addCategoryForm">
                            <div class="form-group">
                                <label for="categoryName">分类名称 *</label>
                                <input type="text" id="categoryName" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="categoryDescription">分类描述</label>
                                <textarea id="categoryDescription" name="description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="categoryColor">分类颜色</label>
                                <input type="color" id="categoryColor" name="color" value="#3b82f6">
                            </div>
                            <div class="form-group">
                                <label for="categoryIcon">图标 (emoji)</label>
                                <input type="text" id="categoryIcon" name="icon" placeholder="💻">
                            </div>
                            <div class="form-group">
                                <label for="categorySortOrder">排序</label>
                                <input type="number" id="categorySortOrder" name="sort_order" value="0" min="0">
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="categoryIsActive" name="is_active" checked>
                                    启用分类
                                </label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                        <button class="btn btn-primary" onclick="testAddCategory()">添加</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function testAddCategory() {
            console.log('testAddCategory 函数被调用');
            const form = document.getElementById('addCategoryForm');
            const formData = new FormData(form);
            
            console.log('表单数据:', {
                name: formData.get('name'),
                description: formData.get('description'),
                color: formData.get('color'),
                icon: formData.get('icon'),
                sort_order: formData.get('sort_order'),
                is_active: formData.has('is_active')
            });
            
            alert('测试成功！表单数据已输出到控制台');
            document.querySelector('.modal-overlay').remove();
        }
    </script>
</body>
</html>
