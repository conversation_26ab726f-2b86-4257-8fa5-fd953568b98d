<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>数据库表结构检查</h2>";

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    // 检查所有表
    $tables = ['comments', 'users', 'posts', 'likes', 'comment_reports', 'user_profiles'];
    
    foreach ($tables as $table) {
        echo "<h3>表: $table</h3>";
        
        // 检查表是否存在
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ 表存在</p>";
            
            // 显示表结构
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
            foreach ($columns as $col) {
                echo "<tr>";
                echo "<td>" . $col['Field'] . "</td>";
                echo "<td>" . $col['Type'] . "</td>";
                echo "<td>" . $col['Null'] . "</td>";
                echo "<td>" . $col['Key'] . "</td>";
                echo "<td>" . $col['Default'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 显示数据量
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>数据量: " . $result['count'] . " 条</p>";
            
            // 如果是likes表，显示一些示例数据
            if ($table === 'likes' && $result['count'] > 0) {
                echo "<h4>示例数据:</h4>";
                $stmt = $db->query("SELECT * FROM likes LIMIT 5");
                $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table border='1' style='border-collapse: collapse;'>";
                if (!empty($samples)) {
                    echo "<tr>";
                    foreach (array_keys($samples[0]) as $key) {
                        echo "<th>$key</th>";
                    }
                    echo "</tr>";
                    
                    foreach ($samples as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars($value) . "</td>";
                        }
                        echo "</tr>";
                    }
                }
                echo "</table>";
            }
            
        } else {
            echo "<p style='color: red;'>✗ 表不存在</p>";
        }
        
        echo "<hr>";
    }
    
    // 测试评论统计查询
    echo "<h3>评论统计测试</h3>";
    
    // 测试基本评论统计
    $stmt = $db->query("SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN status = 'hidden' THEN 1 ELSE 0 END) as hidden
        FROM comments");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>评论统计:</p>";
    echo "<ul>";
    echo "<li>总评论数: " . $stats['total'] . "</li>";
    echo "<li>已发布: " . $stats['published'] . "</li>";
    echo "<li>已隐藏: " . $stats['hidden'] . "</li>";
    echo "</ul>";
    
    // 如果likes表存在，测试点赞统计
    $stmt = $db->query("SHOW TABLES LIKE 'likes'");
    if ($stmt->rowCount() > 0) {
        echo "<h4>点赞统计测试</h4>";
        
        // 获取一个评论ID进行测试
        $stmt = $db->query("SELECT id FROM comments LIMIT 1");
        $comment = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($comment) {
            $commentId = $comment['id'];
            
            $stmt = $db->prepare("SELECT 
                (SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = ? AND type = 'like') as like_count,
                (SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = ? AND type = 'dislike') as dislike_count");
            $stmt->execute([$commentId, $commentId]);
            $likeStats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p>评论ID $commentId 的点赞统计:</p>";
            echo "<ul>";
            echo "<li>点赞数: " . $likeStats['like_count'] . "</li>";
            echo "<li>踩数: " . $likeStats['dislike_count'] . "</li>";
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
