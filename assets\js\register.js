// 用户注册页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const avatarInput = document.getElementById('avatarInput');
    const avatarPreview = document.getElementById('avatarPreview');
    const avatarImage = document.getElementById('avatarImage');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const passwordToggle = document.getElementById('passwordToggle');
    const registerButton = document.getElementById('registerButton');

    // 头像上传处理
    avatarPreview.addEventListener('click', function() {
        avatarInput.click();
    });

    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 验证文件类型
            if (!file.type.match('image.*')) {
                showToast('请选择图片文件', 'error');
                return;
            }

            // 验证文件大小 (2MB)
            if (file.size > 2 * 1024 * 1024) {
                showToast('图片文件大小不能超过2MB', 'error');
                return;
            }

            // 预览图片
            const reader = new FileReader();
            reader.onload = function(e) {
                avatarImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });

    // 密码显示/隐藏切换
    passwordToggle.addEventListener('click', function() {
        const type = passwordInput.type === 'password' ? 'text' : 'password';
        passwordInput.type = type;
        
        // 更新图标
        const icon = passwordToggle.querySelector('svg');
        if (type === 'text') {
            icon.innerHTML = '<path d="M17.94 17.94C16.2 19.68 14.21 20.5 12 20.5C7.58 20.5 1 12 1 12S2.68 9.84 5.47 7.69M9.9 4.24C10.58 4.08 11.3 4 12 4C16.42 4 23 12 23 12S22.18 13.35 20.82 14.94M14.12 14.12C13.8 14.44 13.4 14.67 12.97 14.82C12.54 14.97 12.08 15.03 11.63 14.99C11.18 14.95 10.75 14.81 10.37 14.58C9.99 14.35 9.67 14.04 9.43 13.66C9.19 13.28 9.05 12.85 9.01 12.4C8.97 11.95 9.03 11.49 9.18 11.06C9.33 10.63 9.56 10.23 9.88 9.91" stroke="currentColor" stroke-width="2"/><line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>';
        } else {
            icon.innerHTML = '<path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>';
        }
    });

    // 密码强度检测
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        const strengthContainer = document.getElementById('passwordStrength');
        
        if (password.length === 0) {
            strengthContainer.innerHTML = '';
            return;
        }

        const strength = calculatePasswordStrength(password);
        displayPasswordStrength(strength, strengthContainer);
    });

    // 实时验证
    const inputs = ['username', 'email', 'nickname', 'password', 'confirmPassword'];
    inputs.forEach(inputName => {
        const input = document.getElementById(inputName);
        input.addEventListener('blur', () => validateField(inputName));
        input.addEventListener('input', () => clearFieldError(inputName));
    });

    // 表单提交
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        const formData = new FormData(registerForm);
        
        // 显示加载状态
        setLoadingState(true);

        try {
            const response = await fetch('api/register.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showToast('注册成功！正在跳转到登录页面...', 'success');
                setTimeout(() => {
                    window.location.href = 'login.php';
                }, 2000);
            } else {
                showToast(result.message || '注册失败，请重试', 'error');
                
                // 显示字段错误
                if (result.errors) {
                    Object.keys(result.errors).forEach(field => {
                        showFieldError(field, result.errors[field]);
                    });
                }
            }
        } catch (error) {
            console.error('注册错误:', error);
            showToast('网络错误，请检查网络连接后重试', 'error');
        } finally {
            setLoadingState(false);
        }
    });

    // 验证单个字段
    function validateField(fieldName) {
        const input = document.getElementById(fieldName);
        const value = input.value.trim();
        let isValid = true;
        let errorMessage = '';

        switch (fieldName) {
            case 'username':
                if (!value) {
                    errorMessage = '请输入用户名';
                    isValid = false;
                } else if (value.length < 3) {
                    errorMessage = '用户名至少需要3个字符';
                    isValid = false;
                } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(value)) {
                    errorMessage = '用户名只能包含字母、数字、下划线和中文';
                    isValid = false;
                }
                break;

            case 'email':
                if (!value) {
                    errorMessage = '请输入邮箱地址';
                    isValid = false;
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    errorMessage = '请输入有效的邮箱地址';
                    isValid = false;
                }
                break;

            case 'nickname':
                if (!value) {
                    errorMessage = '请输入昵称';
                    isValid = false;
                } else if (value.length < 2) {
                    errorMessage = '昵称至少需要2个字符';
                    isValid = false;
                }
                break;

            case 'password':
                if (!value) {
                    errorMessage = '请输入密码';
                    isValid = false;
                } else if (value.length < 6) {
                    errorMessage = '密码至少需要6个字符';
                    isValid = false;
                }
                break;

            case 'confirmPassword':
                const password = document.getElementById('password').value;
                if (!value) {
                    errorMessage = '请确认密码';
                    isValid = false;
                } else if (value !== password) {
                    errorMessage = '两次输入的密码不一致';
                    isValid = false;
                }
                break;
        }

        if (!isValid) {
            showFieldError(fieldName, errorMessage);
        } else {
            clearFieldError(fieldName);
        }

        return isValid;
    }

    // 验证整个表单
    function validateForm() {
        let isValid = true;
        
        inputs.forEach(inputName => {
            if (!validateField(inputName)) {
                isValid = false;
            }
        });

        // 验证服务条款
        const agreeTerms = document.getElementById('agreeTerms');
        if (!agreeTerms.checked) {
            showToast('请阅读并同意服务条款和隐私政策', 'error');
            isValid = false;
        }

        return isValid;
    }

    // 显示字段错误
    function showFieldError(fieldName, message) {
        const errorElement = document.getElementById(fieldName + 'Error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'flex';
        }
    }

    // 清除字段错误
    function clearFieldError(fieldName) {
        const errorElement = document.getElementById(fieldName + 'Error');
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }
    }

    // 计算密码强度
    function calculatePasswordStrength(password) {
        let score = 0;
        
        // 长度
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;
        
        // 包含小写字母
        if (/[a-z]/.test(password)) score += 1;
        
        // 包含大写字母
        if (/[A-Z]/.test(password)) score += 1;
        
        // 包含数字
        if (/\d/.test(password)) score += 1;
        
        // 包含特殊字符
        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

        return Math.min(score, 4);
    }

    // 显示密码强度
    function displayPasswordStrength(strength, container) {
        const levels = ['弱', '一般', '良好', '强'];
        const colors = ['#f87171', '#fbbf24', '#4ade80', '#22c55e'];
        
        let html = '<div class="password-strength">';
        for (let i = 0; i < 4; i++) {
            const isActive = i < strength;
            html += `<div class="strength-bar ${isActive ? 'active' : ''}" style="background-color: ${isActive ? colors[Math.min(strength - 1, 3)] : 'rgba(255, 255, 255, 0.2)'}"></div>`;
        }
        html += '</div>';
        
        if (strength > 0) {
            html += `<div style="color: ${colors[Math.min(strength - 1, 3)]}; font-size: 0.75rem; margin-top: 0.25rem;">密码强度: ${levels[Math.min(strength - 1, 3)]}</div>`;
        }
        
        container.innerHTML = html;
    }

    // 设置加载状态
    function setLoadingState(loading) {
        const buttonText = registerButton.querySelector('.button-text');
        const buttonLoader = registerButton.querySelector('.button-loader');
        
        if (loading) {
            buttonText.style.display = 'none';
            buttonLoader.style.display = 'block';
            registerButton.disabled = true;
        } else {
            buttonText.style.display = 'block';
            buttonLoader.style.display = 'none';
            registerButton.disabled = false;
        }
    }
});

// Toast 通知函数
function showToast(message, type = 'info') {
    const container = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icons = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
    };
    
    toast.innerHTML = `
        <div class="toast-icon">${icons[type] || icons.info}</div>
        <div class="toast-message">${message}</div>
        <button class="toast-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    container.appendChild(toast);
    
    // 自动移除
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
