# PowerShell SSH连接脚本
Write-Host "正在连接到比特熊服务器..." -ForegroundColor Green
Write-Host "服务器IP: *************" -ForegroundColor Yellow
Write-Host "用户名: root" -ForegroundColor Yellow
Write-Host "密码: ZbDX7%=]?H2(LAUz" -ForegroundColor Red
Write-Host ""
Write-Host "注意: 输入密码时不会显示字符，这是正常的安全特性" -ForegroundColor Cyan
Write-Host ""

# 添加Git SSH到PATH
$env:PATH += ";C:\Program Files\Git\usr\bin"

# 连接到服务器
& ssh root@*************
