<?php
/**
 * 统计数据API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    $db = db();
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $type = $_GET['type'] ?? 'all';
        
        switch ($type) {
            case 'users':
                getUserStats($db);
                break;
            case 'system':
                getSystemStats($db);
                break;
            case 'all':
            default:
                getAllStats($db);
                break;
        }
    } else {
        http_response_code(405);
        echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 获取用户统计数据
 */
function getUserStats($db) {
    // 获取用户角色统计
    $sql = "SELECT 
                ur.role_name,
                ur.role_code,
                COUNT(u.id) as count
            FROM user_roles ur
            LEFT JOIN users u ON ur.id = u.role_id AND u.status = 'active'
            GROUP BY ur.id, ur.role_name, ur.role_code
            ORDER BY ur.id";
    
    $roleStats = $db->fetchAll($sql);
    
    // 获取总用户数
    $totalUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    
    // 获取今日新增用户
    $todayUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()");
    
    // 获取在线用户数（假设30分钟内有活动的用户为在线）
    $onlineUsers = $db->fetchOne("SELECT COUNT(DISTINCT user_id) as count FROM user_activities WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)");
    
    echo json_encode([
        'success' => true,
        'data' => [
            'roles' => $roleStats,
            'total_users' => $totalUsers['count'],
            'today_users' => $todayUsers['count'],
            'online_users' => $onlineUsers['count']
        ]
    ]);
}

/**
 * 获取系统统计数据
 */
function getSystemStats($db) {
    // 获取系统信息
    $systemInfo = [
        'server_os' => php_uname('s') . ' ' . php_uname('r'),
        'php_version' => PHP_VERSION,
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 1) . 'MB',
        'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 1) . 'MB',
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get()
    ];
    
    // 尝试获取系统运行时间
    $uptime = 'N/A';
    if (function_exists('shell_exec') && !in_array('shell_exec', explode(',', ini_get('disable_functions')))) {
        $uptimeCmd = shell_exec('uptime -p 2>/dev/null');
        if ($uptimeCmd) {
            $uptime = trim(str_replace('up ', '', $uptimeCmd));
        }
    } else {
        // 使用PHP启动时间作为替代
        $uptime = '约 ' . round((time() - $_SERVER['REQUEST_TIME']) / 3600, 1) . ' 小时';
    }
    $systemInfo['uptime'] = $uptime;
    
    // 获取数据库统计
    $dbStats = [
        'total_events' => $db->fetchOne("SELECT COUNT(*) as count FROM events")['count'],
        'total_notifications' => $db->fetchOne("SELECT COUNT(*) as count FROM notifications")['count'],
        'total_activities' => $db->fetchOne("SELECT COUNT(*) as count FROM user_activities")['count']
    ];
    
    echo json_encode([
        'success' => true,
        'data' => [
            'system' => $systemInfo,
            'database' => $dbStats
        ]
    ]);
}

/**
 * 获取所有统计数据
 */
function getAllStats($db) {
    // 用户统计
    $userRoleStats = $db->fetchAll("
        SELECT 
            ur.role_name,
            ur.role_code,
            COUNT(u.id) as count
        FROM user_roles ur
        LEFT JOIN users u ON ur.id = u.role_id AND u.status = 'active'
        GROUP BY ur.id, ur.role_name, ur.role_code
        ORDER BY ur.id
    ");
    
    $totalUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'];
    $todayUsers = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()")['count'];
    $onlineUsers = $db->fetchOne("SELECT COUNT(DISTINCT user_id) as count FROM user_activities WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)")['count'];
    
    // 系统信息
    $systemInfo = [
        'server_os' => php_uname('s') . ' ' . php_uname('r'),
        'php_version' => PHP_VERSION,
        'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 1) . 'MB',
        'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 1) . 'MB'
    ];
    
    // 尝试获取系统运行时间
    $uptime = 'N/A';
    if (function_exists('shell_exec') && !in_array('shell_exec', explode(',', ini_get('disable_functions')))) {
        $uptimeCmd = shell_exec('uptime -p 2>/dev/null');
        if ($uptimeCmd) {
            $uptime = trim(str_replace('up ', '', $uptimeCmd));
        }
    } else {
        // 使用PHP启动时间作为替代
        $uptime = '约 ' . round((time() - $_SERVER['REQUEST_TIME']) / 3600, 1) . ' 小时';
    }
    $systemInfo['uptime'] = $uptime;
    
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => [
                'roles' => $userRoleStats,
                'total' => $totalUsers,
                'today' => $todayUsers,
                'online' => $onlineUsers
            ],
            'system' => $systemInfo
        ]
    ]);
}
?>
