/* 社区页面样式 - <PERSON><PERSON><PERSON>风格 */

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: linear-gradient(242deg, rgb(142, 34, 167) 0.01%, rgb(37, 99, 234) 97.15%);
    min-height: 100vh;
}

/* 导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.2rem;
}

.nav-brand a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #1a1a1a;
}

.logo {
    height: 40px;
    margin-right: 12px;
}

.nav-menu {
    display: flex;
    gap: 32px;
}

.nav-link {
    text-decoration: none;
    color: #666;
    font-weight: 500;
    transition: color 0.2s;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #3b82f6;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #3b82f6;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(59, 130, 246, 0.1);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.btn-primary {
    background: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6b7280;
    color: white;
    position: relative;
}

.btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-1px);
}

.draft-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.btn-outline:hover {
    background: #3b82f6;
    color: white;
}

.btn-large {
    padding: 12px 24px;
    font-size: 16px;
}

/* 主要内容 */
.main-content {
    padding: 40px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 社区头部 */
.community-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    margin-bottom: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.community-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.community-description {
    font-size: 1.1rem;
    color: #666;
}

/* 过滤栏 */
.filters-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
}

.categories-filter {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.category-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    background: rgba(0, 0, 0, 0.05);
    color: #666;
    transition: all 0.2s;
    border: 2px solid transparent;
}

.category-tag:hover {
    background: rgba(var(--category-color, 59, 130, 246), 0.1);
    color: rgb(var(--category-color, 59, 130, 246));
}

.category-tag.active {
    background: rgb(var(--category-color, 59, 130, 246));
    color: white;
}

.category-icon {
    font-size: 16px;
}

.sort-filter select {
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

/* 帖子容器 */
.posts-container {
    margin-bottom: 40px;
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 帖子卡片 */
.post-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.2s;
    position: relative;
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.post-card.pinned {
    border-left: 4px solid #f59e0b;
}

.pin-indicator {
    position: absolute;
    top: 16px;
    right: 16px;
    background: #f59e0b;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-avatar-link {
    display: block;
    border-radius: 50%;
    transition: transform 0.2s;
}

.author-avatar-link:hover {
    transform: scale(1.1);
}

.author-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
}

.author-name-link {
    text-decoration: none;
    color: inherit;
}

.author-name-link:hover .author-name {
    color: #3b82f6;
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: #1a1a1a;
}

.post-time {
    font-size: 14px;
    color: #666;
}

.post-category {
    background: var(--category-color, #3b82f6);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 分类颜色样式 */
.post-category[style*="#3b82f6"] { background: #3b82f6 !important; } /* 蓝色 - 技术讨论 */
.post-category[style*="#10b981"] { background: #10b981 !important; } /* 绿色 - 学习交流 */
.post-category[style*="#f59e0b"] { background: #f59e0b !important; } /* 橙色 - 项目展示 */
.post-category[style*="#ef4444"] { background: #ef4444 !important; } /* 红色 - 求助问答 */
.post-category[style*="#8b5cf6"] { background: #8b5cf6 !important; } /* 紫色 - 闲聊灌水 */
.post-category[style*="#dc2626"] { background: #dc2626 !important; } /* 深红色 - 公告通知 */

.post-content {
    margin-bottom: 20px;
}

.post-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
}

.post-title a {
    text-decoration: none;
    color: #1a1a1a;
    transition: color 0.2s;
}

.post-title a:hover {
    color: #3b82f6;
}

.post-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 16px;
}

.post-image {
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 16px;
}

.post-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.post-stats {
    display: flex;
    gap: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    font-size: 14px;
}

.stat-item i {
    font-size: 16px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
}

.empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 24px;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: #374151;
    margin-bottom: 8px;
}

.empty-state p {
    color: #6b7280;
    margin-bottom: 24px;
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 40px;
}

.page-btn,
.page-num {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    border: 1px solid #e5e7eb;
}

.page-btn:hover,
.page-num:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-1px);
}

.page-num.active {
    background: #3b82f6;
    color: white;
}

.page-dots {
    padding: 10px 8px;
    color: #9ca3af;
}

/* 帖子发布表单样式 */
.post-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 900px;
    margin: 0 auto;
}

.form-header {
    text-align: center;
    margin-bottom: 40px;
}

.form-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.form-description {
    color: #666;
    font-size: 1.1rem;
}

.post-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-label {
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.required {
    color: #ef4444;
}

.form-input,
.form-select,
.form-textarea {
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s;
    background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-help {
    font-size: 12px;
    color: #6b7280;
}

.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn-secondary {
    background: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background: #4b5563;
}

/* 警告框 */
.alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 24px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
}

.preview-post {
    line-height: 1.6;
}

.preview-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 24px;
    line-height: 1.3;
}

.preview-content {
    color: #374151;
}

.preview-content h1,
.preview-content h2,
.preview-content h3,
.preview-content h4,
.preview-content h5,
.preview-content h6 {
    margin: 24px 0 16px 0;
    color: #1a1a1a;
}

.preview-content p {
    margin-bottom: 16px;
}

.preview-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

.preview-content pre {
    background: #f8fafc;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 16px 0;
}

.preview-content blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 16px;
    margin: 16px 0;
    color: #6b7280;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 16px;
    }

    .nav-menu {
        display: none;
    }

    .community-header {
        flex-direction: column;
        text-align: center;
        gap: 24px;
        padding: 32px 20px;
    }

    .community-title {
        font-size: 2rem;
    }

    .filters-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .categories-filter {
        justify-content: center;
    }

    .post-card {
        padding: 20px;
    }

    .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .post-stats {
        gap: 16px;
    }

    .pagination {
        flex-wrap: wrap;
    }

    .post-form-container {
        padding: 24px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 20px;
    }
}

/* 通知图标样式 */
.notification-icon {
    position: relative;
    margin-right: 15px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.notification-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.notification-icon i {
    font-size: 18px;
    color: #1a1a1a;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
