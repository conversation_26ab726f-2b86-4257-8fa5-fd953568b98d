@echo off
echo ========================================
echo PHP 安装脚本 - 比特熊项目
echo ========================================
echo.

:: 检查是否已安装PHP
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo PHP 已经安装！
    php --version
    echo.
    goto :start_server
)

echo 正在检查PHP安装...
echo PHP 未找到，开始安装过程...
echo.

:: 创建PHP目录
if not exist "C:\php" (
    echo 创建 C:\php 目录...
    mkdir "C:\php"
)

:: 下载PHP (使用PowerShell)
echo 正在下载 PHP 8.3 (Thread Safe)...
echo 这可能需要几分钟时间，请耐心等待...
echo.

powershell -Command "& {
    $url = 'https://windows.php.net/downloads/releases/php-8.3.14-Win32-vs16-x64.zip'
    $output = 'C:\php\php.zip'
    Write-Host '下载 PHP...'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host 'PHP 下载完成！'
    } catch {
        Write-Host '下载失败，请检查网络连接'
        exit 1
    }
}"

if not exist "C:\php\php.zip" (
    echo 下载失败！请检查网络连接或手动下载PHP。
    echo 请访问: https://windows.php.net/download/
    pause
    exit /b 1
)

:: 解压PHP
echo 正在解压 PHP...
powershell -Command "& {
    try {
        Expand-Archive -Path 'C:\php\php.zip' -DestinationPath 'C:\php' -Force
        Write-Host 'PHP 解压完成！'
    } catch {
        Write-Host '解压失败！'
        exit 1
    }
}"

:: 删除zip文件
del "C:\php\php.zip"

:: 复制配置文件
if exist "C:\php\php.ini-development" (
    echo 配置 PHP...
    copy "C:\php\php.ini-development" "C:\php\php.ini"
)

:: 添加到PATH (临时)
set PATH=%PATH%;C:\php

:: 验证安装
echo.
echo 验证 PHP 安装...
C:\php\php.exe --version
if %errorlevel% == 0 (
    echo.
    echo ✅ PHP 安装成功！
    echo.
) else (
    echo ❌ PHP 安装失败！
    pause
    exit /b 1
)

:start_server
echo ========================================
echo 启动 PHP 开发服务器
echo ========================================
echo.
echo 服务器将在以下地址运行:
echo   http://localhost:8000
echo.
echo 可用页面:
echo   - 主页: http://localhost:8000/index.php
echo   - 管理后台: http://localhost:8000/admin.php
echo.
echo 按 Ctrl+C 停止服务器
echo.

:: 启动PHP服务器
if exist "C:\php\php.exe" (
    C:\php\php.exe -S localhost:8000
) else (
    php -S localhost:8000
)

pause
