@echo off
echo ========================================
echo 比特熊 PHP 服务器启动
echo ========================================
echo.

:: 检查本地PHP
if exist "php\php.exe" (
    echo ✅ 使用本地 PHP
    php\php.exe --version
    echo.
    echo 🚀 启动服务器...
    echo 服务器地址: http://localhost:8000
    echo.
    echo 可用页面:
    echo   - 主页: http://localhost:8000/index.php
    echo   - 管理后台: http://localhost:8000/admin.php
    echo.
    start http://localhost:8000/index.php
    php\php.exe -S localhost:8000
    goto :end
)

:: 检查XAMPP
if exist "C:\xampp\php\php.exe" (
    echo ✅ 使用 XAMPP PHP
    C:\xampp\php\php.exe --version
    echo.
    echo 🚀 启动服务器...
    echo 服务器地址: http://localhost:8000
    echo.
    echo 可用页面:
    echo   - 主页: http://localhost:8000/index.php
    echo   - 管理后台: http://localhost:8000/admin.php
    echo.
    start http://localhost:8000/index.php
    C:\xampp\php\php.exe -S localhost:8000
    goto :end
)

:: 检查系统PHP
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 使用系统 PHP
    php --version
    echo.
    echo 🚀 启动服务器...
    echo 服务器地址: http://localhost:8000
    echo.
    echo 可用页面:
    echo   - 主页: http://localhost:8000/index.php
    echo   - 管理后台: http://localhost:8000/admin.php
    echo.
    start http://localhost:8000/index.php
    php -S localhost:8000
    goto :end
)

:: 没有找到PHP
echo ❌ 未找到 PHP 安装
echo.
echo 请先安装 PHP：
echo.
echo 方法1 - 便携版PHP:
echo   1. 访问 https://windows.php.net/download/
echo   2. 下载 Non Thread Safe 版本
echo   3. 解压到当前目录的 php 文件夹
echo.
echo 方法2 - XAMPP:
echo   1. 访问 https://www.apachefriends.org/download.html
echo   2. 下载并安装 XAMPP
echo.
echo 方法3 - 使用静态版本:
echo   直接打开 index-standalone.html
echo.

:end
pause
