<?php
/**
 * 导航栏管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    
    // 确保导航栏表存在
    $db->query("CREATE TABLE IF NOT EXISTS navbar_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        url VARCHAR(255) NOT NULL,
        type ENUM('link', 'dropdown', 'submenu') DEFAULT 'link',
        parent_id INT NULL,
        icon VARCHAR(255) NULL,
        visible BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES navbar_items(id) ON DELETE CASCADE,
        INDEX idx_parent (parent_id),
        INDEX idx_order (sort_order)
    )");
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取导航栏结构
 */
function handleGet($db) {
    $action = $_GET['action'] ?? 'list';
    
    if ($action === 'preview') {
        // 生成预览HTML
        $navbarHtml = generateNavbarPreview($db);
        echo json_encode([
            'success' => true,
            'data' => ['html' => $navbarHtml]
        ]);
        return;
    }
    
    // 获取所有导航项
    $sql = "SELECT * FROM navbar_items ORDER BY sort_order ASC, id ASC";
    $items = $db->fetchAll($sql);
    
    // 构建树形结构
    $tree = buildNavbarTree($items);
    
    echo json_encode([
        'success' => true,
        'data' => $tree
    ]);
}

/**
 * 处理POST请求 - 创建导航项
 */
function handlePost($db, $input) {
    if (!$input || !isset($input['name'], $input['url'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        return;
    }
    
    $data = [
        'name' => $input['name'],
        'url' => $input['url'],
        'type' => $input['type'] ?? 'link',
        'parent_id' => $input['parent_id'] ?? null,
        'icon' => $input['icon'] ?? null,
        'visible' => $input['visible'] ?? true,
        'sort_order' => $input['sort_order'] ?? 0
    ];
    
    $sql = "INSERT INTO navbar_items (name, url, type, parent_id, icon, visible, sort_order) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $db->query($sql, [
        $data['name'],
        $data['url'],
        $data['type'],
        $data['parent_id'],
        $data['icon'],
        $data['visible'],
        $data['sort_order']
    ]);
    
    $itemId = $db->lastInsertId();
    
    echo json_encode([
        'success' => true,
        'message' => '导航项创建成功',
        'data' => ['id' => $itemId]
    ]);
}

/**
 * 处理PUT请求 - 更新导航项
 */
function handlePut($db, $input) {
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少导航项ID']);
        return;
    }
    
    $itemId = $input['id'];
    
    // 检查导航项是否存在
    $item = $db->fetchOne("SELECT id FROM navbar_items WHERE id = ?", [$itemId]);
    if (!$item) {
        http_response_code(404);
        echo json_encode(['error' => '导航项不存在']);
        return;
    }
    
    // 构建更新数据
    $updateFields = [];
    $params = [];
    
    if (isset($input['name'])) {
        $updateFields[] = 'name = ?';
        $params[] = $input['name'];
    }
    if (isset($input['url'])) {
        $updateFields[] = 'url = ?';
        $params[] = $input['url'];
    }
    if (isset($input['type'])) {
        $updateFields[] = 'type = ?';
        $params[] = $input['type'];
    }
    if (isset($input['parent_id'])) {
        $updateFields[] = 'parent_id = ?';
        $params[] = $input['parent_id'];
    }
    if (isset($input['icon'])) {
        $updateFields[] = 'icon = ?';
        $params[] = $input['icon'];
    }
    if (isset($input['visible'])) {
        $updateFields[] = 'visible = ?';
        $params[] = $input['visible'];
    }
    if (isset($input['sort_order'])) {
        $updateFields[] = 'sort_order = ?';
        $params[] = $input['sort_order'];
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => '没有要更新的字段']);
        return;
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $itemId;
    
    $sql = "UPDATE navbar_items SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->query($sql, $params);
    
    echo json_encode(['success' => true, 'message' => '导航项更新成功']);
}

/**
 * 处理DELETE请求 - 删除导航项
 */
function handleDelete($db) {
    $itemId = $_GET['id'] ?? null;
    
    if (!$itemId) {
        http_response_code(400);
        echo json_encode(['error' => '缺少导航项ID']);
        return;
    }
    
    // 检查导航项是否存在
    $item = $db->fetchOne("SELECT name FROM navbar_items WHERE id = ?", [$itemId]);
    if (!$item) {
        http_response_code(404);
        echo json_encode(['error' => '导航项不存在']);
        return;
    }
    
    $sql = "DELETE FROM navbar_items WHERE id = ?";
    $db->query($sql, [$itemId]);
    
    echo json_encode(['success' => true, 'message' => '导航项删除成功']);
}

/**
 * 构建导航栏树形结构
 */
function buildNavbarTree($items) {
    $tree = [];
    $itemsById = [];
    
    // 按ID索引所有项目
    foreach ($items as $item) {
        $itemsById[$item['id']] = $item;
        $itemsById[$item['id']]['children'] = [];
    }
    
    // 构建树形结构
    foreach ($items as $item) {
        if ($item['parent_id'] === null) {
            $tree[] = &$itemsById[$item['id']];
        } else {
            if (isset($itemsById[$item['parent_id']])) {
                $itemsById[$item['parent_id']]['children'][] = &$itemsById[$item['id']];
            }
        }
    }
    
    return $tree;
}

/**
 * 生成导航栏预览HTML - 使用真实的前台样式
 */
function generateNavbarPreview($db) {
    $sql = "SELECT * FROM navbar_items WHERE visible = 1 ORDER BY sort_order ASC, id ASC";
    $items = $db->fetchAll($sql);
    $tree = buildNavbarTree($items);

    // 包含完整的Bootstrap导航栏结构
    $html = '<nav class="navbar navbar-expand-lg">';
    $html .= '<div class="container-fluid">';

    // 品牌Logo
    $html .= '<a class="navbar-brand" href="/index.php">';
    $html .= '<span class="brand-text">比特熊智慧系统</span>';
    $html .= '</a>';

    // 移动端切换按钮
    $html .= '<button class="navbar-toggler" type="button">';
    $html .= '<span class="navbar-toggler-icon"></span>';
    $html .= '</button>';

    // 导航菜单
    $html .= '<div class="collapse navbar-collapse">';
    $html .= '<ul class="navbar-nav me-auto">';

    foreach ($tree as $item) {
        $html .= generateNavItemHtml($item);
    }

    $html .= '</ul>';

    // 右侧操作区
    $html .= '<div class="navbar-actions">';
    $html .= '<div class="search-box">';
    $html .= '<input type="text" placeholder="搜索..." class="search-input">';
    $html .= '<button class="search-btn">🔍</button>';
    $html .= '</div>';
    $html .= '<button class="login-btn">登录</button>';
    $html .= '</div>';

    $html .= '</div>';
    $html .= '</div>';
    $html .= '</nav>';

    return $html;
}

/**
 * 生成单个导航项HTML - 使用Bootstrap样式
 */
function generateNavItemHtml($item) {
    $hasChildren = !empty($item['children']);
    $itemClass = $hasChildren ? 'nav-item dropdown' : 'nav-item';

    $html = '<li class="' . $itemClass . '">';

    if ($hasChildren) {
        // 下拉菜单
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle">';
        $html .= htmlspecialchars($item['name']);
        $html .= '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
        $html .= '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>';
        $html .= '</svg>';
        $html .= '</a>';

        // 下拉菜单内容
        $html .= '<ul class="dropdown-menu">';
        foreach ($item['children'] as $child) {
            $html .= '<li>';
            $html .= '<a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">';
            $html .= htmlspecialchars($child['name']);
            $html .= '</a>';
            $html .= '</li>';
        }
        $html .= '</ul>';
    } else {
        // 普通链接
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">';
        $html .= htmlspecialchars($item['name']);
        $html .= '</a>';
    }

    $html .= '</li>';

    return $html;
}

// 初始化默认导航数据
try {
    $db = db();
    
    // 检查是否已有数据
    $count = $db->fetchOne("SELECT COUNT(*) as count FROM navbar_items");
    if ($count['count'] == 0) {
        // 插入默认导航项
        $defaultItems = [
            ['name' => '首页', 'url' => '/index.php', 'type' => 'link', 'sort_order' => 1],
            ['name' => '组织', 'url' => '#', 'type' => 'dropdown', 'sort_order' => 2],
            ['name' => '服务', 'url' => '/services.php', 'type' => 'link', 'sort_order' => 3],
            ['name' => '新闻', 'url' => '/news.php', 'type' => 'link', 'sort_order' => 4]
        ];
        
        foreach ($defaultItems as $item) {
            $db->query("INSERT INTO navbar_items (name, url, type, sort_order) VALUES (?, ?, ?, ?)", [
                $item['name'], $item['url'], $item['type'], $item['sort_order']
            ]);
            
            // 为"组织"菜单添加子项
            if ($item['name'] === '组织') {
                $parentId = $db->lastInsertId();
                $subItems = [
                    ['name' => '关于我们', 'url' => '/about.php', 'parent_id' => $parentId, 'sort_order' => 1],
                    ['name' => '团队介绍', 'url' => '/team.php', 'parent_id' => $parentId, 'sort_order' => 2],
                    ['name' => '联系我们', 'url' => '/contact.php', 'parent_id' => $parentId, 'sort_order' => 3]
                ];
                
                foreach ($subItems as $subItem) {
                    $db->query("INSERT INTO navbar_items (name, url, type, parent_id, sort_order) VALUES (?, ?, ?, ?, ?)", [
                        $subItem['name'], $subItem['url'], 'submenu', $subItem['parent_id'], $subItem['sort_order']
                    ]);
                }
            }
        }
    }
} catch (Exception $e) {
    // 忽略错误，可能是在API调用中
}
?>
