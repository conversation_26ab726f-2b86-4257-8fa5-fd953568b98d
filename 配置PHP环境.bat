@echo off
chcp 65001 >nul
echo ========================================
echo    比特熊智慧系统 - PHP环境配置向导
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查PHP环境...
echo.

:: 检查常见的PHP安装路径
set "php_found=0"
set "php_path="

:: 检查XAMPP
if exist "C:\xampp\php\php.exe" (
    set "php_path=C:\xampp\php\php.exe"
    set "php_found=1"
    echo [发现] XAMPP PHP: C:\xampp\php\php.exe
)

:: 检查WAMP
if exist "C:\wamp64\bin\php\php8.2.0\php.exe" (
    set "php_path=C:\wamp64\bin\php\php8.2.0\php.exe"
    set "php_found=1"
    echo [发现] WAMP PHP: C:\wamp64\bin\php\php8.2.0\php.exe
)

:: 检查系统PATH中的PHP
php --version >nul 2>&1
if %errorlevel% equ 0 (
    set "php_path=php"
    set "php_found=1"
    echo [发现] 系统PATH中的PHP
    php --version
)

if %php_found% equ 1 (
    echo.
    echo [成功] 找到PHP环境！
    echo 使用的PHP路径: %php_path%
    echo.
    
    echo 正在启动PHP开发服务器...
    echo 服务器地址: http://localhost:8000
    echo 后台初始化: http://localhost:8000/admin/init-database.php
    echo 后台登录: http://localhost:8000/admin/login.php
    echo.
    echo 按 Ctrl+C 停止服务器
    echo ========================================
    echo.
    
    :: 自动打开浏览器
    start "" "http://localhost:8000/admin/init-database.php"
    
    :: 启动PHP服务器
    "%php_path%" -S localhost:8000
    
) else (
    echo.
    echo [警告] 未找到PHP环境！
    echo.
    echo 请选择安装方式：
    echo 1. 自动下载安装XAMPP（推荐）
    echo 2. 手动安装PHP
    echo 3. 退出
    echo.
    set /p choice="请输入选择 (1-3): "
    
    if "%choice%"=="1" (
        echo.
        echo 正在为您打开XAMPP下载页面...
        start "" "https://www.apachefriends.org/zh_cn/download.html"
        echo.
        echo 下载完成后：
        echo 1. 运行XAMPP安装程序
        echo 2. 安装到默认路径 C:\xampp
        echo 3. 启动Apache和MySQL服务
        echo 4. 重新运行此脚本
        echo.
    ) else if "%choice%"=="2" (
        echo.
        echo 手动安装PHP步骤：
        echo 1. 访问 https://windows.php.net/download/
        echo 2. 下载 PHP 8.x Thread Safe 版本
        echo 3. 解压到 C:\php
        echo 4. 将 C:\php 添加到系统PATH环境变量
        echo 5. 重新运行此脚本
        echo.
        start "" "https://windows.php.net/download/"
    )
)

echo.
pause
