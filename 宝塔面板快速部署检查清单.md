# ✅ 宝塔面板快速部署检查清单

## 🎯 部署前准备
- [ ] 本地项目运行正常 ✅ (已完成)
- [ ] 项目文件已打包 (运行 `打包项目.bat`)
- [ ] 服务器信息确认：
  - IP: *************
  - 用户: root
  - 密码: ZbDX7%=]?H2(LAUz

## 🌐 第一步：访问宝塔面板
- [ ] 尝试访问 http://*************:8888
- [ ] 尝试访问 https://*************:8888
- [ ] 如果无法访问，SSH执行 `bt default` 获取面板信息
- [ ] 成功登录宝塔面板

**常见端口**：8888, 8080, 888, 8000

## 🗄️ 第二步：数据库配置
- [ ] 点击左侧菜单"数据库"
- [ ] 点击"添加数据库"
- [ ] 填写信息：
  - 数据库名：`bitbear_website`
  - 用户名：`bitbear_user`
  - 密码：`309290133q`
- [ ] 点击数据库"管理"进入phpMyAdmin
- [ ] 按顺序导入SQL文件：
  - [ ] `database/init.sql`
  - [ ] `database/create_community_tables.sql`
  - [ ] `database/homepage_content_tables.sql`
- [ ] 验证表创建成功（应有15+个表）

## 📁 第三步：上传项目文件
- [ ] 点击左侧菜单"文件"
- [ ] 导航到 `/www/wwwroot/`
- [ ] 创建目录 `www.bitbear.top`（如不存在）
- [ ] 进入 `www.bitbear.top` 目录
- [ ] 上传项目zip包
- [ ] 右键解压zip文件
- [ ] 删除zip文件（节省空间）
- [ ] 设置目录权限：
  - [ ] 整个项目：755
  - [ ] uploads目录：777

## 🌐 第四步：添加网站
- [ ] 点击左侧菜单"网站"
- [ ] 点击"添加站点"
- [ ] 填写信息：
  - 域名：`www.bitbear.top,*************`
  - 根目录：`/www/wwwroot/www.bitbear.top`
  - PHP版本：8.1+
- [ ] 创建成功

## ⚙️ 第五步：PHP配置检查
- [ ] 点击网站"设置"
- [ ] 检查PHP版本（8.1+）
- [ ] 确认扩展启用：
  - [ ] pdo_mysql
  - [ ] mysqli
  - [ ] gd
  - [ ] curl
  - [ ] json
  - [ ] fileinfo

## 🧪 第六步：测试访问
- [ ] 访问 http://*************
- [ ] 首页正常显示
- [ ] 无PHP错误
- [ ] 图片正常加载
- [ ] 数据库连接正常

## 🔧 第七步：功能测试
- [ ] 用户注册功能
- [ ] 用户登录功能
- [ ] 管理后台访问 `/admin.php`
- [ ] 社区功能测试
- [ ] 文件上传功能

## 🔒 第八步：安全配置（可选）
- [ ] 配置SSL证书
- [ ] 修改宝塔面板端口
- [ ] 设置访问IP白名单
- [ ] 配置防火墙规则

## 🚨 故障排除指南

### 无法访问宝塔面板
1. 检查服务器防火墙
2. 确认宝塔服务运行状态：`bt restart`
3. 重置面板密码：`bt password`

### 数据库连接失败
1. 检查MySQL服务：在宝塔面板"软件商店"查看MySQL状态
2. 验证数据库用户权限
3. 检查配置文件中的数据库信息

### 网站无法访问
1. 检查网站状态（是否启动）
2. 查看网站日志
3. 检查PHP版本和扩展
4. 验证文件权限

### PHP错误
1. 查看网站错误日志
2. 检查PHP配置
3. 确认所需扩展已安装

## 📞 获取帮助的命令

**SSH连接服务器后可执行：**
```bash
# 查看宝塔面板信息
bt default

# 重启宝塔面板
bt restart

# 查看面板日志
bt logs

# 重置面板密码
bt password

# 查看系统信息
bt info
```

## 🎯 部署成功标志
- [ ] 网站正常访问
- [ ] 数据库连接正常
- [ ] 所有功能正常工作
- [ ] 无错误日志
- [ ] 性能表现良好

---

## 📋 下一步行动

1. **立即执行**：运行 `打包项目.bat` 打包文件
2. **访问面板**：尝试访问宝塔面板
3. **反馈结果**：告诉我每一步的执行结果

**遇到任何问题都可以随时询问我！** 🚀
