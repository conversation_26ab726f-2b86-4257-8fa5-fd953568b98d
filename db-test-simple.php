<?php
/**
 * 简化的数据库连接测试
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库连接测试</h1>";

// 检查环境
echo "<h2>环境信息:</h2>";
echo "<p>SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? '未设置') . "</p>";
echo "<p>HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? '未设置') . "</p>";

// 检查是否为服务器环境
$isServer = isset($_SERVER['SERVER_NAME']) && strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false;
echo "<p>是否为服务器环境: " . ($isServer ? '是' : '否') . "</p>";

if ($isServer) {
    echo "<h2>服务器环境 - 测试MySQL连接:</h2>";
    
    // 服务器配置
    $host = 'localhost';
    $username = 'root';
    $password = '309290133q';
    $database = 'bitbear_website';
    
    try {
        // 尝试连接MySQL
        $dsn = "mysql:host={$host};port=3306;charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 5
        ];
        
        $pdo = new PDO($dsn, $username, $password, $options);
        echo "<p>✓ MySQL连接成功</p>";
        
        // 检查数据库
        $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>可用数据库: " . implode(', ', $databases) . "</p>";
        
        if (!in_array($database, $databases)) {
            // 创建数据库
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            echo "<p>✓ 数据库 {$database} 创建成功</p>";
        }
        
        // 连接到指定数据库
        $dsn = "mysql:host={$host};port=3306;dbname={$database};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, $options);
        echo "<p>✓ 连接到数据库 {$database} 成功</p>";
        
        // 检查表
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>现有表: " . (empty($tables) ? '无' : implode(', ', $tables)) . "</p>";
        
        // 如果没有用户表，创建基本表结构
        if (!in_array('users', $tables)) {
            echo "<p>创建基本表结构...</p>";
            
            // 创建用户角色表
            $pdo->exec("
                CREATE TABLE user_roles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    role_code VARCHAR(50) UNIQUE NOT NULL,
                    role_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    permissions TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            // 创建用户表
            $pdo->exec("
                CREATE TABLE users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    phone VARCHAR(20),
                    role_id INT,
                    status VARCHAR(20) DEFAULT 'active',
                    last_login TIMESTAMP NULL,
                    login_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES user_roles(id)
                )
            ");
            
            // 创建用户资料表
            $pdo->exec("
                CREATE TABLE user_profiles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    nickname VARCHAR(100),
                    avatar_url VARCHAR(500),
                    bio TEXT,
                    location VARCHAR(100),
                    website VARCHAR(200),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ");
            
            // 插入默认角色
            $pdo->exec("
                INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
            ");
            
            echo "<p>✓ 基本表结构创建完成</p>";
        }
        
        // 测试查询
        $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
        echo "<p>用户表记录数: {$userCount}</p>";
        
        echo "<p style='color: green;'>✓ 数据库连接和表结构正常</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ MySQL连接失败: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<h2>本地环境 - 使用SQLite</h2>";
    echo "<p>本地环境将自动使用SQLite数据库</p>";
}

echo "<h2>测试完成</h2>";
echo "<p><a href='index.php'>返回首页</a></p>";
echo "<p><a href='register.php'>测试注册</a></p>";
?>
