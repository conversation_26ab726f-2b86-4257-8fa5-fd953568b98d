@echo off
echo ========================================
echo 比特熊智慧系统 - 服务器配置工具
echo ========================================
echo.

set PROJECT_DIR=/www/wwwroot/www.bitbear.top
set DB_NAME=bitbear_website
set DB_USER=bitbear_website
set DB_PASS=309290133q

echo 配置信息:
echo 项目目录: %PROJECT_DIR%
echo 数据库名: %DB_NAME%
echo 用户名: %DB_USER%
echo.

echo 步骤1: 检查项目文件...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "ls -la %PROJECT_DIR%/ | head -10"
echo.

echo 步骤2: 设置文件权限...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "chown -R www-data:www-data %PROJECT_DIR%"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "chmod -R 755 %PROJECT_DIR%"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "chmod -R 777 %PROJECT_DIR%/uploads"
echo ✓ 文件权限设置完成
echo.

echo 步骤3: 检查Web服务器状态...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "systemctl status nginx | head -5"
echo.

echo 步骤4: 重启Web服务器...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "systemctl restart nginx"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "systemctl restart php8.1-fpm"
echo ✓ Web服务器重启完成
echo.

echo 步骤5: 测试数据库连接...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "cd %PROJECT_DIR% && php -r \"try { \$pdo = new PDO('mysql:host=localhost;dbname=%DB_NAME%', '%DB_USER%', '%DB_PASS%'); echo 'Database connection: SUCCESS\n'; } catch(Exception \$e) { echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n'; }\""
echo.

echo 步骤6: 检查PHP配置...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "php --version | head -1"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "php -m | grep -E '(pdo|mysql)'"
echo.

echo ========================================
echo 服务器配置完成！
echo ========================================
echo.
echo 网站访问地址:
echo http://*************
echo http://www.bitbear.top
echo.
echo 管理后台:
echo http://*************/admin/
echo.

pause
