<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .result { margin-top: 10px; }
    </style>
</head>
<body>
    <h1>比特熊智慧系统 - API功能测试</h1>
    
    <div class="test-section">
        <h2>数据库连接测试</h2>
        <button onclick="testDatabase()">测试数据库连接</button>
        <div id="dbResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>统计数据API测试</h2>
        <button onclick="testStats()">获取统计数据</button>
        <div id="statsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>通知API测试</h2>
        <button onclick="testNotifications()">获取通知列表</button>
        <button onclick="testNotificationCount()">获取未读数量</button>
        <button onclick="testMarkAllRead()">标记全部已读</button>
        <div id="notificationsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>日程API测试</h2>
        <button onclick="testEvents()">获取日程列表</button>
        <button onclick="testCreateEvent()">创建测试日程</button>
        <div id="eventsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>用户活动API测试</h2>
        <button onclick="testActivities()">获取用户活动</button>
        <button onclick="testCreateActivity()">创建测试活动</button>
        <div id="activitiesResult" class="result"></div>
    </div>

    <script>
        // 数据库连接测试
        async function testDatabase() {
            const result = document.getElementById('dbResult');
            result.innerHTML = '<p class="info">正在测试数据库连接...</p>';
            
            try {
                const response = await fetch('test_3307.php');
                const text = await response.text();
                
                if (text.includes('🎉 数据库连接和操作测试全部成功！')) {
                    result.innerHTML = '<p class="success">✅ 数据库连接测试成功！</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 数据库连接测试失败</p><pre>' + text + '</pre>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 测试失败: ' + error.message + '</p>';
            }
        }

        // 统计数据测试
        async function testStats() {
            const result = document.getElementById('statsResult');
            result.innerHTML = '<p class="info">正在获取统计数据...</p>';
            
            try {
                const response = await fetch('api/stats.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 统计数据获取成功</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    result.innerHTML = '<p class="error">❌ 获取失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        // 通知测试
        async function testNotifications() {
            const result = document.getElementById('notificationsResult');
            result.innerHTML = '<p class="info">正在获取通知列表...</p>';
            
            try {
                const response = await fetch('api/notifications.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 通知列表获取成功</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    result.innerHTML = '<p class="error">❌ 获取失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        async function testNotificationCount() {
            const result = document.getElementById('notificationsResult');
            result.innerHTML = '<p class="info">正在获取未读数量...</p>';
            
            try {
                const response = await fetch('api/notifications.php?action=count');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 未读数量获取成功: ' + data.data.count + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 获取失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        async function testMarkAllRead() {
            const result = document.getElementById('notificationsResult');
            result.innerHTML = '<p class="info">正在标记全部已读...</p>';
            
            try {
                const response = await fetch('api/notifications.php', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'mark_all_read' })
                });
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 标记成功: ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 操作失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        // 日程测试
        async function testEvents() {
            const result = document.getElementById('eventsResult');
            result.innerHTML = '<p class="info">正在获取日程列表...</p>';
            
            try {
                const response = await fetch('api/events.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 日程列表获取成功</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    result.innerHTML = '<p class="error">❌ 获取失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        async function testCreateEvent() {
            const result = document.getElementById('eventsResult');
            result.innerHTML = '<p class="info">正在创建测试日程...</p>';
            
            const eventData = {
                title: 'API测试日程',
                description: '这是通过API创建的测试日程',
                event_date: '2025-02-01',
                start_time: '10:00',
                end_time: '11:00'
            };
            
            try {
                const response = await fetch('api/events.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(eventData)
                });
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 日程创建成功: ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 创建失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        // 用户活动测试
        async function testActivities() {
            const result = document.getElementById('activitiesResult');
            result.innerHTML = '<p class="info">正在获取用户活动...</p>';
            
            try {
                const response = await fetch('api/activities.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 用户活动获取成功</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    result.innerHTML = '<p class="error">❌ 获取失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        async function testCreateActivity() {
            const result = document.getElementById('activitiesResult');
            result.innerHTML = '<p class="info">正在创建测试活动...</p>';
            
            const activityData = {
                activity_type: 'system_update',
                title: 'API测试活动',
                description: '这是通过API创建的测试活动记录'
            };
            
            try {
                const response = await fetch('api/activities.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(activityData)
                });
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ 活动创建成功: ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 创建失败: ' + data.error + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 请求失败: ' + error.message + '</p>';
            }
        }

        // 页面加载时自动测试数据库连接
        window.onload = function() {
            testDatabase();
        };
    </script>
</body>
</html>
