<?php
/**
 * 注册API调试脚本
 * 专门用于诊断注册API的问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>注册API调试</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; font-size: 12px; }";
echo ".section { border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0; padding: 15px; }";
echo ".test-form { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🔍 注册API调试工具</h1>";

// 1. 检查API文件是否存在
echo "<div class='section'>";
echo "<h2>1. 文件检查</h2>";

$apiFile = __DIR__ . '/api/register.php';
if (file_exists($apiFile)) {
    echo "<div class='success'>✅ API文件存在: {$apiFile}</div>";
    echo "<div class='info'>文件大小: " . filesize($apiFile) . " 字节</div>";
    echo "<div class='info'>文件权限: " . substr(sprintf('%o', fileperms($apiFile)), -4) . "</div>";
    echo "<div class='info'>最后修改: " . date('Y-m-d H:i:s', filemtime($apiFile)) . "</div>";
} else {
    echo "<div class='error'>❌ API文件不存在: {$apiFile}</div>";
}

// 检查语法
if (file_exists($apiFile)) {
    $output = [];
    $return_var = 0;
    exec("php -l {$apiFile} 2>&1", $output, $return_var);
    if ($return_var === 0) {
        echo "<div class='success'>✅ PHP语法检查通过</div>";
    } else {
        echo "<div class='error'>❌ PHP语法错误:</div>";
        echo "<div class='code'>" . implode("\n", $output) . "</div>";
    }
}
echo "</div>";

// 2. 数据库连接检查
echo "<div class='section'>";
echo "<h2>2. 数据库连接检查</h2>";

try {
    require_once __DIR__ . '/config/database.php';
    echo "<div class='success'>✅ 数据库配置文件加载成功</div>";
    
    $db = DatabaseConfig::getInstance();
    echo "<div class='success'>✅ 数据库实例创建成功</div>";
    
    $result = $db->query("SELECT 1 as test");
    echo "<div class='success'>✅ 数据库连接测试成功</div>";
    
    // 检查必要的表
    $tables = ['users', 'user_roles', 'user_profiles'];
    foreach ($tables as $table) {
        try {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}");
            echo "<div class='success'>✅ 表 {$table} 存在，记录数: {$count['count']}</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ 表 {$table} 不存在或无法访问: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 3. 模拟API请求
echo "<div class='section'>";
echo "<h2>3. API请求模拟测试</h2>";

// 创建测试表单
echo "<div class='test-form'>";
echo "<h3>测试注册请求</h3>";
echo "<form method='POST' action='api/register.php' target='_blank'>";
echo "<p><label>用户名: <input type='text' name='username' value='testuser" . time() . "' required></label></p>";
echo "<p><label>邮箱: <input type='email' name='email' value='test" . time() . "@example.com' required></label></p>";
echo "<p><label>昵称: <input type='text' name='nickname' value='测试用户" . time() . "' required></label></p>";
echo "<p><label>密码: <input type='password' name='password' value='test123456' required></label></p>";
echo "<p><label>确认密码: <input type='password' name='confirmPassword' value='test123456' required></label></p>";
echo "<p><label><input type='checkbox' name='agreeTerms' checked> 同意服务条款</label></p>";
echo "<p><button type='submit'>测试注册API</button></p>";
echo "</form>";
echo "</div>";

// 使用cURL测试
if (function_exists('curl_init')) {
    echo "<div class='info'>";
    echo "<h4>cURL测试结果:</h4>";
    
    $testData = [
        'username' => 'curltest' . time(),
        'email' => 'curltest' . time() . '@example.com',
        'nickname' => 'cURL测试' . time(),
        'password' => 'test123456',
        'confirmPassword' => 'test123456',
        'agreeTerms' => 'on'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://www.bitbear.top/api/register.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded',
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div class='info'>HTTP状态码: {$httpCode}</div>";
    
    if ($error) {
        echo "<div class='error'>cURL错误: {$error}</div>";
    } else {
        echo "<div class='success'>cURL请求成功</div>";
        echo "<div class='info'>响应内容:</div>";
        echo "<div class='code'>" . htmlspecialchars($response) . "</div>";
        
        // 尝试解析JSON
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse !== null) {
            echo "<div class='success'>✅ 响应是有效的JSON</div>";
            echo "<div class='code'>" . json_encode($jsonResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</div>";
        } else {
            echo "<div class='warning'>⚠️ 响应不是有效的JSON格式</div>";
        }
    }
    echo "</div>";
} else {
    echo "<div class='warning'>⚠️ cURL扩展未安装，无法进行HTTP测试</div>";
}

echo "</div>";

// 4. JavaScript测试代码
echo "<div class='section'>";
echo "<h2>4. JavaScript测试代码</h2>";
echo "<div class='info'>您可以在浏览器控制台中运行以下代码来测试注册API:</div>";
echo "<div class='code'>";
echo "// 测试注册API的JavaScript代码\n";
echo "const testData = new FormData();\n";
echo "testData.append('username', 'jstest' + Date.now());\n";
echo "testData.append('email', 'jstest' + Date.now() + '@example.com');\n";
echo "testData.append('nickname', 'JS测试' + Date.now());\n";
echo "testData.append('password', 'test123456');\n";
echo "testData.append('confirmPassword', 'test123456');\n";
echo "testData.append('agreeTerms', 'on');\n\n";
echo "fetch('/api/register.php', {\n";
echo "    method: 'POST',\n";
echo "    body: testData\n";
echo "})\n";
echo ".then(response => {\n";
echo "    console.log('HTTP状态:', response.status);\n";
echo "    return response.text();\n";
echo "})\n";
echo ".then(data => {\n";
echo "    console.log('响应数据:', data);\n";
echo "    try {\n";
echo "        const json = JSON.parse(data);\n";
echo "        console.log('解析后的JSON:', json);\n";
echo "    } catch (e) {\n";
echo "        console.error('JSON解析失败:', e);\n";
echo "    }\n";
echo "})\n";
echo ".catch(error => {\n";
echo "    console.error('请求失败:', error);\n";
echo "});";
echo "</div>";
echo "</div>";

// 5. 服务器环境信息
echo "<div class='section'>";
echo "<h2>5. 服务器环境信息</h2>";
echo "<div class='info'>PHP版本: " . PHP_VERSION . "</div>";
echo "<div class='info'>服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</div>";
echo "<div class='info'>文档根目录: " . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</div>";
echo "<div class='info'>当前脚本路径: " . __FILE__ . "</div>";
echo "<div class='info'>POST最大大小: " . ini_get('post_max_size') . "</div>";
echo "<div class='info'>上传最大大小: " . ini_get('upload_max_filesize') . "</div>";
echo "<div class='info'>内存限制: " . ini_get('memory_limit') . "</div>";

// 检查必要的PHP扩展
$extensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl'];
echo "<div class='info'><strong>PHP扩展检查:</strong></div>";
foreach ($extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✅ {$ext}</div>";
    } else {
        echo "<div class='error'>❌ {$ext}</div>";
    }
}
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>返回注册页面</a>";
echo "<a href='test_register_simple.php' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>简单注册测试</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
