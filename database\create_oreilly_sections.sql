-- <PERSON><PERSON><PERSON>风格区域内容管理表
-- 严格区分主标题(h1/h2)和副标题(h3)的层级

-- 1. <PERSON><PERSON><PERSON>英雄区域 (技能建设区域)
CREATE TABLE IF NOT EXISTS oreilly_hero_section (
    id INT PRIMARY KEY AUTO_INCREMENT,
    main_title VARCHAR(255) NOT NULL COMMENT '主标题 (h1级别)',
    description TEXT NOT NULL COMMENT '描述文本',
    primary_button_text VARCHAR(100) NOT NULL COMMENT '主按钮文本',
    primary_button_url VARCHAR(255) NOT NULL COMMENT '主按钮链接',
    secondary_button_text VARCHAR(100) NOT NULL COMMENT '次按钮文本', 
    secondary_button_url VARCHAR(255) NOT NULL COMMENT '次按钮链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专家课程区域 (包含两个子区域)
CREATE TABLE IF NOT EXISTS oreilly_courses_section (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_type ENUM('live_courses', 'ai_answers') NOT NULL COMMENT '区域类型',
    title VARCHAR(255) NOT NULL COMMENT '标题 (h3级别)',
    description TEXT NOT NULL COMMENT '描述文本',
    button_text VARCHAR(100) NOT NULL COMMENT '按钮文本',
    button_url VARCHAR(255) NOT NULL COMMENT '按钮链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. 专家展示区域 (知识分享区域)
CREATE TABLE IF NOT EXISTS oreilly_experts_section (
    id INT PRIMARY KEY AUTO_INCREMENT,
    main_title VARCHAR(255) NOT NULL COMMENT '主标题 (h2级别)',
    description TEXT NOT NULL COMMENT '描述文本',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 视频推荐区域 (推荐区域)
CREATE TABLE IF NOT EXISTS oreilly_testimonial_section (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT '推荐标题 (h3级别)',
    description TEXT NOT NULL COMMENT '推荐描述',
    stat1_number VARCHAR(20) NOT NULL COMMENT '统计数字1',
    stat1_label VARCHAR(50) NOT NULL COMMENT '统计标签1',
    stat2_number VARCHAR(20) NOT NULL COMMENT '统计数字2',
    stat2_label VARCHAR(50) NOT NULL COMMENT '统计标签2',
    stat3_number VARCHAR(20) NOT NULL COMMENT '统计数字3',
    stat3_label VARCHAR(50) NOT NULL COMMENT '统计标签3',
    button_text VARCHAR(100) NOT NULL COMMENT '按钮文本',
    button_url VARCHAR(255) NOT NULL COMMENT '按钮链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 5. 底部行动号召区域 (帮助团队区域)
CREATE TABLE IF NOT EXISTS oreilly_cta_section (
    id INT PRIMARY KEY AUTO_INCREMENT,
    main_title VARCHAR(255) NOT NULL COMMENT '主标题 (h2级别)',
    primary_button_text VARCHAR(100) NOT NULL COMMENT '主按钮文本',
    primary_button_url VARCHAR(255) NOT NULL COMMENT '主按钮链接',
    secondary_button_text VARCHAR(100) NOT NULL COMMENT '次按钮文本',
    secondary_button_url VARCHAR(255) NOT NULL COMMENT '次按钮链接',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认数据
INSERT INTO oreilly_hero_section (main_title, description, primary_button_text, primary_button_url, secondary_button_text, secondary_button_url) VALUES
('Build the skills your teams need', 'Give your teams the O''Reilly learning platform and equip them with the resources that drive business outcomes. <strong>Click on a feature below to explore.</strong>', 'Request a demo ›', '#', 'Try it free ›', '#');

INSERT INTO oreilly_courses_section (section_type, title, description, button_text, button_url) VALUES
('live_courses', 'Level up with<br>expert-led live courses', 'Reserve your seat for interactive workshops to gain hands-on experience—and ask questions along the way.', 'Pick your events ›', '#'),
('ai_answers', 'O''Reilly AI-powered Answers<br>just got even smarter', 'O''Reilly Answers instantly generates information teams can trust, sourced from thousands of titles on our learning platform.', 'Discover Answers ›', '#');

INSERT INTO oreilly_experts_section (main_title, description) VALUES
('We share the knowledge of<br>innovators. You put it to work.', 'Tech teams love tapping into the minds of innovators through our expert-led courses, renowned text-based content, and bite-size online Superstream tech conferences. In fact, in a recent survey, one-third of tech practitioners rated O''Reilly content a five out of five (excellent)—better than Pluralsight, LinkedIn Learning, Udacity, or Skillsoft.');

INSERT INTO oreilly_testimonial_section (title, description, stat1_number, stat1_label, stat2_number, stat2_label, stat3_number, stat3_label, button_text, button_url) VALUES
('Why Jose uses O''Reilly every day', 'As a principal software engineer, I rely on O''Reilly''s platform to keep my team updated with the latest technologies and best practices.', '5+', 'Years using', '200+', 'Books read', '50+', 'Courses completed', 'View more testimonials ›', '#');

INSERT INTO oreilly_cta_section (main_title, primary_button_text, primary_button_url, secondary_button_text, secondary_button_url) VALUES
('See how O''Reilly can help your tech teams stay ahead', 'Request a demo', '#', 'Try it free', '#');
