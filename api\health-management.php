<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';

// 验证用户权限 - 兼容两种认证方式
$isAuthenticated = false;
$currentUser = null;

// 方式1: 新的Auth类认证
if (class_exists('Auth')) {
    require_once '../classes/Auth.php';
    $auth = new Auth();
    if ($auth->isLoggedIn()) {
        $isAuthenticated = true;
        $currentUser = $auth->getCurrentUser();
    }
}

// 方式2: 管理后台认证（回退方案）
if (!$isAuthenticated && isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $isAuthenticated = true;
    $currentUser = [
        'id' => $_SESSION['user_id'] ?? 1,
        'username' => $_SESSION['admin_user'] ?? 'admin',
        'role_code' => $_SESSION['user_type'] ?? 'admin',
        'full_name' => '管理员'
    ];
}

if (!$isAuthenticated) {
    echo json_encode(['success' => false, 'error' => '未登录']);
    exit;
}
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    switch ($action) {
        case 'get_fields':
            handleGetFields($db);
            break;
        case 'save_field':
            handleSaveField($db, $currentUser);
            break;
        case 'delete_field':
            handleDeleteField($db, $currentUser);
            break;
        case 'get_data':
            handleGetData($db, $currentUser);
            break;
        case 'save_data':
            handleSaveData($db, $currentUser);
            break;
        case 'get_statistics':
            handleGetStatistics($db, $currentUser);
            break;
        default:
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
} catch (Exception $e) {
    error_log("健康管理API错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => '服务器错误: ' . $e->getMessage()]);
}

// 获取健康字段列表
function handleGetFields($db) {
    $sql = "SELECT * FROM health_fields WHERE is_active = 1 ORDER BY display_order, id";
    $stmt = $db->query($sql);
    $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'fields' => $fields
    ]);
}

// 保存健康字段
function handleSaveField($db, $currentUser) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $fieldId = $input['id'] ?? null;
    $fieldName = trim($input['field_name'] ?? '');
    $fieldType = $input['field_type'] ?? 'number';
    $fieldUnit = trim($input['field_unit'] ?? '');
    $fieldOptions = $input['field_options'] ?? null;
    $displayOrder = intval($input['display_order'] ?? 0);
    
    if (empty($fieldName)) {
        echo json_encode(['success' => false, 'error' => '字段名称不能为空']);
        return;
    }
    
    if ($fieldId) {
        // 更新字段
        $sql = "UPDATE health_fields SET 
                field_name = ?, field_type = ?, field_unit = ?, 
                field_options = ?, display_order = ?, updated_at = NOW()
                WHERE id = ? AND (is_system = 0 OR user_id = ?)";
        $params = [$fieldName, $fieldType, $fieldUnit, $fieldOptions, $displayOrder, $fieldId, $currentUser['id']];
    } else {
        // 新增字段
        $sql = "INSERT INTO health_fields 
                (field_name, field_type, field_unit, field_options, display_order, is_system, is_active) 
                VALUES (?, ?, ?, ?, ?, 0, 1)";
        $params = [$fieldName, $fieldType, $fieldUnit, $fieldOptions, $displayOrder];
    }
    
    try {
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => $fieldId ? '字段更新成功' : '字段添加成功',
            'field_id' => $fieldId ?: $db->lastInsertId()
        ]);
    } catch (PDOException $e) {
        if ($e->getCode() == 23000) {
            echo json_encode(['success' => false, 'error' => '字段名称已存在']);
        } else {
            throw $e;
        }
    }
}

// 删除健康字段
function handleDeleteField($db, $currentUser) {
    $input = json_decode(file_get_contents('php://input'), true);
    $fieldId = $input['id'] ?? 0;
    
    if (!$fieldId) {
        echo json_encode(['success' => false, 'error' => '字段ID不能为空']);
        return;
    }
    
    // 检查是否为系统字段
    $stmt = $db->prepare("SELECT is_system FROM health_fields WHERE id = ?");
    $stmt->execute([$fieldId]);
    $field = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$field) {
        echo json_encode(['success' => false, 'error' => '字段不存在']);
        return;
    }
    
    if ($field['is_system']) {
        echo json_encode(['success' => false, 'error' => '系统字段不能删除']);
        return;
    }
    
    // 软删除字段
    $stmt = $db->prepare("UPDATE health_fields SET is_active = 0, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$fieldId]);
    
    echo json_encode([
        'success' => true,
        'message' => '字段删除成功'
    ]);
}

// 获取健康数据
function handleGetData($db, $currentUser) {
    $year = intval($_GET['year'] ?? date('Y'));
    $month = intval($_GET['month'] ?? date('n'));
    
    // 获取该月的所有日期
    $startDate = sprintf('%04d-%02d-01', $year, $month);
    $endDate = date('Y-m-t', strtotime($startDate));
    
    // 获取健康字段
    $fieldsStmt = $db->query("SELECT * FROM health_fields WHERE is_active = 1 ORDER BY display_order, id");
    $fields = $fieldsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取健康数据
    $dataStmt = $db->prepare("
        SELECT log_date, field_id, field_value 
        FROM health_logs 
        WHERE user_id = ? AND log_date BETWEEN ? AND ?
        ORDER BY log_date, field_id
    ");
    $dataStmt->execute([$currentUser['id'], $startDate, $endDate]);
    $logs = $dataStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 组织数据结构
    $healthData = [];
    foreach ($logs as $log) {
        $healthData[$log['log_date']][$log['field_id']] = $log['field_value'];
    }
    
    echo json_encode([
        'success' => true,
        'fields' => $fields,
        'data' => $healthData,
        'year' => $year,
        'month' => $month
    ]);
}

// 保存健康数据
function handleSaveData($db, $currentUser) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        $input = $_POST;
    }
    
    $date = $input['date'] ?? '';
    $fieldId = intval($input['field_id'] ?? 0);
    $value = $input['value'] ?? '';
    
    if (empty($date) || !$fieldId) {
        echo json_encode(['success' => false, 'error' => '参数不完整']);
        return;
    }
    
    // 验证日期格式
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        echo json_encode(['success' => false, 'error' => '日期格式错误']);
        return;
    }
    
    // 验证字段是否存在
    $fieldStmt = $db->prepare("SELECT id FROM health_fields WHERE id = ? AND is_active = 1");
    $fieldStmt->execute([$fieldId]);
    if (!$fieldStmt->fetch()) {
        echo json_encode(['success' => false, 'error' => '字段不存在']);
        return;
    }
    
    try {
        if (empty($value)) {
            // 删除数据
            $stmt = $db->prepare("DELETE FROM health_logs WHERE user_id = ? AND log_date = ? AND field_id = ?");
            $stmt->execute([$currentUser['id'], $date, $fieldId]);
        } else {
            // 插入或更新数据
            $stmt = $db->prepare("
                INSERT INTO health_logs (user_id, log_date, field_id, field_value) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE field_value = VALUES(field_value), updated_at = NOW()
            ");
            $stmt->execute([$currentUser['id'], $date, $fieldId, $value]);
        }
        
        echo json_encode([
            'success' => true,
            'message' => '数据保存成功'
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => '保存失败: ' . $e->getMessage()]);
    }
}

// 获取统计数据
function handleGetStatistics($db, $currentUser) {
    $type = $_GET['type'] ?? 'month'; // month, week
    $year = intval($_GET['year'] ?? date('Y'));
    $month = intval($_GET['month'] ?? date('n'));
    
    if ($type === 'week') {
        // 周统计逻辑
        $stats = getWeeklyStatistics($db, $currentUser, $year, $month);
    } else {
        // 月统计逻辑
        $stats = getMonthlyStatistics($db, $currentUser, $year, $month);
    }
    
    echo json_encode([
        'success' => true,
        'statistics' => $stats,
        'type' => $type,
        'year' => $year,
        'month' => $month
    ]);
}

function getMonthlyStatistics($db, $currentUser, $year, $month) {
    // 获取当月和上月的数据进行对比
    $currentStart = sprintf('%04d-%02d-01', $year, $month);
    $currentEnd = date('Y-m-t', strtotime($currentStart));
    
    $prevMonth = $month - 1;
    $prevYear = $year;
    if ($prevMonth < 1) {
        $prevMonth = 12;
        $prevYear--;
    }
    $prevStart = sprintf('%04d-%02d-01', $prevYear, $prevMonth);
    $prevEnd = date('Y-m-t', strtotime($prevStart));
    
    // 获取数值类型字段的统计
    $sql = "
        SELECT 
            hf.id, hf.field_name, hf.field_unit,
            AVG(CASE WHEN hl.log_date BETWEEN ? AND ? THEN CAST(hl.field_value AS DECIMAL(10,2)) END) as current_avg,
            AVG(CASE WHEN hl.log_date BETWEEN ? AND ? THEN CAST(hl.field_value AS DECIMAL(10,2)) END) as prev_avg,
            COUNT(CASE WHEN hl.log_date BETWEEN ? AND ? THEN 1 END) as current_count,
            COUNT(CASE WHEN hl.log_date BETWEEN ? AND ? THEN 1 END) as prev_count
        FROM health_fields hf
        LEFT JOIN health_logs hl ON hf.id = hl.field_id AND hl.user_id = ?
        WHERE hf.field_type = 'number' AND hf.is_active = 1
        GROUP BY hf.id, hf.field_name, hf.field_unit
        HAVING current_count > 0 OR prev_count > 0
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute([
        $currentStart, $currentEnd, $prevStart, $prevEnd,
        $currentStart, $currentEnd, $prevStart, $prevEnd,
        $currentUser['id']
    ]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getWeeklyStatistics($db, $currentUser, $year, $month) {
    // 简化的周统计，这里可以根据需要扩展
    return getMonthlyStatistics($db, $currentUser, $year, $month);
}
?>
