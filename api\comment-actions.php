<?php
// 开启错误报告用于调试
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不直接显示错误，避免破坏JSON输出
ini_set('log_errors', 1);

// 开启输出缓冲，防止意外输出
ob_start();

// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// 清理任何意外的输出
ob_clean();
header('Content-Type: application/json');

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    ob_end_flush();
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ob_clean();
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    ob_end_flush();
    exit;
}

// 读取输入数据
$rawInput = file_get_contents('php://input');
error_log("评论API原始输入: " . $rawInput);

$input = json_decode($rawInput, true);
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log("评论API JSON解析错误: " . json_last_error_msg());
    ob_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'JSON格式错误']);
    ob_end_flush();
    exit;
}

$action = $input['action'] ?? '';
$commentId = intval($input['comment_id'] ?? 0);

error_log("评论API解析后: action={$action}, comment_id={$commentId}");

if (!$commentId) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '无效的评论ID']);
    ob_end_flush();
    exit;
}

try {
    $db = db();
    
    // 验证评论是否存在
    $comment = $db->fetchOne("SELECT * FROM comments WHERE id = ? AND status = 'published'", [$commentId]);
    if (!$comment) {
        ob_clean();
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '评论不存在']);
        ob_end_flush();
        exit;
    }
    
    switch ($action) {
        case 'like':
        case 'dislike':
            handleLikeDislike($db, $currentUser, $commentId, $action);
            break;
            
        case 'delete':
            handleDelete($db, $currentUser, $comment);
            break;
            
        default:
            ob_clean();
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            ob_end_flush();
            exit;
    }
    
} catch (Exception $e) {
    error_log("评论操作错误: " . $e->getMessage());
    ob_clean(); // 清理任何之前的输出
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误']);
    ob_end_flush();
}

function handleLikeDislike($db, $currentUser, $commentId, $action) {
    try {
        $db->query("START TRANSACTION");
        
        // 检查用户是否已经点赞/反对过
        $existingLike = $db->fetchOne(
            "SELECT * FROM likes WHERE user_id = ? AND target_type = 'comment' AND target_id = ?",
            [$currentUser['id'], $commentId]
        );
        
        if ($existingLike) {
            if ($existingLike['type'] === $action) {
                // 取消点赞/反对
                $db->execute(
                    "DELETE FROM likes WHERE user_id = ? AND target_type = 'comment' AND target_id = ?",
                    [$currentUser['id'], $commentId]
                );
                
                // 更新评论统计
                $column = $action === 'like' ? 'like_count' : 'dislike_count';
                $db->execute("UPDATE comments SET {$column} = {$column} - 1 WHERE id = ?", [$commentId]);
                
            } else {
                // 切换点赞/反对类型
                $db->execute(
                    "UPDATE likes SET type = ? WHERE user_id = ? AND target_type = 'comment' AND target_id = ?",
                    [$action, $currentUser['id'], $commentId]
                );
                
                // 更新评论统计
                if ($action === 'like') {
                    $db->execute("UPDATE comments SET like_count = like_count + 1, dislike_count = dislike_count - 1 WHERE id = ?", [$commentId]);
                } else {
                    $db->execute("UPDATE comments SET dislike_count = dislike_count + 1, like_count = like_count - 1 WHERE id = ?", [$commentId]);
                }
            }
        } else {
            // 新增点赞/反对
            $currentTime = date('Y-m-d H:i:s');
            $db->execute(
                "INSERT INTO likes (user_id, target_type, target_id, type, ip_address, created_at) VALUES (?, 'comment', ?, ?, ?, ?)",
                [$currentUser['id'], $commentId, $action, $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '', $currentTime]
            );
            
            // 更新评论统计
            $column = $action === 'like' ? 'like_count' : 'dislike_count';
            $db->execute("UPDATE comments SET {$column} = {$column} + 1 WHERE id = ?", [$commentId]);
        }
        
        $db->query("COMMIT");
        
        // 获取更新后的数据
        $comment = $db->fetchOne("SELECT like_count, dislike_count FROM comments WHERE id = ?", [$commentId]);
        $userLike = $db->fetchOne(
            "SELECT type FROM likes WHERE user_id = ? AND target_type = 'comment' AND target_id = ?",
            [$currentUser['id'], $commentId]
        );
        
        echo json_encode([
            'success' => true,
            'data' => [
                'like_count' => intval($comment['like_count']),
                'dislike_count' => intval($comment['dislike_count']),
                'user_liked' => $userLike && $userLike['type'] === 'like',
                'user_disliked' => $userLike && $userLike['type'] === 'dislike'
            ]
        ]);
        ob_end_flush();
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

function handleDelete($db, $currentUser, $comment) {
    // 检查权限
    $canDelete = false;
    
    // 用户可以删除自己的评论
    if ($currentUser['id'] == $comment['user_id']) {
        $canDelete = true;
    }
    
    // 管理员可以删除任何评论
    if (in_array($currentUser['role_code'] ?? '', ['admin', 'super_admin'])) {
        $canDelete = true;
    }
    
    // 帖子作者可以删除自己帖子下的评论
    $post = $db->fetchOne("SELECT user_id FROM posts WHERE id = ?", [$comment['post_id']]);
    if ($post && $currentUser['id'] == $post['user_id']) {
        $canDelete = true;
    }
    
    if (!$canDelete) {
        ob_clean();
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => '没有权限删除此评论']);
        ob_end_flush();
        return;
    }
    
    try {
        $db->query("START TRANSACTION");
        
        // 软删除评论（标记为已删除）
        $db->execute("UPDATE comments SET status = 'deleted' WHERE id = ?", [$comment['id']]);
        
        // 更新帖子评论数
        $db->execute("UPDATE posts SET comment_count = comment_count - 1 WHERE id = ?", [$comment['post_id']]);
        
        // 更新父评论回复数
        if ($comment['parent_id']) {
            $db->execute("UPDATE comments SET reply_count = reply_count - 1 WHERE id = ?", [$comment['parent_id']]);
        }
        
        // 更新用户评论数
        $db->execute("UPDATE user_profiles SET comment_count = comment_count - 1 WHERE user_id = ?", [$comment['user_id']]);
        
        // 删除相关的点赞记录
        $db->execute("DELETE FROM likes WHERE target_type = 'comment' AND target_id = ?", [$comment['id']]);
        
        $db->query("COMMIT");
        
        echo json_encode(['success' => true, 'message' => '评论已删除']);

    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

// 结束输出缓冲并发送内容
ob_end_flush();
?>
