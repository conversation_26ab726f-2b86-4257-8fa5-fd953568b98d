# Simple Network Check for Local Device Issues
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Local Device Network Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$serverPort = 8888
$fullUrl = "http://$serverIP`:$serverPort/tencentcloud"

# 1. Basic connectivity
Write-Host "1. Basic Connectivity Test" -ForegroundColor Yellow
Write-Host ""

Write-Host "Ping test to $serverIP..." -ForegroundColor Cyan
try {
    $ping = Test-Connection -ComputerName $serverIP -Count 4 -ErrorAction Stop
    $success = ($ping | Where-Object { $_.StatusCode -eq 0 }).Count
    Write-Host "  Success: $success/4 packets" -ForegroundColor $(if($success -gt 0){"Green"}else{"Red"})
} catch {
    Write-Host "  Ping failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Port $serverPort connectivity..." -ForegroundColor Cyan
try {
    $portTest = Test-NetConnection -ComputerName $serverIP -Port $serverPort -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host "  Port ${serverPort}: OPEN" -ForegroundColor Green
    } else {
        Write-Host "  Port ${serverPort}: CLOSED" -ForegroundColor Red
    }
} catch {
    Write-Host "  Port test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 2. Network adapters
Write-Host "2. Network Adapters" -ForegroundColor Yellow
Write-Host ""

$adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
Write-Host "Active network adapters:" -ForegroundColor Cyan
foreach ($adapter in $adapters) {
    $ip = Get-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($ip) {
        Write-Host "  $($adapter.Name): $($ip.IPAddress)" -ForegroundColor White
        if ($adapter.InterfaceDescription -like "*VMware*") {
            Write-Host "    (VMware Virtual Adapter)" -ForegroundColor Yellow
        }
    }
}

Write-Host ""

# 3. Default gateway
Write-Host "3. Default Gateway" -ForegroundColor Yellow
Write-Host ""

$routes = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Sort-Object RouteMetric
Write-Host "Default gateways:" -ForegroundColor Cyan
foreach ($route in $routes) {
    $adapter = Get-NetAdapter -InterfaceIndex $route.InterfaceIndex -ErrorAction SilentlyContinue
    if ($adapter) {
        Write-Host "  $($route.NextHop) via $($adapter.Name) (metric: $($route.RouteMetric))" -ForegroundColor White
    }
}

Write-Host ""

# 4. DNS settings
Write-Host "4. DNS Settings" -ForegroundColor Yellow
Write-Host ""

$dnsServers = Get-DnsClientServerAddress | Where-Object { $_.AddressFamily -eq 2 -and $_.ServerAddresses.Count -gt 0 }
Write-Host "DNS servers:" -ForegroundColor Cyan
foreach ($dns in $dnsServers) {
    $adapter = Get-NetAdapter -InterfaceIndex $dns.InterfaceIndex -ErrorAction SilentlyContinue
    if ($adapter -and $adapter.Status -eq "Up") {
        Write-Host "  $($adapter.Name): $($dns.ServerAddresses -join ', ')" -ForegroundColor White
    }
}

Write-Host ""

# 5. Firewall status
Write-Host "5. Firewall Status" -ForegroundColor Yellow
Write-Host ""

$profiles = Get-NetFirewallProfile
Write-Host "Windows Firewall:" -ForegroundColor Cyan
foreach ($profile in $profiles) {
    $status = if ($profile.Enabled) { "ENABLED" } else { "DISABLED" }
    $color = if ($profile.Enabled) { "Yellow" } else { "Green" }
    Write-Host "  $($profile.Name): $status" -ForegroundColor $color
}

Write-Host ""

# 6. Proxy settings
Write-Host "6. Proxy Settings" -ForegroundColor Yellow
Write-Host ""

Write-Host "System proxy:" -ForegroundColor Cyan
try {
    $proxy = Get-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -ErrorAction Stop
    if ($proxy.ProxyEnable -eq 1) {
        Write-Host "  ENABLED: $($proxy.ProxyServer)" -ForegroundColor Yellow
    } else {
        Write-Host "  DISABLED" -ForegroundColor Green
    }
} catch {
    Write-Host "  Cannot read proxy settings" -ForegroundColor Red
}

Write-Host ""

# 7. HTTP test
Write-Host "7. HTTP Request Test" -ForegroundColor Yellow
Write-Host ""

Write-Host "Testing HTTP request to $fullUrl..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri $fullUrl -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
    Write-Host "  SUCCESS: HTTP $($response.StatusCode)" -ForegroundColor Green
    Write-Host "  Content length: $($response.Content.Length) bytes" -ForegroundColor Green
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode) {
        Write-Host "  HTTP ERROR: $statusCode" -ForegroundColor Red
    } else {
        Write-Host "  CONNECTION FAILED: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# 8. Route trace
Write-Host "8. Route Trace" -ForegroundColor Yellow
Write-Host ""

Write-Host "Tracing route to $serverIP..." -ForegroundColor Cyan
try {
    $trace = Test-NetConnection -ComputerName $serverIP -TraceRoute -WarningAction SilentlyContinue
    if ($trace.TraceRoute) {
        for ($i = 0; $i -lt [Math]::Min(5, $trace.TraceRoute.Count); $i++) {
            Write-Host "  $($i+1). $($trace.TraceRoute[$i])" -ForegroundColor White
        }
        if ($trace.TraceRoute.Count -gt 5) {
            Write-Host "  ... ($($trace.TraceRoute.Count - 5) more hops)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  Route trace failed" -ForegroundColor Red
    }
} catch {
    Write-Host "  Route trace error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 9. Recommendations
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Recommendations" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "If the target is unreachable from this device only:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Check if VMware virtual adapters are interfering" -ForegroundColor White
Write-Host "   - Temporarily disable VMware network adapters" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Clear DNS cache:" -ForegroundColor White
Write-Host "   - Run: ipconfig /flushdns" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Reset network stack:" -ForegroundColor White
Write-Host "   - Run: netsh winsock reset" -ForegroundColor Gray
Write-Host "   - Run: netsh int ip reset" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Try different network connection:" -ForegroundColor White
Write-Host "   - Use mobile hotspot" -ForegroundColor Gray
Write-Host "   - Connect to different WiFi" -ForegroundColor Gray
Write-Host ""
Write-Host "5. Check browser-specific issues:" -ForegroundColor White
Write-Host "   - Clear browser cache completely" -ForegroundColor Gray
Write-Host "   - Try incognito/private mode" -ForegroundColor Gray
Write-Host "   - Try different browser" -ForegroundColor Gray
Write-Host ""
Write-Host "6. Temporarily disable security software:" -ForegroundColor White
Write-Host "   - Antivirus real-time protection" -ForegroundColor Gray
Write-Host "   - Windows Defender" -ForegroundColor Gray
Write-Host ""

Write-Host "Diagnosis completed!" -ForegroundColor Green
Write-Host ""
