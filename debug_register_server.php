<?php
/**
 * 云服务器注册问题诊断脚本
 * 专门用于诊断云服务器环境下的注册问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器注册问题诊断</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        .step.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .step.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .step.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .step.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 云服务器注册问题诊断</h1>
            <p>详细检查云服务器环境下的注册功能问题</p>
        </div>
        
        <div class="content">
            <?php
            
            // 1. 环境信息检查
            echo "<div class='step info'>";
            echo "<h3>🌐 服务器环境信息</h3>";
            echo "<table>";
            echo "<tr><th>项目</th><th>值</th></tr>";
            echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
            echo "<tr><td>服务器软件</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</td></tr>";
            echo "<tr><td>服务器名称</td><td>" . ($_SERVER['SERVER_NAME'] ?? '未知') . "</td></tr>";
            echo "<tr><td>HTTP_HOST</td><td>" . ($_SERVER['HTTP_HOST'] ?? '未知') . "</td></tr>";
            echo "<tr><td>SERVER_ADDR</td><td>" . ($_SERVER['SERVER_ADDR'] ?? '未知') . "</td></tr>";
            echo "<tr><td>DOCUMENT_ROOT</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</td></tr>";
            echo "<tr><td>当前时间</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
            echo "</table>";
            echo "</div>";
            
            // 2. 数据库连接测试
            echo "<div class='step info'>";
            echo "<h3>🗄️ 数据库连接测试</h3>";
            
            try {
                require_once 'config/database.php';
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<p>✅ 数据库连接成功</p>";
                
                // 测试查询
                $result = $db->query("SELECT 1 as test, NOW() as current_time")->fetch();
                echo "<p>✅ 数据库查询测试成功</p>";
                echo "<div class='code-block'>测试结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "</div>";
                
                // 检查数据库信息
                $version = $db->query("SELECT VERSION() as version")->fetch();
                echo "<p>数据库版本: " . $version['version'] . "</p>";
                
                // 检查字符集
                $charset = $db->query("SELECT @@character_set_database as charset, @@collation_database as collation")->fetch();
                echo "<p>数据库字符集: " . $charset['charset'] . " / " . $charset['collation'] . "</p>";
                
            } catch (Exception $e) {
                echo "<p>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
            }
            echo "</div>";
            
            // 3. 检查必要的表结构
            if (isset($db)) {
                echo "<div class='step info'>";
                echo "<h3>📋 数据库表结构检查</h3>";
                
                $requiredTables = ['user_roles', 'users', 'user_profiles'];
                $tableStatus = [];
                
                foreach ($requiredTables as $table) {
                    try {
                        $result = $db->query("SHOW TABLES LIKE '{$table}'")->fetch();
                        if ($result) {
                            echo "<p>✅ {$table} 表存在</p>";
                            
                            // 检查表结构
                            $columns = $db->query("DESCRIBE {$table}")->fetchAll();
                            echo "<details><summary>查看 {$table} 表结构</summary>";
                            echo "<table>";
                            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
                            foreach ($columns as $column) {
                                echo "<tr>";
                                echo "<td>{$column['Field']}</td>";
                                echo "<td>{$column['Type']}</td>";
                                echo "<td>{$column['Null']}</td>";
                                echo "<td>{$column['Key']}</td>";
                                echo "<td>{$column['Default']}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                            echo "</details>";
                            
                            $tableStatus[$table] = true;
                        } else {
                            echo "<p>❌ {$table} 表不存在</p>";
                            $tableStatus[$table] = false;
                        }
                    } catch (Exception $e) {
                        echo "<p>❌ 检查 {$table} 表时出错: " . $e->getMessage() . "</p>";
                        $tableStatus[$table] = false;
                    }
                }
                echo "</div>";
                
                // 4. 检查用户角色数据
                if ($tableStatus['user_roles']) {
                    echo "<div class='step info'>";
                    echo "<h3>👥 用户角色数据检查</h3>";
                    
                    try {
                        $roles = $db->query("SELECT * FROM user_roles")->fetchAll();
                        if (empty($roles)) {
                            echo "<p>❌ 用户角色表为空</p>";
                        } else {
                            echo "<p>✅ 找到 " . count($roles) . " 个用户角色</p>";
                            echo "<table>";
                            echo "<tr><th>ID</th><th>角色代码</th><th>角色名称</th><th>描述</th></tr>";
                            foreach ($roles as $role) {
                                echo "<tr>";
                                echo "<td>{$role['id']}</td>";
                                echo "<td>{$role['role_code']}</td>";
                                echo "<td>{$role['role_name']}</td>";
                                echo "<td>{$role['description']}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        }
                    } catch (Exception $e) {
                        echo "<p>❌ 检查用户角色数据时出错: " . $e->getMessage() . "</p>";
                    }
                    echo "</div>";
                }
                
                // 5. 模拟注册流程测试
                echo "<div class='step info'>";
                echo "<h3>🧪 模拟注册流程测试</h3>";
                
                try {
                    // 开始事务
                    $db->query("BEGIN");
                    
                    $testData = [
                        'username' => 'test_user_' . time(),
                        'email' => 'test_' . time() . '@example.com',
                        'nickname' => '测试用户' . time(),
                        'password' => 'test123456'
                    ];
                    
                    echo "<p>测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "</p>";
                    
                    // 1. 检查用户名是否已存在
                    $existingUser = $db->query("SELECT id FROM users WHERE username = ?", [$testData['username']])->fetch();
                    if ($existingUser) {
                        echo "<p>❌ 用户名已存在</p>";
                    } else {
                        echo "<p>✅ 用户名可用</p>";
                    }
                    
                    // 2. 检查邮箱是否已存在
                    $existingEmail = $db->query("SELECT id FROM users WHERE email = ?", [$testData['email']])->fetch();
                    if ($existingEmail) {
                        echo "<p>❌ 邮箱已存在</p>";
                    } else {
                        echo "<p>✅ 邮箱可用</p>";
                    }
                    
                    // 3. 获取用户角色ID
                    $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
                    $roleId = $userRole ? $userRole['id'] : 3;
                    echo "<p>用户角色ID: {$roleId}</p>";
                    
                    // 4. 创建用户记录
                    $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
                    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
                    
                    $stmt = $db->getConnection()->prepare($sql);
                    $result = $stmt->execute([
                        $testData['username'],
                        $testData['email'],
                        $passwordHash,
                        $testData['nickname'],
                        $roleId
                    ]);
                    
                    if ($result) {
                        $userId = $db->getConnection()->lastInsertId();
                        echo "<p>✅ 用户创建成功，ID: {$userId}</p>";
                        
                        // 5. 创建用户资料
                        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                        
                        $profileStmt = $db->getConnection()->prepare($profileSql);
                        $profileResult = $profileStmt->execute([
                            $userId,
                            $testData['nickname'],
                            'assets/images/default-avatar.png'
                        ]);
                        
                        if ($profileResult) {
                            echo "<p>✅ 用户资料创建成功</p>";
                            echo "<p><strong>🎉 注册流程测试完全成功！</strong></p>";
                        } else {
                            echo "<p>❌ 用户资料创建失败</p>";
                        }
                        
                        // 清理测试数据
                        $db->query("DELETE FROM user_profiles WHERE user_id = ?", [$userId]);
                        $db->query("DELETE FROM users WHERE id = ?", [$userId]);
                        echo "<p>✅ 测试数据清理完成</p>";
                        
                    } else {
                        echo "<p>❌ 用户创建失败</p>";
                    }
                    
                    // 回滚事务
                    $db->query("ROLLBACK");
                    
                } catch (Exception $e) {
                    $db->query("ROLLBACK");
                    echo "<p>❌ 注册流程测试失败: " . $e->getMessage() . "</p>";
                    echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                }
                echo "</div>";
            }
            
            // 6. 文件权限检查
            echo "<div class='step info'>";
            echo "<h3>📁 文件权限检查</h3>";
            
            $checkPaths = [
                'uploads/avatars' => '头像上传目录',
                'config/database.php' => '数据库配置文件',
                'api/register.php' => '注册API文件'
            ];
            
            foreach ($checkPaths as $path => $description) {
                if (file_exists($path)) {
                    $perms = fileperms($path);
                    $readable = is_readable($path) ? '✅' : '❌';
                    $writable = is_writable($path) ? '✅' : '❌';
                    echo "<p>{$description}: {$readable} 可读 {$writable} 可写 (权限: " . substr(sprintf('%o', $perms), -4) . ")</p>";
                } else {
                    echo "<p>❌ {$description} 不存在: {$path}</p>";
                }
            }
            echo "</div>";
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="register.php" class="btn">测试注册页面</a>
                <a href="api/register.php" class="btn" onclick="testRegisterAPI(); return false;">测试注册API</a>
                <a href="index.php" class="btn">返回首页</a>
            </div>
        </div>
    </div>
    
    <script>
    function testRegisterAPI() {
        // 创建测试表单数据
        const formData = new FormData();
        formData.append('username', 'test_user_' + Date.now());
        formData.append('email', 'test_' + Date.now() + '@example.com');
        formData.append('nickname', '测试用户' + Date.now());
        formData.append('password', 'test123456');
        formData.append('confirmPassword', 'test123456');
        formData.append('agreeTerms', 'on');
        
        fetch('api/register.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert('API测试结果: ' + JSON.stringify(data, null, 2));
        })
        .catch(error => {
            alert('API测试错误: ' + error.message);
        });
    }
    </script>
</body>
</html>
