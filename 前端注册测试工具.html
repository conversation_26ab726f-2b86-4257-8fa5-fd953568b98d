<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端注册测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log-warning {
            background: #fff3cd;
            color: #856404;
        }
        .network-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 前端注册测试工具</h1>
            <p>测试前端注册表单的提交流程，检查JavaScript错误和网络请求</p>
        </div>

        <!-- 1. 环境检查 -->
        <div class="section info">
            <h3>🌐 浏览器环境检查</h3>
            <div id="browserInfo"></div>
        </div>

        <!-- 2. 网络连接测试 -->
        <div class="section info">
            <h3>🔗 网络连接测试</h3>
            <button class="btn" onclick="testNetworkConnection()">测试网络连接</button>
            <div id="networkResults"></div>
        </div>

        <!-- 3. 注册表单测试 -->
        <div class="section info">
            <h3>📝 注册表单测试</h3>
            <form id="testForm">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" value="">
                </div>
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" name="email" value="">
                </div>
                <div class="form-group">
                    <label for="nickname">昵称:</label>
                    <input type="text" id="nickname" name="nickname" value="">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" value="test123456">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" value="test123456">
                </div>
                <button type="button" class="btn" onclick="generateTestData()">生成测试数据</button>
                <button type="button" class="btn success" onclick="testRegistration()">测试注册</button>
            </form>
        </div>

        <!-- 4. 测试日志 -->
        <div class="section info">
            <h3>📋 测试日志</h3>
            <button class="btn" onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log-area"></div>
        </div>

        <!-- 5. 网络请求详情 -->
        <div class="section info">
            <h3>🌐 网络请求详情</h3>
            <div id="networkDetails"></div>
        </div>

        <!-- 6. 错误信息 -->
        <div class="section error" id="errorSection" style="display: none;">
            <h3>❌ 错误信息</h3>
            <div id="errorDetails"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="register.php" class="btn">实际注册页面</a>
            <a href="错误日志分析工具.php" class="btn">错误日志分析</a>
            <a href="快速修复云服务器注册问题.php" class="btn">快速修复</a>
            <a href="index.php" class="btn">返回首页</a>
        </div>
    </div>

    <script>
        // 全局变量
        let testLog = [];
        let networkRequests = [];

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeBrowserInfo();
            setupErrorHandling();
            log('页面加载完成', 'info');
        });

        // 初始化浏览器信息
        function initializeBrowserInfo() {
            const browserInfo = document.getElementById('browserInfo');
            const info = {
                'User Agent': navigator.userAgent,
                '浏览器语言': navigator.language,
                '屏幕分辨率': `${screen.width}x${screen.height}`,
                '视口大小': `${window.innerWidth}x${window.innerHeight}`,
                'Cookie启用': navigator.cookieEnabled ? '是' : '否',
                '在线状态': navigator.onLine ? '在线' : '离线',
                '当前URL': window.location.href,
                '协议': window.location.protocol,
                '主机': window.location.host
            };

            let html = '<table style="width: 100%; border-collapse: collapse;">';
            for (const [key, value] of Object.entries(info)) {
                html += `<tr><td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">${key}</td><td style="border: 1px solid #ddd; padding: 8px;">${value}</td></tr>`;
            }
            html += '</table>';
            browserInfo.innerHTML = html;
        }

        // 设置错误处理
        function setupErrorHandling() {
            window.addEventListener('error', function(e) {
                log(`JavaScript错误: ${e.message} (文件: ${e.filename}, 行: ${e.lineno})`, 'error');
                showError(`JavaScript错误: ${e.message}`);
            });

            window.addEventListener('unhandledrejection', function(e) {
                log(`未处理的Promise拒绝: ${e.reason}`, 'error');
                showError(`Promise错误: ${e.reason}`);
            });
        }

        // 日志记录函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp: timestamp,
                message: message,
                type: type
            };
            testLog.push(logEntry);
            
            const logArea = document.getElementById('testLog');
            const logDiv = document.createElement('div');
            logDiv.className = `log-entry log-${type}`;
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            logArea.appendChild(logDiv);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 清空日志
        function clearLog() {
            testLog = [];
            document.getElementById('testLog').innerHTML = '';
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('networkDetails').innerHTML = '';
        }

        // 显示错误
        function showError(message) {
            const errorSection = document.getElementById('errorSection');
            const errorDetails = document.getElementById('errorDetails');
            errorDetails.innerHTML += `<div class="log-entry log-error">${message}</div>`;
            errorSection.style.display = 'block';
        }

        // 生成测试数据
        function generateTestData() {
            const timestamp = Date.now();
            document.getElementById('username').value = `testuser_${timestamp}`;
            document.getElementById('email').value = `test_${timestamp}@example.com`;
            document.getElementById('nickname').value = `测试用户${timestamp}`;
            log('生成测试数据完成', 'success');
        }

        // 测试网络连接
        async function testNetworkConnection() {
            log('开始测试网络连接...', 'info');
            const resultsDiv = document.getElementById('networkResults');
            resultsDiv.innerHTML = '<p>正在测试...</p>';

            const tests = [
                { name: '测试当前域名连接', url: window.location.origin },
                { name: '测试注册API可达性', url: 'api/register.php' },
                { name: '测试数据库配置文件', url: 'config/database.php' }
            ];

            let results = '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
            results += '<tr><th style="border: 1px solid #ddd; padding: 8px;">测试项</th><th style="border: 1px solid #ddd; padding: 8px;">状态</th><th style="border: 1px solid #ddd; padding: 8px;">响应时间</th></tr>';

            for (const test of tests) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(test.url, { method: 'HEAD' });
                    const endTime = performance.now();
                    const responseTime = Math.round(endTime - startTime);
                    
                    const status = response.ok ? '✅ 成功' : `❌ 失败 (${response.status})`;
                    results += `<tr><td style="border: 1px solid #ddd; padding: 8px;">${test.name}</td><td style="border: 1px solid #ddd; padding: 8px;">${status}</td><td style="border: 1px solid #ddd; padding: 8px;">${responseTime}ms</td></tr>`;
                    
                    log(`${test.name}: ${status}, 响应时间: ${responseTime}ms`, response.ok ? 'success' : 'error');
                } catch (error) {
                    results += `<tr><td style="border: 1px solid #ddd; padding: 8px;">${test.name}</td><td style="border: 1px solid #ddd; padding: 8px;">❌ 错误</td><td style="border: 1px solid #ddd; padding: 8px;">-</td></tr>`;
                    log(`${test.name}: 连接错误 - ${error.message}`, 'error');
                }
            }
            results += '</table>';
            resultsDiv.innerHTML = results;
        }

        // 测试注册功能
        async function testRegistration() {
            log('开始测试注册功能...', 'info');
            
            // 验证表单数据
            const formData = new FormData();
            const fields = ['username', 'email', 'nickname', 'password', 'confirmPassword'];
            
            for (const field of fields) {
                const value = document.getElementById(field).value;
                if (!value) {
                    log(`错误: ${field} 字段为空`, 'error');
                    showError(`${field} 字段为空`);
                    return;
                }
                formData.append(field, value);
            }

            log('表单数据验证通过', 'success');

            try {
                // 记录请求开始时间
                const startTime = performance.now();
                log('发送注册请求...', 'info');

                // 发送请求
                const response = await fetch('api/register.php', {
                    method: 'POST',
                    body: formData
                });

                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);

                // 记录网络请求详情
                const requestDetails = {
                    url: 'api/register.php',
                    method: 'POST',
                    status: response.status,
                    statusText: response.statusText,
                    responseTime: responseTime,
                    headers: {}
                };

                // 获取响应头
                for (const [key, value] of response.headers.entries()) {
                    requestDetails.headers[key] = value;
                }

                networkRequests.push(requestDetails);
                displayNetworkDetails(requestDetails);

                log(`请求完成: ${response.status} ${response.statusText}, 响应时间: ${responseTime}ms`, 
                    response.ok ? 'success' : 'error');

                // 解析响应
                const responseText = await response.text();
                log(`响应内容长度: ${responseText.length} 字符`, 'info');

                try {
                    const responseData = JSON.parse(responseText);
                    log('响应JSON解析成功', 'success');
                    
                    if (responseData.success) {
                        log('✅ 注册成功！', 'success');
                        log(`用户ID: ${responseData.user_id || '未知'}`, 'info');
                        log(`消息: ${responseData.message || '无消息'}`, 'info');
                    } else {
                        log('❌ 注册失败', 'error');
                        log(`错误消息: ${responseData.message || '未知错误'}`, 'error');
                        
                        if (responseData.errors) {
                            for (const [field, error] of Object.entries(responseData.errors)) {
                                log(`字段错误 ${field}: ${error}`, 'error');
                            }
                        }
                        
                        showError(`注册失败: ${responseData.message}`);
                    }
                } catch (jsonError) {
                    log('❌ 响应JSON解析失败', 'error');
                    log(`原始响应: ${responseText.substring(0, 500)}...`, 'warning');
                    showError(`响应解析错误: ${jsonError.message}`);
                }

            } catch (networkError) {
                log(`❌ 网络请求失败: ${networkError.message}`, 'error');
                showError(`网络错误: ${networkError.message}`);
            }
        }

        // 显示网络请求详情
        function displayNetworkDetails(details) {
            const networkDiv = document.getElementById('networkDetails');
            
            let html = '<div class="network-info">';
            html += `<h4>最新请求详情</h4>`;
            html += `<p><strong>URL:</strong> ${details.url}</p>`;
            html += `<p><strong>方法:</strong> ${details.method}</p>`;
            html += `<p><strong>状态:</strong> ${details.status} ${details.statusText}</p>`;
            html += `<p><strong>响应时间:</strong> ${details.responseTime}ms</p>`;
            html += `<p><strong>响应头:</strong></p>`;
            html += '<ul>';
            for (const [key, value] of Object.entries(details.headers)) {
                html += `<li><strong>${key}:</strong> ${value}</li>`;
            }
            html += '</ul>';
            html += '</div>';
            
            networkDiv.innerHTML = html;
        }
    </script>
</body>
</html>
