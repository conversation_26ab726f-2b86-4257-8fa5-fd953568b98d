<?php
/**
 * 用户登录API
 * 处理用户登录请求，集成现有Auth类
 */

// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // 本地开发环境设为0
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once '../classes/Auth.php';

header('Content-Type: application/json; charset=utf-8');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    exit;
}

try {
    // 获取表单数据
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $rememberMe = isset($_POST['rememberMe']) && $_POST['rememberMe'] === 'on';

    // 数据验证
    $errors = [];

    if (empty($username)) {
        $errors['username'] = '请输入用户名或邮箱';
    }

    if (empty($password)) {
        $errors['password'] = '请输入密码';
    }

    // 如果有验证错误，返回错误信息
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => '请检查输入信息',
            'errors' => $errors
        ]);
        exit;
    }

    // 检查数据库连接
    try {
        require_once '../config/database.php';
        $db = DatabaseConfig::getInstance();
        // 测试数据库连接
        $db->query("SELECT 1");
    } catch (Exception $dbError) {
        error_log("数据库连接失败: " . $dbError->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '系统暂时无法连接数据库，请稍后重试'
        ]);
        exit;
    }

    // 使用Auth类进行登录
    $auth = new Auth();
    $result = $auth->login($username, $password, $rememberMe);
    
    if ($result['success']) {
        // 登录成功，获取用户信息
        $user = $result['user'];
        
        // 记录登录日志
        error_log("用户登录成功: ID={$user['id']}, 用户名={$user['username']}, IP=" . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '登录成功',
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'full_name' => $user['full_name'],
                'role' => $user['role_code'],
                'role_name' => $user['role_name'],
                'avatar' => $user['avatar']
            ],
            'redirect_url' => determineRedirectUrl($user['role_code'])
        ]);
        
    } else {
        // 登录失败
        echo json_encode([
            'success' => false,
            'message' => $result['message']
        ]);
    }
    
} catch (Exception $e) {
    error_log("用户登录错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '登录过程中发生错误，请稍后重试'
    ]);
}

/**
 * 根据用户角色确定重定向URL
 */
function determineRedirectUrl($roleCode) {
    switch ($roleCode) {
        case 'super_admin':
        case 'admin':
            return 'admin-dashboard.php';
        case 'user':
        default:
            return 'index.php';
    }
}
?>
