# Mobile vs Desktop Access Analysis
# Analyze why mobile can access but desktop cannot

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Mobile vs Desktop Access Analysis" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$serverPort = 8888
$targetPath = "/tencentcloud"
$fullUrl = "http://$serverIP`:$serverPort$targetPath"

# Different User Agents to test
$userAgents = @{
    "iPhone" = "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
    "Android" = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
    "Desktop_Chrome" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    "Desktop_Firefox" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
    "Desktop_Edge" = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59"
}

Write-Host "1. Testing different User Agents..." -ForegroundColor Yellow
Write-Host ""

foreach ($name in $userAgents.Keys) {
    $ua = $userAgents[$name]
    Write-Host "Testing with $name User Agent:" -ForegroundColor Cyan
    Write-Host "  UA: $($ua.Substring(0, [Math]::Min(60, $ua.Length)))..." -ForegroundColor Gray
    
    try {
        $headers = @{'User-Agent' = $ua}
        $response = Invoke-WebRequest -Uri $fullUrl -Headers $headers -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "  ✅ SUCCESS - Status: $($response.StatusCode), Length: $($response.Content.Length) bytes" -ForegroundColor Green
        
        # Check if response contains actual content
        if ($response.Content.Length -gt 100) {
            Write-Host "  📄 Content preview: $($response.Content.Substring(0, [Math]::Min(100, $response.Content.Length)))..." -ForegroundColor Blue
        }
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "  ❌ HTTP $statusCode - $($_.Exception.Message)" -ForegroundColor Red
        } else {
            Write-Host "  ❌ Connection failed - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    Write-Host ""
}

Write-Host "2. Testing different HTTP methods..." -ForegroundColor Yellow
Write-Host ""

$methods = @("GET", "HEAD", "OPTIONS")
foreach ($method in $methods) {
    Write-Host "Testing $method method:" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $fullUrl -Method $method -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "  ✅ $method - Status: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "  ❌ $method - HTTP $statusCode" -ForegroundColor Red
        } else {
            Write-Host "  ❌ $method - Connection failed" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "3. Testing with different Accept headers..." -ForegroundColor Yellow
Write-Host ""

$acceptHeaders = @{
    "HTML" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
    "JSON" = "application/json,text/plain,*/*"
    "Mobile" = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8"
    "Any" = "*/*"
}

foreach ($name in $acceptHeaders.Keys) {
    $accept = $acceptHeaders[$name]
    Write-Host "Testing with $name Accept header:" -ForegroundColor Cyan
    try {
        $headers = @{
            'Accept' = $accept
            'User-Agent' = $userAgents["iPhone"]
        }
        $response = Invoke-WebRequest -Uri $fullUrl -Headers $headers -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "  ✅ SUCCESS with $name - Status: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "  ❌ HTTP $statusCode with $name" -ForegroundColor Red
        } else {
            Write-Host "  ❌ Connection failed with $name" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "4. Network path analysis..." -ForegroundColor Yellow
Write-Host ""

# Check if we're using different network interfaces
$adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
Write-Host "Active network adapters:" -ForegroundColor Cyan
foreach ($adapter in $adapters) {
    $ip = Get-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($ip) {
        Write-Host "  $($adapter.Name): $($ip.IPAddress)" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "5. DNS resolution comparison..." -ForegroundColor Yellow
Write-Host ""

$dnsServers = @("*******", "*******", "*********")
foreach ($dns in $dnsServers) {
    Write-Host "Testing DNS resolution via $dns" -ForegroundColor Cyan
    try {
        $result = Resolve-DnsName -Name $serverIP -Server $dns -ErrorAction Stop
        Write-Host "  ✅ Resolved via $dns" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ Failed via $dns - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "6. Proxy detection..." -ForegroundColor Yellow
Write-Host ""

# Check for proxy settings
$proxySettings = Get-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -ErrorAction SilentlyContinue
if ($proxySettings.ProxyEnable -eq 1) {
    Write-Host "  ⚠️  Proxy is enabled: $($proxySettings.ProxyServer)" -ForegroundColor Yellow
} else {
    Write-Host "  ✅ No proxy configured" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Analysis Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Possible reasons for mobile vs desktop difference:" -ForegroundColor White
Write-Host ""
Write-Host "1. User-Agent filtering - Server may block desktop browsers" -ForegroundColor Yellow
Write-Host "2. Network path difference - Mobile uses cellular, desktop uses WiFi" -ForegroundColor Yellow
Write-Host "3. Proxy settings - Desktop may have proxy configured" -ForegroundColor Yellow
Write-Host "4. DNS caching - Different DNS resolution results" -ForegroundColor Yellow
Write-Host "5. Firewall rules - Desktop firewall may block the connection" -ForegroundColor Yellow
Write-Host "6. Browser cache - Desktop browser may have cached error response" -ForegroundColor Yellow
Write-Host ""
Write-Host "Recommended next steps:" -ForegroundColor Green
Write-Host "• Try accessing from desktop using mobile hotspot" -ForegroundColor White
Write-Host "• Clear all browser cache and cookies" -ForegroundColor White
Write-Host "• Try incognito/private browsing mode" -ForegroundColor White
Write-Host "• Disable any proxy settings" -ForegroundColor White
Write-Host "• Try different browsers on desktop" -ForegroundColor White

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
