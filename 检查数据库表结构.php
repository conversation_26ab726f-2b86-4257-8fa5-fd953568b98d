<?php
/**
 * 检查数据库表结构
 */

require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>检查数据库表结构</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .btn { display: inline-block; padding: 12px 24px; background: #ff9a9e; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #fecfef; color: #333; }
    .btn.fix { background: #28a745; }
    .btn.fix:hover { background: #1e7e34; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔍 检查数据库表结构</h1>";

try {
    $db = db();
    
    // 检查users表结构
    echo "<div class='info'>";
    echo "<h3>👥 users表结构</h3>";
    
    $tableInfo = $db->fetchAll("DESCRIBE users");
    echo "<table>";
    echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    $hasAutoIncrement = false;
    $idField = null;
    
    foreach ($tableInfo as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'id') {
            $idField = $column;
            if (strpos($column['Extra'], 'auto_increment') !== false) {
                $hasAutoIncrement = true;
            }
        }
    }
    echo "</table>";
    
    if ($hasAutoIncrement) {
        echo "<div class='success'>✅ id字段有AUTO_INCREMENT属性</div>";
    } else {
        echo "<div class='error'>❌ id字段缺少AUTO_INCREMENT属性</div>";
        echo "<p>当前id字段信息：</p>";
        echo "<pre>" . print_r($idField, true) . "</pre>";
        
        echo "<div class='warning'>";
        echo "<h4>⚠️ 需要修复AUTO_INCREMENT</h4>";
        echo "<p>点击下面的按钮修复AUTO_INCREMENT属性：</p>";
        
        if (isset($_POST['fix_auto_increment'])) {
            try {
                // 修复AUTO_INCREMENT
                $db->getConnection()->exec("ALTER TABLE users MODIFY id INT AUTO_INCREMENT PRIMARY KEY");
                echo "<div class='success'>✅ AUTO_INCREMENT属性修复成功！请刷新页面查看结果。</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ 修复AUTO_INCREMENT失败: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<form method='post'>";
            echo "<button type='submit' name='fix_auto_increment' class='btn fix'>修复AUTO_INCREMENT</button>";
            echo "</form>";
        }
        echo "</div>";
    }
    
    echo "</div>";
    
    // 检查表引擎
    echo "<div class='info'>";
    echo "<h3>🔧 表引擎信息</h3>";
    
    $engineInfo = $db->fetchOne("SELECT ENGINE, TABLE_COLLATION FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users'");
    if ($engineInfo) {
        echo "<p><strong>存储引擎:</strong> {$engineInfo['ENGINE']}</p>";
        echo "<p><strong>字符集:</strong> {$engineInfo['TABLE_COLLATION']}</p>";
        
        if ($engineInfo['ENGINE'] === 'InnoDB') {
            echo "<div class='success'>✅ 使用InnoDB引擎，支持事务和外键</div>";
        } else {
            echo "<div class='warning'>⚠️ 当前使用{$engineInfo['ENGINE']}引擎，建议使用InnoDB</div>";
            
            if (isset($_POST['fix_engine'])) {
                try {
                    $db->getConnection()->exec("ALTER TABLE users ENGINE=InnoDB");
                    echo "<div class='success'>✅ 表引擎已修改为InnoDB！请刷新页面查看结果。</div>";
                } catch (Exception $e) {
                    echo "<div class='error'>❌ 修改表引擎失败: " . $e->getMessage() . "</div>";
                }
            } else {
                echo "<form method='post'>";
                echo "<button type='submit' name='fix_engine' class='btn fix'>修改为InnoDB引擎</button>";
                echo "</form>";
            }
        }
    }
    echo "</div>";
    
    // 检查AUTO_INCREMENT当前值
    echo "<div class='info'>";
    echo "<h3>🔢 AUTO_INCREMENT状态</h3>";
    
    $autoIncrementInfo = $db->fetchOne("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'users'");
    if ($autoIncrementInfo) {
        echo "<p><strong>下一个AUTO_INCREMENT值:</strong> {$autoIncrementInfo['AUTO_INCREMENT']}</p>";
    }
    
    // 检查当前最大ID
    $maxId = $db->fetchOne("SELECT MAX(id) as max_id, COUNT(*) as total_users FROM users");
    if ($maxId) {
        echo "<p><strong>当前最大ID:</strong> " . ($maxId['max_id'] ?? '无') . "</p>";
        echo "<p><strong>用户总数:</strong> {$maxId['total_users']}</p>";
    }
    echo "</div>";
    
    // 测试插入和ID获取
    echo "<div class='info'>";
    echo "<h3>🧪 测试插入和ID获取</h3>";
    
    if (isset($_POST['test_insert'])) {
        try {
            $db->getConnection()->beginTransaction();
            
            $testData = [
                'username' => 'test_structure_' . time(),
                'email' => 'test_structure_' . time() . '@example.com',
                'password_hash' => password_hash('test123', PASSWORD_DEFAULT),
                'full_name' => '结构测试用户',
                'role_id' => 3,
                'status' => 'active'
            ];
            
            $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
            
            $stmt = $db->getConnection()->prepare($sql);
            $result = $stmt->execute([
                $testData['username'],
                $testData['email'],
                $testData['password_hash'],
                $testData['full_name'],
                $testData['role_id'],
                $testData['status']
            ]);
            
            if ($result) {
                echo "<div class='success'>✅ 测试插入成功</div>";
                
                // 测试不同的ID获取方法
                $userId1 = $db->getConnection()->lastInsertId();
                echo "<p>PDO lastInsertId(): " . ($userId1 ?: '失败') . "</p>";
                
                $result2 = $db->getConnection()->query("SELECT LAST_INSERT_ID() as last_id")->fetch();
                $userId2 = $result2 ? $result2['last_id'] : null;
                echo "<p>MySQL LAST_INSERT_ID(): " . ($userId2 ?: '失败') . "</p>";
                
                $result3 = $db->fetchOne("SELECT id FROM users WHERE username = ?", [$testData['username']]);
                $userId3 = $result3 ? $result3['id'] : null;
                echo "<p>查询获取ID: " . ($userId3 ?: '失败') . "</p>";
                
                if ($userId1 && $userId2 && $userId3) {
                    echo "<div class='success'>🎉 所有ID获取方法都成功！</div>";
                } else {
                    echo "<div class='error'>❌ 部分ID获取方法失败</div>";
                }
            } else {
                echo "<div class='error'>❌ 测试插入失败</div>";
            }
            
            // 回滚测试
            $db->getConnection()->rollback();
            echo "<p>✅ 测试数据已回滚</p>";
            
        } catch (Exception $e) {
            $db->getConnection()->rollback();
            echo "<div class='error'>❌ 测试失败: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<form method='post'>";
        echo "<button type='submit' name='test_insert' class='btn'>测试插入和ID获取</button>";
        echo "</form>";
    }
    echo "</div>";
    
    // 显示最近的用户
    echo "<div class='info'>";
    echo "<h3>👤 最近注册的用户</h3>";
    
    $recentUsers = $db->fetchAll("SELECT id, username, email, created_at FROM users ORDER BY id DESC LIMIT 5");
    if ($recentUsers) {
        echo "<table>";
        echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>创建时间</th></tr>";
        foreach ($recentUsers as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无用户数据</p>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='测试注册修复.php' class='btn'>测试注册功能</a>";
echo "<a href='修复用户ID获取问题.php' class='btn'>诊断工具</a>";
echo "<a href='register.php' class='btn'>注册页面</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
