<?php
/**
 * 新数据库连接测试脚本
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>数据库连接测试</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    // 引入数据库配置
    require_once 'config/database.php';
    
    echo "<p>正在尝试连接数据库...</p>\n";
    
    // 获取数据库实例
    $db = db();
    
    echo "<p class='success'>✓ 数据库连接成功！</p>\n";
    
    // 获取数据库类型
    $connection = $db->getConnection();
    $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<p class='info'>数据库类型: " . htmlspecialchars($driver) . "</p>\n";
    
    if ($driver === 'sqlite') {
        echo "<p class='info'>ℹ 当前使用SQLite数据库作为备用方案</p>\n";
    } else {
        echo "<p class='success'>✓ 当前使用MySQL数据库</p>\n";
    }
    
    // 测试查询
    echo "<h3>测试数据库查询</h3>\n";
    
    // 检查用户表
    $users = $db->fetchAll("SELECT * FROM users LIMIT 5");
    echo "<p>用户表记录数: " . count($users) . "</p>\n";
    
    if (!empty($users)) {
        echo "<h4>用户列表:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>角色ID</th><th>状态</th><th>创建时间</th></tr>\n";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($user['role_id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 检查角色表
    $roles = $db->fetchAll("SELECT * FROM user_roles");
    echo "<p>角色表记录数: " . count($roles) . "</p>\n";
    
    if (!empty($roles)) {
        echo "<h4>角色列表:</h4>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>ID</th><th>角色代码</th><th>角色名称</th><th>描述</th><th>权限</th></tr>\n";
        
        foreach ($roles as $role) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($role['id']) . "</td>";
            echo "<td>" . htmlspecialchars($role['role_code']) . "</td>";
            echo "<td>" . htmlspecialchars($role['role_name']) . "</td>";
            echo "<td>" . htmlspecialchars($role['description'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($role['permissions'] ?? '') . "</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
    // 测试登录验证
    echo "<h3>测试登录验证</h3>\n";
    
    $test_username = 'admin';
    $test_password = 'admin123';
    
    $sql = "SELECT u.*, ur.role_code, ur.role_name
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            WHERE u.username = ? AND u.status = 'active'";
    $user_data = $db->fetchOne($sql, [$test_username]);
    
    if ($user_data) {
        echo "<p class='success'>✓ 找到用户: " . htmlspecialchars($user_data['username']) . "</p>\n";
        echo "<p>角色: " . htmlspecialchars($user_data['role_name']) . " (" . htmlspecialchars($user_data['role_code']) . ")</p>\n";
        
        // 验证密码
        if (password_verify($test_password, $user_data['password_hash'])) {
            echo "<p class='success'>✓ 密码验证成功</p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ 密码验证失败（可能使用了简单验证）</p>\n";
        }
    } else {
        echo "<p class='error'>✗ 未找到用户</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>错误详情: " . htmlspecialchars($e->getTraceAsString()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='admin-login.php'>返回登录页面</a> | <a href='admin-dashboard.php'>管理面板</a></p>\n";
?>
