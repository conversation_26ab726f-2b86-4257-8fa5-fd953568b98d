<?php
// 生成专家头像占位图片的脚本
// 这个脚本会创建6个不同颜色的占位图片

// 检查GD库是否可用
if (!extension_loaded('gd')) {
    die('GD库未安装，无法生成图片');
}

// 创建image目录（如果不存在）
if (!file_exists('image')) {
    mkdir('image', 0755, true);
}

// 专家信息和对应的颜色
$experts = [
    ['name' => 'Arianne Dee', 'color' => [52, 152, 219]], // 蓝色
    ['name' => 'Sari Greene', 'color' => [155, 89, 182]], // 紫色
    ['name' => '<PERSON> Gonçalves', 'color' => [46, 204, 113]], // 绿色
    ['name' => '<PERSON> Ford', 'color' => [230, 126, 34]], // 橙色
    ['name' => 'Kelsey Hightower', 'color' => [231, 76, 60]], // 红色
    ['name' => '<PERSON>', 'color' => [52, 73, 94]] // 深灰色
];

// 图片尺寸
$width = 300;
$height = 300;

foreach ($experts as $index => $expert) {
    $expertNumber = $index + 1;
    $filename = "image/expert-{$expertNumber}.jpg";
    
    // 创建画布
    $image = imagecreatetruecolor($width, $height);
    
    // 设置背景色（渐变效果）
    $baseColor = $expert['color'];
    
    // 创建渐变背景
    for ($y = 0; $y < $height; $y++) {
        $ratio = $y / $height;
        $r = $baseColor[0] + ($ratio * 30);
        $g = $baseColor[1] + ($ratio * 30);
        $b = $baseColor[2] + ($ratio * 30);
        
        // 确保颜色值在有效范围内
        $r = min(255, max(0, $r));
        $g = min(255, max(0, $g));
        $b = min(255, max(0, $b));
        
        $color = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $y, $width, $y, $color);
    }
    
    // 添加白色文字
    $white = imagecolorallocate($image, 255, 255, 255);
    $black = imagecolorallocate($image, 0, 0, 0);
    
    // 添加专家姓名（如果支持TTF字体）
    $fontSize = 16;
    $fontFile = null; // 使用内置字体
    
    // 计算文字位置（居中）
    $textBox = imagettfbbox($fontSize, 0, $fontFile, $expert['name']);
    if ($textBox) {
        $textWidth = $textBox[4] - $textBox[0];
        $textHeight = $textBox[1] - $textBox[7];
        $x = ($width - $textWidth) / 2;
        $y = ($height + $textHeight) / 2;
        
        // 添加文字阴影
        imagettftext($image, $fontSize, 0, $x + 1, $y + 1, $black, $fontFile, $expert['name']);
        // 添加白色文字
        imagettftext($image, $fontSize, 0, $x, $y, $white, $fontFile, $expert['name']);
    } else {
        // 使用内置字体作为备选
        $x = ($width - strlen($expert['name']) * 10) / 2;
        $y = $height / 2;
        imagestring($image, 5, $x, $y, $expert['name'], $white);
    }
    
    // 添加一个简单的头像轮廓
    $centerX = $width / 2;
    $centerY = $height / 2 - 30;
    $radius = 40;
    
    // 画头部圆形
    $lightColor = imagecolorallocate($image, 255, 255, 255);
    imagefilledellipse($image, $centerX, $centerY, $radius * 2, $radius * 2, $lightColor);
    
    // 画身体
    $bodyWidth = 60;
    $bodyHeight = 40;
    imagefilledrectangle($image, 
        $centerX - $bodyWidth/2, 
        $centerY + $radius - 10, 
        $centerX + $bodyWidth/2, 
        $centerY + $radius + $bodyHeight - 10, 
        $lightColor
    );
    
    // 保存图片
    if (imagejpeg($image, $filename, 85)) {
        echo "生成图片: {$filename}\n";
    } else {
        echo "生成图片失败: {$filename}\n";
    }
    
    // 释放内存
    imagedestroy($image);
}

echo "所有专家头像占位图片生成完成！\n";
?>
