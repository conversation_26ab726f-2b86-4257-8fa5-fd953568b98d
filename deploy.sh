#!/bin/bash

# 比特熊智慧系统部署脚本
echo "开始部署比特熊智慧系统..."

# 1. 拉取最新代码
echo "拉取最新代码..."
git pull origin main

# 2. 备份数据库（可选）
echo "备份数据库..."
mysqldump -u root -p wisdom_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 3. 检查是否有数据库更新
if [ -f "database/updates.sql" ]; then
    echo "执行数据库更新..."
    mysql -u root -p wisdom_system < database/updates.sql
    # 移动已执行的更新文件
    mv database/updates.sql database/executed/updates_$(date +%Y%m%d_%H%M%S).sql
fi

# 4. 设置文件权限
echo "设置文件权限..."
chmod -R 755 .
chmod -R 777 uploads/  # 如果有上传目录

# 5. 清理缓存（如果有）
if [ -d "cache" ]; then
    echo "清理缓存..."
    rm -rf cache/*
fi

# 6. 重启服务（如果需要）
# systemctl reload apache2

echo "部署完成！时间: $(date)"
echo "请检查网站是否正常运行"
