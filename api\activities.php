<?php
/**
 * 用户活动API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取用户活动
 */
function handleGet($db) {
    $type = $_GET['type'] ?? 'all';
    $limit = $_GET['limit'] ?? 10;

    if ($type === 'user_activities') {
        // 获取用户动态（包括注册等）
        try {
            $sql = "SELECT ua.id, ua.user_id, ua.activity_type, ua.description, ua.created_at, ua.data
                    FROM user_activities ua
                    ORDER BY ua.created_at DESC
                    LIMIT ?";

            $activities = $db->fetchAll($sql, [(int)$limit]);
            echo json_encode(['success' => true, 'activities' => $activities]);
        } catch (Exception $e) {
            // 如果user_activities表不存在，返回空数据
            echo json_encode(['success' => true, 'activities' => []]);
        }
    } else {
        // 原有的活动获取逻辑
        $sql = "SELECT ua.id, ua.user_id, ua.activity_type, ua.title, ua.description, ua.created_at,
                       u.username, u.full_name,
                       CASE
                           WHEN TIMESTAMPDIFF(MINUTE, ua.created_at, NOW()) < 60 THEN CONCAT(TIMESTAMPDIFF(MINUTE, ua.created_at, NOW()), '分钟前')
                           WHEN TIMESTAMPDIFF(HOUR, ua.created_at, NOW()) < 24 THEN CONCAT(TIMESTAMPDIFF(HOUR, ua.created_at, NOW()), '小时前')
                           ELSE CONCAT(TIMESTAMPDIFF(DAY, ua.created_at, NOW()), '天前')
                       END as time_ago
                FROM user_activities ua
                LEFT JOIN users u ON ua.user_id = u.id
                ORDER BY ua.created_at DESC
                LIMIT ?";

        $activities = $db->fetchAll($sql, [(int)$limit]);
        echo json_encode(['success' => true, 'data' => $activities]);
    }
}

/**
 * 处理POST请求 - 创建用户活动记录
 */
function handlePost($db, $input) {
    if (!$input || !isset($input['activity_type'], $input['title'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        return;
    }
    
    $data = [
        'user_id' => $input['user_id'] ?? null,
        'activity_type' => $input['activity_type'],
        'title' => $input['title'],
        'description' => $input['description'] ?? '',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];
    
    $sql = "INSERT INTO user_activities (user_id, activity_type, title, description, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $db->query($sql, [
        $data['user_id'],
        $data['activity_type'],
        $data['title'],
        $data['description'],
        $data['ip_address'],
        $data['user_agent']
    ]);
    
    $activityId = $db->lastInsertId();
    
    echo json_encode([
        'success' => true, 
        'message' => '活动记录创建成功',
        'data' => ['id' => $activityId]
    ]);
}

/**
 * 记录用户活动的便捷函数
 */
function logActivity($activityType, $title, $description = '', $userId = null) {
    try {
        $db = db();
        $sql = "INSERT INTO user_activities (user_id, activity_type, title, description, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $db->query($sql, [
            $userId,
            $activityType,
            $title,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        return $db->lastInsertId();
    } catch (Exception $e) {
        error_log("记录用户活动失败: " . $e->getMessage());
        return false;
    }
}
?>
