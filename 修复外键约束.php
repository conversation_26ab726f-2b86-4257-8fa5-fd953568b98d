<?php
/**
 * 修复外键约束问题
 * 专门解决注册时的外键约束错误
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复外键约束 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .step.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .step.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .step.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #ee5a24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 修复外键约束</h1>
            <p>解决注册时的外键约束错误</p>
        </div>
        
        <div class="content">
            <?php
            
            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<div class='step success'>";
                echo "<h3>✅ 数据库连接成功</h3>";
                echo "</div>";
                
                // 1. 禁用外键检查
                echo "<div class='step info'>";
                echo "<h3>🔧 步骤1: 禁用外键检查</h3>";
                $db->exec("SET FOREIGN_KEY_CHECKS = 0");
                echo "<p>✅ 外键检查已禁用</p>";
                echo "</div>";
                
                // 2. 删除所有相关表（按依赖顺序）
                echo "<div class='step info'>";
                echo "<h3>🗑️ 步骤2: 清理现有表</h3>";
                
                $tablesToDrop = [
                    'user_profiles',
                    'user_sessions', 
                    'notifications',
                    'user_activities',
                    'users',
                    'user_roles'
                ];
                
                foreach ($tablesToDrop as $table) {
                    try {
                        $db->exec("DROP TABLE IF EXISTS {$table}");
                        echo "<p>✅ 删除表: {$table}</p>";
                    } catch (Exception $e) {
                        echo "<p>⚠️ 删除表 {$table} 失败: " . $e->getMessage() . "</p>";
                    }
                }
                echo "</div>";
                
                // 3. 重新创建用户角色表
                echo "<div class='step info'>";
                echo "<h3>🏗️ 步骤3: 创建用户角色表</h3>";
                
                $createUserRoles = "
                    CREATE TABLE user_roles (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        role_code VARCHAR(50) NOT NULL UNIQUE,
                        role_name VARCHAR(100) NOT NULL,
                        description TEXT,
                        permissions TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $db->exec($createUserRoles);
                echo "<p>✅ 用户角色表创建成功</p>";
                
                // 插入默认角色
                $insertRoles = "
                    INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                    ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                    ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                    ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                ";
                
                $db->exec($insertRoles);
                echo "<p>✅ 默认角色数据插入成功</p>";
                echo "</div>";
                
                // 4. 重新创建用户表
                echo "<div class='step info'>";
                echo "<h3>🏗️ 步骤4: 创建用户表</h3>";
                
                $createUsers = "
                    CREATE TABLE users (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        username VARCHAR(50) NOT NULL UNIQUE,
                        email VARCHAR(100) NOT NULL UNIQUE,
                        password_hash VARCHAR(255) NOT NULL,
                        full_name VARCHAR(100),
                        avatar VARCHAR(255),
                        role_id INT DEFAULT 3,
                        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                        last_login TIMESTAMP NULL,
                        login_count INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_role_id (role_id),
                        CONSTRAINT fk_users_role_id FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL ON UPDATE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $db->exec($createUsers);
                echo "<p>✅ 用户表创建成功</p>";
                echo "</div>";
                
                // 5. 重新创建用户资料表
                echo "<div class='step info'>";
                echo "<h3>🏗️ 步骤5: 创建用户资料表</h3>";
                
                $createUserProfiles = "
                    CREATE TABLE user_profiles (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        user_id INT NOT NULL,
                        nickname VARCHAR(100),
                        bio TEXT,
                        signature VARCHAR(500),
                        avatar_url VARCHAR(255),
                        location VARCHAR(100),
                        website VARCHAR(255),
                        social_links JSON,
                        post_count INT DEFAULT 0,
                        comment_count INT DEFAULT 0,
                        like_received_count INT DEFAULT 0,
                        reputation_score INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_user_id (user_id),
                        CONSTRAINT fk_user_profiles_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $db->exec($createUserProfiles);
                echo "<p>✅ 用户资料表创建成功</p>";
                echo "</div>";
                
                // 6. 创建通知表
                echo "<div class='step info'>";
                echo "<h3>🏗️ 步骤6: 创建通知表</h3>";
                
                $createNotifications = "
                    CREATE TABLE notifications (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        user_id INT NOT NULL,
                        title VARCHAR(255) NOT NULL,
                        content TEXT,
                        type VARCHAR(50) DEFAULT 'info',
                        is_read BOOLEAN DEFAULT FALSE,
                        data JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_user_id (user_id),
                        INDEX idx_is_read (is_read),
                        CONSTRAINT fk_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $db->exec($createNotifications);
                echo "<p>✅ 通知表创建成功</p>";
                echo "</div>";
                
                // 7. 创建用户活动表
                echo "<div class='step info'>";
                echo "<h3>🏗️ 步骤7: 创建用户活动表</h3>";
                
                $createUserActivities = "
                    CREATE TABLE user_activities (
                        id INT PRIMARY KEY AUTO_INCREMENT,
                        user_id INT NOT NULL,
                        activity_type VARCHAR(50) NOT NULL,
                        description TEXT,
                        data JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_user_id (user_id),
                        INDEX idx_activity_type (activity_type),
                        CONSTRAINT fk_user_activities_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ";
                
                $db->exec($createUserActivities);
                echo "<p>✅ 用户活动表创建成功</p>";
                echo "</div>";
                
                // 8. 启用外键检查
                echo "<div class='step info'>";
                echo "<h3>🔧 步骤8: 启用外键检查</h3>";
                $db->exec("SET FOREIGN_KEY_CHECKS = 1");
                echo "<p>✅ 外键检查已启用</p>";
                echo "</div>";
                
                // 9. 测试注册功能
                echo "<div class='step info'>";
                echo "<h3>🧪 步骤9: 测试注册功能</h3>";
                
                try {
                    $testUsername = 'fktest_' . time();
                    $testEmail = 'fktest_' . time() . '@example.com';
                    
                    $db->exec("BEGIN");
                    
                    // 获取用户角色ID
                    $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
                    $roleId = $userRole ? $userRole['id'] : 3;
                    
                    // 创建用户
                    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
                    
                    $stmt = $db->prepare($sql);
                    $result = $stmt->execute([
                        $testUsername,
                        $testEmail,
                        password_hash('test123', PASSWORD_DEFAULT),
                        '外键测试用户',
                        $roleId
                    ]);
                    
                    if ($result) {
                        $userId = $db->lastInsertId();
                        echo "<p>✅ 用户创建成功，ID: {$userId}</p>";
                        
                        // 创建用户资料
                        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                        
                        $profileStmt = $db->prepare($profileSql);
                        $profileResult = $profileStmt->execute([
                            $userId,
                            '外键测试用户',
                            'assets/images/default-avatar.png'
                        ]);
                        
                        if ($profileResult) {
                            echo "<p>✅ 用户资料创建成功</p>";
                            
                            // 清理测试数据
                            $db->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                            $db->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
                            echo "<p>✅ 测试数据清理完成</p>";
                            
                            echo "<p><strong>🎉 外键约束修复成功！注册功能正常！</strong></p>";
                            
                        } else {
                            echo "<p>❌ 用户资料创建失败</p>";
                        }
                    } else {
                        echo "<p>❌ 用户创建失败</p>";
                    }
                    
                    $db->exec("COMMIT");
                    
                } catch (Exception $e) {
                    $db->exec("ROLLBACK");
                    echo "<p>❌ 注册测试失败: " . $e->getMessage() . "</p>";
                    echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                }
                echo "</div>";
                
                echo "<div class='step success'>";
                echo "<h3>🎯 修复完成</h3>";
                echo "<p>外键约束问题已修复，数据库表结构已重建，现在可以正常注册用户了！</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ 修复失败</h3>";
                echo "<p>错误信息: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                echo "</div>";
            }
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="注册测试.php" class="btn">测试注册功能</a>
                <a href="诊断界面.php" class="btn">重新诊断</a>
                <a href="register.php" class="btn">用户注册</a>
            </div>
        </div>
    </div>
</body>
</html>
