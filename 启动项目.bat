@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 比特熊项目启动器
echo ========================================
echo.

echo 正在启动项目...
echo.

echo 📄 可用页面:
echo   1. 静态版本 (推荐)
echo   2. 完整功能版本 (需要服务器)
echo   3. 管理后台 (需要PHP)
echo.

set /p choice="请选择要打开的版本 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🌟 启动静态版本...
    echo 这个版本包含所有视觉效果和动画
    start "" "index-standalone.html"
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo 🔄 尝试启动HTTP服务器...
    
    :: 尝试Python服务器
    python -m http.server 8000 >nul 2>&1 &
    if %errorlevel% == 0 (
        echo ✅ Python服务器启动成功!
        echo 🌐 访问地址: http://localhost:8000
        timeout /t 2 >nul
        start "" "http://localhost:8000/index.html"
        goto :end
    )
    
    :: 尝试PHP服务器
    if exist "php\php.exe" (
        echo 🔄 尝试本地PHP服务器...
        start /min php\php.exe -S localhost:8000
        timeout /t 2 >nul
        echo ✅ PHP服务器启动成功!
        echo 🌐 访问地址: http://localhost:8000
        start "" "http://localhost:8000/index.php"
        goto :end
    )
    
    if exist "C:\xampp\php\php.exe" (
        echo 🔄 尝试XAMPP PHP服务器...
        start /min C:\xampp\php\php.exe -S localhost:8000
        timeout /t 2 >nul
        echo ✅ XAMPP PHP服务器启动成功!
        echo 🌐 访问地址: http://localhost:8000
        start "" "http://localhost:8000/index.php"
        goto :end
    )
    
    echo ❌ 无法启动服务器
    echo 💡 建议选择静态版本 (选项1)
    pause
    goto :end
)

if "%choice%"=="3" (
    echo.
    echo 🔧 启动管理后台...
    
    if exist "php\php.exe" (
        start /min php\php.exe -S localhost:8000
        timeout /t 2 >nul
        echo ✅ PHP服务器启动成功!
        echo 🌐 管理后台: http://localhost:8000/admin.php
        start "" "http://localhost:8000/admin.php"
        goto :end
    )
    
    if exist "C:\xampp\php\php.exe" (
        start /min C:\xampp\php\php.exe -S localhost:8000
        timeout /t 2 >nul
        echo ✅ XAMPP PHP服务器启动成功!
        echo 🌐 管理后台: http://localhost:8000/admin.php
        start "" "http://localhost:8000/admin.php"
        goto :end
    )
    
    echo ❌ 需要PHP环境才能运行管理后台
    echo 💡 请先安装PHP或选择静态版本
    pause
    goto :end
)

echo ❌ 无效选择
pause

:end
echo.
echo 🎉 项目启动完成!
echo 💡 如需停止服务器，请关闭命令行窗口
echo.
pause
