<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        input, button { padding: 8px; margin: 5px; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>简单登录测试</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="admin" required>
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="admin123" required>
        </div>
        <div class="form-group">
            <button type="submit">登录</button>
        </div>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<p>正在登录...</p>';
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('api/login.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h3>✅ 登录成功!</h3>
                            <p>消息: ${data.message}</p>
                            <p>用户: ${data.user.username} (${data.user.full_name})</p>
                            <p>角色: ${data.user.role_name}</p>
                            <p>重定向: ${data.redirect_url}</p>
                            <p><a href="${data.redirect_url}">点击跳转</a></p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ 登录失败</h3>
                            <p>错误: ${data.message}</p>
                            ${data.errors ? '<p>详细错误: ' + JSON.stringify(data.errors) + '</p>' : ''}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h3>❌ 请求失败</h3>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
