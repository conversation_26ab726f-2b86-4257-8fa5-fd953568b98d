@echo off
echo ========================================
echo 比特熊智慧系统 - SSH连接工具
echo ========================================
echo.

echo 服务器信息:
echo IP地址: *************
echo 用户名: root
echo 密码: ZbDX7%=]?H2(LAUz
echo.

echo 正在尝试多种SSH连接方式...
echo.

REM 方法1: 使用plink (推荐)
echo [方法1] 使用plink连接...
if exist "putty\plink.exe" (
    echo 找到plink，正在连接...
    putty\plink.exe -ssh -pw "ZbDX7%=]?H2(LAUz" root@*************
    if %errorlevel% equ 0 (
        echo 连接成功！
        goto :end
    ) else (
        echo plink连接失败，尝试其他方法...
    )
) else (
    echo plink不存在，跳过...
)

echo.

REM 方法2: 使用Windows内置SSH
echo [方法2] 使用Windows内置SSH...
if exist "C:\Windows\System32\OpenSSH\ssh.exe" (
    echo 找到Windows SSH，正在连接...
    echo 请输入密码: ZbDX7%=]?H2(LAUz
    C:\Windows\System32\OpenSSH\ssh.exe -o StrictHostKeyChecking=no root@*************
    goto :end
) else (
    echo Windows SSH不存在，跳过...
)

echo.

REM 方法3: 使用Git SSH
echo [方法3] 使用Git SSH...
if exist "C:\Program Files\Git\usr\bin\ssh.exe" (
    echo 找到Git SSH，正在连接...
    echo 请输入密码: ZbDX7%=]?H2(LAUz
    "C:\Program Files\Git\usr\bin\ssh.exe" -o StrictHostKeyChecking=no root@*************
    goto :end
) else (
    echo Git SSH不存在，跳过...
)

echo.

REM 方法4: 尝试系统PATH中的SSH
echo [方法4] 尝试系统SSH...
ssh --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 找到系统SSH，正在连接...
    echo 请输入密码: ZbDX7%=]?H2(LAUz
    ssh -o StrictHostKeyChecking=no root@*************
    goto :end
) else (
    echo 系统SSH不可用...
)

echo.
echo ========================================
echo 所有SSH方法都失败了
echo ========================================
echo.
echo 请手动安装SSH客户端：
echo.
echo 1. Windows OpenSSH:
echo    - 打开"设置" ^> "应用" ^> "可选功能"
echo    - 搜索并安装"OpenSSH客户端"
echo.
echo 2. Git for Windows:
echo    - 访问 https://git-scm.com/download/win
echo    - 下载并安装
echo.
echo 3. PuTTY:
echo    - 访问 https://www.putty.org/
echo    - 下载并安装
echo.

:end
echo.
echo 连接已断开
pause
