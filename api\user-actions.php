<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

header('Content-Type: application/json');

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';
$targetUserId = intval($input['user_id'] ?? 0);

if (!$targetUserId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '无效的用户ID']);
    exit;
}

if ($currentUser['id'] == $targetUserId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '不能对自己执行此操作']);
    exit;
}

try {
    $db = db();
    
    // 验证目标用户是否存在
    $targetUser = $db->fetchOne("SELECT id FROM users WHERE id = ?", [$targetUserId]);
    if (!$targetUser) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit;
    }
    
    switch ($action) {
        case 'follow':
            handleFollow($db, $currentUser['id'], $targetUserId);
            break;
            
        case 'unfollow':
            handleUnfollow($db, $currentUser['id'], $targetUserId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            exit;
    }
    
} catch (Exception $e) {
    error_log("用户操作错误: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '服务器错误']);
}

function handleFollow($db, $followerId, $followingId) {
    try {
        $db->query("START TRANSACTION");
        
        // 检查是否已经关注
        $existing = $db->fetchOne(
            "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
            [$followerId, $followingId]
        );
        
        if ($existing) {
            echo json_encode(['success' => false, 'message' => '已经关注了该用户']);
            return;
        }
        
        // 添加关注记录
        $db->execute(
            "INSERT INTO follows (follower_id, following_id, created_at) VALUES (?, ?, NOW())",
            [$followerId, $followingId]
        );
        
        // 更新用户统计
        $db->execute(
            "UPDATE user_profiles SET followers_count = followers_count + 1 WHERE user_id = ?",
            [$followingId]
        );
        
        $db->execute(
            "UPDATE user_profiles SET following_count = following_count + 1 WHERE user_id = ?",
            [$followerId]
        );
        
        // 创建通知（如果有通知系统）
        $db->execute(
            "INSERT INTO notifications (user_id, type, title, content, related_user_id, created_at) 
             VALUES (?, 'follow', '新的关注者', '有用户关注了你', ?, NOW())",
            [$followingId, $followerId]
        );
        
        $db->query("COMMIT");
        
        // 获取更新后的粉丝数
        $followersCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM follows WHERE following_id = ?",
            [$followingId]
        )['count'];
        
        echo json_encode([
            'success' => true,
            'message' => '关注成功',
            'data' => [
                'followers_count' => intval($followersCount)
            ]
        ]);
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}

function handleUnfollow($db, $followerId, $followingId) {
    try {
        $db->query("START TRANSACTION");
        
        // 检查是否已经关注
        $existing = $db->fetchOne(
            "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
            [$followerId, $followingId]
        );
        
        if (!$existing) {
            echo json_encode(['success' => false, 'message' => '还没有关注该用户']);
            return;
        }
        
        // 删除关注记录
        $db->execute(
            "DELETE FROM follows WHERE follower_id = ? AND following_id = ?",
            [$followerId, $followingId]
        );
        
        // 更新用户统计
        $db->execute(
            "UPDATE user_profiles SET followers_count = GREATEST(0, followers_count - 1) WHERE user_id = ?",
            [$followingId]
        );
        
        $db->execute(
            "UPDATE user_profiles SET following_count = GREATEST(0, following_count - 1) WHERE user_id = ?",
            [$followerId]
        );
        
        // 删除相关通知
        $db->execute(
            "UPDATE notifications SET status = 'deleted' 
             WHERE user_id = ? AND type = 'follow' AND related_user_id = ?",
            [$followingId, $followerId]
        );
        
        $db->query("COMMIT");
        
        // 获取更新后的粉丝数
        $followersCount = $db->fetchOne(
            "SELECT COUNT(*) as count FROM follows WHERE following_id = ?",
            [$followingId]
        )['count'];
        
        echo json_encode([
            'success' => true,
            'message' => '取消关注成功',
            'data' => [
                'followers_count' => intval($followersCount)
            ]
        ]);
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        throw $e;
    }
}
?>
