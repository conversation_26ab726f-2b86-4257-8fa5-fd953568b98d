<?php
/**
 * 数据库修复脚本
 * 用于修复服务器上的数据库连接问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库修复脚本</h1>";

try {
    // 检查环境
    echo "<h2>环境检测:</h2>";
    echo "<p>SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? '未设置') . "</p>";
    echo "<p>HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? '未设置') . "</p>";
    echo "<p>SERVER_ADDR: " . ($_SERVER['SERVER_ADDR'] ?? '未设置') . "</p>";
    
    // 检查是否为服务器环境
    $isServer = isset($_SERVER['SERVER_NAME']) &&
               (strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false ||
                (isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] === '*************') ||
                (isset($_SERVER['HTTP_HOST']) && strpos($_SERVER['HTTP_HOST'], 'bitbear.top') !== false));
    
    echo "<p>是否为服务器环境: " . ($isServer ? '是' : '否') . "</p>";
    
    if ($isServer) {
        echo "<h2>服务器环境 - 尝试MySQL连接:</h2>";
        
        // 尝试连接MySQL
        $host = 'localhost';
        $username = 'root';
        $password = '309290133q';
        $database = 'bitbear_website';
        
        try {
            $dsn = "mysql:host={$host};port=3306;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_TIMEOUT => 5
            ];
            
            $pdo = new PDO($dsn, $username, $password, $options);
            echo "<p>✓ MySQL连接成功</p>";
            
            // 检查数据库是否存在
            $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
            echo "<p>可用数据库: " . implode(', ', $databases) . "</p>";
            
            if (in_array($database, $databases)) {
                echo "<p>✓ 数据库 {$database} 存在</p>";
                
                // 连接到指定数据库
                $dsn = "mysql:host={$host};port=3306;dbname={$database};charset=utf8mb4";
                $pdo = new PDO($dsn, $username, $password, $options);
                
                // 检查表
                $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                echo "<p>数据库表: " . implode(', ', $tables) . "</p>";
                
                // 检查用户表
                if (in_array('users', $tables)) {
                    $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
                    echo "<p>用户表记录数: {$userCount}</p>";
                } else {
                    echo "<p>⚠ 用户表不存在，需要创建</p>";
                }
                
            } else {
                echo "<p>⚠ 数据库 {$database} 不存在，需要创建</p>";
                
                // 创建数据库
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                echo "<p>✓ 数据库 {$database} 创建成功</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ MySQL连接失败: " . $e->getMessage() . "</p>";
            echo "<p>将使用SQLite作为备用方案</p>";
            
            // 使用SQLite
            $sqlite_dir = __DIR__ . '/database';
            if (!is_dir($sqlite_dir)) {
                mkdir($sqlite_dir, 0755, true);
                echo "<p>✓ 创建SQLite目录: {$sqlite_dir}</p>";
            }
            
            $sqlite_path = $sqlite_dir . '/bitbear_system.sqlite';
            $dsn = "sqlite:" . $sqlite_path;
            
            $pdo = new PDO($dsn, null, null, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]);
            
            echo "<p>✓ SQLite连接成功: {$sqlite_path}</p>";
            
            // 检查表是否存在
            $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
            echo "<p>SQLite表: " . implode(', ', $tables) . "</p>";
        }
        
    } else {
        echo "<h2>本地环境 - 使用SQLite:</h2>";
        
        $sqlite_dir = __DIR__ . '/database';
        if (!is_dir($sqlite_dir)) {
            mkdir($sqlite_dir, 0755, true);
            echo "<p>✓ 创建SQLite目录: {$sqlite_dir}</p>";
        }
        
        $sqlite_path = $sqlite_dir . '/bitbear_system.sqlite';
        $dsn = "sqlite:" . $sqlite_path;
        
        $pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        
        echo "<p>✓ SQLite连接成功: {$sqlite_path}</p>";
        
        // 检查表是否存在
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>SQLite表: " . implode(', ', $tables) . "</p>";
    }
    
    echo "<h2>修复完成</h2>";
    echo "<p><a href='index.php'>返回首页</a></p>";
    echo "<p><a href='register.php'>测试注册</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 修复过程中发生错误: " . $e->getMessage() . "</p>";
    echo "<p>错误详情: " . $e->getTraceAsString() . "</p>";
}
?>
