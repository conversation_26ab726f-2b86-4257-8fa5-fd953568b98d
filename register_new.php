<?php
session_start();
require_once __DIR__ . '/../config/database.php';

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $db = DatabaseConfig::getInstance();
    $db->query("SELECT 1");
} catch (Exception $dbError) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

$username = trim($_POST['username'] ?? '');
$email = trim($_POST['email'] ?? '');
$nickname = trim($_POST['nickname'] ?? '');
$password = $_POST['password'] ?? '';
$confirmPassword = $_POST['confirmPassword'] ?? '';

$errors = [];

if (empty($username)) {
    $errors['username'] = 'Username is required';
} elseif (strlen($username) < 3) {
    $errors['username'] = 'Username must be at least 3 characters';
}

if (empty($email)) {
    $errors['email'] = 'Email is required';
} elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors['email'] = 'Invalid email format';
}

if (empty($nickname)) {
    $errors['nickname'] = 'Nickname is required';
}

if (empty($password)) {
    $errors['password'] = 'Password is required';
} elseif (strlen($password) < 6) {
    $errors['password'] = 'Password must be at least 6 characters';
}

if ($password !== $confirmPassword) {
    $errors['confirmPassword'] = 'Passwords do not match';
}

if (!empty($errors)) {
    echo json_encode([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $errors
    ]);
    exit;
}

try {
    $db->getConnection()->beginTransaction();
    
    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
            VALUES (?, ?, ?, ?, 3, 'active', CURRENT_TIMESTAMP)";
    
    $stmt = $db->getConnection()->prepare($sql);
    $result = $stmt->execute([$username, $email, $passwordHash, $nickname]);
    
    if (!$result) {
        throw new Exception('Failed to create user');
    }
    
    $userId = $db->getConnection()->lastInsertId();
    
    if (!$userId) {
        throw new Exception('Failed to get user ID');
    }
    
    $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                   VALUES (?, ?, 'assets/images/default-avatar.png', CURRENT_TIMESTAMP)";
    
    $profileStmt = $db->getConnection()->prepare($profileSql);
    $profileResult = $profileStmt->execute([$userId, $nickname]);
    
    if (!$profileResult) {
        throw new Exception('Failed to create user profile');
    }
    
    $db->getConnection()->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Registration successful',
        'user_id' => $userId
    ]);
    
} catch (Exception $e) {
    $db->getConnection()->rollback();
    
    echo json_encode([
        'success' => false,
        'message' => 'Registration failed: ' . $e->getMessage()
    ]);
}
?>
