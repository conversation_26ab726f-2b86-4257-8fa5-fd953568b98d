@echo off
echo ========================================
echo 比特熊智慧系统 - DNS快速修复工具
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要管理员权限！
    echo.
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo ✅ 管理员权限确认
echo.

REM 备份Hosts文件
set HOSTS_FILE=%SystemRoot%\System32\drivers\etc\hosts
set BACKUP_FILE=%HOSTS_FILE%.backup.%date:~0,4%%date:~5,2%%date:~8,2%-%time:~0,2%%time:~3,2%%time:~6,2%
set BACKUP_FILE=%BACKUP_FILE: =0%

echo 1. 备份当前Hosts文件...
copy "%HOSTS_FILE%" "%BACKUP_FILE%" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 备份成功: %BACKUP_FILE%
) else (
    echo ⚠️  备份失败，但继续执行
)
echo.

REM 添加Hosts条目
echo 2. 添加Hosts条目...
echo.
echo # BitBear System - DNS Fix %date% %time% >> "%HOSTS_FILE%"
echo ************* bitbear.panel >> "%HOSTS_FILE%"
echo ************* bitbear.local >> "%HOSTS_FILE%"
echo ************* panel.bitbear.com >> "%HOSTS_FILE%"
echo ************* tencentcloud.panel >> "%HOSTS_FILE%"
echo # End BitBear System >> "%HOSTS_FILE%"

if %errorlevel% equ 0 (
    echo ✅ Hosts条目添加成功
) else (
    echo ❌ Hosts条目添加失败
    pause
    exit /b 1
)
echo.

REM 刷新DNS缓存
echo 3. 刷新DNS缓存...
ipconfig /flushdns >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS缓存刷新成功
) else (
    echo ⚠️  DNS缓存刷新可能失败
)
echo.

REM 测试连接
echo 4. 测试连接...
echo.
echo 正在测试服务器连接...
ping -n 1 ************* >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 服务器连接正常
) else (
    echo ⚠️  服务器连接可能有问题
)
echo.

REM 显示结果
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 修复摘要：
echo • Hosts文件已备份到: %BACKUP_FILE%
echo • 已添加服务器IP解析条目
echo • DNS缓存已刷新
echo.
echo 现在可以尝试访问以下地址：
echo • http://*************:8888/tencentcloud
echo • http://bitbear.panel:8888/tencentcloud
echo • http://bitbear.local:8888/tencentcloud
echo.
echo 如果仍有问题，请：
echo 1. 重启浏览器
echo 2. 清除浏览器缓存 (Ctrl+Shift+Delete)
echo 3. 尝试隐私模式访问
echo.
echo 如需恢复原始设置，请用备份文件替换当前Hosts文件
echo.

REM 询问是否打开测试页面
set /p OPEN_TEST="是否打开浏览器测试？(Y/N): "
if /i "%OPEN_TEST%"=="Y" (
    echo.
    echo 正在打开测试页面...
    start http://*************:8888/tencentcloud
    timeout /t 2 >nul
    start http://bitbear.local:8888/tencentcloud
)

echo.
echo 按任意键退出...
pause >nul
