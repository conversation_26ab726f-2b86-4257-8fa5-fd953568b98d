#!/bin/bash

# 数据库部署脚本
# 使用方法: ./setup-database.sh

set -e

# 配置变量
SERVER_IP="*************"
SERVER_USER="root"
DB_NAME="wisdom_system"
DB_USER="wisdom_user"
DB_ROOT_PASS=""  # MySQL root密码，需要手动输入

echo "=== 数据库部署开始 ==="

# 1. 导出本地数据库
echo "导出本地数据库..."
read -p "请输入本地MySQL用户名: " LOCAL_DB_USER
read -s -p "请输入本地MySQL密码: " LOCAL_DB_PASS
echo

mysqldump -u${LOCAL_DB_USER} -p${LOCAL_DB_PASS} \
    --single-transaction \
    --routines \
    --triggers \
    wisdom_system > database_backup.sql

if [ $? -ne 0 ]; then
    echo "错误: 本地数据库导出失败"
    exit 1
fi

echo "本地数据库导出成功"

# 2. 上传数据库文件
echo "上传数据库文件到服务器..."
scp database_backup.sql ${SERVER_USER}@${SERVER_IP}:/tmp/

# 3. 在服务器上设置数据库
echo "在服务器上设置数据库..."
read -s -p "请输入服务器MySQL root密码: " DB_ROOT_PASS
echo

# 生成随机密码
DB_PASS=$(openssl rand -base64 32)

ssh ${SERVER_USER}@${SERVER_IP} << EOF
    set -e
    
    echo "创建数据库和用户..."
    mysql -uroot -p${DB_ROOT_PASS} << MYSQL_SCRIPT
        -- 创建数据库
        CREATE DATABASE IF NOT EXISTS ${DB_NAME} 
        CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        
        -- 创建用户
        CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' 
        IDENTIFIED BY '${DB_PASS}';
        
        -- 授权
        GRANT ALL PRIVILEGES ON ${DB_NAME}.* TO '${DB_USER}'@'localhost';
        FLUSH PRIVILEGES;
        
        -- 显示数据库
        SHOW DATABASES;
MYSQL_SCRIPT
    
    echo "导入数据库数据..."
    mysql -uroot -p${DB_ROOT_PASS} ${DB_NAME} < /tmp/database_backup.sql
    
    echo "清理临时文件..."
    rm /tmp/database_backup.sql
    
    echo "数据库设置完成"
    echo "数据库名: ${DB_NAME}"
    echo "用户名: ${DB_USER}"
    echo "密码: ${DB_PASS}"
EOF

# 4. 更新配置文件
echo "更新服务器配置文件..."
ssh ${SERVER_USER}@${SERVER_IP} << EOF
    cd "/www/wwwroot/比特熊组织网站项目(v0.0.1)"
    
    # 更新数据库配置
    sed -i "s/define('DB_USER', '.*');/define('DB_USER', '${DB_USER}');/" config/config.php
    sed -i "s/define('DB_PASS', '.*');/define('DB_PASS', '${DB_PASS}');/" config/config.php
    sed -i "s/define('DB_NAME', '.*');/define('DB_NAME', '${DB_NAME}');/" config/config.php
    
    echo "配置文件更新完成"
EOF

# 清理本地文件
rm -f database_backup.sql

echo "=== 数据库部署完成 ==="
echo "数据库信息:"
echo "  数据库名: ${DB_NAME}"
echo "  用户名: ${DB_USER}"
echo "  密码: ${DB_PASS}"
echo ""
echo "请保存这些信息！"
