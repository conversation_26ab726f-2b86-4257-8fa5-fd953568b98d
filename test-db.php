<?php
/**
 * 数据库连接测试页面
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库连接测试</h1>";

try {
    require_once 'config/database.php';
    
    echo "<p>✓ 数据库配置文件加载成功</p>";
    
    $db = DatabaseConfig::getInstance();
    echo "<p>✓ 数据库实例创建成功</p>";
    
    $connection = $db->getConnection();
    echo "<p>✓ 数据库连接获取成功</p>";
    
    // 测试查询
    $result = $db->fetchOne("SELECT 1 as test");
    if ($result && $result['test'] == 1) {
        echo "<p>✓ 数据库查询测试成功</p>";
    } else {
        echo "<p>✗ 数据库查询测试失败</p>";
    }
    
    // 检查表是否存在
    $tables = $db->fetchAll("SELECT name FROM sqlite_master WHERE type='table'");
    echo "<h2>数据库表列表:</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . htmlspecialchars($table['name']) . "</li>";
    }
    echo "</ul>";
    
    // 检查用户表
    $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users");
    echo "<p>用户表记录数: " . ($userCount ? $userCount['count'] : '0') . "</p>";
    
    // 检查用户角色表
    $roleCount = $db->fetchOne("SELECT COUNT(*) as count FROM user_roles");
    echo "<p>用户角色表记录数: " . ($roleCount ? $roleCount['count'] : '0') . "</p>";
    
    echo "<h2>环境信息:</h2>";
    echo "<p>SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? '未设置') . "</p>";
    echo "<p>HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? '未设置') . "</p>";
    echo "<p>SERVER_ADDR: " . ($_SERVER['SERVER_ADDR'] ?? '未设置') . "</p>";
    
    $dbConfig = new ReflectionClass('DatabaseConfig');
    $method = $dbConfig->getMethod('isServerEnvironment');
    $method->setAccessible(true);
    $instance = DatabaseConfig::getInstance();
    $isServer = $method->invoke($instance);
    echo "<p>是否为服务器环境: " . ($isServer ? '是' : '否') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>错误详情: " . htmlspecialchars($e->getTraceAsString()) . "</p>";
}
?>
