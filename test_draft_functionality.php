<?php
/**
 * 测试草稿功能
 */

require_once 'config/database.php';

try {
    $db = db();
    $pdo = $db->getConnection();
    
    echo "<h2>草稿功能测试</h2>";
    
    // 检查posts表结构
    echo "<h3>1. 检查posts表结构</h3>";
    $columns = $pdo->query("SHOW COLUMNS FROM posts")->fetchAll(PDO::FETCH_ASSOC);
    
    $hasStatus = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'status') {
            $hasStatus = true;
            echo "✅ status字段存在: " . $column['Type'] . "<br>";
            break;
        }
    }
    
    if (!$hasStatus) {
        echo "❌ status字段不存在，需要添加<br>";
        
        // 添加status字段
        $alterSql = "ALTER TABLE posts ADD COLUMN status ENUM('draft', 'pending', 'published', 'rejected') DEFAULT 'pending'";
        $pdo->exec($alterSql);
        echo "✅ 已添加status字段<br>";
    }
    
    // 检查现有帖子的状态
    echo "<h3>2. 检查现有帖子状态</h3>";
    $postsSql = "SELECT id, title, status, user_id FROM posts ORDER BY created_at DESC LIMIT 5";
    $posts = $pdo->query($postsSql)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($posts)) {
        echo "ℹ️ 暂无帖子数据<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>标题</th><th>状态</th><th>用户ID</th></tr>";
        foreach ($posts as $post) {
            echo "<tr>";
            echo "<td>" . $post['id'] . "</td>";
            echo "<td>" . htmlspecialchars(mb_substr($post['title'], 0, 30)) . "</td>";
            echo "<td>" . $post['status'] . "</td>";
            echo "<td>" . $post['user_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 检查用户数据
    echo "<h3>3. 检查用户数据</h3>";
    $usersSql = "SELECT id, username FROM users LIMIT 3";
    $users = $pdo->query($usersSql)->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($users)) {
        echo "❌ 没有用户数据<br>";
    } else {
        echo "✅ 找到用户数据:<br>";
        foreach ($users as $user) {
            echo "- 用户ID: {$user['id']}, 用户名: {$user['username']}<br>";
        }
    }
    
    // 创建测试草稿
    if (!empty($users)) {
        echo "<h3>4. 创建测试草稿</h3>";
        $testUser = $users[0];
        
        $insertSql = "INSERT INTO posts (user_id, title, content, status, created_at, updated_at) 
                     VALUES (?, ?, ?, 'draft', NOW(), NOW())";
        
        $pdo->prepare($insertSql)->execute([
            $testUser['id'],
            '测试草稿帖子 - ' . date('Y-m-d H:i:s'),
            '这是一个测试草稿的内容，用于验证草稿功能是否正常工作。'
        ]);
        
        $draftId = $pdo->lastInsertId();
        echo "✅ 创建测试草稿成功，ID: {$draftId}<br>";
        
        // 验证草稿数量API
        echo "<h3>5. 测试草稿数量API</h3>";
        $countSql = "SELECT COUNT(*) as count FROM posts WHERE user_id = ? AND (status = 'draft' OR status = 'rejected')";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute([$testUser['id']]);
        $draftCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "✅ 用户 {$testUser['username']} 的草稿数量: {$draftCount}<br>";
    }
    
    echo "<h3>6. 功能链接</h3>";
    echo "<a href='community.php' target='_blank'>📝 访问社区页面</a><br>";
    echo "<a href='community-drafts.php' target='_blank'>📄 访问草稿页面</a><br>";
    echo "<a href='community-post.php' target='_blank'>✏️ 发布新帖</a><br>";
    
    if (!empty($users) && isset($draftId)) {
        echo "<a href='community-post.php?edit={$draftId}' target='_blank'>✏️ 编辑测试草稿</a><br>";
    }
    
    echo "<br><h3>✅ 草稿功能测试完成！</h3>";
    echo "<p>所有功能组件已就绪，可以开始使用草稿功能。</p>";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "<br>";
    echo "错误详情: " . $e->getTraceAsString();
}
?>
