# Network Diagnostic Script for BitBear System
# Diagnose server connection issues

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "BitBear System - Network Diagnostic Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$serverPort = 8888
$targetPath = "/tencentcloud"

# 1. Basic network connectivity test
Write-Host "1. Testing basic network connectivity..." -ForegroundColor Yellow
try {
    $pingResult = Test-Connection -ComputerName $serverIP -Count 4 -ErrorAction Stop
    $successCount = ($pingResult | Where-Object { $_.StatusCode -eq 0 }).Count
    Write-Host "   Ping result: $successCount/4 successful" -ForegroundColor Green

    if ($successCount -eq 0) {
        Write-Host "   Network unreachable, check network connection" -ForegroundColor Red
        exit 1
    } elseif ($successCount -lt 4) {
        Write-Host "   Network unstable, packet loss detected" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Ping test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Port connectivity test
Write-Host ""
Write-Host "2. Testing port connectivity..." -ForegroundColor Yellow
try {
    $portTest = Test-NetConnection -ComputerName $serverIP -Port $serverPort -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host "   Port $serverPort is reachable" -ForegroundColor Green
    } else {
        Write-Host "   Port $serverPort is not reachable" -ForegroundColor Red
        Write-Host "   Check server firewall settings" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   Port test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. HTTP service test
Write-Host ""
Write-Host "3. Testing HTTP service..." -ForegroundColor Yellow

$urls = @(
    "http://$serverIP`:$serverPort/",
    "http://$serverIP`:$serverPort$targetPath",
    "http://$serverIP`:$serverPort/index.html",
    "http://$serverIP`:$serverPort/index.php"
)

foreach ($url in $urls) {
    Write-Host "   Testing: $url" -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "     Status code: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "     Content length: $($response.Content.Length) bytes" -ForegroundColor Green
    } catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "     Status code: $statusCode" -ForegroundColor Yellow
            if ($statusCode -eq 404) {
                Write-Host "     Path not found, but server is running" -ForegroundColor Blue
            }
        } else {
            Write-Host "     Connection failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 4. Local network configuration
Write-Host ""
Write-Host "4. Local network configuration..." -ForegroundColor Yellow
$adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -and $_.InterfaceDescription -notlike "*Loopback*" } | Select-Object -First 1
if ($adapter) {
    $ipConfig = Get-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 | Select-Object -First 1
    Write-Host "   Local IP: $($ipConfig.IPAddress)" -ForegroundColor Green
    Write-Host "   Gateway: $($(Get-NetRoute -DestinationPrefix "0.0.0.0/0").NextHop | Select-Object -First 1)" -ForegroundColor Green
}

# 5. Firewall status
Write-Host ""
Write-Host "5. Firewall status..." -ForegroundColor Yellow
$firewallProfiles = Get-NetFirewallProfile
foreach ($profile in $firewallProfiles) {
    $status = if ($profile.Enabled) { "Enabled" } else { "Disabled" }
    Write-Host "   $($profile.Name): $status" -ForegroundColor $(if ($profile.Enabled) { "Yellow" } else { "Green" })
}

# 6. Recommendations
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Diagnostic Recommendations" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Based on diagnostic results, check the following:" -ForegroundColor White
Write-Host ""
Write-Host "1. Server-side issues:" -ForegroundColor Yellow
Write-Host "   - Check if web server has /tencentcloud path configured" -ForegroundColor White
Write-Host "   - Verify server file and directory structure" -ForegroundColor White
Write-Host "   - Check server firewall rules" -ForegroundColor White
Write-Host ""
Write-Host "2. Client-side issues:" -ForegroundColor Yellow
Write-Host "   - Clear browser cache and cookies" -ForegroundColor White
Write-Host "   - Check for proxy settings affecting access" -ForegroundColor White
Write-Host "   - Try different browsers" -ForegroundColor White
Write-Host ""
Write-Host "3. Network issues:" -ForegroundColor Yellow
Write-Host "   - Check local firewall settings" -ForegroundColor White
Write-Host "   - Verify ISP port restrictions" -ForegroundColor White
Write-Host "   - Try VPN or mobile hotspot" -ForegroundColor White

Write-Host ""
Write-Host "Diagnostic completed!" -ForegroundColor Green
