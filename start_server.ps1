# 比特熊智慧系统 - PowerShell启动脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "比特熊智慧系统 - 服务器启动脚本" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查PHP环境
Write-Host "[1/5] 检查PHP环境..." -ForegroundColor Blue
try {
    $phpVersion = php --version 2>$null
    if ($phpVersion) {
        Write-Host "✅ PHP环境正常" -ForegroundColor Green
        Write-Host "   版本: $($phpVersion.Split("`n")[0])" -ForegroundColor Gray
    } else {
        throw "PHP未找到"
    }
} catch {
    Write-Host "❌ PHP未安装或未添加到PATH环境变量" -ForegroundColor Red
    Write-Host "请安装PHP并添加到系统PATH" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查工作目录
Write-Host ""
Write-Host "[2/5] 检查工作目录..." -ForegroundColor Blue
if (Test-Path "index.php") {
    Write-Host "✅ 项目文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 未找到index.php文件" -ForegroundColor Red
    Write-Host "请确保在正确的项目目录中运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查MySQL服务
Write-Host ""
Write-Host "[3/5] 检查MySQL服务..." -ForegroundColor Blue
$mysqlService = Get-Service -Name "*mysql*" -ErrorAction SilentlyContinue
if ($mysqlService) {
    if ($mysqlService.Status -eq "Running") {
        Write-Host "✅ MySQL服务正在运行" -ForegroundColor Green
    } else {
        Write-Host "⚠️  MySQL服务未运行，尝试启动..." -ForegroundColor Yellow
        try {
            Start-Service $mysqlService.Name -ErrorAction Stop
            Write-Host "✅ MySQL服务启动成功" -ForegroundColor Green
        } catch {
            Write-Host "❌ 无法启动MySQL服务" -ForegroundColor Red
        }
    }
} else {
    Write-Host "⚠️  未找到MySQL服务" -ForegroundColor Yellow
}

# 检查端口占用
Write-Host ""
Write-Host "[4/5] 检查端口占用..." -ForegroundColor Blue
$portCheck = Get-NetTCPConnection -LocalPort 8000 -ErrorAction SilentlyContinue
if ($portCheck) {
    Write-Host "⚠️  端口8000已被占用，尝试终止现有进程..." -ForegroundColor Yellow
    Get-Process -Name "php" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
    Write-Host "✅ 已清理端口" -ForegroundColor Green
} else {
    Write-Host "✅ 端口8000可用" -ForegroundColor Green
}

# 启动服务器
Write-Host ""
Write-Host "[5/5] 启动PHP服务器..." -ForegroundColor Blue
Write-Host "🚀 正在启动服务器 http://localhost:8000" -ForegroundColor Green
Write-Host ""

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "服务器启动成功！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📋 可用页面:" -ForegroundColor Yellow
Write-Host "  • 测试页面: http://localhost:8000/test.html" -ForegroundColor White
Write-Host "  • 快速启动: http://localhost:8000/quick_start.php" -ForegroundColor White
Write-Host "  • 轻量首页: http://localhost:8000/index_lite.php" -ForegroundColor White
Write-Host "  • 完整首页: http://localhost:8000/index.php" -ForegroundColor White
Write-Host "  • 管理后台: http://localhost:8000/admin-dashboard.php" -ForegroundColor White
Write-Host "  • 系统诊断: http://localhost:8000/diagnostic.php" -ForegroundColor White
Write-Host ""

Write-Host "💡 提示: 按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 启动浏览器
Start-Process "http://localhost:8000/test.html"

# 启动PHP服务器
php -S localhost:8000
