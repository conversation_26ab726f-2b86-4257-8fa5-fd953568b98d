<?php
/**
 * 云服务器注册问题诊断增强版
 * 专门用于深度诊断云服务器环境下的注册问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器注册问题诊断增强版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        .step.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .step.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .step.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .step.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 云服务器注册问题诊断增强版</h1>
            <p>深度检查云服务器环境下的注册功能问题</p>
        </div>
        
        <div class="content">
            <?php
            
            // 1. 环境信息检查
            echo "<div class='step info'>";
            echo "<h3>🌐 服务器环境信息</h3>";
            echo "<table>";
            echo "<tr><th>项目</th><th>值</th></tr>";
            echo "<tr><td>PHP版本</td><td>" . PHP_VERSION . "</td></tr>";
            echo "<tr><td>服务器软件</td><td>" . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</td></tr>";
            echo "<tr><td>服务器名称</td><td>" . ($_SERVER['SERVER_NAME'] ?? '未知') . "</td></tr>";
            echo "<tr><td>HTTP_HOST</td><td>" . ($_SERVER['HTTP_HOST'] ?? '未知') . "</td></tr>";
            echo "<tr><td>SERVER_ADDR</td><td>" . ($_SERVER['SERVER_ADDR'] ?? '未知') . "</td></tr>";
            echo "<tr><td>DOCUMENT_ROOT</td><td>" . ($_SERVER['DOCUMENT_ROOT'] ?? '未知') . "</td></tr>";
            echo "<tr><td>当前时间</td><td>" . date('Y-m-d H:i:s') . "</td></tr>";
            echo "<tr><td>请求URI</td><td>" . ($_SERVER['REQUEST_URI'] ?? '未知') . "</td></tr>";
            echo "<tr><td>用户代理</td><td>" . ($_SERVER['HTTP_USER_AGENT'] ?? '未知') . "</td></tr>";
            echo "</table>";
            echo "</div>";
            
            // 2. 环境检测逻辑验证
            echo "<div class='step info'>";
            echo "<h3>🔧 环境检测逻辑验证</h3>";
            
            $serverName = $_SERVER['SERVER_NAME'] ?? '';
            $serverAddr = $_SERVER['SERVER_ADDR'] ?? '';
            $httpHost = $_SERVER['HTTP_HOST'] ?? '';
            
            $isBitbearTop = strpos($serverName, 'bitbear.top') !== false;
            $isTargetIP = $serverAddr === '*************';
            $isHostBitbear = strpos($httpHost, 'bitbear.top') !== false;
            
            $isServerEnvironment = $isBitbearTop || $isTargetIP || $isHostBitbear;
            
            echo "<table>";
            echo "<tr><th>检测条件</th><th>结果</th><th>值</th></tr>";
            echo "<tr><td>SERVER_NAME包含bitbear.top</td><td>" . ($isBitbearTop ? '✅ 是' : '❌ 否') . "</td><td>{$serverName}</td></tr>";
            echo "<tr><td>SERVER_ADDR是*************</td><td>" . ($isTargetIP ? '✅ 是' : '❌ 否') . "</td><td>{$serverAddr}</td></tr>";
            echo "<tr><td>HTTP_HOST包含bitbear.top</td><td>" . ($isHostBitbear ? '✅ 是' : '❌ 否') . "</td><td>{$httpHost}</td></tr>";
            echo "<tr><td><strong>最终环境判断</strong></td><td><strong>" . ($isServerEnvironment ? '🌐 服务器环境' : '💻 本地环境') . "</strong></td><td>-</td></tr>";
            echo "</table>";
            echo "</div>";
            
            // 3. 数据库配置检查
            echo "<div class='step info'>";
            echo "<h3>🗄️ 数据库配置检查</h3>";
            
            try {
                require_once 'config/database.php';
                
                // 创建一个临时的DatabaseConfig实例来获取配置信息
                $reflection = new ReflectionClass('DatabaseConfig');
                $method = $reflection->getMethod('getEnvironmentConfig');
                $method->setAccessible(true);
                
                $tempInstance = $reflection->newInstanceWithoutConstructor();
                $envMethod = $reflection->getMethod('isServerEnvironment');
                $envMethod->setAccessible(true);
                $isServer = $envMethod->invoke($tempInstance);
                
                echo "<p>环境检测结果: " . ($isServer ? '服务器环境' : '本地环境') . "</p>";
                
                // 显示配置信息
                if ($isServer) {
                    $config = [
                        'host' => 'localhost',
                        'username' => 'root',
                        'password' => '309290133q',
                        'database' => 'bitbear_website',
                        'charset' => 'utf8mb4',
                        'ports' => [3306]
                    ];
                } else {
                    $config = [
                        'host' => 'localhost',
                        'username' => 'root',
                        'password' => '',
                        'database' => 'bitbear_system',
                        'charset' => 'utf8mb4',
                        'ports' => [3307, 3306]
                    ];
                }
                
                echo "<table>";
                echo "<tr><th>配置项</th><th>值</th></tr>";
                echo "<tr><td>主机</td><td>{$config['host']}</td></tr>";
                echo "<tr><td>用户名</td><td>{$config['username']}</td></tr>";
                echo "<tr><td>密码</td><td>" . (empty($config['password']) ? '(空)' : '***') . "</td></tr>";
                echo "<tr><td>数据库</td><td>{$config['database']}</td></tr>";
                echo "<tr><td>字符集</td><td>{$config['charset']}</td></tr>";
                echo "<tr><td>端口</td><td>" . implode(', ', $config['ports']) . "</td></tr>";
                echo "</table>";
                
            } catch (Exception $e) {
                echo "<p>❌ 获取数据库配置失败: " . $e->getMessage() . "</p>";
            }
            echo "</div>";

            // 4. 数据库连接测试
            echo "<div class='step info'>";
            echo "<h3>🔌 数据库连接测试</h3>";

            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();

                echo "<p>✅ 数据库连接成功</p>";

                // 测试查询
                $result = $db->query("SELECT 1 as test, NOW() as current_time")->fetch();
                echo "<p>✅ 数据库查询测试成功</p>";
                echo "<div class='code-block'>测试结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "</div>";

                // 检查数据库信息
                $version = $db->query("SELECT VERSION() as version")->fetch();
                echo "<p>数据库版本: " . $version['version'] . "</p>";

                // 检查字符集
                $charset = $db->query("SELECT @@character_set_database as charset, @@collation_database as collation")->fetch();
                echo "<p>数据库字符集: " . $charset['charset'] . " / " . $charset['collation'] . "</p>";

                // 5. 检查必要的表结构
                echo "</div>";
                echo "<div class='step info'>";
                echo "<h3>📋 数据库表结构检查</h3>";

                $requiredTables = ['user_roles', 'users', 'user_profiles'];
                $tableStatus = [];

                foreach ($requiredTables as $table) {
                    try {
                        $result = $db->query("SHOW TABLES LIKE '{$table}'")->fetch();
                        if ($result) {
                            echo "<p>✅ {$table} 表存在</p>";

                            // 检查表结构
                            $columns = $db->query("DESCRIBE {$table}")->fetchAll();
                            echo "<details><summary>查看 {$table} 表结构</summary>";
                            echo "<table>";
                            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
                            foreach ($columns as $column) {
                                echo "<tr>";
                                echo "<td>{$column['Field']}</td>";
                                echo "<td>{$column['Type']}</td>";
                                echo "<td>{$column['Null']}</td>";
                                echo "<td>{$column['Key']}</td>";
                                echo "<td>{$column['Default']}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                            echo "</details>";

                            $tableStatus[$table] = true;
                        } else {
                            echo "<p>❌ {$table} 表不存在</p>";
                            $tableStatus[$table] = false;
                        }
                    } catch (Exception $e) {
                        echo "<p>❌ 检查 {$table} 表时出错: " . $e->getMessage() . "</p>";
                        $tableStatus[$table] = false;
                    }
                }

                // 6. 检查用户角色数据
                if ($tableStatus['user_roles']) {
                    echo "</div>";
                    echo "<div class='step info'>";
                    echo "<h3>👥 用户角色数据检查</h3>";

                    try {
                        $roles = $db->query("SELECT * FROM user_roles")->fetchAll();
                        if (empty($roles)) {
                            echo "<p>❌ 用户角色表为空</p>";
                        } else {
                            echo "<p>✅ 找到 " . count($roles) . " 个用户角色</p>";
                            echo "<table>";
                            echo "<tr><th>ID</th><th>角色代码</th><th>角色名称</th><th>描述</th></tr>";
                            foreach ($roles as $role) {
                                echo "<tr>";
                                echo "<td>{$role['id']}</td>";
                                echo "<td>{$role['role_code']}</td>";
                                echo "<td>{$role['role_name']}</td>";
                                echo "<td>{$role['description']}</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        }
                    } catch (Exception $e) {
                        echo "<p>❌ 检查用户角色数据时出错: " . $e->getMessage() . "</p>";
                    }
                }

            } catch (Exception $e) {
                echo "<p>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
            }
            echo "</div>";

            ?>

            <div style="text-align: center; margin-top: 30px;">
                <a href="register.php" class="btn">测试注册页面</a>
                <a href="api/register.php" class="btn" onclick="testRegisterAPI(); return false;">测试注册API</a>
                <a href="debug_register_server.php" class="btn">原版诊断</a>
                <a href="index.php" class="btn">返回首页</a>
            </div>
        </div>
    </div>

    <script>
    function testRegisterAPI() {
        // 创建测试表单数据
        const formData = new FormData();
        formData.append('username', 'test_user_' + Date.now());
        formData.append('email', 'test_' + Date.now() + '@example.com');
        formData.append('nickname', '测试用户' + Date.now());
        formData.append('password', 'test123456');
        formData.append('confirmPassword', 'test123456');
        formData.append('agreeTerms', 'on');

        fetch('api/register.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            alert('API测试结果: ' + JSON.stringify(data, null, 2));
        })
        .catch(error => {
            alert('API测试错误: ' + error.message);
        });
    }
    </script>
</body>
</html>
