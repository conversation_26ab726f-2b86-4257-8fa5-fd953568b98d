<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学业目标管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #f59e0b;
            --secondary-color: #d97706;
            --accent-color: #fbbf24;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: var(--primary-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .controls-section {
            background: var(--primary-color);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .goal-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .tab-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-left: auto;
        }

        .btn-primary {
            background: rgba(245, 158, 11, 0.8);
            border: 1px solid rgba(245, 158, 11, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(245, 158, 11, 0.9);
            border-color: rgba(245, 158, 11, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: var(--primary-color);
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .goals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .goal-card {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .goal-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .goal-card.short-term::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .goal-card.long-term::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .goal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .goal-title {
            color: #333;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .goal-type {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .type-short {
            background: rgba(16, 185, 129, 0.8);
            color: #333;
        }

        .type-long {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
        }

        .goal-description {
            color: #495057;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .goal-progress {
            margin-bottom: 1rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-label {
            color: #495057;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .progress-percentage {
            color: #333;
            font-weight: 600;
        }

        .progress {
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar {
            background: var(--primary-color);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .goal-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .goal-meta-item {
            text-align: center;
        }

        .goal-meta-label {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.25rem;
        }

        .goal-meta-value {
            color: #333;
            font-weight: 600;
        }

        .goal-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .smart-template-section {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .smart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .smart-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .smart-letter {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .smart-word {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .smart-description {
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .goals-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .smart-grid {
                grid-template-columns: 1fr;
            }
            
            .goal-tabs {
                flex-wrap: wrap;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-bullseye me-2"></i>
                学业目标管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">学业目标管理</h1>
            <p class="page-description">短期/长期目标设定、进度跟踪、SMART目标模板</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="goal-tabs">
                    <button class="tab-btn active" onclick="switchTab('all')">全部目标</button>
                    <button class="tab-btn" onclick="switchTab('short')">短期目标</button>
                    <button class="tab-btn" onclick="switchTab('long')">长期目标</button>
                    <button class="tab-btn" onclick="switchTab('completed')">已完成</button>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addGoal()">
                        <i class="fas fa-plus me-1"></i>
                        添加目标
                    </button>
                    <button class="btn btn-primary" onclick="useSMARTTemplate()">
                        <i class="fas fa-lightbulb me-1"></i>
                        SMART模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 目标列表 -->
            <div class="goals-grid">
                <div class="goal-card short-term">
                    <div class="goal-header">
                        <div class="goal-title">期末考试目标</div>
                        <div class="goal-type type-short">短期目标</div>
                    </div>
                    <div class="goal-description">
                        在本学期期末考试中，高等数学达到90分以上，数据结构达到85分以上，英语听力达到80分以上。
                    </div>
                    <div class="goal-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">75%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="goal-meta">
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">开始时间</div>
                            <div class="goal-meta-value">2024-01-01</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">截止时间</div>
                            <div class="goal-meta-value">2024-01-30</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">剩余天数</div>
                            <div class="goal-meta-value">15天</div>
                        </div>
                    </div>
                    <div class="goal-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">编辑</button>
                    </div>
                </div>

                <div class="goal-card long-term">
                    <div class="goal-header">
                        <div class="goal-title">学年GPA目标</div>
                        <div class="goal-type type-long">长期目标</div>
                    </div>
                    <div class="goal-description">
                        在2023-2024学年结束时，累计GPA达到3.8以上，获得优秀学生奖学金。
                    </div>
                    <div class="goal-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">60%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                    </div>
                    <div class="goal-meta">
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">开始时间</div>
                            <div class="goal-meta-value">2023-09-01</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">截止时间</div>
                            <div class="goal-meta-value">2024-07-01</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">当前GPA</div>
                            <div class="goal-meta-value">3.6</div>
                        </div>
                    </div>
                    <div class="goal-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">编辑</button>
                    </div>
                </div>

                <div class="goal-card short-term">
                    <div class="goal-header">
                        <div class="goal-title">编程技能提升</div>
                        <div class="goal-type type-short">短期目标</div>
                    </div>
                    <div class="goal-description">
                        在本月内完成3个编程项目，掌握新的算法和数据结构，提升编程能力。
                    </div>
                    <div class="goal-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">33%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 33%"></div>
                        </div>
                    </div>
                    <div class="goal-meta">
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">已完成</div>
                            <div class="goal-meta-value">1/3</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">截止时间</div>
                            <div class="goal-meta-value">2024-01-31</div>
                        </div>
                        <div class="goal-meta-item">
                            <div class="goal-meta-label">剩余天数</div>
                            <div class="goal-meta-value">16天</div>
                        </div>
                    </div>
                    <div class="goal-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">编辑</button>
                    </div>
                </div>
            </div>

            <!-- SMART目标模板 -->
            <div class="smart-template-section">
                <h2 class="section-title">
                    <i class="fas fa-lightbulb"></i>
                    SMART目标设定原则
                </h2>
                <div class="smart-grid">
                    <div class="smart-item">
                        <div class="smart-letter">S</div>
                        <div class="smart-word">Specific</div>
                        <div class="smart-description">具体明确的目标</div>
                    </div>
                    <div class="smart-item">
                        <div class="smart-letter">M</div>
                        <div class="smart-word">Measurable</div>
                        <div class="smart-description">可衡量的指标</div>
                    </div>
                    <div class="smart-item">
                        <div class="smart-letter">A</div>
                        <div class="smart-word">Achievable</div>
                        <div class="smart-description">可实现的目标</div>
                    </div>
                    <div class="smart-item">
                        <div class="smart-letter">R</div>
                        <div class="smart-word">Relevant</div>
                        <div class="smart-description">相关性强的目标</div>
                    </div>
                    <div class="smart-item">
                        <div class="smart-letter">T</div>
                        <div class="smart-word">Time-bound</div>
                        <div class="smart-description">有时间限制的目标</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            // 这里可以添加筛选逻辑
        }

        function addGoal() {
            alert('添加目标功能正在开发中...');
        }

        function useSMARTTemplate() {
            alert('SMART模板功能正在开发中...');
        }
    </script>
</body>
</html>
