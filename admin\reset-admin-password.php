<?php
/**
 * 管理员密码重置工具
 * 仅在忘记密码时使用
 */

// 安全检查：只允许在本地环境运行
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    die('此工具只能在本地环境运行');
}

require_once __DIR__ . '/../config/database.php';

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = DatabaseConfig::getInstance();
        
        // 生成新的密码哈希
        $newPassword = 'admin123';
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // 更新管理员密码
        $result = $db->execute(
            "UPDATE users SET password_hash = ? WHERE username = 'admin'",
            [$passwordHash]
        );
        
        if ($result) {
            $success = true;
            $message = "管理员密码已重置为: admin123";
        } else {
            $message = "密码重置失败，请检查数据库连接";
        }
        
    } catch (Exception $e) {
        $message = "错误: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置管理员密码</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .reset-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .reset-title {
            color: #1e293b;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .reset-subtitle {
            color: #64748b;
            margin-bottom: 30px;
        }
        
        .message {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .reset-form {
            margin-bottom: 20px;
        }
        
        .reset-btn {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
        }
        
        .login-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .login-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .warning {
            background: #fef3c7;
            color: #92400e;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #fde68a;
        }
        
        .info {
            background: #dbeafe;
            color: #1e40af;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border: 1px solid #bfdbfe;
            text-align: left;
        }
        
        .info h4 {
            margin-bottom: 10px;
            color: #1e40af;
        }
        
        .info ul {
            margin-left: 20px;
        }
        
        .info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <h1 class="reset-title">🔐 重置管理员密码</h1>
        <p class="reset-subtitle">紧急密码重置工具</p>
        
        <div class="warning">
            ⚠️ <strong>警告：</strong>此工具仅在忘记管理员密码时使用，重置后请立即更改密码！
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $success ? 'success' : 'error'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!$success): ?>
            <form method="POST" class="reset-form">
                <button type="submit" class="reset-btn" onclick="return confirm('确定要重置管理员密码吗？')">
                    🔄 重置密码为 admin123
                </button>
            </form>
        <?php else: ?>
            <a href="login.php" class="login-link">前往登录页面</a>
        <?php endif; ?>
        
        <div class="info">
            <h4>📋 使用说明：</h4>
            <ul>
                <li>此工具会将管理员密码重置为：<strong>admin123</strong></li>
                <li>重置后请立即登录并修改密码</li>
                <li>此工具只能在本地环境运行</li>
                <li>生产环境请删除此文件</li>
            </ul>
        </div>
    </div>
</body>
</html>
