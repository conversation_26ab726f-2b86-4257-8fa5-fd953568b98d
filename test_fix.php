<?php
// 测试修复后的函数定义
echo "测试开始...\n";

// 引入文件
require_once 'includes/auth-init.php';
echo "✓ auth-init.php 加载成功\n";

require_once 'includes/homepage-data.php';
echo "✓ homepage-data.php 加载成功\n";

// 测试函数
if (function_exists('escapeHtml')) {
    echo "✓ escapeHtml 函数存在\n";
    echo "测试输出: " . escapeHtml('<script>alert("test")</script>') . "\n";
} else {
    echo "✗ escapeHtml 函数不存在\n";
}

if (function_exists('escapeUrl')) {
    echo "✓ escapeUrl 函数存在\n";
    echo "测试输出: " . escapeUrl('http://example.com?test=<script>') . "\n";
} else {
    echo "✗ escapeUrl 函数不存在\n";
}

echo "测试完成！\n";
?>
