@echo off
chcp 65001 >nul
echo ========================================
echo 快速 PHP 设置 - 比特熊项目
echo ========================================
echo.

:: 检查当前目录下是否有PHP
if exist "php\php.exe" (
    echo ✅ 发现本地 PHP 安装！
    php\php.exe --version
    echo.
    goto :start_server
)

:: 检查系统PHP
php --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 发现系统 PHP 安装！
    php --version
    echo.
    goto :start_server_system
)

echo 📥 正在下载便携版 PHP...
echo 这是一个轻量级的PHP版本，无需安装。
echo.

:: 创建php目录
if not exist "php" mkdir php

:: 下载便携版PHP
echo 正在下载 PHP 8.3 便携版...
powershell -Command "& {
    $ProgressPreference = 'SilentlyContinue'
    $url = 'https://windows.php.net/downloads/releases/php-8.3.14-nts-Win32-vs16-x64.zip'
    $output = 'php.zip'
    Write-Host '下载中...'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host '✅ 下载完成！'
    } catch {
        Write-Host '❌ 下载失败，尝试备用链接...'
        $url2 = 'https://windows.php.net/downloads/releases/php-8.2.26-nts-Win32-vs16-x64.zip'
        try {
            Invoke-WebRequest -Uri $url2 -OutFile $output -UseBasicParsing
            Write-Host '✅ 备用下载完成！'
        } catch {
            Write-Host '❌ 所有下载链接都失败了'
            exit 1
        }
    }
}"

if not exist "php.zip" (
    echo ❌ 下载失败！
    echo.
    echo 🔧 手动解决方案：
    echo 1. 访问 https://windows.php.net/download/
    echo 2. 下载 "Non Thread Safe" 版本的 PHP
    echo 3. 解压到当前目录的 "php" 文件夹中
    echo 4. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

:: 解压PHP
echo 📦 正在解压 PHP...
powershell -Command "& {
    try {
        Expand-Archive -Path 'php.zip' -DestinationPath 'php' -Force
        Write-Host '✅ 解压完成！'
    } catch {
        Write-Host '❌ 解压失败！'
        exit 1
    }
}"

:: 清理
del php.zip

:: 创建基本配置
echo 🔧 配置 PHP...
if exist "php\php.ini-development" (
    copy "php\php.ini-development" "php\php.ini" >nul
)

:: 验证安装
echo.
echo 🔍 验证 PHP 安装...
php\php.exe --version
if %errorlevel% == 0 (
    echo ✅ PHP 设置成功！
) else (
    echo ❌ PHP 设置失败！
    pause
    exit /b 1
)

:start_server
echo.
echo ========================================
echo 🚀 启动 PHP 开发服务器
echo ========================================
echo.
echo 🌐 服务器地址: http://localhost:8000
echo.
echo 📄 可用页面:
echo   • 主页: http://localhost:8000/index.php
echo   • 管理后台: http://localhost:8000/admin.php
echo   • 静态版本: http://localhost:8000/index.html
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo.

:: 启动服务器并自动打开浏览器
start http://localhost:8000/index.php
php\php.exe -S localhost:8000
goto :end

:start_server_system
echo.
echo ========================================
echo 🚀 启动 PHP 开发服务器 (系统版本)
echo ========================================
echo.
echo 🌐 服务器地址: http://localhost:8000
echo.
echo 📄 可用页面:
echo   • 主页: http://localhost:8000/index.php
echo   • 管理后台: http://localhost:8000/admin.php
echo   • 静态版本: http://localhost:8000/index.html
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo.

:: 启动服务器并自动打开浏览器
start http://localhost:8000/index.php
php -S localhost:8000

:end
pause
