<?php
/**
 * 本地数据导出脚本
 * 用于将本地数据库数据导出，准备迁移到服务器
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 引入数据库配置
require_once __DIR__ . '/config/database.php';

echo "<h1>本地数据导出工具</h1>";
echo "<hr>";

try {
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    echo "<h2>1. 检查本地数据库连接</h2>";
    echo "<p style='color: green;'>✓ 本地数据库连接成功</p>";
    
    // 获取所有表
    echo "<h2>2. 获取数据库表列表</h2>";
    $tables = $db->fetchAll("SHOW TABLES");
    
    if (empty($tables)) {
        echo "<p style='color: red;'>✗ 数据库中没有表</p>";
        exit;
    }
    
    echo "<p>找到 " . count($tables) . " 个表：</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "<li>{$tableName}</li>";
    }
    echo "</ul>";
    
    // 创建导出目录
    $exportDir = __DIR__ . '/database_export';
    if (!is_dir($exportDir)) {
        mkdir($exportDir, 0755, true);
        echo "<p>创建导出目录: {$exportDir}</p>";
    }
    
    echo "<h2>3. 导出数据</h2>";
    
    // 导出每个表的数据
    $exportedTables = [];
    $totalRecords = 0;
    
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        
        try {
            // 获取表结构
            $createTable = $db->fetchOne("SHOW CREATE TABLE `{$tableName}`");
            $createTableSQL = $createTable['Create Table'];
            
            // 获取表数据
            $data = $db->fetchAll("SELECT * FROM `{$tableName}`");
            $recordCount = count($data);
            $totalRecords += $recordCount;
            
            // 生成INSERT语句
            $insertStatements = [];
            if (!empty($data)) {
                $columns = array_keys($data[0]);
                $columnsList = '`' . implode('`, `', $columns) . '`';
                
                foreach ($data as $row) {
                    $values = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $values[] = 'NULL';
                        } else {
                            $values[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $insertStatements[] = "INSERT INTO `{$tableName}` ({$columnsList}) VALUES (" . implode(', ', $values) . ");";
                }
            }
            
            // 保存到文件
            $filename = $exportDir . "/{$tableName}.sql";
            $content = "-- 表结构: {$tableName}\n";
            $content .= "DROP TABLE IF EXISTS `{$tableName}`;\n";
            $content .= $createTableSQL . ";\n\n";
            $content .= "-- 表数据: {$tableName} ({$recordCount} 条记录)\n";
            $content .= implode("\n", $insertStatements) . "\n\n";
            
            file_put_contents($filename, $content);
            
            $exportedTables[] = [
                'table' => $tableName,
                'records' => $recordCount,
                'file' => $filename
            ];
            
            echo "<p>✓ {$tableName}: {$recordCount} 条记录</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ 导出表 {$tableName} 失败: " . $e->getMessage() . "</p>";
        }
    }
    
    // 创建完整的导出文件
    echo "<h2>4. 创建完整导出文件</h2>";
    $fullExportFile = $exportDir . '/bitbear_full_export.sql';
    $fullContent = "-- 比特熊智慧系统数据库完整导出\n";
    $fullContent .= "-- 导出时间: " . date('Y-m-d H:i:s') . "\n";
    $fullContent .= "-- 总表数: " . count($exportedTables) . "\n";
    $fullContent .= "-- 总记录数: " . $totalRecords . "\n\n";
    $fullContent .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    foreach ($exportedTables as $tableInfo) {
        $fullContent .= file_get_contents($tableInfo['file']);
    }
    
    $fullContent .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    file_put_contents($fullExportFile, $fullContent);
    
    echo "<p style='color: green;'>✓ 完整导出文件已创建: {$fullExportFile}</p>";
    
    // 创建服务器导入脚本
    echo "<h2>5. 创建服务器导入脚本</h2>";
    $importScript = $exportDir . '/import_to_server.sql';
    $importContent = "-- 服务器导入脚本\n";
    $importContent .= "-- 使用方法: mysql -u root -p309290133q bitbear_website < import_to_server.sql\n\n";
    $importContent .= "USE bitbear_website;\n\n";
    $importContent .= file_get_contents($fullExportFile);
    
    file_put_contents($importScript, $importContent);
    echo "<p style='color: green;'>✓ 服务器导入脚本已创建: {$importScript}</p>";
    
    // 创建导入说明
    $readmeFile = $exportDir . '/README.md';
    $readmeContent = "# 数据库导出文件说明\n\n";
    $readmeContent .= "## 导出信息\n";
    $readmeContent .= "- 导出时间: " . date('Y-m-d H:i:s') . "\n";
    $readmeContent .= "- 总表数: " . count($exportedTables) . "\n";
    $readmeContent .= "- 总记录数: " . $totalRecords . "\n\n";
    $readmeContent .= "## 文件说明\n";
    $readmeContent .= "- `bitbear_full_export.sql`: 完整的数据库导出文件\n";
    $readmeContent .= "- `import_to_server.sql`: 服务器导入脚本\n";
    $readmeContent .= "- 各个表的单独导出文件\n\n";
    $readmeContent .= "## 服务器导入步骤\n\n";
    $readmeContent .= "1. 将 `import_to_server.sql` 文件上传到服务器\n";
    $readmeContent .= "2. 在服务器上执行以下命令：\n";
    $readmeContent .= "```bash\n";
    $readmeContent .= "mysql -u root -p309290133q bitbear_website < import_to_server.sql\n";
    $readmeContent .= "```\n\n";
    $readmeContent .= "## 表详情\n\n";
    
    foreach ($exportedTables as $tableInfo) {
        $readmeContent .= "- **{$tableInfo['table']}**: {$tableInfo['records']} 条记录\n";
    }
    
    file_put_contents($readmeFile, $readmeContent);
    echo "<p style='color: green;'>✓ 说明文件已创建: {$readmeFile}</p>";
    
    echo "<hr>";
    echo "<h2>导出完成</h2>";
    echo "<p><strong>导出统计：</strong></p>";
    echo "<ul>";
    echo "<li>导出表数: " . count($exportedTables) . "</li>";
    echo "<li>总记录数: " . $totalRecords . "</li>";
    echo "<li>导出目录: {$exportDir}</li>";
    echo "</ul>";
    
    echo "<p><strong>下一步操作：</strong></p>";
    echo "<ol>";
    echo "<li>将 <code>{$importScript}</code> 文件上传到服务器</li>";
    echo "<li>在服务器上执行导入命令</li>";
    echo "<li>验证数据导入是否成功</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 导出失败: " . $e->getMessage() . "</p>";
    echo "<p>错误详情: " . $e->getTraceAsString() . "</p>";
}
?>
