<?php
/**
 * 修复用户ID获取问题
 * 专门解决"Failed to get user ID"错误
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修复用户ID获取问题</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #ee5a24; }
    .btn.success { background: #28a745; }
    .btn.success:hover { background: #1e7e34; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔧 修复用户ID获取问题</h1>";
echo "<p>专门解决 'Failed to get user ID' 错误</p>";
echo "</div>";

// 1. 问题分析
echo "<div class='error'>";
echo "<h3>❌ 问题分析</h3>";
echo "<p>根据错误信息 'Registration failed: Failed to get user ID'，问题出现在用户创建后无法获取新用户的ID。</p>";
echo "<p>这通常是由以下原因造成的：</p>";
echo "<ul>";
echo "<li>数据库连接在插入后断开</li>";
echo "<li>lastInsertId() 方法失效</li>";
echo "<li>事务处理问题</li>";
echo "<li>数据库引擎不支持AUTO_INCREMENT</li>";
echo "<li>表结构问题</li>";
echo "</ul>";
echo "</div>";

// 2. 数据库连接测试
echo "<div class='info'>";
echo "<h3>🔌 数据库连接测试</h3>";

try {
    // 强制使用服务器配置
    $serverConfig = [
        'host' => 'localhost',
        'username' => 'root',
        'password' => '309290133q',
        'database' => 'bitbear_website',
        'charset' => 'utf8mb4',
        'port' => 3306
    ];
    
    $dsn = "mysql:host={$serverConfig['host']};port={$serverConfig['port']};dbname={$serverConfig['database']};charset={$serverConfig['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ];
    
    $pdo = new PDO($dsn, $serverConfig['username'], $serverConfig['password'], $options);
    echo "<div class='success'>✅ 数据库连接成功</div>";
    
    // 检查数据库引擎
    $engineInfo = $pdo->query("SELECT ENGINE FROM information_schema.TABLES WHERE TABLE_SCHEMA = '{$serverConfig['database']}' AND TABLE_NAME = 'users'")->fetch();
    if ($engineInfo) {
        echo "<p>users表引擎: {$engineInfo['ENGINE']}</p>";
        if ($engineInfo['ENGINE'] !== 'InnoDB') {
            echo "<div class='warning'>⚠️ 建议使用InnoDB引擎以获得更好的事务支持</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
    echo "<p>请检查数据库配置和服务状态</p>";
    echo "</div></div></body></html>";
    exit;
}
echo "</div>";

// 3. 检查users表结构
echo "<div class='info'>";
echo "<h3>📋 检查users表结构</h3>";

try {
    $tableInfo = $pdo->query("DESCRIBE users")->fetchAll();
    echo "<table>";
    echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    $hasAutoIncrement = false;
    foreach ($tableInfo as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'id' && strpos($column['Extra'], 'auto_increment') !== false) {
            $hasAutoIncrement = true;
        }
    }
    echo "</table>";
    
    if ($hasAutoIncrement) {
        echo "<div class='success'>✅ id字段有AUTO_INCREMENT属性</div>";
    } else {
        echo "<div class='error'>❌ id字段缺少AUTO_INCREMENT属性</div>";
        
        // 修复AUTO_INCREMENT
        echo "<p>正在修复AUTO_INCREMENT属性...</p>";
        try {
            $pdo->exec("ALTER TABLE users MODIFY id INT AUTO_INCREMENT PRIMARY KEY");
            echo "<div class='success'>✅ AUTO_INCREMENT属性修复成功</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ 修复AUTO_INCREMENT失败: " . $e->getMessage() . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 检查表结构失败: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 4. 测试lastInsertId功能
echo "<div class='info'>";
echo "<h3>🧪 测试lastInsertId功能</h3>";

try {
    // 开始事务
    $pdo->beginTransaction();
    
    $testData = [
        'username' => 'idtest_' . time(),
        'email' => 'idtest_' . time() . '@example.com',
        'nickname' => 'ID测试用户',
        'password' => 'test123456'
    ];
    
    echo "<p>测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "</p>";
    
    // 获取普通用户角色ID
    $userRole = $pdo->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
    $roleId = $userRole ? $userRole['id'] : 3;
    
    // 创建用户记录
    $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        $testData['username'],
        $testData['email'],
        $passwordHash,
        $testData['nickname'],
        $roleId
    ]);
    
    if ($result) {
        echo "<div class='success'>✅ 用户插入成功</div>";
        
        // 测试多种获取ID的方法
        echo "<h4>测试不同的ID获取方法:</h4>";
        
        // 方法1: PDO::lastInsertId()
        $userId1 = $pdo->lastInsertId();
        echo "<p>方法1 - PDO::lastInsertId(): " . ($userId1 ? $userId1 : '失败') . "</p>";
        
        // 方法2: MySQL LAST_INSERT_ID()
        $result2 = $pdo->query("SELECT LAST_INSERT_ID() as last_id")->fetch();
        $userId2 = $result2 ? $result2['last_id'] : null;
        echo "<p>方法2 - LAST_INSERT_ID(): " . ($userId2 ? $userId2 : '失败') . "</p>";
        
        // 方法3: 查询最新插入的记录
        $result3 = $pdo->prepare("SELECT id FROM users WHERE username = ? ORDER BY id DESC LIMIT 1");
        $result3->execute([$testData['username']]);
        $user3 = $result3->fetch();
        $userId3 = $user3 ? $user3['id'] : null;
        echo "<p>方法3 - 查询用户名: " . ($userId3 ? $userId3 : '失败') . "</p>";
        
        // 方法4: 查询最大ID
        $result4 = $pdo->query("SELECT MAX(id) as max_id FROM users")->fetch();
        $userId4 = $result4 ? $result4['max_id'] : null;
        echo "<p>方法4 - MAX(id): " . ($userId4 ? $userId4 : '失败') . "</p>";
        
        // 选择最佳方法
        $finalUserId = $userId1 ?: $userId2 ?: $userId3 ?: $userId4;
        
        if ($finalUserId) {
            echo "<div class='success'>✅ 成功获取用户ID: {$finalUserId}</div>";
            
            // 测试创建用户资料
            $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                           VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
            
            $profileStmt = $pdo->prepare($profileSql);
            $profileResult = $profileStmt->execute([
                $finalUserId,
                $testData['nickname'],
                'assets/images/default-avatar.png'
            ]);
            
            if ($profileResult) {
                echo "<div class='success'>✅ 用户资料创建成功</div>";
                echo "<div class='success'><strong>🎉 完整注册流程测试成功！</strong></div>";
            } else {
                echo "<div class='error'>❌ 用户资料创建失败</div>";
            }
            
            // 清理测试数据
            $pdo->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$finalUserId]);
            $pdo->prepare("DELETE FROM users WHERE id = ?")->execute([$finalUserId]);
            echo "<div class='success'>✅ 测试数据清理完成</div>";
            
        } else {
            echo "<div class='error'>❌ 所有方法都无法获取用户ID</div>";
        }
        
    } else {
        echo "<div class='error'>❌ 用户插入失败</div>";
    }
    
    // 回滚事务
    $pdo->rollback();
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "<div class='error'>❌ 测试过程出错: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
echo "</div>";

// 5. 修复建议
echo "<div class='info'>";
echo "<h3>💡 修复建议</h3>";
echo "<p>基于测试结果，建议采用以下修复方案：</p>";
echo "<ol>";
echo "<li><strong>修改注册API</strong>：使用多种方法获取用户ID，增加容错性</li>";
echo "<li><strong>检查事务处理</strong>：确保在同一个连接中获取lastInsertId</li>";
echo "<li><strong>添加错误日志</strong>：记录详细的错误信息便于调试</li>";
echo "<li><strong>优化表结构</strong>：确保AUTO_INCREMENT属性正确设置</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' class='btn success'>测试注册功能</a>";
echo "<a href='api/register.php' class='btn'>查看注册API</a>";
echo "<a href='综合修复验证工具.php' class='btn'>综合修复</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
