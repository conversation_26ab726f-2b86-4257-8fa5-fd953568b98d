<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作业管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #f97316;
            --secondary-color: #ea580c;
            --accent-color: #fb923c;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: var(--primary-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .controls-section {
            background: var(--primary-color);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-controls label {
            color: #333;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .form-select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
        }

        .form-select:focus {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: #333;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
        }

        .form-select option {
            background: var(--primary-color);
            color: #333;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-left: auto;
        }

        .btn-primary {
            background: rgba(249, 115, 22, 0.8);
            border: 1px solid rgba(249, 115, 22, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(249, 115, 22, 0.9);
            border-color: rgba(249, 115, 22, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: var(--primary-color);
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .homework-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .homework-card {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .homework-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .homework-card.urgent::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .homework-card.normal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .homework-card.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .homework-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .homework-info {
            flex: 1;
        }

        .homework-title {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .homework-course {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .homework-type {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        .homework-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-pending {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
        }

        .status-urgent {
            background: rgba(239, 68, 68, 0.8);
            color: #333;
        }

        .status-completed {
            background: rgba(16, 185, 129, 0.8);
            color: #333;
        }

        .status-overdue {
            background: rgba(220, 38, 38, 0.8);
            color: #333;
        }

        .homework-description {
            color: #495057;
            margin-bottom: 1rem;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .homework-meta {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .homework-meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .homework-meta-icon {
            color: rgba(255, 255, 255, 0.7);
            width: 16px;
        }

        .homework-meta-label {
            color: #666;
        }

        .homework-meta-value {
            color: #333;
            font-weight: 600;
            margin-left: auto;
        }

        .deadline-warning {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .deadline-warning-icon {
            color: #ef4444;
            font-size: 1.1rem;
        }

        .deadline-warning-text {
            color: #495057;
            font-size: 0.9rem;
        }

        .homework-progress {
            margin-bottom: 1rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-label {
            color: #495057;
            font-size: 0.9rem;
        }

        .progress-percentage {
            color: #333;
            font-weight: 600;
        }

        .progress {
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            background: var(--primary-color);
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .homework-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .homework-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .filter-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .action-buttons {
                margin-left: 0;
                justify-content: center;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
            
            .homework-meta {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-tasks me-2"></i>
                作业管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">作业管理</h1>
            <p class="page-description">作业提交跟踪、截止日期提醒、分类管理、完成状态</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="filter-controls">
                <label>状态筛选：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部作业</option>
                    <option>待完成</option>
                    <option>进行中</option>
                    <option>已完成</option>
                    <option>已逾期</option>
                </select>
                
                <label>课程筛选：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部课程</option>
                    <option>高等数学</option>
                    <option>数据结构</option>
                    <option>英语听力</option>
                    <option>计算机网络</option>
                </select>
                
                <label>优先级：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部优先级</option>
                    <option>紧急</option>
                    <option>重要</option>
                    <option>普通</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addHomework()">
                        <i class="fas fa-plus me-1"></i>
                        添加作业
                    </button>
                    <button class="btn btn-primary" onclick="exportHomework()">
                        <i class="fas fa-download me-1"></i>
                        导出列表
                    </button>
                </div>
            </div>
        </div>

        <!-- 作业列表 -->
        <div class="dashboard-container">
            <div class="homework-grid">
                <div class="homework-card urgent">
                    <div class="homework-header">
                        <div class="homework-info">
                            <div class="homework-title">数据结构课程设计</div>
                            <div class="homework-course">数据结构</div>
                            <div class="homework-type">课程设计 · 编程作业</div>
                        </div>
                        <div class="homework-status status-urgent">紧急</div>
                    </div>
                    <div class="homework-description">
                        设计并实现一个图书管理系统，要求使用链表、栈、队列等数据结构，实现图书的增删改查功能。
                    </div>
                    <div class="deadline-warning">
                        <i class="fas fa-exclamation-triangle deadline-warning-icon"></i>
                        <span class="deadline-warning-text">距离截止时间还有2天</span>
                    </div>
                    <div class="homework-meta">
                        <div class="homework-meta-item">
                            <i class="fas fa-calendar homework-meta-icon"></i>
                            <span class="homework-meta-label">截止时间</span>
                            <span class="homework-meta-value">2024-01-17</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-weight-hanging homework-meta-icon"></i>
                            <span class="homework-meta-label">权重</span>
                            <span class="homework-meta-value">30%</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-clock homework-meta-icon"></i>
                            <span class="homework-meta-label">预计时长</span>
                            <span class="homework-meta-value">20小时</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-user homework-meta-icon"></i>
                            <span class="homework-meta-label">提交方式</span>
                            <span class="homework-meta-value">在线提交</span>
                        </div>
                    </div>
                    <div class="homework-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">60%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                    </div>
                    <div class="homework-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">提交作业</button>
                    </div>
                </div>

                <div class="homework-card normal">
                    <div class="homework-header">
                        <div class="homework-info">
                            <div class="homework-title">高等数学习题集</div>
                            <div class="homework-course">高等数学</div>
                            <div class="homework-type">练习题 · 书面作业</div>
                        </div>
                        <div class="homework-status status-pending">进行中</div>
                    </div>
                    <div class="homework-description">
                        完成教材第8章习题，包括导数计算、极值问题、应用题等，共计50道题目。
                    </div>
                    <div class="homework-meta">
                        <div class="homework-meta-item">
                            <i class="fas fa-calendar homework-meta-icon"></i>
                            <span class="homework-meta-label">截止时间</span>
                            <span class="homework-meta-value">2024-01-22</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-weight-hanging homework-meta-icon"></i>
                            <span class="homework-meta-label">权重</span>
                            <span class="homework-meta-value">15%</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-clock homework-meta-icon"></i>
                            <span class="homework-meta-label">预计时长</span>
                            <span class="homework-meta-value">8小时</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-user homework-meta-icon"></i>
                            <span class="homework-meta-label">提交方式</span>
                            <span class="homework-meta-value">纸质提交</span>
                        </div>
                    </div>
                    <div class="homework-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">40%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 40%"></div>
                        </div>
                    </div>
                    <div class="homework-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">编辑</button>
                    </div>
                </div>

                <div class="homework-card completed">
                    <div class="homework-header">
                        <div class="homework-info">
                            <div class="homework-title">英语听力练习</div>
                            <div class="homework-course">英语听力</div>
                            <div class="homework-type">听力练习 · 在线作业</div>
                        </div>
                        <div class="homework-status status-completed">已完成</div>
                    </div>
                    <div class="homework-description">
                        完成Unit 5-8的听力练习，包括对话理解、短文听写、听力填空等题型。
                    </div>
                    <div class="homework-meta">
                        <div class="homework-meta-item">
                            <i class="fas fa-calendar homework-meta-icon"></i>
                            <span class="homework-meta-label">完成时间</span>
                            <span class="homework-meta-value">2024-01-12</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-weight-hanging homework-meta-icon"></i>
                            <span class="homework-meta-label">权重</span>
                            <span class="homework-meta-value">10%</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-star homework-meta-icon"></i>
                            <span class="homework-meta-label">得分</span>
                            <span class="homework-meta-value">85分</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-clock homework-meta-icon"></i>
                            <span class="homework-meta-label">用时</span>
                            <span class="homework-meta-value">3小时</span>
                        </div>
                    </div>
                    <div class="homework-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">100%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="homework-actions">
                        <button class="btn btn-primary btn-sm">查看详情</button>
                        <button class="btn btn-success btn-sm">查看反馈</button>
                    </div>
                </div>

                <div class="homework-card normal">
                    <div class="homework-header">
                        <div class="homework-info">
                            <div class="homework-title">计算机网络实验</div>
                            <div class="homework-course">计算机网络</div>
                            <div class="homework-type">实验报告 · 实践作业</div>
                        </div>
                        <div class="homework-status status-pending">待开始</div>
                    </div>
                    <div class="homework-description">
                        完成TCP/IP协议分析实验，使用Wireshark抓包分析网络数据包，撰写实验报告。
                    </div>
                    <div class="homework-meta">
                        <div class="homework-meta-item">
                            <i class="fas fa-calendar homework-meta-icon"></i>
                            <span class="homework-meta-label">截止时间</span>
                            <span class="homework-meta-value">2024-01-28</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-weight-hanging homework-meta-icon"></i>
                            <span class="homework-meta-label">权重</span>
                            <span class="homework-meta-value">20%</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-clock homework-meta-icon"></i>
                            <span class="homework-meta-label">预计时长</span>
                            <span class="homework-meta-value">12小时</span>
                        </div>
                        <div class="homework-meta-item">
                            <i class="fas fa-user homework-meta-icon"></i>
                            <span class="homework-meta-label">提交方式</span>
                            <span class="homework-meta-value">在线提交</span>
                        </div>
                    </div>
                    <div class="homework-progress">
                        <div class="progress-header">
                            <div class="progress-label">完成进度</div>
                            <div class="progress-percentage">0%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="homework-actions">
                        <button class="btn btn-success btn-sm">开始作业</button>
                        <button class="btn btn-primary btn-sm">查看要求</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addHomework() {
            alert('添加作业功能正在开发中...');
        }

        function exportHomework() {
            alert('导出列表功能正在开发中...');
        }
    </script>
</body>
</html>
