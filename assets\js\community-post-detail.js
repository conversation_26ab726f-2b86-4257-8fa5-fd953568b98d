// 帖子详情页面交互功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initPostActions();
    initCommentActions();
    initReplySystem();
    initCommentSort();
});

// 帖子操作功能
function initPostActions() {
    // 点赞/反对按钮
    const likeBtn = document.querySelector('.post-actions .like-btn');
    const dislikeBtn = document.querySelector('.post-actions .dislike-btn');
    
    if (likeBtn) {
        likeBtn.addEventListener('click', function() {
            handlePostAction(this.dataset.postId, 'like');
        });
    }
    
    if (dislikeBtn) {
        dislikeBtn.addEventListener('click', function() {
            handlePostAction(this.dataset.postId, 'dislike');
        });
    }
    
    // 分享按钮
    const shareBtn = document.querySelector('.post-actions .share-btn');
    if (shareBtn) {
        shareBtn.addEventListener('click', function() {
            handleShare(this.dataset.postId);
        });
    }
    
    // 收藏按钮
    const bookmarkBtn = document.querySelector('.post-actions .bookmark-btn');
    if (bookmarkBtn) {
        bookmarkBtn.addEventListener('click', function() {
            handleBookmark(this.dataset.postId);
        });
    }
}

// 评论操作功能
function initCommentActions() {
    // 评论点赞/反对
    document.addEventListener('click', function(e) {
        if (e.target.closest('.comment-actions .like-btn')) {
            const btn = e.target.closest('.like-btn');
            handleCommentAction(btn.dataset.commentId, 'like');
        }
        
        if (e.target.closest('.comment-actions .dislike-btn')) {
            const btn = e.target.closest('.dislike-btn');
            handleCommentAction(btn.dataset.commentId, 'dislike');
        }
        
        if (e.target.closest('.comment-actions .delete-btn')) {
            const btn = e.target.closest('.delete-btn');
            handleCommentDelete(btn.dataset.commentId);
        }
    });
}

// 回复系统
function initReplySystem() {
    document.addEventListener('click', function(e) {
        if (e.target.closest('.reply-btn')) {
            const btn = e.target.closest('.reply-btn');
            const commentId = btn.dataset.commentId;
            const commentItem = btn.closest('.comment-item');
            const authorName = commentItem.querySelector('.author-name').textContent;
            
            showReplyForm(commentId, authorName);
        }
    });
    
    // 取消回复
    const cancelReplyBtn = document.getElementById('cancelReply');
    if (cancelReplyBtn) {
        cancelReplyBtn.addEventListener('click', function() {
            hideReplyForm();
        });
    }
}

// 评论排序
function initCommentSort() {
    const sortSelect = document.getElementById('commentSort');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            sortComments(this.value);
        });
    }
}

// 处理帖子操作
async function handlePostAction(postId, action) {
    try {
        const response = await fetch('api/post-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                post_id: postId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 更新按钮状态和数量
            updatePostActionButtons(action, result.data);
            showToast('操作成功', 'success');
        } else {
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('操作失败:', error);
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 处理评论操作
async function handleCommentAction(commentId, action) {
    console.log(`🎯 开始处理评论操作: commentId=${commentId}, action=${action}`);

    try {
        const requestData = {
            action: action,
            comment_id: commentId
        };

        console.log('📤 发送请求数据:', requestData);

        const response = await fetch('api/comment-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('📥 收到响应:', {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
        });

        // 先获取响应文本
        const responseText = await response.text();
        console.log('📄 响应文本:', responseText);

        // 尝试解析JSON
        let result;
        try {
            result = JSON.parse(responseText);
            console.log('✅ JSON解析成功:', result);
        } catch (parseError) {
            console.error('❌ JSON解析失败:', parseError);
            console.error('原始响应:', responseText);
            showToast('服务器响应格式错误', 'error');
            return;
        }

        if (result.success) {
            console.log('✅ 操作成功');
            // 更新按钮状态和数量
            updateCommentActionButtons(commentId, action, result.data);
            showToast('操作成功', 'success');
        } else {
            console.log('❌ 操作失败:', result.message);
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('❌ 网络错误:', error);
        console.error('错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 处理评论删除
async function handleCommentDelete(commentId) {
    if (!confirm('确定要删除这条评论吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        const response = await fetch('api/comment-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'delete',
                comment_id: commentId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 移除评论元素
            const commentItem = document.querySelector(`[data-comment-id="${commentId}"]`);
            if (commentItem) {
                commentItem.style.opacity = '0.5';
                commentItem.innerHTML = '<div class="comment-content"><div class="comment-deleted">此评论已被删除</div></div>';
            }
            showToast('评论已删除', 'success');
        } else {
            showToast(result.message || '删除失败', 'error');
        }
    } catch (error) {
        console.error('删除失败:', error);
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 显示回复表单
function showReplyForm(commentId, authorName) {
    const form = document.getElementById('commentForm');
    const parentIdInput = document.getElementById('parentId');
    const formLabel = document.getElementById('formLabel');
    const cancelBtn = document.getElementById('cancelReply');
    const textarea = document.getElementById('commentContent');
    
    if (form && parentIdInput && formLabel && cancelBtn) {
        parentIdInput.value = commentId;
        formLabel.textContent = `回复 @${authorName}`;
        cancelBtn.style.display = 'inline-flex';
        
        // 滚动到表单
        form.scrollIntoView({ behavior: 'smooth', block: 'center' });
        textarea.focus();
    }
}

// 隐藏回复表单
function hideReplyForm() {
    const parentIdInput = document.getElementById('parentId');
    const formLabel = document.getElementById('formLabel');
    const cancelBtn = document.getElementById('cancelReply');
    const textarea = document.getElementById('commentContent');
    
    if (parentIdInput && formLabel && cancelBtn) {
        parentIdInput.value = '';
        formLabel.textContent = '发表评论';
        cancelBtn.style.display = 'none';
        textarea.value = '';
    }
}

// 处理分享
function handleShare(postId) {
    const url = window.location.href;
    const title = document.querySelector('.post-title').textContent;
    
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        }).catch(console.error);
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(url).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showToast('链接已复制到剪贴板', 'success');
        });
    }
}

// 处理收藏
async function handleBookmark(postId) {
    try {
        const response = await fetch('api/post-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'bookmark',
                post_id: postId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            const bookmarkBtn = document.querySelector('.bookmark-btn');
            if (result.data.bookmarked) {
                bookmarkBtn.classList.add('active');
                showToast('已添加到收藏', 'success');
            } else {
                bookmarkBtn.classList.remove('active');
                showToast('已取消收藏', 'success');
            }
        } else {
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('收藏失败:', error);
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 更新帖子操作按钮
function updatePostActionButtons(action, data) {
    console.log(`🔄 更新帖子按钮状态: action=${action}`, data);

    const likeBtn = document.querySelector('.post-actions .like-btn');
    const dislikeBtn = document.querySelector('.post-actions .dislike-btn');

    if (!likeBtn || !dislikeBtn) {
        console.error('❌ 找不到帖子按钮元素:', { likeBtn: !!likeBtn, dislikeBtn: !!dislikeBtn });
        return;
    }

    console.log('✅ 找到帖子按钮元素，开始更新');

    // 更新按钮文本的辅助函数
    function updateButtonText(button, iconClass, count) {
        const icon = button.querySelector('i');
        if (icon) {
            // 保留图标，更新文本
            button.innerHTML = `<i class="${iconClass}"></i> ${formatNumber(count)}`;
        } else {
            // 如果没有图标，直接设置文本
            button.textContent = formatNumber(count);
        }
    }

    if (action === 'like') {
        likeBtn.classList.toggle('active', data.user_liked);
        updateButtonText(likeBtn, 'fas fa-thumbs-up', data.like_count);

        if (data.user_liked) {
            dislikeBtn.classList.remove('active');
        }
        updateButtonText(dislikeBtn, 'fas fa-thumbs-down', data.dislike_count);
    } else if (action === 'dislike') {
        dislikeBtn.classList.toggle('active', data.user_disliked);
        updateButtonText(dislikeBtn, 'fas fa-thumbs-down', data.dislike_count);

        if (data.user_disliked) {
            likeBtn.classList.remove('active');
        }
        updateButtonText(likeBtn, 'fas fa-thumbs-up', data.like_count);
    }

    console.log('✅ 帖子按钮状态更新完成');
}

// 更新评论操作按钮
function updateCommentActionButtons(commentId, action, data) {
    console.log(`🔄 更新按钮状态: commentId=${commentId}, action=${action}`, data);

    const commentItem = document.querySelector(`[data-comment-id="${commentId}"]`);
    if (!commentItem) {
        console.error('❌ 找不到评论元素:', commentId);
        return;
    }

    const likeBtn = commentItem.querySelector('.like-btn');
    const dislikeBtn = commentItem.querySelector('.dislike-btn');

    if (!likeBtn || !dislikeBtn) {
        console.error('❌ 找不到按钮元素:', { likeBtn: !!likeBtn, dislikeBtn: !!dislikeBtn });
        return;
    }

    console.log('✅ 找到按钮元素，开始更新');

    // 更新按钮文本的辅助函数
    function updateButtonText(button, iconClass, count) {
        const icon = button.querySelector('i');
        if (icon) {
            // 保留图标，更新文本
            button.innerHTML = `<i class="${iconClass}"></i> ${formatNumber(count)}`;
        } else {
            // 如果没有图标，直接设置文本
            button.textContent = formatNumber(count);
        }
    }

    if (action === 'like') {
        likeBtn.classList.toggle('active', data.user_liked);
        updateButtonText(likeBtn, 'fas fa-thumbs-up', data.like_count);

        if (data.user_liked) {
            dislikeBtn.classList.remove('active');
        }
        updateButtonText(dislikeBtn, 'fas fa-thumbs-down', data.dislike_count);
    } else if (action === 'dislike') {
        dislikeBtn.classList.toggle('active', data.user_disliked);
        updateButtonText(dislikeBtn, 'fas fa-thumbs-down', data.dislike_count);

        if (data.user_disliked) {
            likeBtn.classList.remove('active');
        }
        updateButtonText(likeBtn, 'fas fa-thumbs-up', data.like_count);
    }

    console.log('✅ 按钮状态更新完成');
}

// 评论排序
function sortComments(sortType) {
    const commentsList = document.querySelector('.comments-list');
    if (!commentsList) return;

    // 只获取顶级评论（margin-left: 0px）
    const comments = Array.from(commentsList.querySelectorAll('.comment-item[style*="margin-left: 0px"]'));

    comments.sort((a, b) => {
        switch (sortType) {
            case 'time':
                const timeA = new Date(a.querySelector('.comment-time').getAttribute('datetime') || 0);
                const timeB = new Date(b.querySelector('.comment-time').getAttribute('datetime') || 0);
                return timeB - timeA; // 最新的在前

            case 'likes':
                // 修正选择器，获取点赞数
                const likesTextA = a.querySelector('.like-btn').textContent.trim();
                const likesTextB = b.querySelector('.like-btn').textContent.trim();
                const likesA = parseInt(likesTextA.replace(/[^\d]/g, '')) || 0;
                const likesB = parseInt(likesTextB.replace(/[^\d]/g, '')) || 0;
                return likesB - likesA; // 点赞多的在前

            case 'floor':
            default:
                // 修正楼层号提取
                const floorTextA = a.querySelector('.floor-number')?.textContent || '';
                const floorTextB = b.querySelector('.floor-number')?.textContent || '';
                const floorA = parseInt(floorTextA.replace(/[^\d]/g, '')) || 0;
                const floorB = parseInt(floorTextB.replace(/[^\d]/g, '')) || 0;
                return floorA - floorB; // 楼层小的在前
        }
    });

    // 重新排列评论（保持子评论跟随父评论）
    comments.forEach(comment => {
        // 移动评论及其所有回复
        const replies = comment.querySelector('.comment-replies');
        commentsList.appendChild(comment);
    });
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'
    });
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}
