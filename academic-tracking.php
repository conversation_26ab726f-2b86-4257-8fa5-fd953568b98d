<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学业跟踪 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #10b981;
            --secondary-color: #059669;
            --accent-color: #34d399;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .dashboard-container {
            background: white;
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: white;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #333;
            font-size: 24px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .courses-section {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .course-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .course-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .course-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .course-name {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .course-credits {
            background: rgba(255, 255, 255, 0.2);
            color: #333;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .progress-section {
            margin-bottom: 1rem;
        }

        .progress-label {
            color: #495057;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }

        .progress {
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar {
            background: white;
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .course-stats {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
        }

        .course-stat {
            text-align: center;
            flex: 1;
        }

        .course-stat-number {
            color: #333;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .course-stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        .time-tracking-section {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .time-chart {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .courses-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-chart-line me-2"></i>
                学业跟踪
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">学业跟踪</h1>
            <p class="page-description">课程进度跟踪、学分统计、学习时间记录与分析</p>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-number">6</div>
                    <div class="stat-label">当前课程</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-number">18</div>
                    <div class="stat-label">已获学分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number">42</div>
                    <div class="stat-label">本周学习时间(小时)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-number">3.8</div>
                    <div class="stat-label">平均GPA</div>
                </div>
            </div>

            <!-- 课程进度 -->
            <div class="courses-section">
                <h2 class="section-title">
                    <i class="fas fa-tasks"></i>
                    课程进度
                </h2>
                <div class="courses-grid">
                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-name">高等数学</div>
                            <div class="course-credits">4学分</div>
                        </div>
                        <div class="progress-section">
                            <div class="progress-label">
                                <span>课程进度</span>
                                <span>75%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="course-stats">
                            <div class="course-stat">
                                <div class="course-stat-number">12</div>
                                <div class="course-stat-label">已完成章节</div>
                            </div>
                            <div class="course-stat">
                                <div class="course-stat-number">4</div>
                                <div class="course-stat-label">剩余章节</div>
                            </div>
                            <div class="course-stat">
                                <div class="course-stat-number">85</div>
                                <div class="course-stat-label">当前成绩</div>
                            </div>
                        </div>
                    </div>

                    <div class="course-card">
                        <div class="course-header">
                            <div class="course-name">数据结构</div>
                            <div class="course-credits">3学分</div>
                        </div>
                        <div class="progress-section">
                            <div class="progress-label">
                                <span>课程进度</span>
                                <span>60%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="course-stats">
                            <div class="course-stat">
                                <div class="course-stat-number">9</div>
                                <div class="course-stat-label">已完成章节</div>
                            </div>
                            <div class="course-stat">
                                <div class="course-stat-label">6</div>
                                <div class="course-stat-label">剩余章节</div>
                            </div>
                            <div class="course-stat">
                                <div class="course-stat-number">78</div>
                                <div class="course-stat-label">当前成绩</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 学习时间统计 -->
            <div class="time-tracking-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    学习时间统计
                </h2>
                <div class="time-chart">
                    <i class="fas fa-chart-line me-2"></i>
                    学习时间图表功能正在开发中...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
