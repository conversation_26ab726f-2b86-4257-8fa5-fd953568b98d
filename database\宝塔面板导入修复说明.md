# 🔧 宝塔面板数据库导入修复说明

## ❌ 问题原因
原始的 `init.sql` 文件中包含了 `CREATE DATABASE` 和 `USE` 语句，这在宝塔面板的phpMyAdmin中会导致错误。

## ✅ 解决方案

### 方法1：使用修复后的SQL文件（推荐）

**步骤1：下载修复文件**
使用 `database/init_fixed.sql` 文件替代原来的 `init.sql`

**步骤2：在phpMyAdmin中导入**
1. 在宝塔面板中进入数据库管理
2. 点击 `bitbear_website` 数据库的"管理"按钮
3. 在phpMyAdmin中确保已选择 `bitbear_website` 数据库
4. 点击"导入"选项卡
5. 选择 `init_fixed.sql` 文件
6. 点击"执行"

### 方法2：手动执行SQL语句

如果文件导入仍有问题，可以直接在phpMyAdmin的SQL选项卡中执行以下语句：

```sql
-- 用户角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    permissions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar VARCHAR(255),
    role_id INT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
);

-- 插入默认角色
INSERT IGNORE INTO user_roles (role_name, role_code, description, permissions) VALUES
('超级管理员', 'super_admin', '拥有系统所有权限', 'all'),
('管理员', 'admin', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
('普通用户', 'user', '基础用户权限', 'dashboard,personal_settings');

-- 插入默认管理员用户（用户名：admin，密码：admin123）
INSERT IGNORE INTO users (username, email, password_hash, full_name, role_id, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 1, 'active');
```

## 📋 正确的导入顺序

1. **第一步**：导入 `init_fixed.sql`（基础用户表）
2. **第二步**：导入 `create_community_tables.sql`（社区功能表）
3. **第三步**：导入 `homepage_content_tables.sql`（主页内容表）

## 🔍 验证导入成功

导入完成后，在phpMyAdmin中执行以下查询验证：

```sql
-- 查看所有表
SHOW TABLES;

-- 查看用户角色
SELECT * FROM user_roles;

-- 查看默认管理员用户
SELECT id, username, email, full_name, role_id FROM users;
```

应该看到：
- 3个用户角色（超级管理员、管理员、普通用户）
- 1个默认管理员用户（admin）

## 🚨 如果仍有问题

**常见错误及解决方案：**

1. **字符编码问题**
   - 确保数据库字符集为 `utf8mb4`
   - 在导入时选择 `utf8mb4_unicode_ci` 排序规则

2. **权限问题**
   - 确保数据库用户有 CREATE、INSERT 权限
   - 检查宝塔面板中的数据库用户设置

3. **MySQL版本兼容性**
   - 如果使用较老的MySQL版本，可能需要移除 `JSON` 数据类型

## 💡 测试登录信息

导入成功后，可以使用以下信息测试登录：
- **用户名**: admin
- **密码**: admin123
- **邮箱**: <EMAIL>

---

**现在请尝试使用 `init_fixed.sql` 文件重新导入！** 🚀
