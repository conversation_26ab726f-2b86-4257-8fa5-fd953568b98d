<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统诊断 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .diagnostic-item {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .diagnostic-item.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .diagnostic-item.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .diagnostic-item.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .diagnostic-item.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-badge.success {
            background: #d1fae5;
            color: #065f46;
        }
        .status-badge.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .status-badge.warning {
            background: #fef3c7;
            color: #92400e;
        }
        .status-badge.info {
            background: #dbeafe;
            color: #1e40af;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #f093fb;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #f5576c;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #f093fb;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .recommendations {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .recommendations h3 {
            margin-top: 0;
            color: #1e293b;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .recommendations li {
            margin: 8px 0;
            color: #475569;
        }
        .section {
            margin: 30px 0;
        }
        .section h3 {
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 系统诊断</h1>
            <p>一键检测注册功能问题</p>
        </div>
        
        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在进行系统诊断...</p>
            </div>
            
            <div id="results" style="display: none;">
                <!-- 诊断结果将在这里显示 -->
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button onclick="runDiagnosis()" class="btn">重新诊断</button>
                <a href="注册测试.php" class="btn success">测试注册</a>
                <a href="云服务器环境初始化.php" class="btn">环境初始化</a>
                <a href="云服务器注册修复.php" class="btn">修复工具</a>
            </div>
        </div>
    </div>

    <script>
        function runDiagnosis() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            fetch('快速诊断.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('results').style.display = 'block';
                    displayResults(data);
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('results').innerHTML = `
                        <div class="diagnostic-item error">
                            <div>
                                <strong>诊断失败</strong><br>
                                <small>网络错误或服务器问题</small>
                            </div>
                            <span class="status-badge error">错误</span>
                        </div>
                    `;
                    document.getElementById('results').style.display = 'block';
                });
        }
        
        function displayResults(data) {
            let html = '';
            
            // 环境信息
            html += '<div class="section">';
            html += '<h3>🌐 环境信息</h3>';
            html += `<div class="diagnostic-item info">
                <div>
                    <strong>运行环境</strong><br>
                    <small>${data.environment === 'cloud_server' ? '云服务器环境' : '本地开发环境'}</small>
                </div>
                <span class="status-badge info">${data.environment === 'cloud_server' ? '云服务器' : '本地环境'}</span>
            </div>`;
            html += '</div>';
            
            // 数据库状态
            html += '<div class="section">';
            html += '<h3>🗄️ 数据库状态</h3>';
            const dbStatus = data.database.status;
            const dbClass = dbStatus === 'connected' ? 'success' : 'error';
            html += `<div class="diagnostic-item ${dbClass}">
                <div>
                    <strong>数据库连接</strong><br>
                    <small>${dbStatus === 'connected' ? '连接正常' : '连接失败: ' + (data.database.error || '未知错误')}</small>
                </div>
                <span class="status-badge ${dbClass}">${dbStatus === 'connected' ? '正常' : '失败'}</span>
            </div>`;
            
            if (data.database.info) {
                html += `<div class="diagnostic-item info">
                    <div>
                        <strong>数据库信息</strong><br>
                        <small>数据库: ${data.database.info.db_name || '未知'} | 版本: ${data.database.info.version || '未知'}</small>
                    </div>
                    <span class="status-badge info">信息</span>
                </div>`;
            }
            html += '</div>';
            
            // 数据表状态
            if (data.tables && Object.keys(data.tables).length > 0) {
                html += '<div class="section">';
                html += '<h3>📋 数据表状态</h3>';
                for (const [tableName, tableInfo] of Object.entries(data.tables)) {
                    const tableClass = tableInfo.exists ? 'success' : 'error';
                    html += `<div class="diagnostic-item ${tableClass}">
                        <div>
                            <strong>${tableName}</strong><br>
                            <small>${tableInfo.exists ? `存在 (${tableInfo.records || 0} 条记录)` : '不存在'}</small>
                        </div>
                        <span class="status-badge ${tableClass}">${tableInfo.exists ? '存在' : '缺失'}</span>
                    </div>`;
                }
                html += '</div>';
            }
            
            // 目录权限
            if (data.permissions && Object.keys(data.permissions).length > 0) {
                html += '<div class="section">';
                html += '<h3>📁 目录权限</h3>';
                for (const [dir, status] of Object.entries(data.permissions)) {
                    let dirClass = 'error';
                    let statusText = '缺失';
                    if (status === 'writable') {
                        dirClass = 'success';
                        statusText = '可写';
                    } else if (status === 'not_writable') {
                        dirClass = 'warning';
                        statusText = '不可写';
                    }
                    
                    html += `<div class="diagnostic-item ${dirClass}">
                        <div>
                            <strong>${dir}</strong><br>
                            <small>权限状态: ${statusText}</small>
                        </div>
                        <span class="status-badge ${dirClass}">${statusText}</span>
                    </div>`;
                }
                html += '</div>';
            }
            
            // 注册测试结果
            if (data.test_registration) {
                html += '<div class="section">';
                html += '<h3>🧪 注册功能测试</h3>';
                const testStatus = data.test_registration.status;
                let testClass = 'error';
                let statusText = '失败';
                
                if (testStatus === 'success') {
                    testClass = 'success';
                    statusText = '成功';
                } else if (testStatus === 'partial_success') {
                    testClass = 'warning';
                    statusText = '部分成功';
                }
                
                html += `<div class="diagnostic-item ${testClass}">
                    <div>
                        <strong>注册流程测试</strong><br>
                        <small>${data.test_registration.message || '无详细信息'}</small>
                    </div>
                    <span class="status-badge ${testClass}">${statusText}</span>
                </div>`;
                html += '</div>';
            }
            
            // 建议
            if (data.recommendations && data.recommendations.length > 0) {
                html += '<div class="recommendations">';
                html += '<h3>💡 修复建议</h3>';
                html += '<ul>';
                for (const recommendation of data.recommendations) {
                    html += `<li>${recommendation}</li>`;
                }
                html += '</ul>';
                html += '</div>';
            }
            
            document.getElementById('results').innerHTML = html;
        }
        
        // 页面加载时自动运行诊断
        window.addEventListener('load', runDiagnosis);
    </script>
</body>
</html>
