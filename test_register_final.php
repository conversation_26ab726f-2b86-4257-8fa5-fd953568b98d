<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 注册功能测试</h1>
        <p>测试修复后的注册API功能</p>
        
        <form id="registerForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称:</label>
                <input type="text" id="nickname" name="nickname" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">确认密码:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <button type="submit">测试注册</button>
            <button type="button" onclick="generateTestData()">生成测试数据</button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        // 生成测试数据
        function generateTestData() {
            const timestamp = Date.now();
            document.getElementById('username').value = 'testuser' + timestamp;
            document.getElementById('email').value = 'test' + timestamp + '@example.com';
            document.getElementById('nickname').value = '测试用户' + timestamp;
            document.getElementById('password').value = 'test123456';
            document.getElementById('confirmPassword').value = 'test123456';
        }

        // 处理表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在注册，请稍候...';
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/api/register.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.text();
                
                // 尝试解析JSON
                let jsonData;
                try {
                    jsonData = JSON.parse(data);
                } catch (e) {
                    throw new Error('服务器返回的不是有效的JSON格式: ' + data);
                }
                
                if (jsonData.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ 注册成功！</h3>
                        <p>用户ID: ${jsonData.user_id}</p>
                        <p>消息: ${jsonData.message}</p>
                    `;
                    
                    // 清空表单
                    this.reset();
                } else {
                    resultDiv.className = 'result error';
                    let errorHtml = `<h3>❌ 注册失败</h3><p>${jsonData.message}</p>`;
                    
                    if (jsonData.errors) {
                        errorHtml += '<ul>';
                        for (const [field, error] of Object.entries(jsonData.errors)) {
                            errorHtml += `<li>${field}: ${error}</li>`;
                        }
                        errorHtml += '</ul>';
                    }
                    
                    resultDiv.innerHTML = errorHtml;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ 请求失败</h3>
                    <p>错误信息: ${error.message}</p>
                `;
            }
        });

        // 页面加载时自动生成测试数据
        window.addEventListener('load', function() {
            generateTestData();
        });
    </script>
</body>
</html>
