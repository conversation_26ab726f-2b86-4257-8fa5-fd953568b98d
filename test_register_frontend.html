<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册测试</title>
    <link rel="stylesheet" href="assets/css/toast.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .debug-info {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        .success {
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>注册功能测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" value="testuser123" required>
                <div class="error" id="usernameError"></div>
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
                <div class="error" id="emailError"></div>
            </div>
            
            <div class="form-group">
                <label for="nickname">昵称</label>
                <input type="text" id="nickname" name="nickname" value="测试用户" required>
                <div class="error" id="nicknameError"></div>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" value="test123456" required>
                <div class="error" id="passwordError"></div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">确认密码</label>
                <input type="password" id="confirmPassword" name="confirmPassword" value="test123456" required>
                <div class="error" id="confirmPasswordError"></div>
            </div>
            
            <button type="submit" id="submitBtn">注册测试</button>
        </form>
        
        <div class="debug-info" id="debugInfo" style="display: none;">
            <h3>调试信息</h3>
            <div id="debugContent"></div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <script>
        // Toast 通知函数
        function showToast(message, type = 'info') {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            
            const icons = {
                success: '✓',
                error: '✕',
                warning: '⚠',
                info: 'ℹ'
            };
            
            toast.innerHTML = `
                <div class="toast-icon">${icons[type] || icons.info}</div>
                <div class="toast-message">${message}</div>
                <button class="toast-close" onclick="this.parentElement.remove()">×</button>
            `;
            
            container.appendChild(toast);
            
            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        // 显示字段错误
        function showFieldError(field, message) {
            const errorElement = document.getElementById(field + 'Error');
            if (errorElement) {
                errorElement.textContent = message;
            }
        }

        // 清除字段错误
        function clearFieldErrors() {
            const errorElements = document.querySelectorAll('.error');
            errorElements.forEach(element => {
                element.textContent = '';
            });
        }

        // 显示调试信息
        function showDebugInfo(info) {
            const debugInfo = document.getElementById('debugInfo');
            const debugContent = document.getElementById('debugContent');
            debugContent.innerHTML = '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
            debugInfo.style.display = 'block';
        }

        // 表单提交
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            clearFieldErrors();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = '注册中...';
            
            const formData = new FormData(this);
            
            // 添加随机数避免重复
            const timestamp = Date.now();
            formData.set('username', 'testuser' + timestamp);
            formData.set('email', 'test' + timestamp + '@example.com');
            
            try {
                console.log('发送注册请求...');
                
                const response = await fetch('api/register.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                const responseText = await response.text();
                console.log('原始响应:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON解析错误:', parseError);
                    showToast('服务器响应格式错误', 'error');
                    showDebugInfo({
                        error: 'JSON解析失败',
                        response: responseText,
                        parseError: parseError.message
                    });
                    return;
                }
                
                console.log('解析后的结果:', result);
                showDebugInfo(result);
                
                if (result.success) {
                    showToast('注册成功！', 'success');
                } else {
                    showToast(result.message || '注册失败，请重试', 'error');
                    
                    // 显示字段错误
                    if (result.errors) {
                        Object.keys(result.errors).forEach(field => {
                            showFieldError(field, result.errors[field]);
                        });
                    }
                }
                
            } catch (error) {
                console.error('请求错误:', error);
                showToast('网络错误：' + error.message, 'error');
                showDebugInfo({
                    error: '网络请求失败',
                    message: error.message,
                    stack: error.stack
                });
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '注册测试';
            }
        });
    </script>
</body>
</html>
