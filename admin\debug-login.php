<?php
/**
 * 调试登录页面 - 用于排查登录问题
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../classes/Auth.php';
require_once __DIR__ . '/../config/database.php';

$debug_info = [];
$error = '';
$success = '';

// 测试数据库连接
try {
    $db = DatabaseConfig::getInstance();
    $debug_info[] = "✅ 数据库连接成功";
    
    // 检查用户表
    $users = $db->fetchAll("SELECT id, username, email, status, created_at FROM users LIMIT 5");
    $debug_info[] = "✅ 用户表查询成功，找到 " . count($users) . " 个用户";
    
    foreach ($users as $user) {
        $debug_info[] = "用户: {$user['username']} ({$user['email']}) - 状态: {$user['status']}";
    }
    
} catch (Exception $e) {
    $debug_info[] = "❌ 数据库错误: " . $e->getMessage();
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    $debug_info[] = "🔍 尝试登录用户: " . $username;
    
    if (empty($username) || empty($password)) {
        $error = '请输入用户名和密码';
        $debug_info[] = "❌ 用户名或密码为空";
    } else {
        try {
            $auth = new Auth();
            $debug_info[] = "✅ Auth 类实例化成功";
            
            // 手动检查用户
            $user = $db->fetchOne(
                "SELECT u.*, r.role_code, r.role_name 
                 FROM users u 
                 LEFT JOIN user_roles r ON u.role_id = r.id 
                 WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'",
                [$username, $username]
            );
            
            if ($user) {
                $debug_info[] = "✅ 找到用户: " . $user['username'] . " (ID: " . $user['id'] . ")";
                $debug_info[] = "用户状态: " . $user['status'];
                $debug_info[] = "用户角色: " . ($user['role_name'] ?? '无角色');
                
                // 检查密码
                if (password_verify($password, $user['password_hash'])) {
                    $debug_info[] = "✅ 密码验证成功";
                } else {
                    $debug_info[] = "❌ 密码验证失败";
                    $debug_info[] = "输入密码: " . $password;
                    $debug_info[] = "存储哈希: " . substr($user['password_hash'], 0, 20) . "...";
                }
            } else {
                $debug_info[] = "❌ 未找到用户: " . $username;
            }
            
            // 尝试正常登录流程
            $result = $auth->login($username, $password, false);
            $debug_info[] = "登录结果: " . json_encode($result);

            if ($result['success']) {
                $success = "登录成功！";
                $debug_info[] = "✅ 登录成功，准备跳转";

                // 检查会话状态
                $debug_info[] = "会话ID: " . session_id();
                $debug_info[] = "用户ID: " . ($_SESSION['user_id'] ?? '未设置');
                $debug_info[] = "用户名: " . ($_SESSION['username'] ?? '未设置');
                $debug_info[] = "角色代码: " . ($_SESSION['role_code'] ?? '未设置');
                $debug_info[] = "会话令牌: " . (isset($_SESSION['session_token']) ? substr($_SESSION['session_token'], 0, 10) . '...' : '未设置');

                // 检查登录状态
                $isLoggedIn = $auth->isLoggedIn();
                $debug_info[] = "登录状态检查: " . ($isLoggedIn ? '✅ 已登录' : '❌ 未登录');

                if ($isLoggedIn) {
                    $debug_info[] = "✅ 可以跳转到后台管理页面";
                    $debug_info[] = "跳转地址: admin/index.php";
                } else {
                    $debug_info[] = "❌ 无法跳转，会话验证失败";
                }

                // 不自动跳转，显示调试信息
            } else {
                $error = $result['message'];
                $debug_info[] = "❌ 登录失败: " . $result['message'];
            }
            
        } catch (Exception $e) {
            $error = "登录过程中发生错误: " . $e->getMessage();
            $debug_info[] = "❌ 异常: " . $e->getMessage();
            $debug_info[] = "异常文件: " . $e->getFile() . ":" . $e->getLine();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录调试 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .login-form {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .debug-info {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .debug-item {
            padding: 5px 0;
            border-bottom: 1px solid #e2e8f0;
            font-family: monospace;
            font-size: 14px;
        }
        
        .debug-item:last-child {
            border-bottom: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .alert-success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        
        .quick-actions {
            margin-top: 20px;
            padding: 20px;
            background: #fef3c7;
            border-radius: 8px;
        }
        
        .quick-actions a {
            display: inline-block;
            margin-right: 10px;
            padding: 8px 16px;
            background: #f59e0b;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 登录调试页面</h1>
        
        <div class="login-form">
            <h3>测试登录</h3>
            <form method="POST">
                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
                <?php endif; ?>
                
                <div class="form-group">
                    <label class="form-label" for="username">用户名</label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-input" 
                        value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>"
                        required
                    >
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="password">密码</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        class="form-input" 
                        value="<?php echo htmlspecialchars($_POST['password'] ?? 'admin123'); ?>"
                        required
                    >
                </div>
                
                <button type="submit" class="login-btn">测试登录</button>
            </form>
        </div>
        
        <div class="debug-info">
            <h3>🔧 调试信息</h3>
            <?php foreach ($debug_info as $info): ?>
                <div class="debug-item"><?php echo htmlspecialchars($info); ?></div>
            <?php endforeach; ?>
        </div>
        
        <div class="quick-actions">
            <h4>🚀 快速操作</h4>
            <a href="init-database.php">重新初始化数据库</a>
            <a href="reset-admin-password.php">重置管理员密码</a>
            <a href="login.php">返回正常登录页面</a>
            <?php if (isset($success) && $success): ?>
                <a href="index.php" style="background: #10b981;">进入后台管理</a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
