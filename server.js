const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const port = 8000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.php': 'text/html' // 简单处理PHP文件
};

const server = http.createServer((req, res) => {
    let pathname = url.parse(req.url).pathname;
    
    // 默认页面
    if (pathname === '/') {
        pathname = '/index.html';
    }
    
    const filePath = path.join(__dirname, pathname);
    const ext = path.extname(filePath).toLowerCase();
    const contentType = mimeTypes[ext] || 'text/plain';
    
    // 检查文件是否存在
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // 文件不存在，返回404
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 页面未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #e74c3c; }
                        .links { margin-top: 30px; }
                        .links a { display: inline-block; margin: 10px; padding: 10px 20px; 
                                  background: #3498db; color: white; text-decoration: none; border-radius: 5px; }
                        .links a:hover { background: #2980b9; }
                    </style>
                </head>
                <body>
                    <h1>404 - 页面未找到</h1>
                    <p>请求的页面 "${pathname}" 不存在</p>
                    <div class="links">
                        <a href="/index.html">静态主页</a>
                        <a href="/index-standalone.html">独立版本</a>
                        <a href="/index.php">PHP版本</a>
                        <a href="/admin.php">管理后台</a>
                    </div>
                </body>
                </html>
            `);
            return;
        }
        
        // 读取文件
        fs.readFile(filePath, (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/html' });
                res.end('<h1>500 - 服务器内部错误</h1>');
                return;
            }
            
            // 如果是PHP文件，简单处理（实际项目中需要PHP解释器）
            if (ext === '.php') {
                let content = data.toString();
                
                // 简单的PHP处理 - 替换专家数据
                if (content.includes('<?php') && content.includes('experts-data.php')) {
                    const expertsData = `
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="0">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Arianne Dee - Software developer" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Arianne Dee</h4>
                                <p class="expert-title">Software developer</p>
                            </div>
                        </div>
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="100">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Sari Greene - Cybersecurity practitioner" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Sari Greene</h4>
                                <p class="expert-title">Cybersecurity practitioner</p>
                            </div>
                        </div>
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="200">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Bruno Gonçalves - Senior data scientist" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Bruno Gonçalves</h4>
                                <p class="expert-title">Senior data scientist</p>
                            </div>
                        </div>
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="300">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Neal Ford - Software architect" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Neal Ford</h4>
                                <p class="expert-title">Software architect</p>
                            </div>
                        </div>
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="400">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Kelsey Hightower - Software engineer" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Kelsey Hightower</h4>
                                <p class="expert-title">Software engineer</p>
                            </div>
                        </div>
                        <div class="expert-card" data-aos="fade-up" data-aos-delay="500">
                            <div class="expert-image-container">
                                <img src="image/default-avatar.svg" alt="Ken Kousen - Java Champion" class="expert-image">
                                <div class="expert-overlay"></div>
                            </div>
                            <div class="expert-info">
                                <h4 class="expert-name">Ken Kousen</h4>
                                <p class="expert-title">Java Champion</p>
                            </div>
                        </div>
                    `;
                    
                    // 替换PHP代码块
                    content = content.replace(
                        /<!-- 动态专家卡片网格 -->[\s\S]*?<\/div>/,
                        `<!-- 动态专家卡片网格 -->
                <div class="experts-grid" id="expertsGrid">
                    ${expertsData}
                </div>`
                    );
                }
            }
            
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        });
    });
});

server.listen(port, () => {
    console.log(`🚀 比特熊项目服务器启动成功!`);
    console.log(`🌐 访问地址: http://localhost:${port}`);
    console.log(`📄 可用页面:`);
    console.log(`   • 静态主页: http://localhost:${port}/index.html`);
    console.log(`   • 独立版本: http://localhost:${port}/index-standalone.html`);
    console.log(`   • PHP版本: http://localhost:${port}/index.php`);
    console.log(`   • 管理后台: http://localhost:${port}/admin.php`);
    console.log(`💡 按 Ctrl+C 停止服务器`);
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.log(`❌ 端口 ${port} 已被占用`);
        console.log(`💡 请尝试其他端口或关闭占用该端口的程序`);
    } else {
        console.log(`❌ 服务器错误: ${err.message}`);
    }
});
