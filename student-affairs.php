<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #f59e0b;
            --secondary-color: #d97706;
            --accent-color: #fbbf24;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header {
            background: #ffffff;
            border-radius: 0;
            padding: 2rem 1rem;
            margin-bottom: 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border-bottom: 2px solid #e9ecef;
            flex-shrink: 0;
            width: 100vw;
        }

        .page-title {
            margin: 0;
            color: #f97316;
            font-weight: 700;
            font-size: 2rem;
        }

        .page-description {
            margin: 0.5rem 0 0 0;
            color: #495057;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .dashboard-container {
            background: white;
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .module-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.15);
            border-color: #f97316;
        }

        .module-card:hover .module-title {
            color: #ea580c;
        }

        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .module-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            color: white;
            font-size: 24px;
        }

        .module-title {
            color: #f97316;
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
        }

        .module-description {
            color: #495057;
            font-size: 1rem;
            margin-bottom: 1rem;
            line-height: 1.6;
            font-weight: 500;
        }

        .module-stats {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .stat-badge {
            background: #fed7aa;
            color: #ea580c;
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            border: 1px solid #fdba74;
        }

        /* 模块图标颜色 */
        .icon-schedule { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
        .icon-academic { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
        .icon-exam { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); }
        .icon-grade { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
        .icon-goal { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .icon-reading { background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); }
        .icon-homework { background: linear-gradient(135deg, #ec4899 0%, #db2777 100%); }
        .icon-entertainment { background: linear-gradient(135deg, #84cc16 0%, #65a30d 100%); }

        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 1.75rem;
            }

            .page-description {
                font-size: 1rem;
            }

            .module-title {
                font-size: 1.25rem;
            }

            .module-description {
                font-size: 0.95rem;
            }

            .module-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .quick-stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .dashboard-container {
                padding: 0.5rem;
            }

            .module-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="admin-dashboard.php">
                <i class="fas fa-graduation-cap me-2"></i>
                学务管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="admin-dashboard.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回控制面板
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">学务管理</h1>
            <p class="page-description">综合学习生活管理系统，整合日程、学业、考试、目标等各个方面</p>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 快速统计 -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">本周课程</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">3</div>
                    <div class="stat-label">待完成作业</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">5</div>
                    <div class="stat-label">即将考试</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">85%</div>
                    <div class="stat-label">目标完成度</div>
                </div>
            </div>

            <!-- 功能模块 -->
            <div class="module-grid">
                <!-- 日程管理 -->
                <div class="module-card" onclick="openScheduleManagement()">
                    <div class="module-icon icon-schedule">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="module-title">日程管理</h3>
                    <p class="module-description">日历视图、课程表管理、提醒通知、重复事件设置</p>
                    <div class="module-stats">
                        <span class="stat-badge">12 个事件</span>
                        <span class="stat-badge">本周</span>
                    </div>
                </div>

                <!-- 学业跟踪 -->
                <div class="module-card" onclick="openAcademicTracking()">
                    <div class="module-icon icon-academic">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="module-title">学业跟踪</h3>
                    <p class="module-description">课程进度跟踪、学分统计、学习时间记录与分析</p>
                    <div class="module-stats">
                        <span class="stat-badge">6 门课程</span>
                        <span class="stat-badge">进行中</span>
                    </div>
                </div>

                <!-- 考试管理 -->
                <div class="module-card" onclick="openExamManagement()">
                    <div class="module-icon icon-exam">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <h3 class="module-title">考试管理</h3>
                    <p class="module-description">考试日程安排、倒计时提醒、复习计划制定</p>
                    <div class="module-stats">
                        <span class="stat-badge">5 场考试</span>
                        <span class="stat-badge">本月</span>
                    </div>
                </div>

                <!-- 成绩管理 -->
                <div class="module-card" onclick="openGradeManagement()">
                    <div class="module-icon icon-grade">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="module-title">成绩管理</h3>
                    <p class="module-description">成绩录入存储、统计分析、GPA计算、历史记录</p>
                    <div class="module-stats">
                        <span class="stat-badge">GPA 3.8</span>
                        <span class="stat-badge">本学期</span>
                    </div>
                </div>

                <!-- 学业目标管理 -->
                <div class="module-card" onclick="openGoalManagement()">
                    <div class="module-icon icon-goal">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <h3 class="module-title">学业目标管理</h3>
                    <p class="module-description">短期/长期目标设定、进度跟踪、SMART目标模板</p>
                    <div class="module-stats">
                        <span class="stat-badge">8 个目标</span>
                        <span class="stat-badge">85% 完成</span>
                    </div>
                </div>

                <!-- 读书管理 -->
                <div class="module-card" onclick="openReadingManagement()">
                    <div class="module-icon icon-reading">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3 class="module-title">读书管理</h3>
                    <p class="module-description">阅读清单、进度跟踪、读书笔记、阅读统计</p>
                    <div class="module-stats">
                        <span class="stat-badge">15 本书</span>
                        <span class="stat-badge">本年度</span>
                    </div>
                </div>

                <!-- 作业管理 -->
                <div class="module-card" onclick="openHomeworkManagement()">
                    <div class="module-icon icon-homework">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3 class="module-title">作业管理</h3>
                    <p class="module-description">作业提交跟踪、截止日期提醒、分类管理</p>
                    <div class="module-stats">
                        <span class="stat-badge">3 个待完成</span>
                        <span class="stat-badge">本周</span>
                    </div>
                </div>

                <!-- 娱乐管理 -->
                <div class="module-card" onclick="openEntertainmentManagement()">
                    <div class="module-icon icon-entertainment">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3 class="module-title">娱乐管理</h3>
                    <p class="module-description">休闲活动记录、娱乐时间统计、学习娱乐平衡分析</p>
                    <div class="module-stats">
                        <span class="stat-badge">2.5 小时</span>
                        <span class="stat-badge">今日</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模块导航函数
        function openScheduleManagement() {
            window.open('schedule-management.php', '_blank');
        }

        function openAcademicTracking() {
            window.open('academic-tracking.php', '_blank');
        }

        function openExamManagement() {
            window.open('exam-management.php', '_blank');
        }

        function openGradeManagement() {
            window.open('grade-management.php', '_blank');
        }

        function openGoalManagement() {
            window.open('goal-management.php', '_blank');
        }

        function openReadingManagement() {
            window.open('reading-management.php', '_blank');
        }

        function openHomeworkManagement() {
            window.open('homework-management.php', '_blank');
        }

        function openEntertainmentManagement() {
            window.open('entertainment-management.php', '_blank');
        }
    </script>
</body>
</html>
