<?php
/**
 * 用户退出登录API
 */

session_start();
require_once __DIR__ . '/../classes/Auth.php';

header('Content-Type: application/json');

try {
    $auth = new Auth();
    
    // 执行退出登录
    $result = $auth->logout();
    
    if ($result['success']) {
        // 如果是AJAX请求，返回JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            echo json_encode($result);
        } else {
            // 否则重定向到首页
            header('Location: ../index.php');
        }
    } else {
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    error_log("退出登录错误: " . $e->getMessage());
    
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        echo json_encode([
            'success' => false,
            'message' => '退出登录失败，请稍后重试'
        ]);
    } else {
        header('Location: ../index.php');
    }
}
?>
