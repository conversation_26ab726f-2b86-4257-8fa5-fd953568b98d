<?php
/**
 * 服务器环境诊断脚本
 * 上传到服务器根目录，通过浏览器访问来诊断问题
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html><head><meta charset='UTF-8'><title>服务器诊断</title>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .section{margin:20px 0;padding:15px;border:1px solid #ddd;}</style>\n";
echo "</head><body>\n";

echo "<h1>比特熊智慧系统 - 服务器环境诊断</h1>\n";

// 1. PHP环境检查
echo "<div class='section'>\n";
echo "<h2>1. PHP环境信息</h2>\n";
echo "<p class='info'>PHP版本: " . PHP_VERSION . "</p>\n";
echo "<p class='info'>操作系统: " . PHP_OS . "</p>\n";
echo "<p class='info'>服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</p>\n";

// 检查关键扩展
$extensions = ['pdo', 'pdo_mysql', 'pdo_sqlite', 'mysqli', 'curl', 'json', 'mbstring', 'openssl'];
echo "<h3>PHP扩展检查:</h3>\n";
foreach ($extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? "<span class='success'>✅</span>" : "<span class='error'>❌</span>";
    echo "<p>{$status} {$ext}: " . ($loaded ? "已安装" : "未安装") . "</p>\n";
}
echo "</div>\n";

// 2. 文件系统检查
echo "<div class='section'>\n";
echo "<h2>2. 文件系统检查</h2>\n";
echo "<p class='info'>当前目录: " . __DIR__ . "</p>\n";
echo "<p class='info'>脚本路径: " . __FILE__ . "</p>\n";

$checkDirs = ['config', 'classes', 'api', 'database', 'assets'];
foreach ($checkDirs as $dir) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    $status = $exists ? "<span class='success'>✅</span>" : "<span class='error'>❌</span>";
    echo "<p>{$status} 目录 {$dir}: " . ($exists ? "存在" : "不存在");
    if ($exists) {
        echo " (可写: " . ($writable ? "是" : "否") . ")";
    }
    echo "</p>\n";
}

$checkFiles = ['config/database.php', 'classes/Auth.php', 'api/login.php', 'index.php'];
foreach ($checkFiles as $file) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $status = $exists ? "<span class='success'>✅</span>" : "<span class='error'>❌</span>";
    echo "<p>{$status} 文件 {$file}: " . ($exists ? "存在" : "不存在");
    if ($exists) {
        echo " (可读: " . ($readable ? "是" : "否") . ")";
    }
    echo "</p>\n";
}
echo "</div>\n";

// 3. 数据库连接测试
echo "<div class='section'>\n";
echo "<h2>3. 数据库连接测试</h2>\n";

if (file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        echo "<p class='success'>✅ 数据库配置文件加载成功</p>\n";
        
        try {
            $db = DatabaseConfig::getInstance();
            $connection = $db->getConnection();
            echo "<p class='success'>✅ 数据库连接成功</p>\n";
            
            // 检查数据库驱动
            $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
            echo "<p class='info'>数据库驱动: {$driver}</p>\n";
            
            // 检查数据库名
            if ($driver === 'mysql') {
                $dbName = $connection->query("SELECT DATABASE() as db")->fetch();
                echo "<p class='info'>当前数据库: " . ($dbName['db'] ?? '未选择') . "</p>\n";
                
                // 检查表
                $tables = $connection->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                echo "<p class='info'>数据库表数量: " . count($tables) . "</p>\n";
                
                if (in_array('users', $tables)) {
                    $userCount = $connection->query("SELECT COUNT(*) as count FROM users")->fetch();
                    echo "<p class='info'>用户数量: {$userCount['count']}</p>\n";
                } else {
                    echo "<p class='error'>❌ users表不存在</p>\n";
                }
                
                if (in_array('user_roles', $tables)) {
                    $roleCount = $connection->query("SELECT COUNT(*) as count FROM user_roles")->fetch();
                    echo "<p class='info'>角色数量: {$roleCount['count']}</p>\n";
                } else {
                    echo "<p class='error'>❌ user_roles表不存在</p>\n";
                }
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据库配置加载失败: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p class='error'>❌ 数据库配置文件不存在</p>\n";
}
echo "</div>\n";

// 4. 权限检查
echo "<div class='section'>\n";
echo "<h2>4. 权限和安全检查</h2>\n";
echo "<p class='info'>当前用户: " . get_current_user() . "</p>\n";
echo "<p class='info'>进程ID: " . getmypid() . "</p>\n";

// 检查session
if (function_exists('session_start')) {
    echo "<p class='success'>✅ Session功能可用</p>\n";
} else {
    echo "<p class='error'>❌ Session功能不可用</p>\n";
}

// 检查错误日志
$errorLog = ini_get('error_log');
echo "<p class='info'>错误日志位置: " . ($errorLog ?: '未设置') . "</p>\n";
echo "</div>\n";

// 5. 网络和URL检查
echo "<div class='section'>\n";
echo "<h2>5. 网络和URL信息</h2>\n";
echo "<p class='info'>服务器名: " . ($_SERVER['SERVER_NAME'] ?? '未知') . "</p>\n";
echo "<p class='info'>请求URI: " . ($_SERVER['REQUEST_URI'] ?? '未知') . "</p>\n";
echo "<p class='info'>HTTP Host: " . ($_SERVER['HTTP_HOST'] ?? '未知') . "</p>\n";
echo "<p class='info'>HTTPS: " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? '是' : '否') . "</p>\n";
echo "</div>\n";

echo "<div class='section'>\n";
echo "<h2>6. 建议的下一步操作</h2>\n";
echo "<ol>\n";
echo "<li>将此页面的完整输出发送给技术支持</li>\n";
echo "<li>检查服务器错误日志</li>\n";
echo "<li>确认数据库配置是否正确</li>\n";
echo "<li>测试登录功能</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "</body></html>\n";
?>
