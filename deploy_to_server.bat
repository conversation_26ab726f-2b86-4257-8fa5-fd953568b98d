@echo off
chcp 65001 >nul
echo =========================================
echo 比特熊智慧系统 - 腾讯云部署助手
echo =========================================
echo.

:: 服务器配置
set SERVER_IP=*************
set SERVER_USER=root
set PROJECT_DIR=/www/wwwroot/www.bitbear.top
set DB_NAME=bitbear_website

echo 🚀 部署准备检查
echo.
echo 服务器信息:
echo   IP地址: %SERVER_IP%
echo   用户名: %SERVER_USER%
echo   项目目录: %PROJECT_DIR%
echo   数据库: %DB_NAME%
echo.

echo 📋 部署步骤概览:
echo   1. 连接服务器测试
echo   2. 检查服务器环境
echo   3. 配置数据库
echo   4. 上传项目文件
echo   5. 设置权限
echo   6. 配置Web服务器
echo   7. 测试部署结果
echo.

pause
echo.

:MENU
echo =========================================
echo 请选择要执行的操作:
echo =========================================
echo.
echo 1. 连接服务器 (测试连接)
echo 2. 检查服务器环境
echo 3. 上传项目文件 (使用SCP)
echo 4. 配置数据库
echo 5. 设置文件权限
echo 6. 查看部署状态
echo 7. 完整自动部署
echo 8. 查看部署指南
echo 9. 退出
echo.
set /p choice=请输入选项 (1-9): 

if "%choice%"=="1" goto CONNECT_TEST
if "%choice%"=="2" goto CHECK_ENV
if "%choice%"=="3" goto UPLOAD_FILES
if "%choice%"=="4" goto SETUP_DB
if "%choice%"=="5" goto SET_PERMISSIONS
if "%choice%"=="6" goto CHECK_STATUS
if "%choice%"=="7" goto AUTO_DEPLOY
if "%choice%"=="8" goto SHOW_GUIDE
if "%choice%"=="9" goto EXIT

echo 无效选项，请重新选择
goto MENU

:CONNECT_TEST
echo.
echo 🔗 测试服务器连接...
echo.
echo 正在连接到 %SERVER_IP%...
echo 密码: ZbDX7%=]?H2(LAUz
echo.
ssh %SERVER_USER%@%SERVER_IP% "echo '连接测试成功'; uname -a; date"
echo.
pause
goto MENU

:CHECK_ENV
echo.
echo 🔍 检查服务器环境...
echo.
echo 创建环境检查脚本...
(
echo #!/bin/bash
echo echo "=== 服务器环境检查 ==="
echo echo "1. 系统信息:"
echo uname -a
echo echo "2. PHP版本:"
echo php -v ^| head -1
echo echo "3. MySQL服务状态:"
echo systemctl is-active mysql
echo echo "4. Nginx服务状态:"
echo systemctl is-active nginx
echo echo "5. 项目目录:"
echo ls -la %PROJECT_DIR% 2^>^/dev^/null ^|^| echo "目录不存在"
echo echo "6. 磁盘空间:"
echo df -h /
echo echo "=== 检查完成 ==="
) > server_check.sh

echo 上传检查脚本到服务器...
scp server_check.sh %SERVER_USER%@%SERVER_IP%:/tmp/
echo.
echo 执行环境检查...
ssh %SERVER_USER%@%SERVER_IP% "bash /tmp/server_check.sh"
echo.
del server_check.sh
pause
goto MENU

:UPLOAD_FILES
echo.
echo 📁 上传项目文件...
echo.
echo 警告: 这将覆盖服务器上的现有文件
set /p confirm=确认继续? (y/N): 
if /i not "%confirm%"=="y" goto MENU

echo.
echo 正在上传文件到服务器...
echo 目标目录: %PROJECT_DIR%
echo.

:: 创建排除文件列表
(
echo .git
echo .gitignore
echo node_modules
echo *.log
echo .env
echo .DS_Store
echo Thumbs.db
echo *.tmp
echo *.bak
echo putty
echo deploy_to_server.bat
echo connect-to-server.bat
) > exclude_list.txt

:: 使用SCP上传文件
echo 开始上传...
scp -r -o StrictHostKeyChecking=no . %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/

if %errorlevel% equ 0 (
    echo ✅ 文件上传完成
) else (
    echo ❌ 文件上传失败
)

del exclude_list.txt
echo.
pause
goto MENU

:SETUP_DB
echo.
echo 🗄️ 配置数据库...
echo.
echo 创建数据库配置脚本...
(
echo #!/bin/bash
echo echo "配置数据库 %DB_NAME%..."
echo mysql -u root -p309290133q -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
echo mysql -u root -p309290133q -e "SHOW DATABASES;" ^| grep %DB_NAME%
echo if [ $? -eq 0 ]; then
echo     echo "✅ 数据库 %DB_NAME% 配置成功"
echo else
echo     echo "❌ 数据库配置失败"
echo fi
echo echo "导入数据库结构..."
echo cd %PROJECT_DIR%
echo if [ -f "database/init.sql" ]; then
echo     mysql -u root -p309290133q %DB_NAME% ^< database/init.sql
echo     echo "✅ 基础表结构导入完成"
echo fi
echo if [ -f "database/create_community_tables.sql" ]; then
echo     mysql -u root -p309290133q %DB_NAME% ^< database/create_community_tables.sql
echo     echo "✅ 社区表结构导入完成"
echo fi
) > setup_db.sh

scp setup_db.sh %SERVER_USER%@%SERVER_IP%:/tmp/
ssh %SERVER_USER%@%SERVER_IP% "bash /tmp/setup_db.sh"
del setup_db.sh
echo.
pause
goto MENU

:SET_PERMISSIONS
echo.
echo ⚙️ 设置文件权限...
echo.
(
echo #!/bin/bash
echo cd %PROJECT_DIR%
echo echo "设置文件所有者为 www-data..."
echo chown -R www-data:www-data .
echo echo "设置目录权限为 755..."
echo find . -type d -exec chmod 755 {} \;
echo echo "设置文件权限为 644..."
echo find . -type f -exec chmod 644 {} \;
echo echo "设置uploads目录可写权限..."
echo chmod -R 777 uploads/ 2^>^/dev^/null ^|^| echo "uploads目录不存在"
echo echo "✅ 权限设置完成"
) > set_permissions.sh

scp set_permissions.sh %SERVER_USER%@%SERVER_IP%:/tmp/
ssh %SERVER_USER%@%SERVER_IP% "bash /tmp/set_permissions.sh"
del set_permissions.sh
echo.
pause
goto MENU

:CHECK_STATUS
echo.
echo 📊 检查部署状态...
echo.
ssh %SERVER_USER%@%SERVER_IP% "cd %PROJECT_DIR% && php test_db_connection.php 2>/dev/null || echo '数据库连接测试文件不存在'"
echo.
echo 测试网站访问:
echo   http://%SERVER_IP%
echo   http://www.bitbear.top
echo.
pause
goto MENU

:AUTO_DEPLOY
echo.
echo 🚀 开始完整自动部署...
echo.
echo 警告: 这将执行完整的部署流程
set /p confirm=确认开始自动部署? (y/N): 
if /i not "%confirm%"=="y" goto MENU

echo.
echo 步骤 1/5: 检查服务器环境...
call :CHECK_ENV_SILENT

echo.
echo 步骤 2/5: 上传项目文件...
call :UPLOAD_FILES_SILENT

echo.
echo 步骤 3/5: 配置数据库...
call :SETUP_DB_SILENT

echo.
echo 步骤 4/5: 设置文件权限...
call :SET_PERMISSIONS_SILENT

echo.
echo 步骤 5/5: 重启服务...
ssh %SERVER_USER%@%SERVER_IP% "systemctl restart nginx && systemctl restart php8.1-fpm"

echo.
echo ✅ 自动部署完成！
echo.
echo 🌐 请访问以下地址测试:
echo   http://%SERVER_IP%
echo   http://www.bitbear.top
echo.
pause
goto MENU

:SHOW_GUIDE
echo.
echo 📖 部署指南
echo.
echo 详细部署指南请查看:
echo   - 部署检查清单.md
echo   - 服务器部署指南.md
echo.
echo 快速部署步骤:
echo   1. 选择选项1测试连接
echo   2. 选择选项7执行自动部署
echo   3. 手动配置Nginx (如需要)
echo   4. 测试网站功能
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 感谢使用比特熊部署助手！
echo.
pause
exit /b 0

:: 静默执行函数
:CHECK_ENV_SILENT
ssh %SERVER_USER%@%SERVER_IP% "php -v | head -1; systemctl is-active nginx; systemctl is-active mysql"
exit /b 0

:UPLOAD_FILES_SILENT
scp -r -q . %SERVER_USER%@%SERVER_IP%:%PROJECT_DIR%/
exit /b 0

:SETUP_DB_SILENT
ssh %SERVER_USER%@%SERVER_IP% "mysql -u root -p309290133q -e 'CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;'"
exit /b 0

:SET_PERMISSIONS_SILENT
ssh %SERVER_USER%@%SERVER_IP% "cd %PROJECT_DIR% && chown -R www-data:www-data . && find . -type d -exec chmod 755 {} \; && find . -type f -exec chmod 644 {} \;"
exit /b 0
