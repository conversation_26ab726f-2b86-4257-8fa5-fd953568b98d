@echo off
chcp 65001 >nul
echo ========================================
echo 比特熊智慧系统快速部署工具
echo ========================================
echo.

echo 1. 导出本地数据库数据...
php export_local_data.php
echo.

echo 2. 检查导出文件...
if exist "database_export\import_to_server.sql" (
    echo ✓ 数据导出成功
    echo   文件位置: database_export\import_to_server.sql
) else (
    echo ✗ 数据导出失败
    pause
    exit /b 1
)
echo.

echo 3. 显示服务器部署指令...
echo.
echo ========================================
echo 服务器部署步骤:
echo ========================================
echo.
echo 1. 使用PuTTY连接服务器:
echo    IP: *************
echo    用户: root
echo    密码: ZbDX7%=]?H2(LAUz
echo.
echo 2. 检查项目文件:
echo    cd /www/wwwroot/www.bitbear.top
echo    ls -la
echo.
echo 3. 设置权限:
echo    chown -R www-data:www-data /www/wwwroot/www.bitbear.top
echo    chmod -R 755 /www/wwwroot/www.bitbear.top
echo    chmod -R 777 /www/wwwroot/www.bitbear.top/uploads
echo.
echo 4. 配置数据库:
echo    mysql -u root -p309290133q
echo    CREATE DATABASE IF NOT EXISTS bitbear_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
echo    USE bitbear_website;
echo    EXIT;
echo.
echo 5. 导入数据库结构:
echo    cd /www/wwwroot/www.bitbear.top
echo    mysql -u root -p309290133q bitbear_website ^< database/init.sql
echo.
echo 6. 导入本地数据 (将 import_to_server.sql 上传到服务器后):
echo    mysql -u root -p309290133q bitbear_website ^< import_to_server.sql
echo.
echo 7. 配置Nginx (创建 /etc/nginx/sites-available/www.bitbear.top):
echo    server {
echo        listen 80;
echo        server_name www.bitbear.top *************;
echo        root /www/wwwroot/www.bitbear.top;
echo        index index.php index.html;
echo        location / { try_files $uri $uri/ /index.php?$query_string; }
echo        location ~ \.php$ {
echo            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
echo            fastcgi_index index.php;
echo            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
echo            include fastcgi_params;
echo        }
echo    }
echo.
echo 8. 启用站点:
echo    ln -s /etc/nginx/sites-available/www.bitbear.top /etc/nginx/sites-enabled/
echo    nginx -t
echo    systemctl reload nginx
echo.
echo 9. 重启服务:
echo    systemctl restart nginx
echo    systemctl restart php8.1-fpm
echo.
echo 10. 测试访问:
echo     http://*************
echo     http://www.bitbear.top
echo.
echo ========================================
echo 部署完成后访问地址:
echo ========================================
echo 网站首页: http://*************
echo 管理后台: http://*************/admin/
echo 默认账号: admin
echo 默认密码: admin123
echo ========================================
echo.

echo 4. 打开导出目录...
if exist "database_export" (
    explorer database_export
)

echo.
echo 部署准备完成！
echo 请按照上述步骤在服务器上执行部署。
echo.
pause
