<?php
/**
 * 清理并重新设置导航栏数据
 */

require_once 'config/database.php';

try {
    echo "开始清理导航栏数据...\n";
    
    $db = db();
    
    // 清空现有导航栏数据
    $db->execute('DELETE FROM navbar_items');
    echo "已清空现有导航栏数据\n";
    
    // 重置自增ID
    $db->execute('ALTER TABLE navbar_items AUTO_INCREMENT = 1');
    echo "已重置自增ID\n";
    
    // 插入新的导航栏数据（按照参考图的样式）
    $navItems = [
        // 主菜单项
        ['name' => '首页', 'url' => '/index.php', 'type' => 'link', 'parent_id' => null, 'sort_order' => 1],
        ['name' => '组织', 'url' => '#', 'type' => 'dropdown', 'parent_id' => null, 'sort_order' => 2],
        ['name' => '主管领导', 'url' => '/leaders.php', 'type' => 'link', 'parent_id' => null, 'sort_order' => 3],
        ['name' => '分管领导', 'url' => '#', 'type' => 'dropdown', 'parent_id' => null, 'sort_order' => 4],
        ['name' => '课程', 'url' => '#', 'type' => 'dropdown', 'parent_id' => null, 'sort_order' => 5],
        ['name' => '游戏', 'url' => '#', 'type' => 'dropdown', 'parent_id' => null, 'sort_order' => 6],
    ];
    
    // 插入主菜单项
    foreach ($navItems as $item) {
        $sql = "INSERT INTO navbar_items (name, url, type, parent_id, sort_order, visible) VALUES (?, ?, ?, ?, ?, 1)";
        $db->query($sql, [$item['name'], $item['url'], $item['type'], $item['parent_id'], $item['sort_order']]);
        echo "已插入主菜单: {$item['name']}\n";
    }
    
    // 获取主菜单项的ID
    $items = $db->fetchAll('SELECT id, name FROM navbar_items WHERE parent_id IS NULL ORDER BY sort_order');
    $itemIds = [];
    foreach ($items as $item) {
        $itemIds[$item['name']] = $item['id'];
    }
    
    // 插入子菜单项
    $subItems = [
        // 组织的子菜单
        ['name' => '关于我们', 'url' => '/about.php', 'parent' => '组织', 'sort_order' => 1],
        ['name' => '团队介绍', 'url' => '/team.php', 'parent' => '组织', 'sort_order' => 2],
        ['name' => '联系我们', 'url' => '/contact.php', 'parent' => '组织', 'sort_order' => 3],
        
        // 分管领导的子菜单
        ['name' => '技术负责人', 'url' => '/leaders/tech.php', 'parent' => '分管领导', 'sort_order' => 1],
        ['name' => '运营负责人', 'url' => '/leaders/ops.php', 'parent' => '分管领导', 'sort_order' => 2],
        ['name' => '产品负责人', 'url' => '/leaders/product.php', 'parent' => '分管领导', 'sort_order' => 3],
        
        // 课程的子菜单
        ['name' => '在线课程', 'url' => '/courses/online.php', 'parent' => '课程', 'sort_order' => 1],
        ['name' => '课程超市', 'url' => '/courses/market.php', 'parent' => '课程', 'sort_order' => 2],
        ['name' => '程序设计', 'url' => '/courses/programming.php', 'parent' => '课程', 'sort_order' => 3],
        
        // 游戏的子菜单
        ['name' => '联机游戏', 'url' => '/games/online.php', 'parent' => '游戏', 'sort_order' => 1],
        ['name' => '单机游戏', 'url' => '/games/single.php', 'parent' => '游戏', 'sort_order' => 2],
        ['name' => '游戏社区', 'url' => '/games/community.php', 'parent' => '游戏', 'sort_order' => 3],
    ];
    
    // 插入子菜单项
    foreach ($subItems as $item) {
        if (isset($itemIds[$item['parent']])) {
            $sql = "INSERT INTO navbar_items (name, url, type, parent_id, sort_order, visible) VALUES (?, ?, 'submenu', ?, ?, 1)";
            $db->query($sql, [$item['name'], $item['url'], $itemIds[$item['parent']], $item['sort_order']]);
            echo "已插入子菜单: {$item['name']} (父级: {$item['parent']})\n";
        }
    }
    
    echo "\n导航栏数据清理完成！\n";
    
    // 显示最终结果
    echo "\n最终导航栏结构:\n";
    $allItems = $db->fetchAll('SELECT * FROM navbar_items ORDER BY sort_order ASC, id ASC');
    foreach ($allItems as $item) {
        $parentInfo = $item['parent_id'] ? " (父级ID: {$item['parent_id']})" : "";
        echo sprintf("- ID: %d, 名称: %s, URL: %s, 类型: %s%s\n", 
            $item['id'], $item['name'], $item['url'], $item['type'], $parentInfo);
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    exit(1);
}
?>
