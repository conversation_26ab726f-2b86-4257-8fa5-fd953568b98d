<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>娱乐管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ec4899;
            --secondary-color: #db2777;
            --accent-color: #f472b6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: var(--primary-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .balance-section {
            background: var(--primary-color);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .balance-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .balance-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .balance-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .balance-icon.study {
            color: #3b82f6;
        }

        .balance-icon.entertainment {
            color: #ec4899;
        }

        .balance-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .balance-label {
            color: #666;
            font-size: 0.9rem;
        }

        .balance-ratio {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            min-width: 200px;
        }

        .ratio-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .ratio-bar {
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            display: flex;
            margin-bottom: 0.5rem;
        }

        .ratio-study {
            background: var(--primary-color);
            height: 100%;
        }

        .ratio-entertainment {
            background: var(--primary-color);
            height: 100%;
        }

        .ratio-text {
            color: #495057;
            font-size: 0.9rem;
        }

        .controls-section {
            background: var(--primary-color);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .activity-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .tab-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .tab-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-left: auto;
        }

        .btn-primary {
            background: rgba(236, 72, 153, 0.8);
            border: 1px solid rgba(236, 72, 153, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(236, 72, 153, 0.9);
            border-color: rgba(236, 72, 153, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: var(--primary-color);
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .activity-card {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .activity-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .activity-card.gaming::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .activity-card.sports::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .activity-card.social::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .activity-card.media::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .activity-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 20px;
        }

        .activity-details {
            flex: 1;
        }

        .activity-name {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .activity-category {
            color: #666;
            font-size: 0.9rem;
        }

        .activity-duration {
            background: rgba(255, 255, 255, 0.2);
            color: #333;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .activity-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .activity-stat {
            text-align: center;
        }

        .activity-stat-number {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.25rem;
        }

        .activity-stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        .activity-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        .recommendations-section {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recommendation-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .recommendation-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .recommendation-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 16px;
        }

        .recommendation-content {
            flex: 1;
        }

        .recommendation-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .recommendation-description {
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .balance-indicator {
                flex-direction: column;
                gap: 1rem;
            }
            
            .activities-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .activity-tabs {
                justify-content: center;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
            
            .activity-stats {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-gamepad me-2"></i>
                娱乐管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">娱乐管理</h1>
            <p class="page-description">休闲活动记录、娱乐时间统计、学习娱乐平衡分析</p>
        </div>

        <!-- 学习娱乐平衡指示器 -->
        <div class="balance-section">
            <div class="balance-indicator">
                <div class="balance-item">
                    <div class="balance-icon study">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="balance-number">6.5</div>
                    <div class="balance-label">今日学习(小时)</div>
                </div>
                
                <div class="balance-ratio">
                    <div class="ratio-title">学习娱乐比例</div>
                    <div class="ratio-bar">
                        <div class="ratio-study" style="width: 70%"></div>
                        <div class="ratio-entertainment" style="width: 30%"></div>
                    </div>
                    <div class="ratio-text">学习 70% : 娱乐 30%</div>
                </div>
                
                <div class="balance-item">
                    <div class="balance-icon entertainment">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <div class="balance-number">2.8</div>
                    <div class="balance-label">今日娱乐(小时)</div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="activity-tabs">
                    <button class="tab-btn active" onclick="switchTab('all')">全部活动</button>
                    <button class="tab-btn" onclick="switchTab('gaming')">游戏娱乐</button>
                    <button class="tab-btn" onclick="switchTab('sports')">运动健身</button>
                    <button class="tab-btn" onclick="switchTab('social')">社交活动</button>
                    <button class="tab-btn" onclick="switchTab('media')">影音娱乐</button>
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addActivity()">
                        <i class="fas fa-plus me-1"></i>
                        记录活动
                    </button>
                    <button class="btn btn-primary" onclick="setBreakReminder()">
                        <i class="fas fa-bell me-1"></i>
                        休息提醒
                    </button>
                </div>
            </div>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 娱乐活动记录 -->
            <div class="activities-grid">
                <div class="activity-card gaming">
                    <div class="activity-header">
                        <div class="activity-info">
                            <div class="activity-icon">
                                <i class="fas fa-gamepad"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-name">王者荣耀</div>
                                <div class="activity-category">手机游戏</div>
                            </div>
                        </div>
                        <div class="activity-duration">1.5小时</div>
                    </div>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <div class="activity-stat-number">5</div>
                            <div class="activity-stat-label">本周次数</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">8.5</div>
                            <div class="activity-stat-label">本周时长</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">15%</div>
                            <div class="activity-stat-label">娱乐占比</div>
                        </div>
                    </div>
                    <div class="activity-actions">
                        <button class="btn btn-primary btn-sm">查看详情</button>
                        <button class="btn btn-success btn-sm">设置限制</button>
                    </div>
                </div>

                <div class="activity-card sports">
                    <div class="activity-header">
                        <div class="activity-info">
                            <div class="activity-icon">
                                <i class="fas fa-running"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-name">晨跑</div>
                                <div class="activity-category">运动健身</div>
                            </div>
                        </div>
                        <div class="activity-duration">0.8小时</div>
                    </div>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <div class="activity-stat-number">4</div>
                            <div class="activity-stat-label">本周次数</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">3.2</div>
                            <div class="activity-stat-label">本周时长</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">6%</div>
                            <div class="activity-stat-label">娱乐占比</div>
                        </div>
                    </div>
                    <div class="activity-actions">
                        <button class="btn btn-primary btn-sm">查看详情</button>
                        <button class="btn btn-success btn-sm">制定计划</button>
                    </div>
                </div>

                <div class="activity-card media">
                    <div class="activity-header">
                        <div class="activity-info">
                            <div class="activity-icon">
                                <i class="fas fa-film"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-name">观看电影</div>
                                <div class="activity-category">影音娱乐</div>
                            </div>
                        </div>
                        <div class="activity-duration">2.0小时</div>
                    </div>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <div class="activity-stat-number">2</div>
                            <div class="activity-stat-label">本周次数</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">4.0</div>
                            <div class="activity-stat-label">本周时长</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">7%</div>
                            <div class="activity-stat-label">娱乐占比</div>
                        </div>
                    </div>
                    <div class="activity-actions">
                        <button class="btn btn-primary btn-sm">查看详情</button>
                        <button class="btn btn-success btn-sm">添加评价</button>
                    </div>
                </div>

                <div class="activity-card social">
                    <div class="activity-header">
                        <div class="activity-info">
                            <div class="activity-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="activity-details">
                                <div class="activity-name">朋友聚餐</div>
                                <div class="activity-category">社交活动</div>
                            </div>
                        </div>
                        <div class="activity-duration">3.0小时</div>
                    </div>
                    <div class="activity-stats">
                        <div class="activity-stat">
                            <div class="activity-stat-number">1</div>
                            <div class="activity-stat-label">本周次数</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">3.0</div>
                            <div class="activity-stat-label">本周时长</div>
                        </div>
                        <div class="activity-stat">
                            <div class="activity-stat-number">5%</div>
                            <div class="activity-stat-label">娱乐占比</div>
                        </div>
                    </div>
                    <div class="activity-actions">
                        <button class="btn btn-primary btn-sm">查看详情</button>
                        <button class="btn btn-success btn-sm">添加记录</button>
                    </div>
                </div>
            </div>

            <!-- 平衡建议 -->
            <div class="recommendations-section">
                <h2 class="section-title">
                    <i class="fas fa-lightbulb"></i>
                    平衡建议
                </h2>
                <div class="recommendation-list">
                    <div class="recommendation-item">
                        <div class="recommendation-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="recommendation-content">
                            <div class="recommendation-title">适度休息</div>
                            <div class="recommendation-description">建议每学习1小时休息10-15分钟，保持学习效率</div>
                        </div>
                    </div>
                    <div class="recommendation-item">
                        <div class="recommendation-icon">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="recommendation-content">
                            <div class="recommendation-title">时间平衡</div>
                            <div class="recommendation-description">当前学习娱乐比例良好，建议保持7:3的黄金比例</div>
                        </div>
                    </div>
                    <div class="recommendation-item">
                        <div class="recommendation-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="recommendation-content">
                            <div class="recommendation-title">健康娱乐</div>
                            <div class="recommendation-description">增加户外运动时间，减少长时间使用电子设备</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function switchTab(tab) {
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            // 这里可以添加筛选逻辑
        }

        function addActivity() {
            alert('记录活动功能正在开发中...');
        }

        function setBreakReminder() {
            alert('休息提醒功能正在开发中...');
        }
    </script>
</body>
</html>
