<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'includes/time_helper.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    header('Location: login.php');
    exit;
}

// 获取通知类型和分页参数
$type = $_GET['type'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $db = db();
    $pdo = $db->getConnection();
    
    // 构建查询条件
    $whereClause = "n.user_id = ?";
    $params = [$currentUser['id']];
    
    if ($type !== 'all') {
        $whereClause .= " AND n.type = ?";
        $params[] = $type;
    }
    
    // 获取通知列表
    $sql = "SELECT n.*, u.username, up.nickname, up.avatar_url
            FROM notifications n
            LEFT JOIN users u ON n.related_user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            WHERE {$whereClause}
            ORDER BY n.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取各类型未读数量
    $countSql = "SELECT type, COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0 GROUP BY type";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute([$currentUser['id']]);
    $typeCounts = [];
    $totalUnread = 0;
    
    foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $row) {
        $typeCounts[$row['type']] = (int)$row['count'];
        $totalUnread += (int)$row['count'];
    }
    
} catch (Exception $e) {
    error_log("通知页面错误: " . $e->getMessage());
    $notifications = [];
    $typeCounts = [];
    $totalUnread = 0;
}

// 通知类型配置
$notificationTypes = [
    'all' => ['name' => '全部消息', 'icon' => 'fas fa-inbox'],
    'reply' => ['name' => '回复我的', 'icon' => 'fas fa-reply'],
    'like' => ['name' => '收到的赞', 'icon' => 'fas fa-heart'],
    'follow' => ['name' => '收到的关注', 'icon' => 'fas fa-user-plus'],
    'system' => ['name' => '系统消息', 'icon' => 'fas fa-cog'],
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .notification-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .notification-header-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .notification-header-card h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .notification-header-card p {
            color: #6b7280;
            font-size: 16px;
        }
        
        .notification-main-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            min-height: 600px;
        }
        
        .notification-sidebar {
            width: 240px;
            background: #f8fafc;
            border-right: 1px solid #e5e7eb;
            padding: 20px 0;
        }
        
        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s;
            border-right: 3px solid transparent;
            position: relative;
        }
        
        .sidebar-item:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .sidebar-item.active {
            background: #dbeafe;
            color: #1e40af;
            border-right-color: #3b82f6;
            font-weight: 600;
        }
        
        .sidebar-item .badge {
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: 600;
            margin-left: auto;
        }
        
        .notification-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .content-title {
            font-size: 20px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .mark-all-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .mark-all-btn:hover {
            background: #2563eb;
        }
        
        .notification-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .notification-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            border-left: 4px solid #e5e7eb;
            transition: all 0.2s;
        }
        
        .notification-item:hover {
            background: #f1f5f9;
            transform: translateY(-1px);
        }
        
        .notification-item.unread {
            background: white;
            border-left-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        }
        
        .notification-item-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }
        
        .notification-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .notification-user {
            font-weight: 600;
            color: #1f2937;
        }
        
        .notification-time {
            color: #6b7280;
            font-size: 12px;
            margin-left: auto;
        }
        
        .notification-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        
        .notification-text {
            color: #4b5563;
            line-height: 1.5;
        }
        
        .notification-type-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .type-system { background: #dbeafe; color: #1e40af; }
        .type-like { background: #fce7f3; color: #be185d; }
        .type-comment { background: #d1fae5; color: #065f46; }
        .type-reply { background: #fef3c7; color: #92400e; }
        .type-follow { background: #e0e7ff; color: #5b21b6; }
        
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #d1d5db;
        }
        
        .empty-state h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #374151;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.2s;
            z-index: 1000;
        }
        
        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .notification-main-card {
                flex-direction: column;
            }
            
            .notification-sidebar {
                width: 100%;
                display: flex;
                overflow-x: auto;
                padding: 10px 0;
            }
            
            .sidebar-item {
                white-space: nowrap;
                border-right: none;
                border-bottom: 3px solid transparent;
            }
            
            .sidebar-item.active {
                border-right: none;
                border-bottom-color: #3b82f6;
            }
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="window.location.href='index.php'" title="返回首页">
        <i class="fas fa-arrow-left"></i>
    </button>

    <div class="notification-container">
        <!-- 头部卡片 -->
        <div class="notification-header-card">
            <h1>通知中心</h1>
            <p>查看您的通知消息和系统提醒</p>
        </div>

        <!-- 主内容卡片 -->
        <div class="notification-main-card">
            <!-- 左侧导航 -->
            <div class="notification-sidebar">
                <?php foreach ($notificationTypes as $typeKey => $typeInfo): ?>
                    <a href="?type=<?php echo $typeKey; ?>"
                       class="sidebar-item <?php echo $type === $typeKey ? 'active' : ''; ?>">
                        <i class="<?php echo $typeInfo['icon']; ?>"></i>
                        <span><?php echo $typeInfo['name']; ?></span>
                        <?php if ($typeKey === 'all' && $totalUnread > 0): ?>
                            <span class="badge"><?php echo $totalUnread; ?></span>
                        <?php elseif ($typeKey !== 'all' && isset($typeCounts[$typeKey]) && $typeCounts[$typeKey] > 0): ?>
                            <span class="badge"><?php echo $typeCounts[$typeKey]; ?></span>
                        <?php endif; ?>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- 右侧内容 -->
            <div class="notification-content">
                <div class="content-header">
                    <h2 class="content-title">
                        <?php echo $notificationTypes[$type]['name']; ?>
                        <?php if ($type === 'all' && $totalUnread > 0): ?>
                            <span style="color: #ef4444; font-size: 14px;">(<?php echo $totalUnread; ?>条未读)</span>
                        <?php elseif ($type !== 'all' && isset($typeCounts[$type]) && $typeCounts[$type] > 0): ?>
                            <span style="color: #ef4444; font-size: 14px;">(<?php echo $typeCounts[$type]; ?>条未读)</span>
                        <?php endif; ?>
                    </h2>
                    <?php if (!empty($notifications)): ?>
                        <button class="mark-all-btn" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> 全部已读
                        </button>
                    <?php endif; ?>
                </div>

                <div class="notification-list">
                    <?php if (empty($notifications)): ?>
                        <div class="empty-state">
                            <i class="fas fa-bell-slash"></i>
                            <h3>暂无通知</h3>
                            <p>您目前没有收到任何通知消息</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($notifications as $notification): ?>
                            <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>"
                                 data-id="<?php echo $notification['id']; ?>">

                                <div class="notification-item-header">
                                    <?php if (!empty($notification['avatar_url'])): ?>
                                        <img src="<?php echo htmlspecialchars($notification['avatar_url']); ?>"
                                             alt="头像" class="notification-avatar">
                                    <?php else: ?>
                                        <img src="assets/images/default-avatar.png"
                                             alt="头像" class="notification-avatar">
                                    <?php endif; ?>

                                    <?php if (!empty($notification['nickname']) || !empty($notification['username'])): ?>
                                        <span class="notification-user">
                                            <?php echo htmlspecialchars($notification['nickname'] ?: $notification['username']); ?>
                                        </span>
                                    <?php endif; ?>

                                    <span class="notification-time">
                                        <?php echo timeAgo($notification['created_at']); ?>
                                    </span>
                                </div>

                                <div class="notification-title">
                                    <?php echo htmlspecialchars($notification['title']); ?>
                                </div>

                                <div class="notification-text">
                                    <?php echo htmlspecialchars($notification['content']); ?>
                                </div>

                                <div class="notification-type-badge type-<?php echo $notification['type']; ?>">
                                    <i class="<?php echo $notificationTypes[$notification['type']]['icon'] ?? 'fas fa-bell'; ?>"></i>
                                    <?php echo $notificationTypes[$notification['type']]['name'] ?? '通知'; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标记所有通知为已读
        function markAllAsRead() {
            const type = '<?php echo $type; ?>';

            fetch('api/notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'mark_all_read',
                    type: type === 'all' ? null : type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }

        // 点击通知项标记为已读
        document.querySelectorAll('.notification-item.unread').forEach(item => {
            item.addEventListener('click', function() {
                const notificationId = this.dataset.id;

                fetch('api/notifications.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'mark_read',
                        id: notificationId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.classList.remove('unread');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    </script>
</body>
</html>
