<?php
/**
 * 云服务器注册问题修复脚本
 * 专门解决云服务器环境下的用户注册问题
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器注册修复 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .step.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .step.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .step.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #ff6b6b;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #ee5a24;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.success:hover {
            background: #059669;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 云服务器注册修复</h1>
            <p>专门解决云服务器环境下的用户注册问题</p>
        </div>
        
        <div class="content">
            <?php
            
            $fixActions = [];
            
            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<div class='step success'>";
                echo "<h3>✅ 数据库连接成功</h3>";
                echo "<p>已成功连接到数据库</p>";
                echo "</div>";
                
                // 1. 强制重建用户角色表
                echo "<div class='step info'>";
                echo "<h3>🔧 修复用户角色表</h3>";
                
                try {
                    // 删除并重建用户角色表
                    $db->exec("DROP TABLE IF EXISTS user_roles");
                    $db->exec("
                        CREATE TABLE user_roles (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            role_code VARCHAR(50) NOT NULL UNIQUE,
                            role_name VARCHAR(100) NOT NULL,
                            description TEXT,
                            permissions TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        )
                    ");
                    
                    // 插入默认角色
                    $db->exec("
                        INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                        ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                        ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                        ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                    ");
                    
                    echo "<p>✅ 用户角色表重建成功</p>";
                    $fixActions[] = "重建用户角色表";
                    
                } catch (Exception $e) {
                    echo "<p>❌ 用户角色表修复失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                // 2. 检查并修复用户表
                echo "<div class='step info'>";
                echo "<h3>🔧 检查用户表结构</h3>";
                
                try {
                    // 检查用户表是否存在
                    $result = $db->query("SHOW TABLES LIKE 'users'")->fetch();
                    if (!$result) {
                        // 创建用户表
                        $db->exec("
                            CREATE TABLE users (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                username VARCHAR(50) NOT NULL UNIQUE,
                                email VARCHAR(100) NOT NULL UNIQUE,
                                password_hash VARCHAR(255) NOT NULL,
                                full_name VARCHAR(100),
                                avatar VARCHAR(255),
                                role_id INT DEFAULT 3,
                                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                                last_login TIMESTAMP NULL,
                                login_count INT DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
                            )
                        ");
                        echo "<p>✅ 用户表创建成功</p>";
                        $fixActions[] = "创建用户表";
                    } else {
                        echo "<p>✅ 用户表已存在</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p>❌ 用户表检查失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                // 3. 检查并修复用户资料表
                echo "<div class='step info'>";
                echo "<h3>🔧 检查用户资料表结构</h3>";
                
                try {
                    // 检查用户资料表是否存在
                    $result = $db->query("SHOW TABLES LIKE 'user_profiles'")->fetch();
                    if (!$result) {
                        // 创建用户资料表
                        $db->exec("
                            CREATE TABLE user_profiles (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                user_id INT NOT NULL,
                                nickname VARCHAR(100),
                                bio TEXT,
                                signature VARCHAR(500),
                                avatar_url VARCHAR(255),
                                location VARCHAR(100),
                                website VARCHAR(255),
                                social_links JSON,
                                post_count INT DEFAULT 0,
                                comment_count INT DEFAULT 0,
                                like_received_count INT DEFAULT 0,
                                reputation_score INT DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                INDEX idx_user_id (user_id)
                            )
                        ");
                        echo "<p>✅ 用户资料表创建成功</p>";
                        $fixActions[] = "创建用户资料表";
                    } else {
                        echo "<p>✅ 用户资料表已存在</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p>❌ 用户资料表检查失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                // 4. 创建上传目录
                echo "<div class='step info'>";
                echo "<h3>🔧 创建上传目录</h3>";
                
                $uploadDirs = [
                    'uploads/',
                    'uploads/avatars/',
                    'uploads/temp/'
                ];
                
                foreach ($uploadDirs as $dir) {
                    if (!is_dir($dir)) {
                        if (mkdir($dir, 0755, true)) {
                            echo "<p>✅ 创建目录: {$dir}</p>";
                            $fixActions[] = "创建目录 {$dir}";
                        } else {
                            echo "<p>❌ 创建目录失败: {$dir}</p>";
                        }
                    } else {
                        echo "<p>✅ 目录已存在: {$dir}</p>";
                    }
                    
                    // 设置目录权限
                    if (is_dir($dir)) {
                        chmod($dir, 0755);
                        echo "<p>✅ 设置目录权限: {$dir}</p>";
                    }
                }
                echo "</div>";
                
                // 5. 测试完整注册流程
                echo "<div class='step info'>";
                echo "<h3>🧪 测试完整注册流程</h3>";
                
                try {
                    $testData = [
                        'username' => 'cloudtest_' . time(),
                        'email' => 'cloudtest_' . time() . '@example.com',
                        'nickname' => '云测试用户',
                        'password' => 'test123456'
                    ];
                    
                    echo "<div class='code-block'>";
                    echo "测试数据:\n";
                    echo "用户名: " . $testData['username'] . "\n";
                    echo "邮箱: " . $testData['email'] . "\n";
                    echo "昵称: " . $testData['nickname'] . "\n";
                    echo "</div>";
                    
                    // 开始事务
                    $db->exec("BEGIN");
                    
                    // 获取普通用户角色ID
                    $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
                    $roleId = $userRole ? $userRole['id'] : 3;
                    
                    // 创建用户记录
                    $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
                    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
                    
                    $stmt = $db->prepare($sql);
                    $result = $stmt->execute([
                        $testData['username'],
                        $testData['email'],
                        $passwordHash,
                        $testData['nickname'],
                        $roleId
                    ]);
                    
                    if ($result) {
                        $userId = $db->lastInsertId();
                        echo "<p>✅ 用户创建成功，ID: {$userId}</p>";
                        
                        // 创建用户资料
                        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                        
                        $profileStmt = $db->prepare($profileSql);
                        $profileResult = $profileStmt->execute([
                            $userId,
                            $testData['nickname'],
                            'assets/images/default-avatar.png'
                        ]);
                        
                        if ($profileResult) {
                            echo "<p>✅ 用户资料创建成功</p>";
                            
                            // 清理测试数据
                            $db->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                            $db->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
                            echo "<p>✅ 测试数据清理完成</p>";
                            
                            echo "<p><strong>🎉 注册流程测试成功！</strong></p>";
                            $fixActions[] = "注册流程测试通过";
                            
                        } else {
                            echo "<p>❌ 用户资料创建失败</p>";
                        }
                    } else {
                        echo "<p>❌ 用户创建失败</p>";
                    }
                    
                    // 提交事务
                    $db->exec("COMMIT");
                    
                } catch (Exception $e) {
                    $db->exec("ROLLBACK");
                    echo "<p>❌ 注册流程测试失败: " . $e->getMessage() . "</p>";
                    echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                }
                echo "</div>";
                
                // 6. 显示修复总结
                echo "<div class='step success'>";
                echo "<h3>🎯 修复总结</h3>";
                if (!empty($fixActions)) {
                    echo "<p>已完成以下修复操作：</p>";
                    echo "<ul>";
                    foreach ($fixActions as $action) {
                        echo "<li>{$action}</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p>系统检查正常，无需修复操作。</p>";
                }
                echo "<p><strong>现在可以尝试用户注册功能了！</strong></p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ 修复过程出错</h3>";
                echo "<p>错误信息: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                echo "</div>";
            }
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="register.php" class="btn success">立即测试注册</a>
                <a href="修复用户注册问题.php" class="btn">详细诊断</a>
                <a href="admin-dashboard.php" class="btn">返回管理后台</a>
            </div>
        </div>
    </div>
</body>
</html>
