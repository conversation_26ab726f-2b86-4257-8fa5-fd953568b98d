# 下载并使用PuTTY连接服务器
param(
    [string]$ServerIP = "*************",
    [string]$Username = "root",
    [string]$Password = "ZbDX7%=]?H2(LAUz"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "比特熊智慧系统 - 自动下载PuTTY并连接" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$puttyDir = Join-Path $PSScriptRoot "putty"
$plinkPath = Join-Path $puttyDir "plink.exe"
$puttyPath = Join-Path $puttyDir "putty.exe"

# 创建putty目录
if (-not (Test-Path $puttyDir)) {
    New-Item -ItemType Directory -Path $puttyDir -Force | Out-Null
    Write-Host "✓ 创建PuTTY目录: $puttyDir" -ForegroundColor Green
}

# 检查plink是否存在
if (-not (Test-Path $plinkPath)) {
    Write-Host "正在下载PuTTY工具..." -ForegroundColor Yellow
    
    try {
        # 下载plink.exe
        $plinkUrl = "https://the.earth.li/~sgtatham/putty/latest/w64/plink.exe"
        Write-Host "下载plink.exe..." -ForegroundColor Gray
        Invoke-WebRequest -Uri $plinkUrl -OutFile $plinkPath -UseBasicParsing
        Write-Host "✓ plink.exe下载完成" -ForegroundColor Green
        
        # 下载putty.exe
        $puttyUrl = "https://the.earth.li/~sgtatham/putty/latest/w64/putty.exe"
        Write-Host "下载putty.exe..." -ForegroundColor Gray
        Invoke-WebRequest -Uri $puttyUrl -OutFile $puttyPath -UseBasicParsing
        Write-Host "✓ putty.exe下载完成" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请手动下载PuTTY: https://www.putty.org/" -ForegroundColor Yellow
        Read-Host "按回车键退出"
        exit 1
    }
} else {
    Write-Host "✓ PuTTY工具已存在" -ForegroundColor Green
}

Write-Host ""
Write-Host "服务器信息:" -ForegroundColor Yellow
Write-Host "IP地址: $ServerIP" -ForegroundColor White
Write-Host "用户名: $Username" -ForegroundColor White
Write-Host "密码: $Password" -ForegroundColor Red
Write-Host ""

# 测试网络连接
Write-Host "正在测试网络连接..." -ForegroundColor Yellow
try {
    $pingResult = Test-Connection -ComputerName $ServerIP -Count 2 -Quiet -ErrorAction Stop
    if ($pingResult) {
        Write-Host "✓ 网络连接正常" -ForegroundColor Green
    } else {
        throw "Ping失败"
    }
} catch {
    Write-Host "❌ 网络连接失败" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "正在使用plink连接到服务器..." -ForegroundColor Yellow
Write-Host "连接命令: $plinkPath -ssh -batch -pw [密码] $Username@$ServerIP" -ForegroundColor Gray
Write-Host ""

try {
    # 使用plink连接，-batch参数避免交互提示，-pw参数直接指定密码
    & $plinkPath -ssh -batch -pw $Password $Username@$ServerIP
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 连接成功！" -ForegroundColor Green
    } else {
        Write-Host "❌ 连接失败，错误代码: $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 连接过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    
    # 如果plink失败，尝试启动图形界面的PuTTY
    Write-Host ""
    Write-Host "正在启动PuTTY图形界面..." -ForegroundColor Yellow
    
    try {
        # 启动PuTTY并预填服务器信息
        Start-Process -FilePath $puttyPath -ArgumentList "-ssh", "$Username@$ServerIP"
        Write-Host "✓ PuTTY已启动，请在界面中输入密码: $Password" -ForegroundColor Green
    } catch {
        Write-Host "❌ 启动PuTTY失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "连接工具已保存在: $puttyDir" -ForegroundColor Cyan
Write-Host "您可以直接运行以下命令连接服务器:" -ForegroundColor Cyan
Write-Host "$plinkPath -ssh -batch -pw $Password $Username@$ServerIP" -ForegroundColor White
Write-Host ""

Read-Host "按回车键退出"
