<?php
require_once 'config/database.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin-login.php');
    exit;
}

$currentUser = $_SESSION['admin_user'] ?? 'Admin';

// 处理管理员操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = DatabaseConfig::getInstance();
        
        if ($action === 'create') {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $parentId = intval($_POST['parent_id'] ?? 0) ?: null;
            $sortOrder = intval($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                throw new Exception('分类名称不能为空');
            }
            
            // 检查名称是否重复
            $existing = $db->fetchOne("SELECT id FROM categories WHERE name = ? AND parent_id = ?", [$name, $parentId]);
            if ($existing) {
                throw new Exception('同级分类名称不能重复');
            }
            
            $db->execute("INSERT INTO categories (name, description, parent_id, sort_order, created_at) VALUES (?, ?, ?, ?, NOW())", 
                        [$name, $description, $parentId, $sortOrder]);
            
            $success = '分类创建成功';
            
        } elseif ($action === 'update') {
            $id = intval($_POST['id'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $parentId = intval($_POST['parent_id'] ?? 0) ?: null;
            $sortOrder = intval($_POST['sort_order'] ?? 0);
            
            if ($id <= 0 || empty($name)) {
                throw new Exception('参数错误');
            }
            
            // 检查名称是否重复（排除自己）
            $existing = $db->fetchOne("SELECT id FROM categories WHERE name = ? AND parent_id = ? AND id != ?", [$name, $parentId, $id]);
            if ($existing) {
                throw new Exception('同级分类名称不能重复');
            }
            
            // 不能将分类设置为自己的子分类
            if ($parentId == $id) {
                throw new Exception('不能将分类设置为自己的子分类');
            }
            
            $db->execute("UPDATE categories SET name = ?, description = ?, parent_id = ?, sort_order = ?, updated_at = NOW() WHERE id = ?", 
                        [$name, $description, $parentId, $sortOrder, $id]);
            
            $success = '分类更新成功';
            
        } elseif ($action === 'delete') {
            $id = intval($_POST['id'] ?? 0);
            
            if ($id <= 0) {
                throw new Exception('参数错误');
            }
            
            // 检查是否有子分类
            $hasChildren = $db->fetchOne("SELECT COUNT(*) as count FROM categories WHERE parent_id = ?", [$id]);
            if ($hasChildren['count'] > 0) {
                throw new Exception('请先删除子分类');
            }
            
            // 检查是否有帖子使用此分类
            $hasPosts = $db->fetchOne("SELECT COUNT(*) as count FROM posts WHERE category_id = ?", [$id]);
            if ($hasPosts['count'] > 0) {
                throw new Exception('该分类下还有帖子，无法删除');
            }
            
            $db->execute("DELETE FROM categories WHERE id = ?", [$id]);
            
            $success = '分类删除成功';
        }
        
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

try {
    $db = DatabaseConfig::getInstance();
    
    // 获取所有分类（树形结构）
    $allCategories = $db->fetchAll("SELECT * FROM categories ORDER BY parent_id, sort_order, name");
    
    // 构建树形结构
    $categoryTree = [];
    $categoryMap = [];
    
    foreach ($allCategories as $category) {
        $categoryMap[$category['id']] = $category;
        $categoryMap[$category['id']]['children'] = [];
    }
    
    foreach ($allCategories as $category) {
        if ($category['parent_id']) {
            if (isset($categoryMap[$category['parent_id']])) {
                $categoryMap[$category['parent_id']]['children'][] = &$categoryMap[$category['id']];
            }
        } else {
            $categoryTree[] = &$categoryMap[$category['id']];
        }
    }
    
    // 获取分类统计
    $categoryStats = [];
    $stats = $db->fetchAll("
        SELECT c.id, c.name, COUNT(p.id) as post_count
        FROM categories c
        LEFT JOIN posts p ON c.id = p.category_id AND p.status = 'published'
        GROUP BY c.id
    ");
    
    foreach ($stats as $stat) {
        $categoryStats[$stat['id']] = $stat['post_count'];
    }
    
} catch (Exception $e) {
    $error = '获取分类列表失败：' . $e->getMessage();
    $categoryTree = [];
    $categoryStats = [];
}

function renderCategoryTree($categories, $level = 0) {
    global $categoryStats;
    $html = '';
    
    foreach ($categories as $category) {
        $postCount = $categoryStats[$category['id']] ?? 0;
        $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
        
        $html .= '<tr data-category-id="' . $category['id'] . '">';
        $html .= '<td>' . $indent;
        if ($level > 0) {
            $html .= '<i class="fas fa-level-up-alt fa-rotate-90 text-gray-400"></i> ';
        }
        $html .= htmlspecialchars($category['name']) . '</td>';
        $html .= '<td>' . htmlspecialchars($category['description']) . '</td>';
        $html .= '<td>' . $category['sort_order'] . '</td>';
        $html .= '<td>' . $postCount . '</td>';
        $html .= '<td>' . date('Y-m-d', strtotime($category['created_at'])) . '</td>';
        $html .= '<td>';
        $html .= '<button class="btn btn-outline btn-small edit-btn" data-id="' . $category['id'] . '">';
        $html .= '<i class="fas fa-edit"></i> 编辑';
        $html .= '</button> ';
        $html .= '<button class="btn btn-danger btn-small delete-btn" data-id="' . $category['id'] . '">';
        $html .= '<i class="fas fa-trash"></i> 删除';
        $html .= '</button>';
        $html .= '</td>';
        $html .= '</tr>';
        
        if (!empty($category['children'])) {
            $html .= renderCategoryTree($category['children'], $level + 1);
        }
    }
    
    return $html;
}

function getCategoryOptions($categories, $level = 0, $excludeId = null) {
    $options = '';
    
    foreach ($categories as $category) {
        if ($excludeId && $category['id'] == $excludeId) {
            continue;
        }
        
        $indent = str_repeat('&nbsp;&nbsp;', $level);
        $options .= '<option value="' . $category['id'] . '">' . $indent . htmlspecialchars($category['name']) . '</option>';
        
        if (!empty($category['children'])) {
            $options .= getCategoryOptions($category['children'], $level + 1, $excludeId);
        }
    }
    
    return $options;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .category-actions {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .category-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .category-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .category-table th,
        .category-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .category-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .category-table tr:hover {
            background: #f9fafb;
        }
        
        .category-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .category-modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }
        
        .category-modal h3 {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        
        .modal-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="admin-header">
                <h1>分类管理</h1>
                <div class="admin-breadcrumb">
                    <a href="admin">管理后台</a>
                    <span>/</span>
                    <span>分类管理</span>
                </div>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <div class="category-actions">
                <button class="btn btn-primary" onclick="showCreateModal()">
                    <i class="fas fa-plus"></i>
                    新建分类
                </button>
            </div>
            
            <div class="category-table">
                <table>
                    <thead>
                        <tr>
                            <th>分类名称</th>
                            <th>描述</th>
                            <th>排序</th>
                            <th>帖子数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($categoryTree)): ?>
                            <tr>
                                <td colspan="6" class="text-center text-gray-500">暂无分类</td>
                            </tr>
                        <?php else: ?>
                            <?php echo renderCategoryTree($categoryTree); ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </main>
    </div>
    
    <!-- 分类模态框 -->
    <div id="categoryModal" class="category-modal">
        <div class="category-modal-content">
            <h3 id="modalTitle">新建分类</h3>
            <form id="categoryForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="id" id="categoryId">
                
                <div class="form-group">
                    <label for="categoryName">分类名称 *</label>
                    <input type="text" name="name" id="categoryName" required>
                </div>
                
                <div class="form-group">
                    <label for="categoryDescription">描述</label>
                    <textarea name="description" id="categoryDescription" placeholder="分类描述（可选）"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="parentCategory">父分类</label>
                    <select name="parent_id" id="parentCategory">
                        <option value="">无（顶级分类）</option>
                        <?php echo getCategoryOptions($categoryTree); ?>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="sortOrder">排序</label>
                    <input type="number" name="sort_order" id="sortOrder" value="0" min="0">
                </div>
                
                <div class="modal-actions">
                    <button type="button" onclick="hideModal()" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showCreateModal() {
            document.getElementById('modalTitle').textContent = '新建分类';
            document.getElementById('formAction').value = 'create';
            document.getElementById('categoryId').value = '';
            document.getElementById('categoryForm').reset();
            document.getElementById('categoryModal').style.display = 'flex';
        }
        
        function showEditModal(id, name, description, parentId, sortOrder) {
            document.getElementById('modalTitle').textContent = '编辑分类';
            document.getElementById('formAction').value = 'update';
            document.getElementById('categoryId').value = id;
            document.getElementById('categoryName').value = name;
            document.getElementById('categoryDescription').value = description;
            document.getElementById('parentCategory').value = parentId || '';
            document.getElementById('sortOrder').value = sortOrder;
            
            // 更新父分类选项（排除当前分类）
            updateParentOptions(id);
            
            document.getElementById('categoryModal').style.display = 'flex';
        }
        
        function updateParentOptions(excludeId) {
            const select = document.getElementById('parentCategory');
            const options = select.querySelectorAll('option');
            
            options.forEach(option => {
                if (option.value == excludeId) {
                    option.style.display = 'none';
                } else {
                    option.style.display = 'block';
                }
            });
        }
        
        function hideModal() {
            document.getElementById('categoryModal').style.display = 'none';
        }
        
        function deleteCategory(id) {
            if (confirm('确定要删除这个分类吗？')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // 事件监听
        document.addEventListener('click', function(e) {
            if (e.target.closest('.edit-btn')) {
                const btn = e.target.closest('.edit-btn');
                const id = btn.dataset.id;
                const row = btn.closest('tr');
                const cells = row.querySelectorAll('td');
                
                // 从表格中提取数据（这里简化处理，实际应该从数据库获取）
                fetch(`api/category.php?action=get&id=${id}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const category = data.data;
                            showEditModal(category.id, category.name, category.description, category.parent_id, category.sort_order);
                        }
                    })
                    .catch(error => {
                        console.error('获取分类信息失败:', error);
                    });
            } else if (e.target.closest('.delete-btn')) {
                const id = e.target.closest('.delete-btn').dataset.id;
                deleteCategory(id);
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('categoryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
    </script>
</body>
</html>
