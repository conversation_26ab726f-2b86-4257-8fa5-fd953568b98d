<?php
require_once __DIR__ . '/../config/database.php';

/**
 * 用户认证和会话管理类
 */
class Auth {
    private $db;
    private $sessionTimeout = 3600; // 1小时
    
    public function __construct() {
        $this->db = DatabaseConfig::getInstance();
        $this->startSession();
    }
    
    private function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }
    
    /**
     * 用户登录
     */
    public function login($username, $password, $rememberMe = false) {
        try {
            // 查找用户（包含用户资料信息）
            $user = $this->db->fetchOne(
                "SELECT u.*, r.role_code, r.role_name, r.permissions,
                        up.nickname, up.avatar_url, up.bio
                 FROM users u
                 LEFT JOIN user_roles r ON u.role_id = r.id
                 LEFT JOIN user_profiles up ON u.id = up.user_id
                 WHERE (u.username = ? OR u.email = ?) AND u.status = 'active'",
                [$username, $username]
            );
            
            if (!$user || !password_verify($password, $user['password_hash'])) {
                $this->logActivity(null, 'login_failed', 'user', null, "登录失败: {$username}");
                return ['success' => false, 'message' => '用户名或密码错误'];
            }
            
            // 创建会话
            $sessionToken = $this->generateSessionToken();
            $expiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);
            
            $this->db->execute(
                "INSERT INTO user_sessions (user_id, session_token, ip_address, user_agent, expires_at) 
                 VALUES (?, ?, ?, ?, ?)",
                [
                    $user['id'],
                    $sessionToken,
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? '',
                    $expiresAt
                ]
            );
            
            // 更新用户登录信息
            $this->db->execute(
                "UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?",
                [$user['id']]
            );
            
            // 设置会话
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role_id'] = $user['role_id'];
            $_SESSION['role_code'] = $user['role_code'];
            $_SESSION['role_name'] = $user['role_name'];
            $_SESSION['permissions'] = json_decode($user['permissions'], true);
            $_SESSION['session_token'] = $sessionToken;
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['nickname'] = $user['nickname'];
            // 优先使用用户资料中的头像，如果没有则使用用户表中的头像，最后使用默认头像
            $_SESSION['avatar'] = $user['avatar_url'] ?? $user['avatar'] ?? 'assets/images/default-avatar.png';
            
            // 记住我功能
            if ($rememberMe) {
                setcookie('remember_token', $sessionToken, time() + (30 * 24 * 3600), '/'); // 30天
            }
            
            $this->logActivity($user['id'], 'login_success', 'user', $user['id'], '用户登录成功');
            
            return ['success' => true, 'user' => $user];
            
        } catch (Exception $e) {
            error_log("登录错误: " . $e->getMessage());
            return ['success' => false, 'message' => '登录过程中发生错误'];
        }
    }
    
    /**
     * 用户登出
     */
    public function logout() {
        try {
            if (isset($_SESSION['session_token'])) {
                // 删除数据库中的会话
                $this->db->execute(
                    "DELETE FROM user_sessions WHERE session_token = ?",
                    [$_SESSION['session_token']]
                );

                $this->logActivity($_SESSION['user_id'] ?? null, 'logout', 'user', $_SESSION['user_id'] ?? null, '用户登出');
            }

            // 清除会话
            session_destroy();

            // 清除记住我cookie
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/');
            }

            return [
                'success' => true,
                'message' => '退出登录成功'
            ];

        } catch (Exception $e) {
            error_log("退出登录失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '退出登录失败，请稍后重试'
            ];
        }
    }
    
    /**
     * 检查用户是否已登录
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_token'])) {
            return false;
        }

        try {
            // 验证会话token (使用当前时间字符串)
            $currentTime = date('Y-m-d H:i:s');
            $session = $this->db->fetchOne(
                "SELECT * FROM user_sessions WHERE session_token = ? AND expires_at > ?",
                [$_SESSION['session_token'], $currentTime]
            );

            if (!$session) {
                error_log("Session not found or expired for token: " . substr($_SESSION['session_token'], 0, 10));
                $this->logout();
                return false;
            }

            // 更新会话过期时间
            $newExpiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);
            $this->db->execute(
                "UPDATE user_sessions SET expires_at = ? WHERE session_token = ?",
                [$newExpiresAt, $_SESSION['session_token']]
            );

            return true;
        } catch (Exception $e) {
            error_log("Auth::isLoggedIn error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'email' => $_SESSION['email'] ?? '',
            'role_id' => $_SESSION['role_id'] ?? null,
            'role_code' => $_SESSION['role_code'],
            'role_name' => $_SESSION['role_name'],
            'permissions' => $_SESSION['permissions'],
            'full_name' => $_SESSION['full_name'],
            'nickname' => $_SESSION['nickname'] ?? $_SESSION['username'],
            'avatar' => $_SESSION['avatar']
        ];
    }
    
    /**
     * 检查用户权限
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $permissions = $_SESSION['permissions'] ?? [];
        
        // 超级管理员拥有所有权限
        if (in_array('*', $permissions)) {
            return true;
        }
        
        return in_array($permission, $permissions);
    }
    
    /**
     * 检查用户角色
     */
    public function hasRole($roleCode) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        return $_SESSION['role_code'] === $roleCode;
    }

    /**
     * 检查用户是否为管理员
     */
    public function isAdmin() {
        if (!$this->isLoggedIn()) {
            return false;
        }

        // 检查是否为管理员角色
        $adminRoles = ['admin', 'super_admin', 'administrator'];
        return in_array($_SESSION['role_code'], $adminRoles) || $this->hasPermission('*');
    }
    
    /**
     * 生成会话token
     */
    private function generateSessionToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * 记录用户活动
     */
    private function logActivity($userId, $action, $resourceType = null, $resourceId = null, $description = null) {
        try {
            $this->db->execute(
                "INSERT INTO activity_logs (user_id, action, resource_type, resource_id, description, ip_address, user_agent) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)",
                [
                    $userId,
                    $action,
                    $resourceType,
                    $resourceId,
                    $description,
                    $_SERVER['REMOTE_ADDR'] ?? '',
                    $_SERVER['HTTP_USER_AGENT'] ?? ''
                ]
            );
        } catch (Exception $e) {
            error_log("记录活动日志失败: " . $e->getMessage());
        }
    }
    
    /**
     * 清理过期会话
     */
    public function cleanupExpiredSessions() {
        try {
            $this->db->execute("DELETE FROM user_sessions WHERE expires_at < NOW()");
        } catch (Exception $e) {
            error_log("清理过期会话失败: " . $e->getMessage());
        }
    }
}
?>
