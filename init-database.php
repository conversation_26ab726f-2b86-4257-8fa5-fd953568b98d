<?php
/**
 * 数据库初始化脚本
 * 用于在服务器上初始化数据库和表结构
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>数据库初始化</title></head><body>";
echo "<h1>数据库初始化脚本</h1>";

try {
    // 检查环境
    $isServer = isset($_SERVER['SERVER_NAME']) && strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false;
    echo "<p>当前环境: " . ($isServer ? '服务器' : '本地') . "</p>";
    
    if ($isServer) {
        // 服务器环境 - 使用MySQL
        $host = 'localhost';
        $username = 'root';
        $password = '309290133q';
        $database = 'bitbear_website';
        
        echo "<h2>初始化MySQL数据库...</h2>";
        
        // 连接MySQL
        $dsn = "mysql:host={$host};port=3306;charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ];
        
        $pdo = new PDO($dsn, $username, $password, $options);
        echo "<p>✓ MySQL连接成功</p>";
        
        // 创建数据库
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p>✓ 数据库创建/检查完成</p>";
        
        // 连接到指定数据库
        $dsn = "mysql:host={$host};port=3306;dbname={$database};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, $options);
        
    } else {
        // 本地环境 - 使用SQLite
        echo "<h2>初始化SQLite数据库...</h2>";
        
        $db_dir = __DIR__ . '/database';
        if (!is_dir($db_dir)) {
            mkdir($db_dir, 0755, true);
        }
        
        $sqlite_path = $db_dir . '/bitbear_system.sqlite';
        $dsn = "sqlite:" . $sqlite_path;
        
        $pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "<p>✓ SQLite连接成功: {$sqlite_path}</p>";
    }
    
    // 检查现有表
    if ($isServer) {
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    } else {
        $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    }
    
    echo "<p>现有表: " . (empty($tables) ? '无' : implode(', ', $tables)) . "</p>";
    
    // 创建表结构
    echo "<h3>创建表结构...</h3>";
    
    // 用户角色表
    if (!in_array('user_roles', $tables)) {
        if ($isServer) {
            $sql = "CREATE TABLE user_roles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                role_code VARCHAR(50) UNIQUE NOT NULL,
                role_name VARCHAR(100) NOT NULL,
                description TEXT,
                permissions TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
        } else {
            $sql = "CREATE TABLE user_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                role_code TEXT UNIQUE NOT NULL,
                role_name TEXT NOT NULL,
                description TEXT,
                permissions TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )";
        }
        $pdo->exec($sql);
        echo "<p>✓ 用户角色表创建完成</p>";
    }
    
    // 用户表
    if (!in_array('users', $tables)) {
        if ($isServer) {
            $sql = "CREATE TABLE users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100),
                phone VARCHAR(20),
                role_id INT,
                status VARCHAR(20) DEFAULT 'active',
                last_login TIMESTAMP NULL,
                login_count INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (role_id) REFERENCES user_roles(id)
            )";
        } else {
            $sql = "CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE,
                password_hash TEXT NOT NULL,
                full_name TEXT,
                phone TEXT,
                role_id INTEGER,
                status TEXT DEFAULT 'active',
                last_login TEXT,
                login_count INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (role_id) REFERENCES user_roles(id)
            )";
        }
        $pdo->exec($sql);
        echo "<p>✓ 用户表创建完成</p>";
    }
    
    // 用户资料表
    if (!in_array('user_profiles', $tables)) {
        if ($isServer) {
            $sql = "CREATE TABLE user_profiles (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                nickname VARCHAR(100),
                avatar_url VARCHAR(500),
                bio TEXT,
                location VARCHAR(100),
                website VARCHAR(200),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )";
        } else {
            $sql = "CREATE TABLE user_profiles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                nickname TEXT,
                avatar_url TEXT,
                bio TEXT,
                location TEXT,
                website TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )";
        }
        $pdo->exec($sql);
        echo "<p>✓ 用户资料表创建完成</p>";
    }
    
    // 插入默认角色
    $roleCount = $pdo->query("SELECT COUNT(*) FROM user_roles")->fetchColumn();
    if ($roleCount == 0) {
        $pdo->exec("
            INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
            ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
            ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
            ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
        ");
        echo "<p>✓ 默认角色插入完成</p>";
    }
    
    echo "<h2 style='color: green;'>数据库初始化完成！</h2>";
    echo "<p><a href='index.php'>返回首页</a></p>";
    echo "<p><a href='register.php'>测试注册</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<p>详细信息: " . $e->getTraceAsString() . "</p>";
}

echo "</body></html>";
?>
