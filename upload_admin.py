#!/usr/bin/env python3
import paramiko
import os

def upload_file():
    # SSH连接信息
    hostname = '*************'
    username = 'root'
    password = 'ZbDX7%=]?H2(LAUz'
    
    # 本地和远程文件路径
    local_file = 'admin-dashboard.php'
    remote_path = '/www/wwwroot/比特熊组织网站项目(v0.0.1)/admin-dashboard.php'
    
    try:
        # 创建SSH客户端
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # 连接到服务器
        print("正在连接到服务器...")
        ssh.connect(hostname, username=username, password=password)
        
        # 创建SFTP客户端
        sftp = ssh.open_sftp()
        
        # 上传文件
        print("正在上传admin-dashboard.php...")
        sftp.put(local_file, remote_path)
        
        # 设置文件权限
        sftp.chmod(remote_path, 0o755)
        
        print("文件上传成功！")
        
        # 验证文件
        stdin, stdout, stderr = ssh.exec_command(f'wc -l "{remote_path}"')
        result = stdout.read().decode().strip()
        print(f"远程文件行数: {result}")
        
        # 关闭连接
        sftp.close()
        ssh.close()
        
    except Exception as e:
        print(f"上传失败: {e}")

if __name__ == "__main__":
    upload_file()
