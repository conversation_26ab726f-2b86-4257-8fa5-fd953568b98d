/* 用户个人资料页面样式 */

/* 个人资料卡片 */
.profile-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    margin-bottom: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.profile-header {
    display: flex;
    gap: 32px;
    margin-bottom: 32px;
    align-items: flex-start;
}

.profile-avatar {
    flex-shrink: 0;
}

.avatar-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.profile-signature {
    font-size: 1.1rem;
    color: #6b7280;
    font-style: italic;
    margin-bottom: 16px;
}

.profile-bio {
    color: #374151;
    line-height: 1.6;
    margin-bottom: 20px;
}

.profile-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.meta-item i {
    color: #3b82f6;
    width: 16px;
}

.meta-item a {
    color: #3b82f6;
    text-decoration: none;
}

.meta-item a:hover {
    text-decoration: underline;
}

.profile-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
}

/* 统计信息 */
.profile-stats {
    display: flex;
    gap: 40px;
    padding-top: 32px;
    border-top: 1px solid #e5e7eb;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
}

/* 内容标签页 */
.content-tabs {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-nav {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    background: rgba(248, 250, 252, 0.8);
}

.tab-btn {
    flex: 1;
    padding: 16px 24px;
    border: none;
    background: transparent;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.tab-btn.active {
    background: white;
    color: #3b82f6;
    border-bottom: 2px solid #3b82f6;
}

.tab-content {
    display: none;
    padding: 32px;
}

.tab-content.active {
    display: block;
}

/* 空内容状态 */
.empty-content {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #d1d5db;
}

/* 帖子列表 */
.posts-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.post-item {
    padding: 24px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: rgba(248, 250, 252, 0.5);
    transition: all 0.2s;
}

.post-item:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.post-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.post-category {
    background: rgb(var(--category-color, 59, 130, 246));
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
}

.post-meta {
    font-size: 14px;
    color: #6b7280;
}

.post-title {
    margin-bottom: 12px;
}

.post-title a {
    color: #1a1a1a;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
}

.post-title a:hover {
    color: #3b82f6;
}

.post-excerpt {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 16px;
}

.post-stats {
    display: flex;
    gap: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 14px;
}

.stat i {
    font-size: 16px;
}

/* 评论列表 */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.comment-item {
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background: rgba(248, 250, 252, 0.5);
}

.comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    font-size: 14px;
}

.comment-post a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.comment-post a:hover {
    text-decoration: underline;
}

.comment-time {
    color: #6b7280;
}

.comment-content {
    color: #374151;
    line-height: 1.6;
    margin-bottom: 12px;
}

.comment-stats {
    display: flex;
    gap: 16px;
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.page-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
}

.page-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.page-info {
    color: #6b7280;
    font-size: 14px;
}

/* 关注按钮 */
.follow-btn {
    transition: all 0.2s;
}

.follow-btn:hover {
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .profile-card {
        padding: 24px 20px;
    }
    
    .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 20px;
    }
    
    .avatar-image {
        width: 100px;
        height: 100px;
    }
    
    .profile-name {
        font-size: 1.5rem;
    }
    
    .profile-stats {
        gap: 20px;
        justify-content: center;
    }
    
    .profile-meta {
        justify-content: center;
    }
    
    .tab-content {
        padding: 24px 20px;
    }
    
    .post-item {
        padding: 20px 16px;
    }
    
    .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .post-stats {
        gap: 16px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 12px;
    }
    
    .tab-nav {
        flex-direction: column;
    }
    
    .tab-btn {
        justify-content: flex-start;
        padding: 12px 20px;
    }
}

@media (max-width: 480px) {
    .profile-stats {
        flex-wrap: wrap;
        gap: 16px;
    }
    
    .stat-item {
        flex: 1;
        min-width: calc(50% - 8px);
    }
}
