#!/usr/bin/expect -f
# 自动SSH连接脚本

set timeout 30
set host "*************"
set user "root"
set password "ZbDX7%=]?H2(LAUz"

# 开始SSH连接
spawn ssh $user@$host

# 处理各种可能的提示
expect {
    "Are you sure you want to continue connecting" {
        send "yes\r"
        expect "password:"
        send "$password\r"
    }
    "password:" {
        send "$password\r"
    }
    "Permission denied" {
        puts "登录失败：密码错误或权限被拒绝"
        exit 1
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}

# 等待登录成功
expect {
    "#" {
        puts "SSH连接成功！"
        # 执行一些基本命令
        send "whoami\r"
        expect "#"
        send "pwd\r"
        expect "#"
        send "ls -la\r"
        expect "#"
    }
    timeout {
        puts "登录超时"
        exit 1
    }
}

# 保持连接
interact
