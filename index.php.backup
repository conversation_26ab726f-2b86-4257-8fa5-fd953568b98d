<!DOCTYPE html>
<html lang="zh-CN">
<!-- 设置页面语言为中文 -->
<head>
    <meta charset="UTF-8">
    <!-- 设置字符编码为UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 设置视口元标签，确保在移动设备上正确显示 -->
    <meta name="description" content="比特熊极简门户网站 - 综合应用型社交网站，学习、游戏、分享生活美好事物">
    <!-- 设置页面描述，有利于SEO优化 -->
    <meta name="keywords" content="比特熊,社交网站,在线游戏,学习,分享">
    <!-- 设置关键词，有利于SEO优化 -->
    <title>比特熊极简门户网站 - 一起度过美好时光</title>
    <!-- 设置页面标题 -->
    <link rel="stylesheet" href="index-style.css?v=20250731v1">
    <!-- 引入主要样式表 -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- 引入AOS动画库样式 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <!-- 预连接到Google字体服务器，提高加载速度 -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- 预连接到Google字体静态资源服务器 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 引入现代化的Inter字体 -->
    <link rel="icon" type="image/png" href="image/bit.png">
    <!-- 设置网站图标 -->
</head>
<body>
    <header class="header">
    <!-- 网站的第一块区域 头部部分-->
        <div class="center">
        <!-- 网站区域：居中盒子 让内容居中显示-->
            <img src="image/bit.png" width="130px" height="60px">
            <!-- 插入比特熊logo图片-->
            <ul class="list-left">
            <!-- 设置导航条左边部分功能-->
                <li class="nav-item dropdown">
                <!-- 第一项：组织下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        组织
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">组织介绍</a>
                        <a href="#" class="dropdown-item">组织架构</a>
                        <a href="#" class="dropdown-item">组织领导</a>
                        <a href="#" class="dropdown-item">加入我们</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                <!-- 第二项：服务下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        服务
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">在线咨询</a>
                        <a href="#" class="dropdown-item">技术支持</a>
                        <a href="#" class="dropdown-item">喊话我们</a>
                        <a href="#" class="dropdown-item">资源共享</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!--  第三项：游戏下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        一起玩游戏
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">在线游戏</a>
                        <a href="#" class="dropdown-item">游戏排行榜</a>
                        <a href="#" class="dropdown-item">游戏攻略</a>
                        <a href="#" class="dropdown-item">游戏社区</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第四项：社区下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        社区|资讯局
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">最新资讯</a>
                        <a href="#" class="dropdown-item">技术文章</a>
                        <a href="#" class="dropdown-item">社区动态</a>
                        <a href="#" class="dropdown-item">用户反馈</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第五项：关于我们下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        关于我们
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">团队介绍</a>
                        <a href="#" class="dropdown-item">发展历程</a>
                        <a href="#" class="dropdown-item">联系我们</a>
                        <a href="#" class="dropdown-item">招聘信息</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第六项：科技创新下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        科技创新
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">AI技术</a>
                        <a href="#" class="dropdown-item">区块链</a>
                        <a href="#" class="dropdown-item">云计算</a>
                        <a href="#" class="dropdown-item">大数据</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第七项：项目展示下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        项目展示
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">成功案例</a>
                        <a href="#" class="dropdown-item">项目演示</a>
                        <a href="#" class="dropdown-item">技术方案</a>
                        <a href="#" class="dropdown-item">客户评价</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第八项：学习资源下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        学习资源
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">在线课程</a>
                        <a href="#" class="dropdown-item">学习路径</a>
                        <a href="#" class="dropdown-item">实战项目</a>
                        <a href="#" class="dropdown-item">认证考试</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第九项：合作伙伴下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        合作伙伴
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">战略合作</a>
                        <a href="#" class="dropdown-item">技术联盟</a>
                        <a href="#" class="dropdown-item">生态伙伴</a>
                        <a href="#" class="dropdown-item">合作申请</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第十项：支持中心下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        支持中心
                        <!-- 下拉箭头图标 -->
                        <svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">帮助文档</a>
                        <a href="#" class="dropdown-item">常见问题</a>
                        <a href="#" class="dropdown-item">提交工单</a>
                        <a href="#" class="dropdown-item">在线客服</a>
                    </div>
                </li>
            </ul>
            <ul class="list-right">
            <!-- 设置导航条右边部分功能-->
                <li class="nav-item">
                <!-- 第一项：登录链接-->
                    <a href="#" class="nav-link">登录</a>
                </li>
                <li class="nav-item">
                <!-- 第二项：注册按钮-->
                    <a href="#" class="nav-link btn-register">注册</a>
                </li>
            </ul>
        </div>
    </header>

    <!-- 移动端搜索按钮 -->
    <button class="mobile-search-btn" id="mobileSearchBtn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
        </svg>
    </button>

    <!-- 移动端搜索模态框 -->
    <div class="mobile-search-modal" id="mobileSearchModal">
        <div class="mobile-search-content">
            <div class="mobile-search-header">
                <h3>搜索</h3>
                <button class="mobile-search-close" id="mobileSearchClose">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="mobile-search-body">
                <input type="text" placeholder="搜索内容..." class="mobile-search-input">
                <button class="mobile-search-submit">搜索</button>
            </div>
        </div>
    </div>
    <!-- 移动端搜索模态框结束 -->
    <!-- 第一区域：网站导航条部分-->
    <!-- 主要内容区域开始 -->
    <main class="main-content">
        <!-- 英雄区域：网站主要介绍部分 -->
        <section class="hero-section">
            <!-- 英雄区域容器，用于居中内容 -->
            <div class="container">
                <!-- 英雄内容区域 -->
                <div class="hero-content">
                    <!-- 文本内容区域 -->
                    <div class="hero-text">
                        <!-- 主标题 -->
                        <h1 class="hero-title">欢迎访问比特熊极简门户网站</h1>
                        <!-- 副标题 -->
                        <h2 class="hero-subtitle">在这里一起和小熊彼彼度过美好的时光</h2>
                        <!-- 描述段落 -->
                        <p class="hero-description">
                            也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的
                            一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等
                        </p>
                        <!-- 行动按钮区域 -->
                        <div class="hero-actions">
                            <!-- 主要行动按钮 -->
                            <a href="#" class="btn btn-primary">立即体验</a>
                            <!-- 次要行动按钮 -->
                            <a href="#features" class="btn btn-secondary">了解更多</a>
                        </div>
                    </div>
                    <!-- 图片区域 -->
                    <div class="hero-image">
                        <!-- 主要展示图片 -->
                        <img src="image/bitlogo.png" alt="比特熊Logo" class="hero-img">
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特色区域：展示网站主要功能 -->
        <section class="features-section" id="features">
            <!-- 功能区域容器 -->
            <div class="container">
                <!-- 功能区域标题 -->
                <div class="features-header">
                    <h2 class="features-title">探索比特熊的精彩功能</h2>
                    <p class="features-subtitle">
                        发现学习、游戏、社交的无限可能，与志同道合的朋友一起成长
                    </p>
                </div>

                <!-- 功能卡片网格 -->
                <div class="features-grid">
                    <!-- 功能卡片1：在线学习 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon">
                            📚
                        </div>
                        <h3 class="feature-title">在线学习</h3>
                        <p class="feature-description">
                            丰富的课程资源，专业的学习路径，让知识获取变得简单高效
                        </p>
                    </div>

                    <!-- 功能卡片2：互动游戏 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon">
                            🎮
                        </div>
                        <h3 class="feature-title">互动游戏</h3>
                        <p class="feature-description">
                            多人在线游戏，休闲娱乐的同时结识新朋友，享受团队协作的乐趣
                        </p>
                    </div>

                    <!-- 功能卡片3：社交分享 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon">
                            💬
                        </div>
                        <h3 class="feature-title">社交分享</h3>
                        <p class="feature-description">
                            分享生活美好瞬间，与朋友交流心得体会，构建温暖的社区氛围
                        </p>
                    </div>

                    <!-- 功能卡片4：个人成长 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-icon">
                            🌱
                        </div>
                        <h3 class="feature-title">个人成长</h3>
                        <p class="feature-description">
                            记录学习进度，追踪个人成长轨迹，见证每一步的进步与蜕变
                        </p>
                    </div>

                    <!-- 功能卡片5：专家指导 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="500">
                        <div class="feature-icon">
                            👨‍🏫
                        </div>
                        <h3 class="feature-title">专家指导</h3>
                        <p class="feature-description">
                            行业专家在线答疑，一对一指导，让学习更有针对性和实效性
                        </p>
                    </div>

                    <!-- 功能卡片6：智能推荐 -->
                    <div class="feature-card" data-aos="fade-up" data-aos-delay="600">
                        <div class="feature-icon">
                            🤖
                        </div>
                        <h3 class="feature-title">智能推荐</h3>
                        <p class="feature-description">
                            基于个人兴趣和学习历史，智能推荐最适合的内容和活动
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 统计数据区域：展示平台成就 -->
        <section class="stats-section">
            <!-- 统计区域容器 -->
            <div class="container">
                <!-- 统计区域标题 -->
                <div class="stats-header">
                    <h2 class="stats-title">我们的成就数据</h2>
                    <p class="stats-subtitle">
                        用数字见证比特熊社区的蓬勃发展，每一个数字都代表着我们共同的努力
                    </p>
                </div>

                <!-- 统计数据网格 -->
                <div class="stats-grid">
                    <!-- 统计项1：注册用户 -->
                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="100">
                        <div class="stat-icon">👥</div>
                        <div class="stat-number" data-target="50000">0</div>
                        <div class="stat-label">注册用户</div>
                    </div>

                    <!-- 统计项2：课程数量 -->
                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="200">
                        <div class="stat-icon">📖</div>
                        <div class="stat-number" data-target="1200">0</div>
                        <div class="stat-label">精品课程</div>
                    </div>

                    <!-- 统计项3：学习时长 -->
                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="300">
                        <div class="stat-icon">⏰</div>
                        <div class="stat-number" data-target="100000">0</div>
                        <div class="stat-label">学习时长(小时)</div>
                    </div>

                    <!-- 统计项4：满意度 -->
                    <div class="stat-item" data-aos="zoom-in" data-aos-delay="400">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-number" data-target="98">0</div>
                        <div class="stat-label">满意度(%)</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- O'Reilly风格技能建设区域 - 按照参考图重新设计 -->
        <section class="oreilly-hero-section">
            <div class="container">
                <div class="hero-main-content">
                    <!-- 左侧大圆形和蝴蝶图标 -->
                    <div class="hero-visual-area">
                        <div class="large-circle">
                            <!-- 蝴蝶SVG图标 -->
                            <svg class="butterfly-svg" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
                                <!-- 蝴蝶身体 -->
                                <ellipse cx="60" cy="60" rx="2.5" ry="30" fill="url(#bodyGradient)"/>

                                <!-- 左上翅膀 -->
                                <path d="M57 45 Q35 25, 20 12 Q15 8, 20 6 Q35 3, 50 18 Q55 30, 57 42 Z"
                                      fill="url(#wingGradient1)"/>

                                <!-- 右上翅膀 -->
                                <path d="M63 45 Q85 25, 100 12 Q105 8, 100 6 Q85 3, 70 18 Q65 30, 63 42 Z"
                                      fill="url(#wingGradient2)"/>

                                <!-- 左下翅膀 -->
                                <path d="M57 75 Q45 88, 35 98 Q30 103, 35 105 Q45 108, 55 98 Q57 88, 57 78 Z"
                                      fill="url(#wingGradient3)"/>

                                <!-- 右下翅膀 -->
                                <path d="M63 75 Q75 88, 85 98 Q90 103, 85 105 Q75 108, 65 98 Q63 88, 63 78 Z"
                                      fill="url(#wingGradient4)"/>

                                <!-- 触角 -->
                                <path d="M55 42 Q50 32, 45 28" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                <path d="M65 42 Q70 32, 75 28" stroke="#3b82f6" stroke-width="2" fill="none"/>
                                <circle cx="45" cy="28" r="2" fill="#60a5fa"/>
                                <circle cx="75" cy="28" r="2" fill="#60a5fa"/>

                                <!-- 渐变定义 -->
                                <defs>
                                    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.9" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.7" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:0.9" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#93c5fd;stop-opacity:0.7" />
                                        <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:0.8" />
                                    </linearGradient>
                                    <linearGradient id="wingGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.6" />
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        <!-- 右上角小白圆点 -->
                        <div class="white-dot"></div>
                    </div>

                    <!-- 右侧文本内容 -->
                    <div class="hero-main-content">
                        <h2 class="hero-main-title">构建团队所需的技能</h2>
                        <p class="hero-description">
                            通过我们的<strong>专家指导课程</strong>、互动学习路径和实践项目，
                            让您的团队掌握最新的技术技能和行业最佳实践。
                        </p>

                        <!-- 行动按钮 -->
                        <div class="hero-action-buttons">
                            <a href="#" class="btn btn-request-demo">Request a demo</a>
                            <a href="#" class="btn btn-secondary">了解更多</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- O'Reilly风格专家课程区域 -->
        <section class="expert-courses-section">
            <div class="container">
                <div class="courses-grid">
                    <!-- 左侧：专家指导课程 -->
                    <div class="course-item">
                        <h3 class="course-title">Level up with<br>expert-led live courses</h3>
                        <p class="course-description">
                            Reserve your seat for interactive workshops to gain hands-on experience—and ask questions along the way.
                        </p>
                        <a href="#" class="course-btn">Pick your events ›</a>
                    </div>

                    <!-- 右侧：AI智能答案 -->
                    <div class="course-item">
                        <h3 class="course-title">比特熊 AI-powered Answers<br>just got even smarter</h3>
                        <p class="course-description">
                            比特熊智能答案系统能够即时生成团队可信赖的信息，来源于我们学习平台上的数千个优质内容。
                        </p>
                        <a href="#" class="course-btn">Discover Answers ›</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- O'Reilly风格专家展示区域 -->
        <section class="experts-section">
            <div class="container">
                <div class="experts-header">
                    <h2 class="experts-title">We share the knowledge of<br>innovators. You put it to work.</h2>
                    <p class="experts-description">
                        Tech teams love tapping into the minds of innovators through our expert-led courses, 
                        renowned text-based content, and bite-size online Superstream tech conferences. In fact, 
                        in a recent survey, one-third of tech practitioners rated O'Reilly content a five out of five 
                        (excellent)—better than Pluralsight, LinkedIn Learning, Udacity, or Skillsoft.
                    </p>
                </div>

                <!-- 动态专家卡片网格 -->
                <div class="experts-grid" id="expertsGrid">
                    <?php
                    // 引入专家数据
                    include 'experts-data.php';
                    $experts = getExperts();
                    
                    // 动态生成专家卡片
                    foreach ($experts as $index => $expert) {
                        echo '<div class="expert-card" data-aos="fade-up" data-aos-delay="' . ($index * 100) . '">';
                        echo '    <div class="expert-image-container">';
                        echo '        <img src="' . htmlspecialchars($expert['image']) . '" alt="' . htmlspecialchars($expert['alt']) . '" class="expert-image">';
                        echo '        <div class="expert-overlay"></div>';
                        echo '    </div>';
                        echo '    <div class="expert-info">';
                        echo '        <h4 class="expert-name">' . htmlspecialchars($expert['name']) . '</h4>';
                        echo '        <p class="expert-title">' . htmlspecialchars($expert['title']) . '</p>';
                        echo '    </div>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
        </section>

    </main>

    <!-- AOS动画库JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // 初始化AOS动画
        AOS.init({
            duration: 800,
            easing: 'ease-out-cubic',
            once: true,
            offset: 100
        });
    </script>
</body>
</html>
