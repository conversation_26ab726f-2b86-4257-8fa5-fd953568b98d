<?php
require_once 'config/database.php';

try {
    $db = DatabaseConfig::getInstance();
    
    echo "=== 检查users表结构 ===\n";
    $result = $db->fetchAll('DESCRIBE users');
    print_r($result);
    
    echo "\n=== 检查users表数据 ===\n";
    $users = $db->fetchAll('SELECT * FROM users ORDER BY id DESC LIMIT 5');
    print_r($users);
    
    echo "\n=== 测试插入和获取ID ===\n";
    $db->getConnection()->beginTransaction();
    
    // 插入测试用户
    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
            VALUES ('test_user_" . time() . "', '<EMAIL>', 'test_hash', 'Test User', 3, 'active', NOW())";
    
    $stmt = $db->getConnection()->prepare($sql);
    $result = $stmt->execute();
    
    echo "插入结果: " . ($result ? 'success' : 'failed') . "\n";
    
    $lastId = $db->getConnection()->lastInsertId();
    echo "lastInsertId: " . $lastId . "\n";
    
    // 回滚测试
    $db->getConnection()->rollback();
    echo "测试完成，已回滚\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈: " . $e->getTraceAsString() . "\n";
}
?>
