// 用户登录页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const passwordInput = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const loginButton = document.getElementById('loginButton');

    // 密码显示/隐藏切换
    passwordToggle.addEventListener('click', function() {
        const type = passwordInput.type === 'password' ? 'text' : 'password';
        passwordInput.type = type;
        
        // 更新图标
        const icon = passwordToggle.querySelector('svg');
        if (type === 'text') {
            icon.innerHTML = '<path d="M17.94 17.94C16.2 19.68 14.21 20.5 12 20.5C7.58 20.5 1 12 1 12S2.68 9.84 5.47 7.69M9.9 4.24C10.58 4.08 11.3 4 12 4C16.42 4 23 12 23 12S22.18 13.35 20.82 14.94M14.12 14.12C13.8 14.44 13.4 14.67 12.97 14.82C12.54 14.97 12.08 15.03 11.63 14.99C11.18 14.95 10.75 14.81 10.37 14.58C9.99 14.35 9.67 14.04 9.43 13.66C9.19 13.28 9.05 12.85 9.01 12.4C8.97 11.95 9.03 11.49 9.18 11.06C9.33 10.63 9.56 10.23 9.88 9.91" stroke="currentColor" stroke-width="2"/><line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2"/>';
        } else {
            icon.innerHTML = '<path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>';
        }
    });

    // 实时验证
    const inputs = ['username', 'password'];
    inputs.forEach(inputName => {
        const input = document.getElementById(inputName);
        input.addEventListener('blur', () => validateField(inputName));
        input.addEventListener('input', () => clearFieldError(inputName));
    });

    // 表单提交
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        const formData = new FormData(loginForm);
        
        // 显示加载状态
        setLoadingState(true);

        try {
            const response = await fetch('api/login.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showToast('登录成功！正在跳转...', 'success');
                
                // 根据用户角色跳转到不同页面
                setTimeout(() => {
                    if (result.user && (result.user.role === 'admin' || result.user.role === 'super_admin')) {
                        window.location.href = 'admin-dashboard.php';
                    } else {
                        window.location.href = 'index.php';
                    }
                }, 1500);
            } else {
                showToast(result.message || '登录失败，请重试', 'error');
                
                // 显示字段错误
                if (result.errors) {
                    Object.keys(result.errors).forEach(field => {
                        showFieldError(field, result.errors[field]);
                    });
                }
            }
        } catch (error) {
            console.error('登录错误:', error);
            showToast('网络错误，请检查网络连接后重试', 'error');
        } finally {
            setLoadingState(false);
        }
    });

    // 第三方登录按钮事件
    const socialButtons = document.querySelectorAll('.social-button');
    socialButtons.forEach(button => {
        button.addEventListener('click', function() {
            const type = this.classList.contains('social-wechat') ? '微信' : 'QQ';
            showToast(`${type}登录功能即将上线，敬请期待！`, 'info');
        });
    });

    // 验证单个字段
    function validateField(fieldName) {
        const input = document.getElementById(fieldName);
        const value = input.value.trim();
        let isValid = true;
        let errorMessage = '';

        switch (fieldName) {
            case 'username':
                if (!value) {
                    errorMessage = '请输入用户名或邮箱';
                    isValid = false;
                }
                break;

            case 'password':
                if (!value) {
                    errorMessage = '请输入密码';
                    isValid = false;
                }
                break;
        }

        if (!isValid) {
            showFieldError(fieldName, errorMessage);
        } else {
            clearFieldError(fieldName);
        }

        return isValid;
    }

    // 验证整个表单
    function validateForm() {
        let isValid = true;
        
        inputs.forEach(inputName => {
            if (!validateField(inputName)) {
                isValid = false;
            }
        });

        return isValid;
    }

    // 显示字段错误
    function showFieldError(fieldName, message) {
        const errorElement = document.getElementById(fieldName + 'Error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'flex';
        }
    }

    // 清除字段错误
    function clearFieldError(fieldName) {
        const errorElement = document.getElementById(fieldName + 'Error');
        if (errorElement) {
            errorElement.textContent = '';
            errorElement.style.display = 'none';
        }
    }

    // 设置加载状态
    function setLoadingState(loading) {
        const buttonText = loginButton.querySelector('.button-text');
        const buttonLoader = loginButton.querySelector('.button-loader');
        
        if (loading) {
            buttonText.style.display = 'none';
            buttonLoader.style.display = 'block';
            loginButton.disabled = true;
        } else {
            buttonText.style.display = 'block';
            buttonLoader.style.display = 'none';
            loginButton.disabled = false;
        }
    }

    // 自动填充演示账户（开发环境）
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // 添加快速登录按钮
        const quickLoginContainer = document.createElement('div');
        quickLoginContainer.className = 'quick-login-container';
        quickLoginContainer.style.cssText = `
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        `;
        
        quickLoginContainer.innerHTML = `
            <p style="color: rgba(255, 255, 255, 0.7); font-size: 0.75rem; margin-bottom: 0.5rem; text-align: center;">开发环境快速登录</p>
            <div style="display: flex; gap: 0.5rem;">
                <button type="button" class="quick-login-btn" data-username="admin" data-password="admin123" style="flex: 1; padding: 0.5rem; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 6px; color: white; font-size: 0.75rem; cursor: pointer;">管理员</button>
                <button type="button" class="quick-login-btn" data-username="user1" data-password="admin123" style="flex: 1; padding: 0.5rem; background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 6px; color: white; font-size: 0.75rem; cursor: pointer;">普通用户</button>
            </div>
        `;
        
        loginForm.appendChild(quickLoginContainer);
        
        // 快速登录按钮事件
        const quickLoginBtns = quickLoginContainer.querySelectorAll('.quick-login-btn');
        quickLoginBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                document.getElementById('username').value = this.dataset.username;
                document.getElementById('password').value = this.dataset.password;
                showToast('已填充演示账户信息', 'info');
            });
        });
    }
});

// Toast 通知函数
function showToast(message, type = 'info') {
    const container = document.getElementById('toastContainer');
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    const icons = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
    };
    
    toast.innerHTML = `
        <div class="toast-icon">${icons[type] || icons.info}</div>
        <div class="toast-message">${message}</div>
        <button class="toast-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    container.appendChild(toast);
    
    // 自动移除
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
