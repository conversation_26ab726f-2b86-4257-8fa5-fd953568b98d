<?php
/**
 * 简单链接测试页面
 * 用于测试帖子链接是否正常工作
 */

require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>简单链接测试</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
    .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
    .test-link { display: block; padding: 15px; margin: 10px 0; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 6px; text-decoration: none; color: #1976d2; transition: all 0.3s; }
    .test-link:hover { background: #bbdefb; transform: translateY(-1px); }
    .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 5px 0; }
    .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 4px; margin: 5px 0; }
    .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 4px; margin: 5px 0; }
    .log { background: #000; color: #0f0; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; margin: 10px 0; }
    .btn { display: inline-block; padding: 10px 20px; background: #2196f3; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    .btn:hover { background: #1976d2; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔗 简单链接测试</h1>";

// 获取测试帖子
try {
    $db = db();
    $posts = $db->fetchAll("SELECT id, title, status FROM posts WHERE status = 'published' ORDER BY id DESC LIMIT 3");
    
    if ($posts) {
        echo "<div class='test-section'>";
        echo "<h3>📄 测试帖子链接</h3>";
        echo "<div class='info'>点击下面的链接，观察是否正常跳转到帖子详情页</div>";
        
        foreach ($posts as $post) {
            $title = htmlspecialchars(mb_substr($post['title'], 0, 40));
            echo "<a href='community-post-detail.php?id={$post['id']}' class='test-link' data-post-id='{$post['id']}'>";
            echo "📖 帖子 {$post['id']}: {$title}";
            echo "</a>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>🆕 新窗口测试</h3>";
        echo "<div class='info'>这些链接会在新窗口打开</div>";
        
        foreach ($posts as $post) {
            $title = htmlspecialchars(mb_substr($post['title'], 0, 40));
            echo "<a href='community-post-detail.php?id={$post['id']}' class='test-link' target='_blank' data-post-id='{$post['id']}'>";
            echo "🔗 新窗口 - 帖子 {$post['id']}: {$title}";
            echo "</a>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h3>🧪 JavaScript跳转测试</h3>";
        echo "<div class='info'>使用JavaScript进行页面跳转</div>";
        
        foreach ($posts as $post) {
            $title = htmlspecialchars(mb_substr($post['title'], 0, 40));
            echo "<a href='#' class='test-link' onclick=\"testJSRedirect({$post['id']}); return false;\" data-post-id='{$post['id']}'>";
            echo "⚡ JS跳转 - 帖子 {$post['id']}: {$title}";
            echo "</a>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='error'>❌ 没有找到可测试的帖子</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库查询失败: " . $e->getMessage() . "</div>";
}

// 错误日志区域
echo "<div class='test-section'>";
echo "<h3>📊 实时日志</h3>";
echo "<div id='jsLog' class='log'>等待JavaScript事件...</div>";
echo "<button onclick='clearJSLog()' class='btn'>清空日志</button>";
echo "</div>";

// 文件检查
echo "<div class='test-section'>";
echo "<h3>📁 文件存在性检查</h3>";

$checkFiles = [
    'community-post-detail.php',
    'community.php',
    'assets/js/community-post-detail.js',
    'assets/css/community.css'
];

foreach ($checkFiles as $file) {
    $exists = file_exists($file);
    $status = $exists ? "<span style='color: green;'>✅ 存在</span>" : "<span style='color: red;'>❌ 不存在</span>";
    echo "<div>{$file}: {$status}</div>";
    
    if ($exists && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
        // 检查PHP文件是否有语法错误
        $output = [];
        $return_var = 0;
        exec("php -l " . escapeshellarg($file) . " 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<div class='success'>✅ PHP语法检查通过</div>";
        } else {
            echo "<div class='error'>❌ PHP语法错误: " . implode('<br>', $output) . "</div>";
        }
    }
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='community.php' class='btn'>返回社区</a>";
echo "<a href='检测JavaScript错误.php' class='btn'>详细检测</a>";
echo "<a href='诊断帖子链接问题.php' class='btn'>诊断工具</a>";
echo "</div>";

echo "</div>";

// JavaScript监控脚本
echo "<script>
let logCount = 0;

function jsLog(message, type = 'info') {
    logCount++;
    const timestamp = new Date().toLocaleTimeString();
    const logArea = document.getElementById('jsLog');
    const colors = {
        'info': '#0f0',
        'error': '#f00',
        'warning': '#ff0',
        'success': '#0f0'
    };
    
    logArea.innerHTML += `<div style='color: ${colors[type] || '#0f0'};'>[${timestamp}] ${message}</div>`;
    logArea.scrollTop = logArea.scrollHeight;
}

function clearJSLog() {
    document.getElementById('jsLog').innerHTML = '日志已清空...';
    logCount = 0;
}

function testJSRedirect(postId) {
    jsLog(`🚀 JavaScript跳转测试 - 帖子ID: ${postId}`);
    
    // 测试不同的跳转方法
    setTimeout(() => {
        jsLog('📍 使用window.location.href跳转...');
        window.location.href = `community-post-detail.php?id=${postId}`;
    }, 1000);
}

// 监听所有错误
window.addEventListener('error', function(e) {
    jsLog(`❌ JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`, 'error');
});

window.addEventListener('unhandledrejection', function(e) {
    jsLog(`❌ Promise错误: ${e.reason}`, 'error');
});

// 监听资源加载错误
window.addEventListener('error', function(e) {
    if (e.target !== window) {
        jsLog(`❌ 资源加载失败: ${e.target.src || e.target.href || e.target.tagName}`, 'error');
    }
}, true);

// 监听链接点击
document.addEventListener('click', function(e) {
    const link = e.target.closest('a');
    if (link) {
        jsLog(`🔗 链接点击: ${link.href}`);
        jsLog(`   - 目标: ${link.target || '当前窗口'}`);
        jsLog(`   - 文本: ${link.textContent.trim().substring(0, 30)}`);
        jsLog(`   - 默认行为阻止: ${e.defaultPrevented}`);
        
        // 检查链接是否指向帖子详情页
        if (link.href.includes('community-post-detail.php')) {
            jsLog(`📄 检测到帖子详情链接`, 'success');
            
            // 检查链接是否有效
            fetch(link.href, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        jsLog(`✅ 链接有效 (${response.status})`, 'success');
                    } else {
                        jsLog(`❌ 链接无效 (${response.status})`, 'error');
                    }
                })
                .catch(error => {
                    jsLog(`❌ 链接检查失败: ${error.message}`, 'error');
                });
        }
    }
}, true);

// 监听页面卸载
window.addEventListener('beforeunload', function(e) {
    jsLog('🔄 页面即将卸载');
});

// 页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    jsLog('✅ 页面DOM加载完成', 'success');
    jsLog(`📊 页面元素统计: ${document.querySelectorAll('*').length}个元素`);
    jsLog(`🔗 链接统计: ${document.querySelectorAll('a').length}个链接`);
});

jsLog('🚀 JavaScript监控已启动', 'success');
</script>";

echo "</body></html>";
?>
