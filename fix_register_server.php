<?php
/**
 * 修复服务器上注册问题的脚本
 */

echo "开始修复注册问题...\n";

// 服务器上的文件路径
$registerFile = '/www/wwwroot/www.bitbear.top/api/register.php';
$backupFile = '/www/wwwroot/www.bitbear.top/api/register.php.backup';

// 检查文件是否存在
if (!file_exists($registerFile)) {
    die("错误：找不到register.php文件\n");
}

// 备份原文件
if (!file_exists($backupFile)) {
    copy($registerFile, $backupFile);
    echo "✅ 已备份原始文件\n";
}

// 读取文件内容
$content = file_get_contents($registerFile);

// 查找需要替换的代码段
$oldPattern = '/\/\/ 获取插入的ID\s*\$userId = \$db->getConnection\(\)->lastInsertId\(\);\s*\/\/ 调试信息\s*error_log\("用户ID: " \. \$userId\);\s*if \(!\$userId \|\| \$userId == 0\) \{\s*throw new Exception\(\'获取用户ID失败\'\);\s*\}/s';

$newCode = '// 获取插入的ID - 修复版本
        $userId = $db->getConnection()->lastInsertId();

        // 调试信息
        error_log("用户ID (lastInsertId): " . $userId);

        // 如果lastInsertId()失败，尝试查询最新插入的记录
        if (!$userId || $userId == 0) {
            error_log("lastInsertId失败，尝试查询最新记录");
            $latestUser = $db->fetchOne("SELECT id FROM users WHERE username = ? ORDER BY id DESC LIMIT 1", [$username]);
            if ($latestUser) {
                $userId = $latestUser[\'id\'];
                error_log("通过查询获取用户ID: " . $userId);
            }
        }

        if (!$userId || $userId == 0) {
            throw new Exception(\'获取用户ID失败\');
        }';

// 执行替换
$newContent = preg_replace($oldPattern, $newCode, $content);

// 如果正则替换失败，尝试简单的字符串替换
if ($newContent === $content) {
    $oldCode = '        // 获取插入的ID
        $userId = $db->getConnection()->lastInsertId();

        // 调试信息
        error_log("用户ID: " . $userId);

        if (!$userId || $userId == 0) {
            throw new Exception(\'获取用户ID失败\');
        }';
    
    $newContent = str_replace($oldCode, $newCode, $content);
}

// 写入修复后的内容
if ($newContent !== $content) {
    file_put_contents($registerFile, $newContent);
    echo "✅ 修复完成！\n";
    
    // 测试修复效果
    echo "\n🧪 测试修复效果...\n";
    
    $testUrl = 'http://localhost/api/register.php';
    $testData = [
        'username' => 'testfix_' . time(),
        'email' => 'testfix_' . time() . '@example.com',
        'password' => 'test123456',
        'confirmPassword' => 'test123456',
        'nickname' => '修复测试'
    ];
    
    $postData = http_build_query($testData);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    $result = file_get_contents($testUrl, false, $context);
    $response = json_decode($result, true);
    
    if ($response && $response['success']) {
        echo "✅ 修复成功！注册功能正常工作\n";
        echo "测试用户ID: " . $response['user_id'] . "\n";
        
        // 清理测试数据
        require_once '/www/wwwroot/www.bitbear.top/config/database.php';
        $db = DatabaseConfig::getInstance();
        $db->execute("DELETE FROM users WHERE username = ?", [$testData['username']]);
        $db->execute("DELETE FROM user_profiles WHERE user_id NOT IN (SELECT id FROM users)");
        echo "✅ 测试数据已清理\n";
    } else {
        echo "❌ 可能还有问题，响应：\n";
        echo $result . "\n";
    }
    
} else {
    echo "⚠️ 未找到需要修复的代码，可能已经修复过了\n";
}

echo "\n修复脚本执行完成！\n";
?>
