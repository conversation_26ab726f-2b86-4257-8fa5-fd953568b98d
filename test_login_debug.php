<?php
// 测试登录功能调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>登录功能调试测试</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

try {
    require_once 'config/database.php';
    require_once 'classes/Auth.php';
    
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    echo "<p class='success'>✅ 数据库连接成功!</p>\n";
    
    // 检查用户表中是否有用户
    $userCount = $connection->query("SELECT COUNT(*) as count FROM users")->fetch();
    echo "<p class='info'>用户表中有 {$userCount['count']} 个用户</p>\n";
    
    if ($userCount['count'] > 0) {
        // 显示前几个用户
        $users = $connection->query("SELECT id, username, email, full_name, status FROM users LIMIT 5")->fetchAll();
        echo "<h3>现有用户:</h3>\n";
        echo "<table border='1' style='border-collapse:collapse;'>\n";
        echo "<tr><th>ID</th><th>用户名</th><th>邮箱</th><th>全名</th><th>状态</th></tr>\n";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['status']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
        
        // 测试登录功能
        echo "<h3>测试登录功能:</h3>\n";
        
        // 获取第一个用户进行测试
        $testUser = $users[0];
        echo "<p class='info'>测试用户: {$testUser['username']}</p>\n";
        
        // 测试Auth类
        $auth = new Auth();
        
        // 测试错误密码
        echo "<h4>测试错误密码:</h4>\n";
        $result = $auth->login($testUser['username'], 'wrong_password');
        if (!$result['success']) {
            echo "<p class='success'>✅ 错误密码正确被拒绝: {$result['message']}</p>\n";
        } else {
            echo "<p class='error'>❌ 错误密码被接受了！</p>\n";
        }
        
        // 如果是admin用户，测试正确密码
        if ($testUser['username'] === 'admin') {
            echo "<h4>测试admin用户正确密码:</h4>\n";
            $result = $auth->login('admin', 'admin123');
            if ($result['success']) {
                echo "<p class='success'>✅ admin用户登录成功!</p>\n";
                echo "<p class='info'>用户信息: " . json_encode($result['user'], JSON_UNESCAPED_UNICODE) . "</p>\n";
            } else {
                echo "<p class='error'>❌ admin用户登录失败: {$result['message']}</p>\n";
            }
        }
        
        // 测试其他用户
        foreach ($users as $user) {
            if ($user['username'] !== 'admin') {
                echo "<h4>测试用户 {$user['username']}:</h4>\n";
                echo "<p class='info'>尝试使用常见密码...</p>\n";
                
                $commonPasswords = ['123456', 'password', 'admin123', $user['username']];
                foreach ($commonPasswords as $pwd) {
                    $result = $auth->login($user['username'], $pwd);
                    if ($result['success']) {
                        echo "<p class='success'>✅ 用户 {$user['username']} 密码是: {$pwd}</p>\n";
                        break;
                    }
                }
            }
        }
        
    } else {
        echo "<p class='error'>❌ 用户表为空，需要创建测试用户</p>\n";
    }
    
    // 测试直接API调用
    echo "<h3>测试API调用:</h3>\n";
    echo "<form method='post' action='api/login.php' target='_blank'>\n";
    echo "<p>用户名: <input type='text' name='username' value='admin'></p>\n";
    echo "<p>密码: <input type='password' name='password' value='admin123'></p>\n";
    echo "<p><input type='submit' value='测试登录API'></p>\n";
    echo "</form>\n";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ 测试失败: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>错误详情: " . $e->getTraceAsString() . "</p>\n";
}
?>
