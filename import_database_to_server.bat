@echo off
echo ========================================
echo 比特熊智慧系统 - 数据库导入工具
echo ========================================
echo.

set DB_NAME=bitbear_website
set DB_USER=bitbear_website
set DB_PASS=309290133q
set SQL_FILE=database_export_2025-08-04_04-21-27.sql

echo 数据库配置信息:
echo 数据库名: %DB_NAME%
echo 用户名: %DB_USER%
echo 密码: %DB_PASS%
echo SQL文件: %SQL_FILE%
echo.

echo 步骤1: 上传SQL文件到服务器...
putty\pscp.exe -pw "ZbDX7%=]?H2(LAUz" %SQL_FILE% root@*************:/tmp/database_import.sql
if %errorlevel% neq 0 (
    echo ❌ 文件上传失败
    pause
    exit /b 1
)
echo ✓ 文件上传成功
echo.

echo 步骤2: 创建数据库和用户...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "mysql -u root -e 'CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;'"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "mysql -u root -e 'CREATE USER IF NOT EXISTS \"%DB_USER%\"@\"localhost\" IDENTIFIED BY \"%DB_PASS%\";'"
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "mysql -u root -e 'GRANT ALL PRIVILEGES ON %DB_NAME%.* TO \"%DB_USER%\"@\"localhost\"; FLUSH PRIVILEGES;'"
echo ✓ 数据库和用户配置完成
echo.

echo 步骤3: 导入数据...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "mysql -u root %DB_NAME% < /tmp/database_import.sql"
if %errorlevel% neq 0 (
    echo ❌ 数据导入失败
    pause
    exit /b 1
)
echo ✓ 数据导入成功
echo.

echo 步骤4: 验证导入结果...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "mysql -u %DB_USER% -p%DB_PASS% -D %DB_NAME% -e 'SHOW TABLES;'"
echo.

echo 步骤5: 清理临时文件...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "rm -f /tmp/database_import.sql"
echo ✓ 临时文件清理完成
echo.

echo ========================================
echo 数据库导入完成！
echo ========================================
echo.
echo 数据库连接信息:
echo 主机: localhost
echo 数据库: %DB_NAME%
echo 用户名: %DB_USER%
echo 密码: %DB_PASS%
echo.

pause
