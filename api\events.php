<?php
/**
 * 日程管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// 模拟用户ID（实际应用中应该从session获取）
$userId = 1; // 假设当前用户ID为1

try {
    $db = db();
    
    switch ($method) {
        case 'GET':
            handleGet($db, $userId);
            break;
        case 'POST':
            handlePost($db, $userId, $input);
            break;
        case 'PUT':
            handlePut($db, $userId, $input);
            break;
        case 'DELETE':
            handleDelete($db, $userId);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取日程
 */
function handleGet($db, $userId) {
    $date = $_GET['date'] ?? null;
    
    if ($date) {
        // 获取指定日期的日程
        $sql = "SELECT * FROM events WHERE user_id = ? AND event_date = ? ORDER BY start_time";
        $events = $db->fetchAll($sql, [$userId, $date]);
    } else {
        // 获取所有日程
        $sql = "SELECT * FROM events WHERE user_id = ? ORDER BY event_date, start_time";
        $events = $db->fetchAll($sql, [$userId]);
    }
    
    echo json_encode(['success' => true, 'data' => $events]);
}

/**
 * 处理POST请求 - 创建日程
 */
function handlePost($db, $userId, $input) {
    if (!$input || !isset($input['title'], $input['event_date'], $input['start_time'], $input['end_time'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        return;
    }
    
    $data = [
        'user_id' => $userId,
        'title' => $input['title'],
        'description' => $input['description'] ?? '',
        'event_date' => $input['event_date'],
        'start_time' => $input['start_time'],
        'end_time' => $input['end_time']
    ];
    
    $sql = "INSERT INTO events (user_id, title, description, event_date, start_time, end_time) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $db->query($sql, [
        $data['user_id'],
        $data['title'],
        $data['description'],
        $data['event_date'],
        $data['start_time'],
        $data['end_time']
    ]);
    
    $eventId = $db->lastInsertId();
    
    echo json_encode([
        'success' => true, 
        'message' => '日程创建成功',
        'data' => ['id' => $eventId]
    ]);
}

/**
 * 处理PUT请求 - 更新日程
 */
function handlePut($db, $userId, $input) {
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少日程ID']);
        return;
    }
    
    $eventId = $input['id'];
    
    // 检查日程是否属于当前用户
    $sql = "SELECT id FROM events WHERE id = ? AND user_id = ?";
    $event = $db->fetchOne($sql, [$eventId, $userId]);
    
    if (!$event) {
        http_response_code(404);
        echo json_encode(['error' => '日程不存在或无权限']);
        return;
    }
    
    // 构建更新数据
    $updateFields = [];
    $params = [];
    
    if (isset($input['title'])) {
        $updateFields[] = 'title = ?';
        $params[] = $input['title'];
    }
    if (isset($input['description'])) {
        $updateFields[] = 'description = ?';
        $params[] = $input['description'];
    }
    if (isset($input['event_date'])) {
        $updateFields[] = 'event_date = ?';
        $params[] = $input['event_date'];
    }
    if (isset($input['start_time'])) {
        $updateFields[] = 'start_time = ?';
        $params[] = $input['start_time'];
    }
    if (isset($input['end_time'])) {
        $updateFields[] = 'end_time = ?';
        $params[] = $input['end_time'];
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => '没有要更新的字段']);
        return;
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $eventId;
    
    $sql = "UPDATE events SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->query($sql, $params);
    
    echo json_encode(['success' => true, 'message' => '日程更新成功']);
}

/**
 * 处理DELETE请求 - 删除日程
 */
function handleDelete($db, $userId) {
    $eventId = $_GET['id'] ?? null;
    
    if (!$eventId) {
        http_response_code(400);
        echo json_encode(['error' => '缺少日程ID']);
        return;
    }
    
    // 检查日程是否属于当前用户
    $sql = "SELECT id FROM events WHERE id = ? AND user_id = ?";
    $event = $db->fetchOne($sql, [$eventId, $userId]);
    
    if (!$event) {
        http_response_code(404);
        echo json_encode(['error' => '日程不存在或无权限']);
        return;
    }
    
    $sql = "DELETE FROM events WHERE id = ? AND user_id = ?";
    $db->query($sql, [$eventId, $userId]);
    
    echo json_encode(['success' => true, 'message' => '日程删除成功']);
}
?>
