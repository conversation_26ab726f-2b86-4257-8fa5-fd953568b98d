#!/bin/bash
# 比特熊智慧系统服务器配置脚本

echo "=== 比特熊智慧系统服务器配置开始 ==="

# 1. 更新系统
echo "1. 更新系统包..."
apt update && apt upgrade -y

# 2. 安装必要软件
echo "2. 安装基础软件..."
apt install -y nginx git curl wget unzip

# 3. 安装Node.js (如果需要)
echo "3. 安装Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt install -y nodejs

# 4. 配置Nginx
echo "4. 配置Nginx..."
systemctl start nginx
systemctl enable nginx

# 5. 创建网站目录
echo "5. 创建网站目录..."
mkdir -p /var/www/bitbear
chown -R www-data:www-data /var/www/bitbear

# 6. 配置防火墙
echo "6. 配置防火墙..."
ufw allow 22
ufw allow 80
ufw allow 443
ufw --force enable

# 7. 创建Nginx站点配置
cat > /etc/nginx/sites-available/bitbear << 'EOF'
server {
    listen 80;
    server_name _;
    root /var/www/bitbear;
    index index.html index.htm;

    location / {
        try_files $uri $uri/ =404;
    }

    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 8. 启用站点
ln -sf /etc/nginx/sites-available/bitbear /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 9. 测试并重启Nginx
nginx -t && systemctl restart nginx

echo "=== 服务器配置完成 ==="
echo "网站目录: /var/www/bitbear"
echo "请上传您的网站文件到此目录"
