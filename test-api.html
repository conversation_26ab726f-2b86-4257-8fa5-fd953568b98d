<!DOCTYPE html>
<html>
<head>
    <title>API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-data { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>API功能测试</h1>
    <button onclick="testAPI()">测试API调用</button>
    <button onclick="clearResults()">清除结果</button>
    <div id="results"></div>
    
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testAPI() {
            log('开始API测试...', 'info');
            
            const testData = {
                action: 'add_category',
                name: '测试分类_' + Date.now(),
                description: '这是一个自动测试创建的分类',
                color: '#ff0000',
                icon: '🧪',
                sort_order: 999,
                is_active: true
            };
            
            log('测试数据:', 'info');
            const dataDiv = document.createElement('div');
            dataDiv.className = 'test-data';
            dataDiv.textContent = JSON.stringify(testData, null, 2);
            document.getElementById('results').appendChild(dataDiv);
            
            try {
                log('发送API请求...', 'info');
                
                const response = await fetch('api/admin-community.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });
                
                log(`HTTP状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                
                const result = await response.json();
                log('API响应:', 'info');
                
                const responseDiv = document.createElement('div');
                responseDiv.className = 'test-data';
                responseDiv.textContent = JSON.stringify(result, null, 2);
                document.getElementById('results').appendChild(responseDiv);
                
                if (result.success) {
                    log('✓ API调用成功！分类已创建', 'success');
                    if (result.id) {
                        log(`✓ 新分类ID: ${result.id}`, 'success');
                    }
                } else {
                    log(`✗ API调用失败: ${result.error || '未知错误'}`, 'error');
                }
                
            } catch (error) {
                log(`✗ 请求失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }
        
        // 页面加载后自动测试
        window.addEventListener('load', function() {
            log('页面加载完成，准备测试API', 'info');
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
