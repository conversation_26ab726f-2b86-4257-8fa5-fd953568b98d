<?php
// 配置文件示例 - 复制为config.php并修改相应值

// 环境配置
define('ENVIRONMENT', 'production'); // development, testing, production

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'wisdom_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_CHARSET', 'utf8mb4');

// 网站配置
define('SITE_URL', 'https://your-domain.com');
define('SITE_NAME', '比特熊智慧系统');

// 安全配置
define('SECRET_KEY', 'your-secret-key-here');
define('SESSION_LIFETIME', 3600); // 1小时

// 文件上传配置
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// 邮件配置（如果需要）
define('SMTP_HOST', 'smtp.example.com');
define('SMTP_PORT', 587);
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'your-email-password');

// 调试配置
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 日志配置
define('LOG_PATH', __DIR__ . '/../logs/');
define('LOG_LEVEL', ENVIRONMENT === 'production' ? 'ERROR' : 'DEBUG');
?>
