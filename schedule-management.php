<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日程管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1d4ed8;
            --accent-color: #60a5fa;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .controls-section {
            background: white;
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .view-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .view-selector label {
            color: #333;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .btn-group .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
        }

        .btn-group .btn.active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .btn-group .btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-primary {
            background: rgba(59, 130, 246, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(59, 130, 246, 0.9);
            border-color: rgba(59, 130, 246, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .calendar-container {
            background: white;
            border-radius: 0;
            padding: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .calendar-header {
            background: rgba(255, 255, 255, 0.15);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .calendar-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .calendar-nav {
            display: flex;
            gap: 0.5rem;
        }

        .calendar-nav .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .calendar-nav .btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: rgba(255, 255, 255, 0.1);
            padding: 1px;
        }

        .calendar-day-header {
            background: rgba(255, 255, 255, 0.15);
            padding: 1rem;
            text-align: center;
            color: #333;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .calendar-day {
            background: rgba(255, 255, 255, 0.05);
            min-height: 120px;
            padding: 0.5rem;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .calendar-day:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .calendar-day.today {
            background: rgba(59, 130, 246, 0.3);
            border: 2px solid rgba(59, 130, 246, 0.8);
        }

        .calendar-day.other-month {
            background: rgba(255, 255, 255, 0.02);
            color: rgba(255, 255, 255, 0.4);
        }

        .day-number {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .day-events {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .event-item {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .event-item:hover {
            background: rgba(59, 130, 246, 1);
            transform: scale(1.02);
        }

        .event-item.course {
            background: rgba(16, 185, 129, 0.8);
        }

        .event-item.exam {
            background: rgba(239, 68, 68, 0.8);
        }

        .event-item.assignment {
            background: rgba(245, 158, 11, 0.8);
        }

        .event-item.personal {
            background: rgba(139, 92, 246, 0.8);
        }

        @media (max-width: 768px) {
            .calendar-day {
                min-height: 80px;
                padding: 0.25rem;
            }
            
            .calendar-day-header {
                padding: 0.5rem;
                font-size: 0.8rem;
            }
            
            .controls-section {
                padding: 0.5rem;
            }
            
            .view-selector {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-calendar-alt me-2"></i>
                日程管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">日程管理</h1>
            <p class="page-description">管理课程表、日程安排、提醒通知和重复事件</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="view-selector">
                <label>视图模式：</label>
                <div class="btn-group" role="group">
                    <button type="button" class="btn active" onclick="switchView('month')">月视图</button>
                    <button type="button" class="btn" onclick="switchView('week')">周视图</button>
                    <button type="button" class="btn" onclick="switchView('day')">日视图</button>
                </div>
                
                <div class="action-buttons ms-auto">
                    <button class="btn btn-success" onclick="addEvent()">
                        <i class="fas fa-plus me-1"></i>
                        添加事件
                    </button>
                    <button class="btn btn-primary" onclick="importSchedule()">
                        <i class="fas fa-download me-1"></i>
                        导入课程表
                    </button>
                </div>
            </div>
        </div>

        <!-- 日历容器 -->
        <div class="calendar-container">
            <div class="calendar-header">
                <h2 class="calendar-title" id="calendarTitle">2024年1月</h2>
                <div class="calendar-nav">
                    <button class="btn" onclick="previousMonth()">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn" onclick="today()">
                        <i class="fas fa-calendar-day"></i>
                    </button>
                    <button class="btn" onclick="nextMonth()">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <div class="calendar-grid" id="calendarGrid">
                <!-- 日历网格将通过JavaScript生成 -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentDate = new Date();
        let currentView = 'month';

        // 示例事件数据
        const events = [
            { date: '2024-01-15', title: '高等数学', type: 'course', time: '08:00' },
            { date: '2024-01-15', title: '英语听力', type: 'course', time: '10:00' },
            { date: '2024-01-16', title: '数据结构作业', type: 'assignment', time: '23:59' },
            { date: '2024-01-18', title: '期中考试', type: 'exam', time: '14:00' },
            { date: '2024-01-20', title: '社团活动', type: 'personal', time: '19:00' }
        ];

        function initCalendar() {
            updateCalendarTitle();
            generateCalendarGrid();
        }

        function updateCalendarTitle() {
            const months = ['1月', '2月', '3月', '4月', '5月', '6月', 
                          '7月', '8月', '9月', '10月', '11月', '12月'];
            document.getElementById('calendarTitle').textContent = 
                `${currentDate.getFullYear()}年${months[currentDate.getMonth()]}`;
        }

        function generateCalendarGrid() {
            const grid = document.getElementById('calendarGrid');
            grid.innerHTML = '';

            // 添加星期标题
            const dayHeaders = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            dayHeaders.forEach(day => {
                const header = document.createElement('div');
                header.className = 'calendar-day-header';
                header.textContent = day;
                grid.appendChild(header);
            });

            // 生成日期格子
            const firstDay = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            for (let i = 0; i < 42; i++) {
                const cellDate = new Date(startDate);
                cellDate.setDate(startDate.getDate() + i);
                
                const dayCell = document.createElement('div');
                dayCell.className = 'calendar-day';
                
                if (cellDate.getMonth() !== currentDate.getMonth()) {
                    dayCell.classList.add('other-month');
                }
                
                if (isToday(cellDate)) {
                    dayCell.classList.add('today');
                }

                const dayNumber = document.createElement('div');
                dayNumber.className = 'day-number';
                dayNumber.textContent = cellDate.getDate();
                dayCell.appendChild(dayNumber);

                const eventsContainer = document.createElement('div');
                eventsContainer.className = 'day-events';
                
                // 添加当天的事件
                const dayEvents = getEventsForDate(cellDate);
                dayEvents.forEach(event => {
                    const eventElement = document.createElement('div');
                    eventElement.className = `event-item ${event.type}`;
                    eventElement.textContent = event.title;
                    eventElement.onclick = () => showEventDetails(event);
                    eventsContainer.appendChild(eventElement);
                });

                dayCell.appendChild(eventsContainer);
                dayCell.onclick = () => selectDate(cellDate);
                grid.appendChild(dayCell);
            }
        }

        function isToday(date) {
            const today = new Date();
            return date.toDateString() === today.toDateString();
        }

        function getEventsForDate(date) {
            const dateStr = date.toISOString().split('T')[0];
            return events.filter(event => event.date === dateStr);
        }

        function previousMonth() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            initCalendar();
        }

        function nextMonth() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            initCalendar();
        }

        function today() {
            currentDate = new Date();
            initCalendar();
        }

        function switchView(view) {
            currentView = view;
            document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            // 这里可以添加不同视图的逻辑
        }

        function addEvent() {
            alert('添加事件功能正在开发中...');
        }

        function importSchedule() {
            alert('导入课程表功能正在开发中...');
        }

        function selectDate(date) {
            console.log('选择日期:', date);
        }

        function showEventDetails(event) {
            alert(`事件详情:\n标题: ${event.title}\n类型: ${event.type}\n时间: ${event.time}`);
        }

        // 初始化日历
        document.addEventListener('DOMContentLoaded', initCalendar);
    </script>
</body>
</html>
