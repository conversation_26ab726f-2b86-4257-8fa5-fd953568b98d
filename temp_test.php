﻿<?php
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    echo "Database connection successful\n";
    $result = $db->query('SELECT 1 as test');
    echo "Query test successful\n";
    
    // 娴嬭瘯鐢ㄦ埛琛ㄦ槸鍚﹀瓨鍦?    try {
        $tables = $db->fetchAll("SHOW TABLES LIKE 'users'");
        if (empty($tables)) {
            echo "Users table does not exist\n";
        } else {
            echo "Users table exists\n";
            $count = $db->fetchOne('SELECT COUNT(*) as count FROM users');
            echo "Users count: " . $count['count'] . "\n";
        }
    } catch (Exception $e) {
        echo "Table check error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>

