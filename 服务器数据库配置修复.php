<?php
/**
 * 服务器数据库配置修复文件
 * 请将此内容替换到 config/database.php 中的服务器环境检测部分
 */

// 修复后的服务器环境检测函数
private function isServerEnvironment() {
    // 检查是否在服务器环境中
    $serverChecks = [
        // 检查域名
        isset($_SERVER['SERVER_NAME']) && (
            strpos($_SERVER['SERVER_NAME'], 'bitbear.top') !== false ||
            strpos($_SERVER['SERVER_NAME'], '*************') !== false
        ),
        // 检查服务器IP
        isset($_SERVER['SERVER_ADDR']) && $_SERVER['SERVER_ADDR'] === '*************',
        // 检查HTTP_HOST
        isset($_SERVER['HTTP_HOST']) && (
            strpos($_SERVER['HTTP_HOST'], 'bitbear.top') !== false ||
            strpos($_SERVER['HTTP_HOST'], '*************') !== false
        ),
        // 检查文档根目录
        isset($_SERVER['DOCUMENT_ROOT']) && strpos($_SERVER['DOCUMENT_ROOT'], '/www/wwwroot') !== false
    ];
    
    // 任何一个条件满足就认为是服务器环境
    foreach ($serverChecks as $check) {
        if ($check) {
            return true;
        }
    }
    
    return false;
}

// 修复后的环境配置函数
private function getEnvironmentConfig() {
    if ($this->isServerEnvironment()) {
        // 服务器环境配置
        return [
            'host' => 'localhost',
            'username' => 'root',
            'password' => '309290133q',
            'database' => 'bitbear_website',
            'charset' => 'utf8mb4',
            'ports' => [3306] // 服务器通常使用3306端口
        ];
    } else {
        // 本地环境配置
        return [
            'host' => $this->host,
            'username' => $this->username,
            'password' => $this->password,
            'database' => $this->database,
            'charset' => $this->charset,
            'ports' => $this->ports
        ];
    }
}
?>
