<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH连接指南 - 腾讯云服务器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .server-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #64748b;
        }
        
        .info-value {
            font-family: monospace;
            background: #f1f5f9;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            color: #1e293b;
        }
        
        .command-box {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            overflow-x: auto;
            position: relative;
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-button:hover {
            background: #2563eb;
        }
        
        .step {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #3b82f6;
        }
        
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        
        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }
        
        .alert-success {
            background: #dcfce7;
            border-color: #16a34a;
            color: #15803d;
        }
        
        .connect-button {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
        }
        
        .connect-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .commands-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .command-category {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .command-category h4 {
            margin-top: 0;
            color: #1e293b;
        }
        
        .command-list {
            list-style: none;
            padding: 0;
        }
        
        .command-list li {
            padding: 0.25rem 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .command-list .cmd {
            color: #059669;
            font-weight: 600;
        }
        
        .command-list .desc {
            color: #64748b;
            margin-left: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 SSH连接指南 - 腾讯云服务器</h1>
        
        <div class="server-info">
            <h3>📋 服务器信息</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">IP地址:</span>
                    <span class="info-value">*************</span>
                </div>
                <div class="info-item">
                    <span class="info-label">用户名:</span>
                    <span class="info-value">root</span>
                </div>
                <div class="info-item">
                    <span class="info-label">密码:</span>
                    <span class="info-value">ZbDX7%=]?H2(LAUz</span>
                </div>
                <div class="info-item">
                    <span class="info-label">操作系统:</span>
                    <span class="info-value">CentOS 7.8 64bit</span>
                </div>
                <div class="info-item">
                    <span class="info-label">SSH端口:</span>
                    <span class="info-value">22</span>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success">
            <strong>✅ 好消息：</strong> 检测到您的系统已安装Windows内置SSH客户端，可以直接连接！
        </div>
        
        <h2>🚀 快速连接</h2>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>打开命令提示符或PowerShell</strong>
            <p>按 <kbd>Win + R</kbd>，输入 <code>cmd</code> 或 <code>powershell</code>，然后按回车。</p>
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <strong>执行SSH连接命令</strong>
            <p>复制并粘贴以下命令：</p>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('ssh-command')">复制</button>
                <div id="ssh-command">ssh root@*************</div>
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <strong>输入密码</strong>
            <p>当提示输入密码时，输入以下密码（注意：输入时不会显示字符，这是正常的安全特性）：</p>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('password')">复制</button>
                <div id="password">ZbDX7%=]?H2(LAUz</div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <strong>💡 提示：</strong> 首次连接时，系统会询问是否信任服务器指纹，输入 <code>yes</code> 并按回车即可。
        </div>
        
        <h2>🛠️ 一键连接工具</h2>
        
        <p>您也可以使用我为您准备的连接工具：</p>
        
        <div style="text-align: center;">
            <button class="connect-button" onclick="runSSHScript()">运行PowerShell连接脚本</button>
            <button class="connect-button" onclick="runBatchScript()">运行批处理连接脚本</button>
        </div>
        
        <h2>📚 连接后常用命令</h2>
        
        <div class="commands-grid">
            <div class="command-category">
                <h4>🗂️ 文件和目录操作</h4>
                <ul class="command-list">
                    <li><span class="cmd">ls -la</span><span class="desc"># 列出所有文件（包括隐藏文件）</span></li>
                    <li><span class="cmd">pwd</span><span class="desc"># 显示当前目录路径</span></li>
                    <li><span class="cmd">cd /var/www</span><span class="desc"># 切换到网站目录</span></li>
                    <li><span class="cmd">find / -name "*.conf"</span><span class="desc"># 查找配置文件</span></li>
                </ul>
            </div>
            
            <div class="command-category">
                <h4>🔧 服务管理</h4>
                <ul class="command-list">
                    <li><span class="cmd">systemctl status httpd</span><span class="desc"># 检查Apache状态</span></li>
                    <li><span class="cmd">systemctl status nginx</span><span class="desc"># 检查Nginx状态</span></li>
                    <li><span class="cmd">systemctl restart httpd</span><span class="desc"># 重启Apache</span></li>
                    <li><span class="cmd">systemctl enable nginx</span><span class="desc"># 设置Nginx开机启动</span></li>
                </ul>
            </div>
            
            <div class="command-category">
                <h4>🌐 网络和端口</h4>
                <ul class="command-list">
                    <li><span class="cmd">netstat -tlnp</span><span class="desc"># 显示监听的端口</span></li>
                    <li><span class="cmd">ss -tlnp</span><span class="desc"># 现代版本的netstat</span></li>
                    <li><span class="cmd">firewall-cmd --list-all</span><span class="desc"># 查看防火墙规则</span></li>
                    <li><span class="cmd">curl localhost:8888</span><span class="desc"># 测试本地8888端口</span></li>
                </ul>
            </div>
            
            <div class="command-category">
                <h4>📊 系统监控</h4>
                <ul class="command-list">
                    <li><span class="cmd">ps aux</span><span class="desc"># 显示所有运行进程</span></li>
                    <li><span class="cmd">top</span><span class="desc"># 实时显示系统资源使用</span></li>
                    <li><span class="cmd">df -h</span><span class="desc"># 显示磁盘使用情况</span></li>
                    <li><span class="cmd">free -h</span><span class="desc"># 显示内存使用情况</span></li>
                </ul>
            </div>
            
            <div class="command-category">
                <h4>📝 日志查看</h4>
                <ul class="command-list">
                    <li><span class="cmd">tail -f /var/log/messages</span><span class="desc"># 实时查看系统日志</span></li>
                    <li><span class="cmd">journalctl -u httpd</span><span class="desc"># 查看Apache日志</span></li>
                    <li><span class="cmd">tail -f /var/log/httpd/error_log</span><span class="desc"># 查看Apache错误日志</span></li>
                    <li><span class="cmd">grep "ERROR" /var/log/messages</span><span class="desc"># 搜索错误信息</span></li>
                </ul>
            </div>
            
            <div class="command-category">
                <h4>🔍 问题排查</h4>
                <ul class="command-list">
                    <li><span class="cmd">which httpd</span><span class="desc"># 查找Apache安装位置</span></li>
                    <li><span class="cmd">rpm -qa | grep httpd</span><span class="desc"># 查看已安装的Apache包</span></li>
                    <li><span class="cmd">lsof -i :8888</span><span class="desc"># 查看8888端口被哪个进程占用</span></li>
                    <li><span class="cmd">exit</span><span class="desc"># 断开SSH连接</span></li>
                </ul>
            </div>
        </div>
        
        <div class="alert alert-warning">
            <strong>⚠️ 安全提醒：</strong>
            <ul>
                <li>连接成功后，建议立即修改root密码</li>
                <li>考虑创建普通用户账户，避免直接使用root</li>
                <li>配置SSH密钥认证，提高安全性</li>
                <li>定期更新系统和软件包</li>
            </ul>
        </div>
        
        <h2>🔧 解决/tencentcloud路径问题</h2>
        
        <div class="alert alert-info">
            <strong>连接到服务器后，您可以检查以下内容来解决404问题：</strong>
        </div>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>检查Web服务器状态</strong>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('check-services')">复制</button>
                <div id="check-services"># 检查Apache
systemctl status httpd

# 检查Nginx  
systemctl status nginx

# 查看监听端口
netstat -tlnp | grep :8888</div>
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <strong>检查网站目录</strong>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('check-webdir')">复制</button>
                <div id="check-webdir"># 查看常见网站目录
ls -la /var/www/
ls -la /var/www/html/
ls -la /usr/share/nginx/html/

# 查找tencentcloud相关文件
find / -name "*tencentcloud*" 2>/dev/null</div>
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <strong>检查Web服务器配置</strong>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('check-config')">复制</button>
                <div id="check-config"># 查看Apache配置
cat /etc/httpd/conf/httpd.conf | grep DocumentRoot
cat /etc/httpd/conf.d/*.conf

# 查看Nginx配置
cat /etc/nginx/nginx.conf
cat /etc/nginx/conf.d/*.conf</div>
            </div>
        </div>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                const button = element.parentNode.querySelector('.copy-button');
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#16a34a';
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.style.background = '#3b82f6';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制内容');
            });
        }
        
        function runSSHScript() {
            alert('请在文件资源管理器中找到 ssh-connect.ps1 文件，右键选择"使用PowerShell运行"');
        }
        
        function runBatchScript() {
            alert('请在文件资源管理器中找到 quick-ssh-connect.bat 文件，双击运行');
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔗 SSH连接指南已加载');
            console.log('📋 服务器信息: ************* (CentOS 7.8)');
        });
    </script>
</body>
</html>
