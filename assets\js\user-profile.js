// 用户个人资料页面交互功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initTabSwitching();
    initFollowButton();
});

// 标签页切换功能
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // 移除所有活动状态
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // 激活当前标签
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
}

// 关注按钮功能
function initFollowButton() {
    const followBtn = document.querySelector('.follow-btn');
    
    if (followBtn) {
        followBtn.addEventListener('click', function() {
            const userId = this.dataset.userId;
            const isFollowing = this.classList.contains('btn-outline');
            
            handleFollow(userId, !isFollowing);
        });
    }
}

// 处理关注/取消关注
async function handleFollow(userId, follow) {
    try {
        const response = await fetch('api/user-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: follow ? 'follow' : 'unfollow',
                user_id: userId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            updateFollowButton(follow, result.data.followers_count);
            showToast(follow ? '关注成功' : '取消关注成功', 'success');
        } else {
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('关注操作失败:', error);
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 更新关注按钮状态
function updateFollowButton(isFollowing, followersCount) {
    const followBtn = document.querySelector('.follow-btn');
    const followersCountElement = document.querySelector('.profile-stats .stat-item:nth-child(4) .stat-number');
    
    if (followBtn) {
        const icon = followBtn.querySelector('i');
        const text = followBtn.querySelector('span') || followBtn.childNodes[followBtn.childNodes.length - 1];
        
        if (isFollowing) {
            followBtn.className = 'btn btn-outline follow-btn';
            icon.className = 'fas fa-user-minus';
            if (text.nodeType === Node.TEXT_NODE) {
                text.textContent = ' 取消关注';
            } else {
                text.textContent = '取消关注';
            }
        } else {
            followBtn.className = 'btn btn-primary follow-btn';
            icon.className = 'fas fa-user-plus';
            if (text.nodeType === Node.TEXT_NODE) {
                text.textContent = ' 关注';
            } else {
                text.textContent = '关注';
            }
        }
    }
    
    // 更新粉丝数
    if (followersCountElement && followersCount !== undefined) {
        followersCountElement.textContent = formatNumber(followersCount);
    }
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'
    });
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 懒加载图片
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 处理键盘导航
document.addEventListener('keydown', function(e) {
    // ESC键返回社区首页
    if (e.key === 'Escape') {
        window.location.href = 'community.php';
    }
    
    // 数字键切换标签页
    if (e.key >= '1' && e.key <= '2') {
        const tabIndex = parseInt(e.key) - 1;
        const tabButtons = document.querySelectorAll('.tab-btn');
        if (tabButtons[tabIndex]) {
            tabButtons[tabIndex].click();
        }
    }
});

// 处理页面可见性变化
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，可以刷新一些数据
        console.log('页面重新可见');
    }
});

// 初始化懒加载（如果需要）
if ('IntersectionObserver' in window) {
    initLazyLoading();
}
