@echo off
echo ========================================
echo XAMPP 安装脚本 - 比特熊项目
echo ========================================
echo.

:: 检查是否已安装XAMPP
if exist "C:\xampp\php\php.exe" (
    echo XAMPP 已经安装！
    C:\xampp\php\php.exe --version
    echo.
    goto :start_server
)

echo 正在检查XAMPP安装...
echo XAMPP 未找到，开始安装过程...
echo.

echo 正在下载 XAMPP...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 下载XAMPP
powershell -Command "& {
    $url = 'https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe/download'
    $output = '%TEMP%\xampp-installer.exe'
    Write-Host '下载 XAMPP...'
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
        Write-Host 'XAMPP 下载完成！'
    } catch {
        Write-Host '下载失败，请检查网络连接'
        exit 1
    }
}"

if not exist "%TEMP%\xampp-installer.exe" (
    echo 下载失败！请手动下载XAMPP。
    echo 请访问: https://www.apachefriends.org/download.html
    pause
    exit /b 1
)

echo.
echo 正在启动 XAMPP 安装程序...
echo 请按照安装向导完成安装。
echo 建议安装到默认位置: C:\xampp
echo.
pause

:: 运行安装程序
start /wait "%TEMP%\xampp-installer.exe"

:: 清理临时文件
del "%TEMP%\xampp-installer.exe"

:: 验证安装
if exist "C:\xampp\php\php.exe" (
    echo.
    echo ✅ XAMPP 安装成功！
    C:\xampp\php\php.exe --version
    echo.
) else (
    echo ❌ XAMPP 安装失败或安装到了其他位置！
    echo 请手动检查安装路径。
    pause
    exit /b 1
)

:start_server
echo ========================================
echo 启动 PHP 开发服务器
echo ========================================
echo.
echo 服务器将在以下地址运行:
echo   http://localhost:8000
echo.
echo 可用页面:
echo   - 主页: http://localhost:8000/index.php
echo   - 管理后台: http://localhost:8000/admin.php
echo.
echo 按 Ctrl+C 停止服务器
echo.

:: 启动PHP服务器
C:\xampp\php\php.exe -S localhost:8000

pause
