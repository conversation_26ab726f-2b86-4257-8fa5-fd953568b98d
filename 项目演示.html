<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊项目演示 - 专家展示区域</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .demo-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .demo-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .demo-btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .demo-btn.secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .demo-btn.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .features {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 2rem;
        }
        
        .features h2 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .feature-item {
            text-align: center;
            padding: 1rem;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        .status {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .status h2 {
            color: #27ae60;
            margin-bottom: 1rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .status-item {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .status-item.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-item.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 比特熊项目演示</h1>
            <p>O'Reilly风格动态专家展示区域 - 项目已成功部署！</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🌟 静态版本 (推荐)</h3>
                <p>完整的视觉效果和动画，无需服务器环境。包含所有专家卡片和O'Reilly风格设计。</p>
                <a href="index-standalone.html" class="demo-btn">查看静态版本</a>
                <a href="index.html" class="demo-btn secondary">标准HTML版本</a>
            </div>
            
            <div class="demo-card">
                <h3>🚀 动态版本 (PHP)</h3>
                <p>支持动态数据管理的完整版本，需要PHP服务器环境。可以通过后台添加/删除专家。</p>
                <a href="index.php" class="demo-btn">PHP主页</a>
                <a href="admin.php" class="demo-btn success">管理后台</a>
            </div>
            
            <div class="demo-card">
                <h3>🔧 项目工具</h3>
                <p>快速启动脚本和安装工具，帮助您轻松部署和运行项目。</p>
                <a href="PHP服务器启动指南.md" class="demo-btn">安装指南</a>
                <a href="#" onclick="alert('请运行 启动项目.bat 文件')" class="demo-btn secondary">启动脚本</a>
            </div>
        </div>
        
        <div class="features">
            <h2>✨ 项目特性</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">O'Reilly风格设计</div>
                    <div class="feature-desc">深色渐变背景，现代化专业设计</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎭</div>
                    <div class="feature-title">动画效果</div>
                    <div class="feature-desc">滚动动画、悬停3D效果、渐变叠加</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">响应式设计</div>
                    <div class="feature-desc">完美适配桌面、平板、手机设备</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">动态数据</div>
                    <div class="feature-desc">支持动态添加/删除专家信息</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🛠️</div>
                    <div class="feature-title">管理后台</div>
                    <div class="feature-desc">直观的专家信息管理界面</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">快速部署</div>
                    <div class="feature-desc">多种启动方式，一键部署</div>
                </div>
            </div>
        </div>
        
        <div class="status">
            <h2>🎯 项目状态</h2>
            <p>所有功能已完成开发并测试通过</p>
            
            <div class="status-grid">
                <div class="status-item success">
                    <strong>✅ 专家展示区域</strong><br>
                    O'Reilly风格设计完成
                </div>
                <div class="status-item success">
                    <strong>✅ 动画效果</strong><br>
                    AOS滚动动画集成
                </div>
                <div class="status-item success">
                    <strong>✅ 响应式布局</strong><br>
                    多设备完美适配
                </div>
                <div class="status-item success">
                    <strong>✅ 管理后台</strong><br>
                    PHP动态管理系统
                </div>
                <div class="status-item success">
                    <strong>✅ 部署脚本</strong><br>
                    多种启动方式支持
                </div>
                <div class="status-item warning">
                    <strong>⚠️ 服务器环境</strong><br>
                    需要PHP支持完整功能
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加点击效果
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'A') {
                        const firstLink = this.querySelector('.demo-btn');
                        if (firstLink) {
                            firstLink.click();
                        }
                    }
                });
            });
            
            // 显示项目信息
            console.log('🎉 比特熊项目演示页面加载完成！');
            console.log('📄 可用页面:');
            console.log('  • 静态版本: index-standalone.html');
            console.log('  • 标准版本: index.html');
            console.log('  • PHP版本: index.php');
            console.log('  • 管理后台: admin.php');
        });
    </script>
</body>
</html>
