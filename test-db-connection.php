<?php
/**
 * 数据库连接测试页面
 * 用于诊断数据库连接问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>数据库连接测试</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>";

// 1. 测试基本PHP环境
echo "<h2>1. PHP环境检查</h2>";
echo "<div class='info'>PHP版本: " . PHP_VERSION . "</div>";

// 检查PDO扩展
if (extension_loaded('pdo')) {
    echo "<div class='success'>✓ PDO扩展已加载</div>";
    
    $drivers = PDO::getAvailableDrivers();
    echo "<div class='info'>可用的PDO驱动: " . implode(', ', $drivers) . "</div>";
    
    if (in_array('mysql', $drivers)) {
        echo "<div class='success'>✓ MySQL PDO驱动可用</div>";
    } else {
        echo "<div class='error'>✗ MySQL PDO驱动不可用</div>";
    }
    
    if (in_array('sqlite', $drivers)) {
        echo "<div class='success'>✓ SQLite PDO驱动可用</div>";
    } else {
        echo "<div class='warning'>⚠ SQLite PDO驱动不可用</div>";
    }
} else {
    echo "<div class='error'>✗ PDO扩展未加载</div>";
}

// 2. 测试数据库配置文件
echo "<h2>2. 数据库配置测试</h2>";
try {
    require_once 'config/database.php';
    echo "<div class='success'>✓ 数据库配置文件加载成功</div>";
    
    // 3. 测试数据库连接
    echo "<h2>3. 数据库连接测试</h2>";
    
    $db = DatabaseConfig::getInstance();
    echo "<div class='success'>✓ 数据库实例创建成功</div>";
    
    // 测试简单查询
    $result = $db->query("SELECT 1 as test");
    $row = $result->fetch();
    if ($row && $row['test'] == 1) {
        echo "<div class='success'>✓ 数据库查询测试成功</div>";
    } else {
        echo "<div class='error'>✗ 数据库查询测试失败</div>";
    }
    
    // 检查数据库类型
    $connection = $db->getConnection();
    $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<div class='info'>当前使用的数据库类型: " . strtoupper($driver) . "</div>";
    
    if ($driver === 'mysql') {
        // MySQL特定测试
        $version = $connection->query('SELECT VERSION() as version')->fetch();
        echo "<div class='info'>MySQL版本: " . $version['version'] . "</div>";
        
        // 检查数据库
        $dbName = $connection->query('SELECT DATABASE() as db')->fetch();
        echo "<div class='info'>当前数据库: " . ($dbName['db'] ?? '未选择') . "</div>";
        
    } elseif ($driver === 'sqlite') {
        // SQLite特定测试
        $version = $connection->query('SELECT sqlite_version() as version')->fetch();
        echo "<div class='info'>SQLite版本: " . $version['version'] . "</div>";
        
        // 检查数据库文件
        $dbFile = $connection->query("PRAGMA database_list")->fetch();
        echo "<div class='info'>SQLite数据库文件: " . $dbFile['file'] . "</div>";
    }
    
    // 4. 测试表结构
    echo "<h2>4. 数据库表检查</h2>";
    
    $tables = [];
    if ($driver === 'mysql') {
        $result = $db->query("SHOW TABLES");
        while ($row = $result->fetch()) {
            $tables[] = array_values($row)[0];
        }
    } elseif ($driver === 'sqlite') {
        $result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        while ($row = $result->fetch()) {
            $tables[] = $row['name'];
        }
    }
    
    if (empty($tables)) {
        echo "<div class='warning'>⚠ 数据库中没有找到表，可能需要初始化数据库</div>";
    } else {
        echo "<div class='success'>✓ 找到 " . count($tables) . " 个数据库表</div>";
        echo "<div class='info'>表列表: " . implode(', ', $tables) . "</div>";
        
        // 检查关键表
        $requiredTables = ['users', 'user_roles', 'navbar_items'];
        foreach ($requiredTables as $table) {
            if (in_array($table, $tables)) {
                echo "<div class='success'>✓ 表 '{$table}' 存在</div>";
                
                // 检查表记录数
                try {
                    $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}");
                    echo "<div class='info'>  - 记录数: " . $count['count'] . "</div>";
                } catch (Exception $e) {
                    echo "<div class='warning'>  - 无法查询记录数: " . $e->getMessage() . "</div>";
                }
            } else {
                echo "<div class='error'>✗ 表 '{$table}' 不存在</div>";
            }
        }
    }
    
    // 5. 测试认证系统
    echo "<h2>5. 认证系统测试</h2>";
    try {
        require_once 'classes/Auth.php';
        $auth = new Auth();
        echo "<div class='success'>✓ Auth类加载成功</div>";
        
        $isLoggedIn = $auth->isLoggedIn();
        echo "<div class='info'>当前登录状态: " . ($isLoggedIn ? '已登录' : '未登录') . "</div>";
        
        if ($isLoggedIn) {
            $user = $auth->getCurrentUser();
            echo "<div class='info'>当前用户: " . ($user['username'] ?? '未知') . "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>✗ 认证系统测试失败: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 数据库连接失败: " . $e->getMessage() . "</div>";
    echo "<div class='info'>错误详情:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    
    // 提供解决建议
    echo "<h2>解决建议</h2>";
    echo "<div class='warning'>";
    echo "1. 检查 XAMPP/WAMP/MAMP 是否正在运行<br>";
    echo "2. 确认MySQL服务已启动<br>";
    echo "3. 检查数据库配置文件中的用户名和密码<br>";
    echo "4. 尝试手动连接数据库确认配置正确<br>";
    echo "5. 如果MySQL不可用，系统会自动使用SQLite作为备用数据库<br>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='admin-dashboard.php'>管理后台</a></p>";
?>
