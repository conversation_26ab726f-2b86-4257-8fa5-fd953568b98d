-- 健康管理模块数据库表

-- 自定义健康字段表
CREATE TABLE IF NOT EXISTS health_fields (
    id INT AUTO_INCREMENT PRIMARY KEY,
    field_name VARCHAR(100) NOT NULL COMMENT '字段名称',
    field_type ENUM('number', 'text', 'boolean', 'select') DEFAULT 'number' COMMENT '字段类型',
    field_unit VARCHAR(20) DEFAULT '' COMMENT '字段单位',
    field_options TEXT DEFAULT NULL COMMENT '选择项（JSON格式，用于select类型）',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否为系统字段',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_field_name (field_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康字段定义表';

-- 健康日志表
CREATE TABLE IF NOT EXISTS health_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    log_date DATE NOT NULL COMMENT '日志日期',
    field_id INT NOT NULL COMMENT '字段ID',
    field_value TEXT DEFAULT NULL COMMENT '字段值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (field_id) REFERENCES health_fields(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date_field (user_id, log_date, field_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='健康日志数据表';

-- 插入默认的系统字段
INSERT INTO health_fields (field_name, field_type, field_unit, display_order, is_system, is_active) VALUES
('发热', 'boolean', '', 1, TRUE, TRUE),
('咳嗽', 'boolean', '', 2, TRUE, TRUE),
('呼吸不通畅', 'boolean', '', 3, TRUE, TRUE),
('最大心率数', 'number', 'bpm', 4, TRUE, TRUE),
('最小心率数', 'number', 'bpm', 5, TRUE, TRUE),
('静息心率数', 'number', 'bpm', 6, TRUE, TRUE),
('心率异常总数', 'number', '次', 7, TRUE, TRUE),
('运动步数', 'number', '步', 8, TRUE, TRUE),
('房颤数', 'number', '次', 9, TRUE, TRUE),
('早搏数', 'number', '次', 10, TRUE, TRUE),
('疑似房颤标识', 'boolean', '', 11, TRUE, TRUE),
('疑似早搏标识', 'boolean', '', 12, TRUE, TRUE),
('血氧饱和度', 'number', '%', 13, TRUE, TRUE),
('其他', 'text', '', 14, TRUE, TRUE)
ON DUPLICATE KEY UPDATE 
    field_type = VALUES(field_type),
    field_unit = VALUES(field_unit),
    display_order = VALUES(display_order),
    is_system = VALUES(is_system),
    is_active = VALUES(is_active);

-- 创建索引以提高查询性能
CREATE INDEX idx_health_logs_user_date ON health_logs(user_id, log_date);
CREATE INDEX idx_health_logs_date ON health_logs(log_date);
CREATE INDEX idx_health_fields_order ON health_fields(display_order, is_active);
