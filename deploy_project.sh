#!/bin/bash
# 比特熊项目部署脚本

echo "=== 开始部署比特熊智慧系统 ==="

# 1. 备份现有文件（如果存在）
if [ -d "/var/www/bitbear" ]; then
    echo "1. 备份现有文件..."
    cp -r /var/www/bitbear /var/www/bitbear_backup_$(date +%Y%m%d_%H%M%S)
fi

# 2. 创建项目目录
echo "2. 创建项目目录..."
mkdir -p /var/www/bitbear
cd /var/www/bitbear

# 3. 上传文件提示
echo "3. 请将以下文件上传到 /var/www/bitbear/ 目录:"
echo "   - index.html"
echo "   - index-style.css"
echo "   - image/ (整个目录)"
echo "   - 其他项目文件"

# 4. 设置权限
echo "4. 设置文件权限..."
chown -R www-data:www-data /var/www/bitbear
chmod -R 755 /var/www/bitbear

# 5. 重启服务
echo "5. 重启Nginx..."
systemctl restart nginx

echo "=== 部署完成 ==="
echo "访问地址: http://43.134.80.134"
