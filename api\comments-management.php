<?php
// 启用错误报告用于调试
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../config/database.php';

// 检查管理员权限
session_start();

// 添加调试信息
error_log("评论管理API调用 - Session数据: " . print_r($_SESSION, true));
error_log("评论管理API调用 - 请求参数: " . print_r($_GET, true));

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => '未授权访问',
        'debug' => [
            'admin_logged_in' => $_SESSION['admin_logged_in'] ?? 'not_set',
            'session_keys' => array_keys($_SESSION)
        ]
    ]);
    exit;
}

header('Content-Type: application/json');

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'list':
            handleListComments($db);
            break;
        case 'stats':
            handleGetStats($db);
            break;
        case 'update_status':
            handleUpdateStatus($db);
            break;
        case 'delete':
            handleDeleteComment($db);
            break;
        case 'bulk_update_status':
            handleBulkUpdateStatus($db);
            break;
        case 'bulk_delete':
            handleBulkDelete($db);
            break;
        default:
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
} catch (Exception $e) {
    error_log("评论管理API错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => '服务器内部错误']);
}

function handleListComments($db) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $perPage = 10;
    $offset = ($page - 1) * $perPage;
    
    $search = trim($_GET['search'] ?? '');
    $status = trim($_GET['status'] ?? '');
    
    // 构建WHERE条件
    $whereConditions = [];
    $params = [];
    
    if (!empty($search)) {
        $whereConditions[] = "(c.content LIKE ? OR u.username LIKE ? OR COALESCE(up.nickname, u.full_name, u.username) LIKE ? OR p.title LIKE ?)";
        $searchParam = "%$search%";
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
    }
    
    if (!empty($status)) {
        $whereConditions[] = "c.status = ?";
        $params[] = $status;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total 
                 FROM comments c 
                 LEFT JOIN users u ON c.user_id = u.id 
                 LEFT JOIN user_profiles up ON u.id = up.user_id 
                 LEFT JOIN posts p ON c.post_id = p.id 
                 $whereClause";
    
    $countStmt = $db->prepare($countSql);
    $countStmt->execute($params);
    $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 获取评论列表
    $sql = "SELECT c.*,
                   u.username,
                   u.full_name,
                   COALESCE(up.nickname, u.full_name, u.username) as nickname,
                   p.title as post_title,
                   COALESCE((SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = c.id AND type = 'like'), 0) as like_count,
                   COALESCE((SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = c.id AND type = 'dislike'), 0) as dislike_count,
                   0 as report_count,
                   parent_c.content as parent_content
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON c.post_id = p.id
            LEFT JOIN comments parent_c ON c.parent_id = parent_c.id
            $whereClause
            ORDER BY c.created_at DESC
            LIMIT $perPage OFFSET $offset";
    
    try {
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 如果查询失败，可能是likes表不存在，使用简化查询
        error_log("点赞统计查询失败，使用简化查询: " . $e->getMessage());

        $simpleSql = "SELECT c.*,
                           u.username,
                           u.full_name,
                           COALESCE(up.nickname, u.full_name, u.username) as nickname,
                           p.title as post_title,
                           0 as like_count,
                           0 as dislike_count,
                           0 as report_count,
                           parent_c.content as parent_content
                    FROM comments c
                    LEFT JOIN users u ON c.user_id = u.id
                    LEFT JOIN user_profiles up ON u.id = up.user_id
                    LEFT JOIN posts p ON c.post_id = p.id
                    LEFT JOIN comments parent_c ON c.parent_id = parent_c.id
                    $whereClause
                    ORDER BY c.created_at DESC
                    LIMIT $perPage OFFSET $offset";

        $stmt = $db->prepare($simpleSql);
        $stmt->execute($params);
        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    $totalPages = ceil($totalRecords / $perPage);
    
    echo json_encode([
        'success' => true,
        'comments' => $comments,
        'pagination' => [
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalRecords' => $totalRecords,
            'perPage' => $perPage
        ]
    ]);
}

function handleGetStats($db) {
    try {
        // 基本统计查询
        $sql = "SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                    SUM(CASE WHEN status = 'hidden' THEN 1 ELSE 0 END) as hidden
                FROM comments";

        $stmt = $db->query($sql);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 检查comment_reports表是否存在
        $reportedCount = 0;
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'comment_reports'");
            if ($stmt->rowCount() > 0) {
                $stmt = $db->query("SELECT COUNT(DISTINCT comment_id) as reported FROM comment_reports WHERE status = 'pending'");
                $reportResult = $stmt->fetch(PDO::FETCH_ASSOC);
                $reportedCount = $reportResult['reported'] ?? 0;
            }
        } catch (Exception $e) {
            error_log("获取举报统计失败: " . $e->getMessage());
        }

        $stats['reported'] = $reportedCount;

        echo json_encode([
            'success' => true,
            'stats' => $stats
        ]);
    } catch (Exception $e) {
        error_log("获取评论统计失败: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'error' => '获取统计数据失败: ' . $e->getMessage()
        ]);
    }
}

function handleUpdateStatus($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $commentId = intval($input['commentId'] ?? 0);
    $status = $input['status'] ?? '';
    
    if ($commentId <= 0 || !in_array($status, ['published', 'hidden'])) {
        echo json_encode(['success' => false, 'error' => '参数无效']);
        return;
    }
    
    $sql = "UPDATE comments SET status = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $db->prepare($sql);
    
    if ($stmt->execute([$status, $commentId])) {
        echo json_encode(['success' => true, 'message' => '状态更新成功']);
    } else {
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
}

function handleDeleteComment($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $commentId = intval($input['commentId'] ?? 0);
    
    if ($commentId <= 0) {
        echo json_encode(['success' => false, 'error' => '参数无效']);
        return;
    }
    
    $db->beginTransaction();
    
    try {
        // 删除子评论
        $deleteChildrenSql = "DELETE FROM comments WHERE parent_id = ?";
        $stmt = $db->prepare($deleteChildrenSql);
        $stmt->execute([$commentId]);
        
        // 删除相关的点赞记录
        $deleteLikesSql = "DELETE FROM likes WHERE target_type = 'comment' AND target_id = ?";
        $stmt = $db->prepare($deleteLikesSql);
        $stmt->execute([$commentId]);
        
        // 删除举报记录
        $deleteReportsSql = "DELETE FROM comment_reports WHERE comment_id = ?";
        $stmt = $db->prepare($deleteReportsSql);
        $stmt->execute([$commentId]);
        
        // 删除评论本身
        $deleteCommentSql = "DELETE FROM comments WHERE id = ?";
        $stmt = $db->prepare($deleteCommentSql);
        $stmt->execute([$commentId]);
        
        $db->commit();
        echo json_encode(['success' => true, 'message' => '评论删除成功']);
    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'error' => '删除失败: ' . $e->getMessage()]);
    }
}

function handleBulkUpdateStatus($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $commentIds = $input['commentIds'] ?? [];
    $status = $input['status'] ?? '';
    
    if (empty($commentIds) || !in_array($status, ['published', 'hidden'])) {
        echo json_encode(['success' => false, 'error' => '参数无效']);
        return;
    }
    
    $placeholders = str_repeat('?,', count($commentIds) - 1) . '?';
    $sql = "UPDATE comments SET status = ?, updated_at = NOW() WHERE id IN ($placeholders)";
    
    $params = array_merge([$status], $commentIds);
    $stmt = $db->prepare($sql);
    
    if ($stmt->execute($params)) {
        $updatedCount = $stmt->rowCount();
        echo json_encode(['success' => true, 'updatedCount' => $updatedCount]);
    } else {
        echo json_encode(['success' => false, 'error' => '批量更新失败']);
    }
}

function handleBulkDelete($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    $commentIds = $input['commentIds'] ?? [];
    
    if (empty($commentIds)) {
        echo json_encode(['success' => false, 'error' => '参数无效']);
        return;
    }
    
    $db->beginTransaction();
    
    try {
        $placeholders = str_repeat('?,', count($commentIds) - 1) . '?';
        
        // 删除子评论
        $deleteChildrenSql = "DELETE FROM comments WHERE parent_id IN ($placeholders)";
        $stmt = $db->prepare($deleteChildrenSql);
        $stmt->execute($commentIds);
        
        // 删除相关的点赞记录
        $deleteLikesSql = "DELETE FROM likes WHERE target_type = 'comment' AND target_id IN ($placeholders)";
        $stmt = $db->prepare($deleteLikesSql);
        $stmt->execute($commentIds);
        
        // 删除举报记录
        $deleteReportsSql = "DELETE FROM comment_reports WHERE comment_id IN ($placeholders)";
        $stmt = $db->prepare($deleteReportsSql);
        $stmt->execute($commentIds);
        
        // 删除评论本身
        $deleteCommentsSql = "DELETE FROM comments WHERE id IN ($placeholders)";
        $stmt = $db->prepare($deleteCommentsSql);
        $stmt->execute($commentIds);
        
        $deletedCount = $stmt->rowCount();
        
        $db->commit();
        echo json_encode(['success' => true, 'deletedCount' => $deletedCount]);
    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'error' => '批量删除失败: ' . $e->getMessage()]);
    }
}
?>
