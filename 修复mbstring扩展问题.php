<?php
/**
 * 修复mbstring扩展问题
 * 检查PHP扩展并提供解决方案
 */

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修复mbstring扩展问题</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #764ba2; }
    .btn.fix { background: #28a745; }
    .btn.fix:hover { background: #1e7e34; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔧 修复mbstring扩展问题</h1>";

// 1. 检查PHP扩展状态
echo "<div class='info'>";
echo "<h3>📋 PHP扩展检查</h3>";

$extensions = [
    'mbstring' => 'mb_substr, mb_strlen等多字节字符串函数',
    'curl' => 'HTTP请求功能',
    'json' => 'JSON处理功能',
    'pdo' => '数据库连接',
    'pdo_mysql' => 'MySQL数据库支持',
    'gd' => '图像处理功能',
    'openssl' => 'SSL/TLS加密功能'
];

echo "<table>";
echo "<tr><th>扩展名</th><th>状态</th><th>功能说明</th></tr>";

$missingExtensions = [];
foreach ($extensions as $ext => $description) {
    $loaded = extension_loaded($ext);
    echo "<tr>";
    echo "<td>{$ext}</td>";
    echo "<td>" . ($loaded ? "<span style='color: green;'>✅ 已安装</span>" : "<span style='color: red;'>❌ 未安装</span>") . "</td>";
    echo "<td>{$description}</td>";
    echo "</tr>";
    
    if (!$loaded) {
        $missingExtensions[] = $ext;
    }
}
echo "</table>";

if (empty($missingExtensions)) {
    echo "<div class='success'>✅ 所有必需的PHP扩展都已安装</div>";
} else {
    echo "<div class='error'>❌ 缺少以下PHP扩展: " . implode(', ', $missingExtensions) . "</div>";
}

echo "</div>";

// 2. 检查mbstring函数
echo "<div class='info'>";
echo "<h3>🔤 mbstring函数检查</h3>";

$mbFunctions = ['mb_substr', 'mb_strlen', 'mb_strpos', 'mb_convert_encoding', 'mb_detect_encoding'];

echo "<table>";
echo "<tr><th>函数名</th><th>状态</th><th>测试结果</th></tr>";

foreach ($mbFunctions as $func) {
    $exists = function_exists($func);
    echo "<tr>";
    echo "<td>{$func}</td>";
    echo "<td>" . ($exists ? "<span style='color: green;'>✅ 可用</span>" : "<span style='color: red;'>❌ 不可用</span>") . "</td>";
    
    if ($exists) {
        try {
            switch ($func) {
                case 'mb_substr':
                    $result = mb_substr('测试中文字符串', 0, 3);
                    echo "<td>结果: '{$result}'</td>";
                    break;
                case 'mb_strlen':
                    $result = mb_strlen('测试中文');
                    echo "<td>长度: {$result}</td>";
                    break;
                default:
                    echo "<td>函数可用</td>";
            }
        } catch (Exception $e) {
            echo "<td style='color: red;'>错误: " . $e->getMessage() . "</td>";
        }
    } else {
        echo "<td style='color: red;'>函数不存在</td>";
    }
    echo "</tr>";
}
echo "</table>";

echo "</div>";

// 3. 提供解决方案
echo "<div class='warning'>";
echo "<h3>💡 解决方案</h3>";

if (in_array('mbstring', $missingExtensions)) {
    echo "<h4>方案1: 安装mbstring扩展（推荐）</h4>";
    echo "<p>在腾讯云服务器上安装mbstring扩展：</p>";
    
    echo "<div class='code-block'>";
    echo "# Ubuntu/Debian系统<br>";
    echo "sudo apt-get update<br>";
    echo "sudo apt-get install php-mbstring<br>";
    echo "sudo systemctl restart apache2  # 或 nginx<br><br>";
    
    echo "# CentOS/RHEL系统<br>";
    echo "sudo yum install php-mbstring<br>";
    echo "sudo systemctl restart httpd  # 或 nginx<br><br>";
    
    echo "# 宝塔面板<br>";
    echo "1. 登录宝塔面板<br>";
    echo "2. 软件商店 -> PHP -> 设置 -> 安装扩展<br>";
    echo "3. 找到mbstring扩展并安装<br>";
    echo "4. 重启PHP服务";
    echo "</div>";
    
    echo "<h4>方案2: 创建兼容函数（临时解决）</h4>";
    echo "<p>如果无法安装扩展，可以创建兼容函数：</p>";
    
    if (isset($_POST['create_compatibility'])) {
        $compatibilityCode = "<?php
/**
 * mbstring兼容函数
 * 当mbstring扩展不可用时的备用方案
 */

if (!function_exists('mb_substr')) {
    function mb_substr(\$string, \$start, \$length = null, \$encoding = 'UTF-8') {
        if (\$encoding !== 'UTF-8') {
            \$string = iconv(\$encoding, 'UTF-8', \$string);
        }
        
        if (\$length === null) {
            return substr(\$string, \$start);
        }
        
        // 简单的UTF-8字符串截取
        \$chars = preg_split('//u', \$string, -1, PREG_SPLIT_NO_EMPTY);
        \$totalChars = count(\$chars);
        
        if (\$start < 0) {
            \$start = max(0, \$totalChars + \$start);
        }
        
        if (\$start >= \$totalChars) {
            return '';
        }
        
        if (\$length < 0) {
            \$length = max(0, \$totalChars - \$start + \$length);
        }
        
        \$result = array_slice(\$chars, \$start, \$length);
        return implode('', \$result);
    }
}

if (!function_exists('mb_strlen')) {
    function mb_strlen(\$string, \$encoding = 'UTF-8') {
        if (\$encoding !== 'UTF-8') {
            \$string = iconv(\$encoding, 'UTF-8', \$string);
        }
        return count(preg_split('//u', \$string, -1, PREG_SPLIT_NO_EMPTY));
    }
}
?>";
        
        if (file_put_contents('includes/mbstring_compatibility.php', $compatibilityCode)) {
            echo "<div class='success'>✅ 兼容函数文件创建成功: includes/mbstring_compatibility.php</div>";
            echo "<p>请在需要使用mbstring函数的文件开头添加：</p>";
            echo "<div class='code-block'>require_once 'includes/mbstring_compatibility.php';</div>";
        } else {
            echo "<div class='error'>❌ 无法创建兼容函数文件，请检查目录权限</div>";
        }
    } else {
        echo "<form method='post'>";
        echo "<button type='submit' name='create_compatibility' class='btn fix'>创建兼容函数</button>";
        echo "</form>";
    }
}

echo "</div>";

// 4. 修复community-post.php
echo "<div class='info'>";
echo "<h3>🔧 修复community-post.php</h3>";

if (isset($_POST['fix_community_post'])) {
    $filePath = 'community-post.php';
    if (file_exists($filePath)) {
        $content = file_get_contents($filePath);
        
        // 在文件开头添加兼容性检查
        $compatibilityCheck = "<?php
// mbstring扩展兼容性检查
if (!extension_loaded('mbstring')) {
    require_once 'includes/mbstring_compatibility.php';
}

";
        
        // 检查是否已经添加了兼容性检查
        if (strpos($content, 'mbstring扩展兼容性检查') === false) {
            $content = str_replace('<?php', $compatibilityCheck, $content);
            
            if (file_put_contents($filePath, $content)) {
                echo "<div class='success'>✅ community-post.php 已修复，添加了mbstring兼容性检查</div>";
            } else {
                echo "<div class='error'>❌ 无法修改文件，请检查文件权限</div>";
            }
        } else {
            echo "<div class='info'>ℹ️ community-post.php 已经包含兼容性检查</div>";
        }
    } else {
        echo "<div class='error'>❌ 找不到community-post.php文件</div>";
    }
} else {
    echo "<p>点击下面的按钮修复community-post.php文件：</p>";
    echo "<form method='post'>";
    echo "<button type='submit' name='fix_community_post' class='btn fix'>修复community-post.php</button>";
    echo "</form>";
}

echo "</div>";

// 5. 测试修复效果
echo "<div class='info'>";
echo "<h3>🧪 测试修复效果</h3>";

if (isset($_POST['test_functions'])) {
    echo "<h4>测试结果：</h4>";
    
    try {
        $testString = "这是一个测试中文字符串，用于验证mbstring函数是否正常工作。";
        
        echo "<p><strong>原始字符串：</strong>{$testString}</p>";
        
        if (function_exists('mb_substr')) {
            $result1 = mb_substr($testString, 0, 10);
            echo "<p><strong>mb_substr(0, 10)：</strong>{$result1}</p>";
            
            $result2 = mb_substr(strip_tags("<p>HTML标签测试内容</p>"), 0, 5);
            echo "<p><strong>处理HTML后截取：</strong>{$result2}</p>";
            
            echo "<div class='success'>✅ mbstring函数测试成功！</div>";
        } else {
            echo "<div class='error'>❌ mb_substr函数仍然不可用</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 测试失败: " . $e->getMessage() . "</div>";
    }
} else {
    echo "<form method='post'>";
    echo "<button type='submit' name='test_functions' class='btn'>测试mbstring函数</button>";
    echo "</form>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='community-post.php' class='btn'>测试发帖功能</a>";
echo "<a href='community.php' class='btn'>返回社区</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
