<?php
/**
 * 通知管理API
 */

session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../includes/time_helper.php';

// 检查用户登录状态
$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$userId = $currentUser['id'];

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    $pdo = $db->getConnection();

    switch ($method) {
        case 'GET':
            handleGet($pdo, $userId);
            break;
        case 'POST':
            handlePost($pdo, $userId, $input);
            break;
        case 'PUT':
            handlePut($pdo, $userId, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取通知
 */
function handleGet($pdo, $userId) {
    $action = $_GET['action'] ?? 'list';

    if ($action === 'count') {
        // 获取未读通知数量和分类统计
        $totalSql = "SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0";
        $stmt = $pdo->prepare($totalSql);
        $stmt->execute([$userId]);
        $totalResult = $stmt->fetch(PDO::FETCH_ASSOC);

        // 获取各类型未读数量
        $typeSql = "SELECT type, COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0 GROUP BY type";
        $stmt = $pdo->prepare($typeSql);
        $stmt->execute([$userId]);
        $typeResults = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $counts = ['all' => (int)$totalResult['count']];
        foreach ($typeResults as $row) {
            $counts[$row['type']] = (int)$row['count'];
        }

        echo json_encode(['success' => true, 'count' => (int)$totalResult['count'], 'counts' => $counts]);

    } elseif ($action === 'dropdown') {
        // 获取下拉菜单通知列表
        $type = $_GET['type'] ?? 'all';
        $limit = (int)($_GET['limit'] ?? 10);

        $sql = "SELECT n.id, n.title, n.content, n.type, n.is_read, n.created_at,
                       u.avatar_url, u.username, up.nickname
                FROM notifications n
                LEFT JOIN users u ON n.related_user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                WHERE n.user_id = ?";

        $params = [$userId];

        if ($type !== 'all') {
            $sql .= " AND n.type = ?";
            $params[] = $type;
        }

        $sql .= " ORDER BY n.created_at DESC LIMIT ?";
        $params[] = $limit;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 格式化通知数据
        foreach ($notifications as &$notification) {
            $notification['time_ago'] = timeAgo($notification['created_at']);
            $notification['avatar'] = $notification['avatar_url'] ?: 'assets/images/default-avatar.png';
            $notification['display_name'] = $notification['nickname'] ?: $notification['username'];
        }

        echo json_encode([
            'success' => true,
            'notifications' => $notifications,
            'has_more' => count($notifications) === $limit
        ]);

    } else {
        // 获取完整通知列表
        $limit = (int)($_GET['limit'] ?? 20);
        $sql = "SELECT id, title, content, type, is_read, created_at
                FROM notifications
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId, $limit]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'notifications' => $notifications]);
    }
}

/**
 * 处理POST请求 - 标记通知为已读
 */
function handlePost($pdo, $userId, $input) {
    $action = $input['action'] ?? '';

    if ($action === 'mark_read' && isset($input['notification_id'])) {
        // 标记单个通知为已读
        $sql = "UPDATE notifications SET is_read = 1 WHERE id = ? AND user_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$input['notification_id'], $userId]);

        echo json_encode(['success' => true, 'message' => '通知已标记为已读']);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '无效的操作']);
    }
}

/**
 * 处理PUT请求 - 更新通知状态
 */
function handlePut($pdo, $userId, $input) {
    $action = $input['action'] ?? '';

    if ($action === 'mark_all_read') {
        // 标记所有通知为已读
        $sql = "UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId]);

        echo json_encode(['success' => true, 'message' => '所有通知已标记为已读']);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '无效的操作']);
    }
}

/**
 * 创建新通知（系统内部使用）
 */
function createNotification($pdo, $title, $content, $type = 'system', $userId = null) {
    $sql = "INSERT INTO notifications (user_id, title, content, type, created_at) VALUES (?, ?, ?, ?, NOW())";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$userId, $title, $content, $type]);
    return $pdo->lastInsertId();
}
?>
