-- 创建分类表
CREATE TABLE IF NOT EXISTS `categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL COMMENT '分类名称',
    `description` text COMMENT '分类描述',
    `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_sort_order` (`sort_order`),
    CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 为posts表添加category_id字段（如果不存在）
ALTER TABLE `posts` 
ADD COLUMN `category_id` int(11) DEFAULT NULL COMMENT '分类ID' AFTER `user_id`,
ADD KEY `idx_category_id` (`category_id`),
ADD CONSTRAINT `fk_posts_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

-- 插入默认分类
INSERT INTO `categories` (`name`, `description`, `sort_order`) VALUES
('技术讨论', '技术相关的讨论和分享', 1),
('生活随笔', '日常生活的点点滴滴', 2),
('学习笔记', '学习过程中的心得体会', 3),
('项目展示', '个人或团队项目的展示', 4),
('问题求助', '遇到问题时的求助帖', 5),
('资源分享', '有用资源的分享', 6);

-- 为技术讨论添加子分类
INSERT INTO `categories` (`name`, `description`, `parent_id`, `sort_order`) VALUES
('前端开发', 'HTML、CSS、JavaScript等前端技术', 1, 1),
('后端开发', 'PHP、Python、Java等后端技术', 1, 2),
('数据库', 'MySQL、PostgreSQL等数据库技术', 1, 3),
('运维部署', '服务器运维和应用部署', 1, 4),
('移动开发', 'Android、iOS等移动端开发', 1, 5);

-- 为学习笔记添加子分类
INSERT INTO `categories` (`name`, `description`, `parent_id`, `sort_order`) VALUES
('编程语言', '各种编程语言的学习笔记', 3, 1),
('框架工具', '开发框架和工具的使用心得', 3, 2),
('算法数据结构', '算法和数据结构的学习记录', 3, 3),
('系统设计', '系统架构和设计模式', 3, 4);
