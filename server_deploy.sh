#!/bin/bash

# 比特熊智慧系统服务器部署脚本
# 使用方法: bash server_deploy.sh

echo "=== 比特熊智慧系统服务器部署脚本 ==="
echo "开始时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_DIR="/www/wwwroot/www.bitbear.top"
DB_NAME="bitbear_website"
DB_USER="root"
DB_PASS="309290133q"
NGINX_SITE="www.bitbear.top"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    log_success "Root权限检查通过"
}

# 检查系统环境
check_system() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        log_info "操作系统: $NAME $VERSION"
    fi
    
    # 检查必要的服务
    services=("nginx" "mysql" "php8.1-fpm")
    for service in "${services[@]}"; do
        if systemctl is-active --quiet $service; then
            log_success "$service 服务正在运行"
        else
            log_warning "$service 服务未运行，尝试启动..."
            systemctl start $service
            if systemctl is-active --quiet $service; then
                log_success "$service 服务启动成功"
            else
                log_error "$service 服务启动失败"
            fi
        fi
    done
}

# 检查项目目录
check_project_dir() {
    log_info "检查项目目录..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        log_info "请确保项目文件已上传到服务器"
        exit 1
    fi
    
    log_success "项目目录存在: $PROJECT_DIR"
    
    # 检查关键文件
    key_files=("index.php" "config/database.php")
    for file in "${key_files[@]}"; do
        if [ -f "$PROJECT_DIR/$file" ]; then
            log_success "关键文件存在: $file"
        else
            log_warning "关键文件缺失: $file"
        fi
    done
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 设置所有者
    chown -R www-data:www-data $PROJECT_DIR
    log_success "设置所有者为 www-data"
    
    # 设置目录权限
    find $PROJECT_DIR -type d -exec chmod 755 {} \;
    log_success "设置目录权限为 755"
    
    # 设置文件权限
    find $PROJECT_DIR -type f -exec chmod 644 {} \;
    log_success "设置文件权限为 644"
    
    # 设置特殊目录权限
    special_dirs=("uploads" "uploads/avatars" "uploads/posts" "uploads/videos")
    for dir in "${special_dirs[@]}"; do
        if [ -d "$PROJECT_DIR/$dir" ]; then
            chmod -R 777 $PROJECT_DIR/$dir
            log_success "设置 $dir 目录权限为 777"
        else
            mkdir -p $PROJECT_DIR/$dir
            chmod -R 777 $PROJECT_DIR/$dir
            chown -R www-data:www-data $PROJECT_DIR/$dir
            log_success "创建并设置 $dir 目录"
        fi
    done
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 检查MySQL连接
    if mysql -u$DB_USER -p$DB_PASS -e "SELECT 1;" &>/dev/null; then
        log_success "MySQL连接成功"
    else
        log_error "MySQL连接失败，请检查数据库配置"
        exit 1
    fi
    
    # 检查数据库是否存在
    if mysql -u$DB_USER -p$DB_PASS -e "USE $DB_NAME;" &>/dev/null; then
        log_success "数据库 $DB_NAME 已存在"
    else
        log_info "创建数据库 $DB_NAME..."
        mysql -u$DB_USER -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        log_success "数据库 $DB_NAME 创建成功"
    fi
    
    # 检查表是否存在
    table_count=$(mysql -u$DB_USER -p$DB_PASS -D$DB_NAME -e "SHOW TABLES;" | wc -l)
    if [ $table_count -gt 1 ]; then
        log_success "数据库包含 $((table_count-1)) 个表"
    else
        log_warning "数据库为空，需要初始化表结构"
        if [ -f "$PROJECT_DIR/database/init.sql" ]; then
            log_info "导入数据库结构..."
            mysql -u$DB_USER -p$DB_PASS $DB_NAME < $PROJECT_DIR/database/init.sql
            log_success "数据库结构导入成功"
        else
            log_error "数据库初始化文件不存在: $PROJECT_DIR/database/init.sql"
        fi
    fi
}

# 配置Nginx
setup_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    nginx_config="/etc/nginx/sites-available/$NGINX_SITE"
    
    cat > $nginx_config << EOF
server {
    listen 80;
    server_name $NGINX_SITE *************;
    root $PROJECT_DIR;
    index index.php index.html index.htm;

    # 日志文件
    access_log /var/log/nginx/${NGINX_SITE}_access.log;
    error_log /var/log/nginx/${NGINX_SITE}_error.log;

    # 主要位置配置
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
        
        # 增加超时时间
        fastcgi_read_timeout 300;
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全配置
    location ~ /\.ht {
        deny all;
    }
    
    location ~ /\.git {
        deny all;
    }
    
    location ~ /config/ {
        deny all;
    }

    # 文件上传大小限制
    client_max_body_size 100M;
}
EOF

    log_success "Nginx配置文件已创建: $nginx_config"
    
    # 启用站点
    if [ ! -L "/etc/nginx/sites-enabled/$NGINX_SITE" ]; then
        ln -s $nginx_config /etc/nginx/sites-enabled/
        log_success "Nginx站点已启用"
    else
        log_info "Nginx站点已经启用"
    fi
    
    # 测试Nginx配置
    if nginx -t; then
        log_success "Nginx配置测试通过"
        systemctl reload nginx
        log_success "Nginx配置已重新加载"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 配置PHP
setup_php() {
    log_info "配置PHP..."
    
    # 检查PHP版本
    php_version=$(php -v | head -n1)
    log_info "PHP版本: $php_version"
    
    # 重启PHP-FPM
    systemctl restart php8.1-fpm
    log_success "PHP-FPM已重启"
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    # 测试数据库连接
    if [ -f "$PROJECT_DIR/test_server_db.php" ]; then
        log_info "运行数据库连接测试..."
        php $PROJECT_DIR/test_server_db.php > /tmp/db_test.html
        log_success "数据库测试完成，结果保存到 /tmp/db_test.html"
    fi
    
    # 测试网站访问
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200"; then
        log_success "网站访问测试通过"
    else
        log_warning "网站访问测试失败，请检查配置"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成信息:"
    echo ""
    echo "项目目录: $PROJECT_DIR"
    echo "数据库名: $DB_NAME"
    echo "网站地址: http://*************"
    echo "网站地址: http://$NGINX_SITE (如果域名已解析)"
    echo ""
    echo "管理后台: http://*************/admin/"
    echo "默认管理员账号: admin"
    echo "默认管理员密码: admin123"
    echo ""
    echo "日志文件:"
    echo "- Nginx访问日志: /var/log/nginx/${NGINX_SITE}_access.log"
    echo "- Nginx错误日志: /var/log/nginx/${NGINX_SITE}_error.log"
    echo "- PHP错误日志: /var/log/php8.1-fpm.log"
    echo ""
}

# 主函数
main() {
    check_root
    check_system
    check_project_dir
    set_permissions
    setup_database
    setup_nginx
    setup_php
    test_deployment
    show_deployment_info
    
    log_success "部署完成！"
    echo "结束时间: $(date)"
}

# 运行主函数
main
