<?php
require_once 'config/database.php';

echo "=== 创建测试数据 ===\n\n";

try {
    $db = db();
    
    // 创建用户个人资料
    $users = $db->fetchAll("SELECT id, username FROM users LIMIT 5");
    
    foreach ($users as $user) {
        // 检查是否已有个人资料
        $profile = $db->fetchOne("SELECT id FROM user_profiles WHERE user_id = ?", [$user['id']]);
        
        if (!$profile) {
            $db->execute(
                "INSERT INTO user_profiles (user_id, nickname, bio, signature, avatar_url, created_at) VALUES (?, ?, ?, ?, ?, NOW())",
                [
                    $user['id'],
                    $user['username'] . '_昵称',
                    '这是' . $user['username'] . '的个人简介',
                    '学而时习之，不亦说乎',
                    'assets/images/default-avatar.png'
                ]
            );
            echo "✓ 创建用户 {$user['username']} 的个人资料\n";
        }
    }
    
    // 创建测试帖子
    $testPosts = [
        [
            'title' => 'PHP 8.4 新特性详解',
            'content' => '<h2>PHP 8.4 带来了哪些新特性？</h2>
                         <p>PHP 8.4 是一个重要的版本更新，带来了许多令人兴奋的新特性和改进。</p>
                         <h3>主要新特性：</h3>
                         <ul>
                         <li><strong>属性钩子（Property Hooks）</strong>：允许在属性访问时执行自定义逻辑</li>
                         <li><strong>不对称可见性</strong>：可以为属性设置不同的读写权限</li>
                         <li><strong>新的数组函数</strong>：array_find()、array_find_key()、array_any()、array_all()</li>
                         </ul>
                         <p>这些新特性将大大提升PHP开发的效率和代码质量。</p>',
            'excerpt' => 'PHP 8.4 带来了属性钩子、不对称可见性等重要新特性，让我们一起来了解这些改进。',
            'category_id' => 1 // 技术讨论
        ],
        [
            'title' => '如何设计一个高性能的数据库架构？',
            'content' => '<h2>数据库架构设计的关键要素</h2>
                         <p>设计高性能数据库架构需要考虑多个方面：</p>
                         <h3>1. 数据建模</h3>
                         <p>合理的数据建模是高性能的基础，需要考虑：</p>
                         <ul>
                         <li>表结构设计</li>
                         <li>索引策略</li>
                         <li>数据类型选择</li>
                         </ul>
                         <h3>2. 查询优化</h3>
                         <p>优化查询性能的方法包括：</p>
                         <ul>
                         <li>使用合适的索引</li>
                         <li>避免N+1查询问题</li>
                         <li>合理使用缓存</li>
                         </ul>',
            'excerpt' => '从数据建模到查询优化，全面解析高性能数据库架构设计的核心要点。',
            'category_id' => 1 // 技术讨论
        ],
        [
            'title' => '我的开源项目开发心得分享',
            'content' => '<h2>开源项目开发的收获与感悟</h2>
                         <p>最近完成了一个开源项目的开发，想和大家分享一些心得体会。</p>
                         <h3>项目背景</h3>
                         <p>这是一个基于PHP的内容管理系统，主要特点：</p>
                         <ul>
                         <li>模块化架构</li>
                         <li>响应式设计</li>
                         <li>多语言支持</li>
                         </ul>
                         <h3>开发过程中的挑战</h3>
                         <p>在开发过程中遇到了不少挑战，比如性能优化、安全防护等。</p>
                         <p>通过这个项目，我学到了很多关于软件架构和团队协作的知识。</p>',
            'excerpt' => '分享我在开源项目开发过程中的经验和心得，希望对大家有所帮助。',
            'category_id' => 3 // 项目展示
        ]
    ];
    
    foreach ($testPosts as $i => $postData) {
        $userId = $users[$i % count($users)]['id'];
        
        $result = $db->execute(
            "INSERT INTO posts (user_id, category_id, title, content, excerpt, status, view_count, like_count, comment_count, published_at, created_at)
             VALUES (?, ?, ?, ?, ?, 'published', ?, ?, 0, NOW(), NOW())",
            [
                $userId,
                $postData['category_id'],
                $postData['title'],
                $postData['content'],
                $postData['excerpt'],
                rand(50, 500), // 随机浏览量
                rand(5, 50)    // 随机点赞数
            ]
        );

        $postId = $db->lastInsertId();

        // 如果lastInsertId返回0，手动查询最新插入的ID
        if ($postId == 0) {
            $postId = $db->fetchOne("SELECT MAX(id) as max_id FROM posts")['max_id'];
        }
        echo "✓ 创建测试帖子: {$postData['title']} (ID: {$postId})\n";
        
        // 为每个帖子创建一些测试评论
        $commentCount = rand(2, 5);
        for ($j = 1; $j <= $commentCount; $j++) {
            $commentUserId = $users[($i + $j) % count($users)]['id'];
            $commentContent = "这是第{$j}条评论。" . [
                "很有用的分享，感谢作者！",
                "学到了很多新知识，点赞支持。",
                "有几个地方想请教一下，能详细说明吗？",
                "非常赞同作者的观点，我也有类似的经验。",
                "这个方案确实不错，我们项目中也在考虑采用。"
            ][($j - 1) % 5];
            
            $db->execute(
                "INSERT INTO comments (post_id, user_id, content, floor_number, level, like_count, created_at) 
                 VALUES (?, ?, ?, ?, 0, ?, NOW())",
                [
                    $postId,
                    $commentUserId,
                    $commentContent,
                    $j,
                    rand(0, 10) // 随机点赞数
                ]
            );
        }
        
        // 更新帖子评论数
        $db->execute("UPDATE posts SET comment_count = ? WHERE id = ?", [$commentCount, $postId]);
        
        echo "  └─ 创建了 {$commentCount} 条评论\n";
    }
    
    echo "\n✅ 测试数据创建完成！\n";
    echo "\n可以访问以下链接测试：\n";
    echo "- 社区首页: http://localhost:8000/community.php\n";
    echo "- 帖子详情: http://localhost:8000/community-post-detail.php?id=1\n";
    echo "- 发布帖子: http://localhost:8000/community-post.php\n";
    
} catch (Exception $e) {
    echo "✗ 创建失败: " . $e->getMessage() . "\n";
}
?>
