@echo off
echo ========================================
echo 比特熊智慧系统 - 服务器启动脚本
echo ========================================
echo.

echo [1/4] 检查PHP环境...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP未安装或未添加到PATH环境变量
    echo 请安装PHP并添加到系统PATH
    pause
    exit /b 1
) else (
    echo ✅ PHP环境正常
)

echo.
echo [2/4] 检查工作目录...
if not exist "index.php" (
    echo ❌ 未找到index.php文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
) else (
    echo ✅ 项目文件存在
)

echo.
echo [3/4] 检查端口占用...
netstat -an | findstr ":8000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口8000已被占用，尝试终止现有进程...
    taskkill /f /im php.exe >nul 2>&1
    timeout /t 2 >nul
)

echo.
echo [4/4] 启动PHP服务器...
echo 🚀 正在启动服务器 http://localhost:8000
echo.
echo ========================================
echo 服务器启动成功！
echo ========================================
echo.
echo 📋 可用页面:
echo   • 测试页面: http://localhost:8000/test.html
echo   • 快速启动: http://localhost:8000/quick_start.php
echo   • 轻量首页: http://localhost:8000/index_lite.php
echo   • 完整首页: http://localhost:8000/index.php
echo   • 管理后台: http://localhost:8000/admin-dashboard.php
echo   • 系统诊断: http://localhost:8000/diagnostic.php
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 启动浏览器
start http://localhost:8000/test.html

REM 启动PHP服务器
php -S localhost:8000
