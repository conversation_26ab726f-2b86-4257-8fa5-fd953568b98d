/* 收藏页面样式 */

/* 页面头部 */
.page-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.breadcrumb {
    margin-bottom: 16px;
}

.breadcrumb-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.breadcrumb-link:hover {
    color: #3b82f6;
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-title i {
    color: #3b82f6;
}

.page-stats {
    display: flex;
    gap: 24px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-weight: 500;
}

.stat-item i {
    color: #ef4444;
}

/* 收藏容器 */
.bookmarks-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 空状态 */
.empty-bookmarks {
    text-align: center;
    padding: 80px 40px;
    color: #6b7280;
}

.empty-icon {
    font-size: 4rem;
    color: #d1d5db;
    margin-bottom: 24px;
}

.empty-bookmarks h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.empty-bookmarks p {
    font-size: 1rem;
    margin-bottom: 32px;
    line-height: 1.6;
}

/* 收藏列表 */
.bookmarks-list {
    display: flex;
    flex-direction: column;
}

.bookmark-item {
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s;
}

.bookmark-item:hover {
    background: rgba(59, 130, 246, 0.02);
}

.bookmark-item:last-child {
    border-bottom: none;
}

.bookmark-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.bookmark-meta {
    display: flex;
    align-items: center;
    gap: 16px;
}

.post-category {
    background: rgb(var(--category-color, 59, 130, 246));
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.bookmark-time {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 14px;
}

.bookmark-time i {
    color: #ef4444;
}

.remove-bookmark-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s;
}

.remove-bookmark-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

/* 收藏内容 */
.bookmark-content {
    padding-left: 0;
}

.bookmark-title {
    margin-bottom: 12px;
}

.bookmark-title a {
    color: #1a1a1a;
    text-decoration: none;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.2s;
}

.bookmark-title a:hover {
    color: #3b82f6;
}

.bookmark-excerpt {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bookmark-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.author-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: inherit;
    transition: color 0.2s;
}

.author-link:hover {
    color: #3b82f6;
}

.author-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.author-name {
    font-weight: 500;
    color: #374151;
}

.post-time {
    color: #9ca3af;
    font-size: 14px;
}

.post-stats {
    display: flex;
    gap: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 14px;
}

.stat i {
    font-size: 14px;
}

/* 分页样式 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 32px;
    border-top: 1px solid #e5e7eb;
}

.page-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
}

.page-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.page-numbers {
    display: flex;
    gap: 8px;
}

.page-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s;
}

.page-number:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.page-number.active {
    background: #3b82f6;
    border-color: #3b82f6;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        padding: 24px 20px;
    }
    
    .page-title {
        font-size: 1.5rem;
    }
    
    .bookmark-item {
        padding: 20px 16px;
    }
    
    .bookmark-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .bookmark-meta {
        flex-wrap: wrap;
        gap: 12px;
    }
    
    .bookmark-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .post-stats {
        gap: 16px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 16px;
    }
    
    .page-numbers {
        order: -1;
    }
}

@media (max-width: 480px) {
    .empty-bookmarks {
        padding: 60px 20px;
    }
    
    .empty-icon {
        font-size: 3rem;
    }
    
    .bookmark-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .author-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
