<?php
/**
 * MySQL数据库连接测试脚本
 */

echo "<h2>MySQL数据库连接测试</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

// 测试配置
$host = 'localhost';
$username = 'root';
$password = '';
$ports = [3306, 3307]; // 可能的端口

echo "<h3>测试配置：</h3>\n";
echo "<ul>\n";
echo "<li>主机: {$host}</li>\n";
echo "<li>用户名: {$username}</li>\n";
echo "<li>密码: " . (empty($password) ? '(空)' : '***') . "</li>\n";
echo "<li>测试端口: " . implode(', ', $ports) . "</li>\n";
echo "</ul>\n";

echo "<h3>连接测试结果：</h3>\n";

$connected = false;
$workingPort = null;
$pdo = null;

// 测试每个端口
foreach ($ports as $port) {
    echo "<h4>测试端口 {$port}:</h4>\n";
    
    try {
        // 尝试连接
        $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, $username, $password, $options);
        
        echo "<p class='success'>✅ 端口 {$port} 连接成功！</p>\n";
        
        // 获取MySQL版本信息
        $version = $pdo->query('SELECT VERSION() as version')->fetch();
        echo "<p class='info'>📋 MySQL版本: {$version['version']}</p>\n";
        
        // 获取当前时间
        $time = $pdo->query('SELECT NOW() as current_time')->fetch();
        echo "<p class='info'>🕒 数据库时间: {$time['current_time']}</p>\n";
        
        // 检查数据库列表
        $databases = $pdo->query('SHOW DATABASES')->fetchAll();
        echo "<p class='info'>📁 可用数据库: ";
        $dbNames = array_column($databases, 'Database');
        echo implode(', ', $dbNames) . "</p>\n";
        
        // 检查是否存在我们的目标数据库
        $targetDb = 'bitbear_system';
        if (in_array($targetDb, $dbNames)) {
            echo "<p class='success'>✅ 目标数据库 '{$targetDb}' 已存在</p>\n";
            
            // 连接到目标数据库
            $dsn = "mysql:host={$host};port={$port};dbname={$targetDb};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, $options);
            
            // 检查表结构
            $tables = $pdo->query('SHOW TABLES')->fetchAll();
            if (!empty($tables)) {
                echo "<p class='info'>📊 数据库表: ";
                $tableNames = array_column($tables, "Tables_in_{$targetDb}");
                echo implode(', ', $tableNames) . "</p>\n";
            } else {
                echo "<p class='info'>📊 数据库为空，需要初始化</p>\n";
            }
        } else {
            echo "<p class='info'>📋 目标数据库 '{$targetDb}' 不存在，需要创建</p>\n";
        }
        
        $connected = true;
        $workingPort = $port;
        break;
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ 端口 {$port} 连接失败: " . $e->getMessage() . "</p>\n";
    }
}

echo "<hr>\n";

if ($connected) {
    echo "<h3 class='success'>🎉 连接测试成功！</h3>\n";
    echo "<p>工作端口: <strong>{$workingPort}</strong></p>\n";
    
    // 测试创建数据库
    echo "<h4>测试数据库操作：</h4>\n";
    try {
        // 重新连接到服务器（不指定数据库）
        $dsn = "mysql:host={$host};port={$workingPort};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, $options);
        
        // 创建测试数据库
        $testDb = 'bitbear_system';
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$testDb}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p class='success'>✅ 数据库 '{$testDb}' 创建/验证成功</p>\n";
        
        // 连接到测试数据库
        $dsn = "mysql:host={$host};port={$workingPort};dbname={$testDb};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, $options);
        
        // 创建测试表
        $pdo->exec("CREATE TABLE IF NOT EXISTS test_connection (
            id INT AUTO_INCREMENT PRIMARY KEY,
            message VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // 插入测试数据
        $stmt = $pdo->prepare("INSERT INTO test_connection (message) VALUES (?)");
        $stmt->execute(['数据库连接测试成功 - ' . date('Y-m-d H:i:s')]);
        
        // 读取测试数据
        $result = $pdo->query("SELECT * FROM test_connection ORDER BY id DESC LIMIT 1")->fetch();
        echo "<p class='success'>✅ 数据库读写测试成功</p>\n";
        echo "<p class='info'>📝 测试记录: {$result['message']}</p>\n";
        
        // 清理测试表
        $pdo->exec("DROP TABLE IF EXISTS test_connection");
        echo "<p class='info'>🧹 测试表已清理</p>\n";
        
    } catch (PDOException $e) {
        echo "<p class='error'>❌ 数据库操作测试失败: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h4>建议的配置：</h4>\n";
    echo "<pre style='background:#f5f5f5;padding:10px;border-radius:5px;'>";
    echo "// config/database.php 配置\n";
    echo "define('DB_HOST', '{$host}');\n";
    echo "define('DB_PORT', {$workingPort});\n";
    echo "define('DB_USER', '{$username}');\n";
    echo "define('DB_PASS', '" . (empty($password) ? '' : $password) . "');\n";
    echo "define('DB_NAME', 'bitbear_system');\n";
    echo "define('DB_CHARSET', 'utf8mb4');\n";
    echo "</pre>\n";
    
} else {
    echo "<h3 class='error'>❌ 连接测试失败！</h3>\n";
    echo "<h4>可能的解决方案：</h4>\n";
    echo "<ol>\n";
    echo "<li><strong>启动MySQL服务：</strong>\n";
    echo "<ul>\n";
    echo "<li>Windows: 在服务管理器中启动MySQL服务</li>\n";
    echo "<li>或运行: <code>net start mysql</code></li>\n";
    echo "</ul></li>\n";
    echo "<li><strong>检查XAMPP/WAMP：</strong>\n";
    echo "<ul>\n";
    echo "<li>确保XAMPP或WAMP中的MySQL已启动</li>\n";
    echo "<li>检查控制面板中的MySQL状态</li>\n";
    echo "</ul></li>\n";
    echo "<li><strong>检查端口：</strong>\n";
    echo "<ul>\n";
    echo "<li>MySQL可能运行在其他端口</li>\n";
    echo "<li>检查MySQL配置文件(my.ini/my.cnf)</li>\n";
    echo "</ul></li>\n";
    echo "<li><strong>检查防火墙：</strong>\n";
    echo "<ul>\n";
    echo "<li>确保防火墙没有阻止MySQL端口</li>\n";
    echo "</ul></li>\n";
    echo "</ol>\n";
}

echo "<hr>\n";
echo "<h4>系统信息：</h4>\n";
echo "<ul>\n";
echo "<li>PHP版本: " . PHP_VERSION . "</li>\n";
echo "<li>操作系统: " . php_uname() . "</li>\n";
echo "<li>PDO MySQL扩展: " . (extension_loaded('pdo_mysql') ? '✅ 已安装' : '❌ 未安装') . "</li>\n";
echo "<li>当前时间: " . date('Y-m-d H:i:s') . "</li>\n";
echo "</ul>\n";

echo "<p><a href='admin-dashboard.php'>返回管理后台</a></p>\n";
?>
