<?php
session_start();

// 如果用户已登录，重定向到首页
require_once 'classes/Auth.php';
$auth = new Auth();
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/login.css">
    <link rel="stylesheet" href="assets/css/toast.css">
</head>
<body>
    <!-- 背景渐变 -->
    <div class="background-gradient"></div>
    
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="image/bitlogo.png" alt="比特熊" class="brand-logo">
                <span class="brand-text">比特熊智慧系统</span>
            </div>
            <div class="nav-links">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="register.php" class="nav-link">注册</a>
            </div>
        </div>
    </nav>

    <!-- 登录表单容器 -->
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1 class="login-title">欢迎回来</h1>
                <p class="login-subtitle">登录您的比特熊智慧系统账户</p>
            </div>

            <form id="loginForm" class="login-form">
                <!-- 用户名/邮箱字段 -->
                <div class="form-group">
                    <label for="username" class="form-label">用户名或邮箱</label>
                    <div class="input-container">
                        <input type="text" id="username" name="username" class="form-input" required placeholder="请输入用户名或邮箱">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                    </div>
                    <div class="field-error" id="usernameError"></div>
                </div>

                <!-- 密码字段 -->
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-container">
                        <input type="password" id="password" name="password" class="form-input" required placeholder="请输入密码">
                        <div class="input-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
                                <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </div>
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                    <div class="field-error" id="passwordError"></div>
                </div>

                <!-- 记住我和忘记密码 -->
                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe" name="rememberMe">
                        <span class="checkmark"></span>
                        <span class="checkbox-text">记住我</span>
                    </label>
                    <a href="#" class="forgot-password-link">忘记密码？</a>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="login-button" id="loginButton">
                    <span class="button-text">登录</span>
                    <div class="button-loader" style="display: none;">
                        <div class="spinner"></div>
                    </div>
                </button>
            </form>

            <!-- 分割线 -->
            <div class="divider">
                <span class="divider-text">或</span>
            </div>

            <!-- 第三方登录 -->
            <div class="social-login">
                <button type="button" class="social-button social-wechat">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M8.5 12C9.32843 12 10 11.3284 10 10.5C10 9.67157 9.32843 9 8.5 9C7.67157 9 7 9.67157 7 10.5C7 11.3284 7.67157 12 8.5 12Z" fill="currentColor"/>
                        <path d="M15.5 12C16.3284 12 17 11.3284 17 10.5C17 9.67157 16.3284 9 15.5 9C14.6716 9 14 9.67157 14 10.5C14 11.3284 14.6716 12 15.5 12Z" fill="currentColor"/>
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
                    </svg>
                    <span>微信登录</span>
                </button>
                <button type="button" class="social-button social-qq">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
                    </svg>
                    <span>QQ登录</span>
                </button>
            </div>

            <!-- 注册链接 -->
            <div class="login-footer">
                <p class="register-prompt">还没有账户？ <a href="register.php" class="register-link">立即注册</a></p>
            </div>
        </div>

        <!-- 登录特性展示 -->
        <div class="features-card">
            <h3 class="features-title">为什么选择比特熊？</h3>
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="feature-content">
                        <h4 class="feature-name">智能学习</h4>
                        <p class="feature-desc">AI驱动的个性化学习体验</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="currentColor" stroke-width="2"/>
                            <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                            <path d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.7018C21.7033 16.0495 20.9999 15.5902 20.2 15.3993" stroke="currentColor" stroke-width="2"/>
                            <path d="M16 3.13C16.8003 3.32105 17.5037 3.78047 18.0098 4.43273C18.5159 5.08499 18.8004 5.89549 18.8004 6.73C18.8004 7.56451 18.5159 8.37501 18.0098 9.02727C17.5037 9.67953 16.8003 10.1389 16 10.33" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="feature-content">
                        <h4 class="feature-name">社区交流</h4>
                        <p class="feature-desc">与志同道合的伙伴一起成长</p>
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="feature-content">
                        <h4 class="feature-name">专业认证</h4>
                        <p class="feature-desc">获得行业认可的技能证书</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="assets/js/login.js"></script>
</body>
</html>
