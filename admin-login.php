<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊智慧系统 - 管理员登录</title>
    <link rel="icon" type="image/png" href="image/bit.png">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 背景装饰元素 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            box-shadow: 
                0 25px 80px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.9),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }

        .logo img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-subtitle {
            color: #64748b;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .user-type-selector {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 2rem;
            position: relative;
        }

        .user-type-option {
            flex: 1;
            padding: 0.75rem 1rem;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 600;
            color: #64748b;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .user-type-option.active {
            color: white;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .login-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(59, 130, 246, 0.4);
        }

        .login-button:hover::before {
            left: 100%;
        }

        .login-button:active {
            transform: translateY(0);
        }

        .forgot-password {
            text-align: center;
            margin-top: 1.5rem;
        }

        .forgot-password a {
            color: #3b82f6;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password a:hover {
            color: #1d4ed8;
        }

        .error-message {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #16a34a;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            margin-bottom: 1rem;
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <img src="image/bit.png" alt="比特熊Logo">
            </div>
            <h1 class="login-title">管理员登录</h1>
            <p class="login-subtitle">欢迎回到比特熊智慧系统</p>
        </div>

        <div class="user-type-selector">
            <div class="user-type-option active" data-type="super">超级管理员</div>
            <div class="user-type-option" data-type="admin">普通管理员</div>
            <div class="user-type-option" data-type="vip">特殊用户</div>
        </div>

        <form id="loginForm" method="POST" action="admin-auth.php">
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>

            <?php if (isset($_GET['logout']) && $_GET['logout'] == '1'): ?>
            <div class="success-message" style="display: block;">
                您已成功退出登录
            </div>
            <?php endif; ?>
            
            <input type="hidden" name="user_type" id="userType" value="super">
            
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <input type="text" id="username" name="username" class="form-input" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" required>
            </div>

            <button type="submit" class="login-button">登录</button>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">忘记密码？</a>
        </div>
    </div>

    <script>
        // 用户类型切换
        document.querySelectorAll('.user-type-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.user-type-option').forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');
                document.getElementById('userType').value = this.dataset.type;
            });
        });

        // 表单提交处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            const submitButton = this.querySelector('button[type="submit"]');

            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';

            // 禁用提交按钮，防止重复提交
            submitButton.disabled = true;
            submitButton.textContent = '登录中...';

            try {
                // 发送登录请求到后端
                const response = await fetch('admin-auth.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    successMessage.textContent = result.message + '，正在跳转...';
                    successMessage.style.display = 'block';

                    // 登录成功后跳转
                    setTimeout(() => {
                        window.location.href = result.redirect || 'admin-dashboard.php';
                    }, 1000);
                } else {
                    errorMessage.textContent = result.message || '登录失败，请重试';
                    errorMessage.style.display = 'block';

                    // 重新启用提交按钮
                    submitButton.disabled = false;
                    submitButton.textContent = '登录';
                }
            } catch (error) {
                console.error('登录请求失败:', error);
                errorMessage.textContent = '网络错误，请检查连接后重试';
                errorMessage.style.display = 'block';

                // 重新启用提交按钮
                submitButton.disabled = false;
                submitButton.textContent = '登录';
            }
        });

        function showForgotPassword() {
            alert('请联系系统管理员重置密码');
        }
    </script>
</body>
</html>
