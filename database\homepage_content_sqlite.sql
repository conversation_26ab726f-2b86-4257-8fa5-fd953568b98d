-- 主页内容管理数据库表结构 (SQLite版本)
-- 用于主页设计器功能

-- 主页英雄区域内容表
CREATE TABLE IF NOT EXISTS homepage_hero (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL DEFAULT '欢迎访问比特熊极简门户网站',
    subtitle VARCHAR(255) NOT NULL DEFAULT '在这里一起和小熊彼彼度过美好的时光',
    description TEXT NOT NULL DEFAULT '也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等',
    primary_button_text VARCHAR(100) NOT NULL DEFAULT '立即体验',
    primary_button_url VARCHAR(255) NOT NULL DEFAULT '#',
    secondary_button_text VARCHAR(100) NOT NULL DEFAULT '了解更多',
    secondary_button_url VARCHAR(255) NOT NULL DEFAULT '#features',
    hero_image VARCHAR(255) NOT NULL DEFAULT 'image/bitlogo.png',
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 专家卡片表
CREATE TABLE IF NOT EXISTS homepage_experts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    title VARCHAR(150) NOT NULL,
    description TEXT,
    image VARCHAR(255) DEFAULT 'image/default-avatar.svg',
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 视频内容表
CREATE TABLE IF NOT EXISTS homepage_videos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    speaker_name VARCHAR(100) NOT NULL,
    speaker_role VARCHAR(150) NOT NULL,
    video_file VARCHAR(255),
    video_url VARCHAR(500),
    thumbnail VARCHAR(255),
    company_badge VARCHAR(100) DEFAULT 'O''REILLY',
    duration VARCHAR(20),
    is_featured BOOLEAN DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 主页区域配置表
CREATE TABLE IF NOT EXISTS homepage_sections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    section_name VARCHAR(100) NOT NULL UNIQUE,
    section_title VARCHAR(255),
    section_subtitle VARCHAR(255),
    section_description TEXT,
    is_visible BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    custom_css TEXT,
    custom_js TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文件上传记录表
CREATE TABLE IF NOT EXISTS homepage_uploads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(20) NOT NULL CHECK (file_type IN ('image', 'video', 'document')),
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_by INTEGER,
    is_used BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入默认数据
INSERT OR IGNORE INTO homepage_hero (id, title, subtitle, description, primary_button_text, primary_button_url, secondary_button_text, secondary_button_url, hero_image) 
VALUES (1, '欢迎访问比特熊极简门户网站', '在这里一起和小熊彼彼度过美好的时光', 
'也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等', 
'立即体验', '#', '了解更多', '#features', 'image/bitlogo.png');

-- 插入默认专家数据
INSERT OR IGNORE INTO homepage_experts (name, title, description, image, sort_order) VALUES
('Arianne Dee', 'Software developer', '资深软件开发工程师，专注于Python和Web开发', 'image/default-avatar.svg', 1),
('Sari Greene', 'Cybersecurity practitioner', '网络安全专家，在信息安全领域有丰富经验', 'image/default-avatar.svg', 2),
('Bruno Gonçalves', 'Senior data scientist', '高级数据科学家，专注于机器学习和数据分析', 'image/default-avatar.svg', 3),
('Neal Ford', 'Software architect', '软件架构师，在企业级应用设计方面经验丰富', 'image/default-avatar.svg', 4),
('Kelsey Hightower', 'Software engineer', '软件工程师，云原生技术专家', 'image/default-avatar.svg', 5),
('Ken Kousen', 'Java Champion', 'Java冠军，在Java生态系统方面有深厚造诣', 'image/default-avatar.svg', 6);

-- 插入默认视频数据
INSERT OR IGNORE INTO homepage_videos (title, description, speaker_name, speaker_role, video_url, company_badge, is_featured, sort_order) VALUES
('Why Jose uses O''Reilly every day', 'As a principal software engineer, I rely on O''Reilly''s platform to keep my team updated with the latest technologies and best practices.', 
'Jose Dunio', 'Principal Software Engineer', '#', 'O''REILLY', 1, 1);

-- 插入默认区域配置
INSERT OR IGNORE INTO homepage_sections (section_name, section_title, section_subtitle, section_description, is_visible, sort_order) VALUES
('hero', '英雄区域', '主页顶部展示区域', '包含主标题、副标题、描述文本和行动按钮', 1, 1),
('stats', '统计数据区域', '数据统计展示', '显示网站的关键统计数据', 1, 2),
('oreilly-hero', 'O''Reilly风格区域', '技能建设区域', '展示学习平台的核心价值', 1, 3),
('experts', '专家展示区域', '专家团队', '展示平台的专家和讲师', 1, 4),
('testimonial', '推荐视频区域', '用户推荐', '展示用户使用体验和推荐视频', 1, 5);
