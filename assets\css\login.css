/* 用户登录页面样式 */
@import url('register.css');

/* 登录页面特定样式 */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rem 1.5rem 2rem;
    gap: 3rem;
}

.login-card {
    width: 100%;
    max-width: 420px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.forgot-password-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.forgot-password-link:hover {
    color: white;
    text-decoration: underline;
}

/* 登录按钮 */
.login-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.login-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 分割线 */
.divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
}

.divider-text {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 0 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
}

/* 第三方登录 */
.social-login {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.social-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.social-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.social-wechat:hover {
    background: rgba(7, 193, 96, 0.2);
    border-color: rgba(7, 193, 96, 0.4);
}

.social-qq:hover {
    background: rgba(18, 183, 245, 0.2);
    border-color: rgba(18, 183, 245, 0.4);
}

/* 登录页脚 */
.login-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.register-prompt {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.register-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.register-link:hover {
    text-decoration: underline;
}

/* 特性展示卡片 */
.features-card {
    width: 100%;
    max-width: 380px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.05);
}

.features-title {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    flex-shrink: 0;
}

.feature-content {
    flex: 1;
}

.feature-name {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.feature-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .login-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .features-card {
        max-width: 420px;
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 5rem 1rem 2rem;
        gap: 1.5rem;
    }
    
    .login-card {
        padding: 2rem 1.5rem;
    }
    
    .features-card {
        padding: 1.5rem;
    }
    
    .login-title {
        font-size: 1.75rem;
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .features-list {
        gap: 1rem;
    }
    
    .feature-item {
        gap: 0.75rem;
    }
    
    .feature-icon {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 480px) {
    .login-card {
        padding: 1.5rem 1rem;
    }
    
    .features-card {
        padding: 1rem;
    }
    
    .features-title {
        font-size: 1.125rem;
    }
}
