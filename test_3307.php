<?php
/**
 * 测试端口3307的MySQL连接
 */

echo "测试MySQL端口3307连接\n";
echo "====================\n";

$host = 'localhost';
$port = 3307;
$username = 'root';
$password = '';

try {
    echo "正在连接到 {$host}:{$port}...\n";
    
    $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_TIMEOUT => 5, // 5秒超时
    ]);
    
    echo "✅ 连接成功！\n";
    
    // 获取版本
    $version = $pdo->query('SELECT VERSION() as v')->fetch();
    echo "📋 MySQL版本: {$version['v']}\n";
    
    // 显示数据库列表
    $databases = $pdo->query('SHOW DATABASES')->fetchAll();
    echo "📁 现有数据库: ";
    foreach ($databases as $db) {
        echo $db['Database'] . " ";
    }
    echo "\n";
    
    // 创建我们的数据库
    $dbName = 'bitbear_system';
    echo "\n正在创建数据库 '{$dbName}'...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ 数据库创建成功\n";
    
    // 连接到我们的数据库
    $dsn = "mysql:host={$host};port={$port};dbname={$dbName};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "✅ 成功连接到数据库 '{$dbName}'\n";
    
    // 测试创建表
    echo "\n测试创建表...\n";
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ 测试表创建成功\n";
    
    // 测试插入数据
    $stmt = $pdo->prepare("INSERT INTO test_table (name) VALUES (?)");
    $stmt->execute(['测试数据']);
    echo "✅ 数据插入成功\n";
    
    // 测试查询数据
    $result = $pdo->query("SELECT * FROM test_table ORDER BY id DESC LIMIT 1")->fetch();
    echo "✅ 数据查询成功: {$result['name']}\n";
    
    // 清理测试表
    $pdo->exec("DROP TABLE test_table");
    echo "✅ 测试表清理完成\n";
    
    echo "\n🎉 数据库连接和操作测试全部成功！\n";
    echo "\n推荐配置:\n";
    echo "主机: {$host}\n";
    echo "端口: {$port}\n";
    echo "用户名: {$username}\n";
    echo "密码: " . (empty($password) ? '(空)' : $password) . "\n";
    echo "数据库: {$dbName}\n";
    
} catch (PDOException $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "\n可能的解决方案:\n";
    echo "1. 确保MySQL服务正在运行\n";
    echo "2. 检查端口3307是否被占用\n";
    echo "3. 确认用户名和密码正确\n";
}

echo "\n测试完成！\n";
?>
