<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 10px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; }
        .highlight { background: yellow; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🔍 仪表板结构调试</h1>
    
    <div class="debug-section">
        <h2>检查管理模块快捷入口</h2>
        <button onclick="checkShortcuts()">检查快捷入口元素</button>
        <button onclick="checkStyles()">检查CSS样式</button>
        <button onclick="checkPosition()">检查元素位置</button>
        <div id="shortcutResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h2>仪表板布局分析</h2>
        <button onclick="analyzeDashboard()">分析仪表板结构</button>
        <button onclick="checkVisibility()">检查元素可见性</button>
        <div id="layoutResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h2>快速修复</h2>
        <button onclick="addTestShortcuts()">添加测试快捷入口</button>
        <button onclick="forceShowShortcuts()">强制显示快捷入口</button>
        <div id="fixResult" class="result"></div>
    </div>

    <script>
        function checkShortcuts() {
            const result = document.getElementById('shortcutResult');
            result.innerHTML = '<p class="info">正在检查快捷入口元素...</p>';
            
            // 在新窗口中打开管理后台并检查
            const adminWindow = window.open('admin-dashboard.php', '_blank');
            
            setTimeout(() => {
                try {
                    const shortcuts = adminWindow.document.querySelector('.management-shortcuts');
                    const shortcutCards = adminWindow.document.querySelectorAll('.shortcut-card');
                    
                    let report = [];
                    
                    if (shortcuts) {
                        report.push('✅ 找到 .management-shortcuts 元素');
                        report.push(`📏 元素尺寸: ${shortcuts.offsetWidth}x${shortcuts.offsetHeight}`);
                        report.push(`📍 元素位置: top=${shortcuts.offsetTop}, left=${shortcuts.offsetLeft}`);
                        
                        const computedStyle = adminWindow.getComputedStyle(shortcuts);
                        report.push(`👁️ 显示状态: display=${computedStyle.display}, visibility=${computedStyle.visibility}`);
                        report.push(`🎨 背景色: ${computedStyle.backgroundColor}`);
                    } else {
                        report.push('❌ 未找到 .management-shortcuts 元素');
                    }
                    
                    if (shortcutCards.length > 0) {
                        report.push(`✅ 找到 ${shortcutCards.length} 个快捷卡片`);
                        shortcutCards.forEach((card, index) => {
                            const title = card.querySelector('h4')?.textContent || '未知';
                            report.push(`  📋 卡片 ${index + 1}: ${title}`);
                        });
                    } else {
                        report.push('❌ 未找到快捷卡片');
                    }
                    
                    result.innerHTML = `
                        <p class="success">检查完成</p>
                        <pre>${report.join('\n')}</pre>
                    `;
                } catch (error) {
                    result.innerHTML = `<p class="error">检查失败: ${error.message}</p>`;
                }
            }, 2000);
        }

        function checkStyles() {
            const result = document.getElementById('shortcutResult');
            result.innerHTML = '<p class="info">正在检查CSS样式...</p>';
            
            // 检查样式是否正确加载
            fetch('admin-dashboard.php')
                .then(response => response.text())
                .then(html => {
                    const hasShortcutStyles = html.includes('.management-shortcuts');
                    const hasShortcutGrid = html.includes('.shortcuts-grid');
                    const hasShortcutCard = html.includes('.shortcut-card');
                    
                    let report = [];
                    report.push(hasShortcutStyles ? '✅ 找到 .management-shortcuts 样式' : '❌ 缺少 .management-shortcuts 样式');
                    report.push(hasShortcutGrid ? '✅ 找到 .shortcuts-grid 样式' : '❌ 缺少 .shortcuts-grid 样式');
                    report.push(hasShortcutCard ? '✅ 找到 .shortcut-card 样式' : '❌ 缺少 .shortcut-card 样式');
                    
                    // 检查HTML结构
                    const hasShortcutHTML = html.includes('management-shortcuts');
                    const hasShortcutCardsHTML = html.includes('shortcut-card');
                    
                    report.push('');
                    report.push('HTML结构检查:');
                    report.push(hasShortcutHTML ? '✅ 找到快捷入口HTML' : '❌ 缺少快捷入口HTML');
                    report.push(hasShortcutCardsHTML ? '✅ 找到快捷卡片HTML' : '❌ 缺少快捷卡片HTML');
                    
                    result.innerHTML = `
                        <p class="success">样式检查完成</p>
                        <pre>${report.join('\n')}</pre>
                    `;
                })
                .catch(error => {
                    result.innerHTML = `<p class="error">样式检查失败: ${error.message}</p>`;
                });
        }

        function analyzeDashboard() {
            const result = document.getElementById('layoutResult');
            result.innerHTML = '<p class="info">正在分析仪表板结构...</p>';
            
            const adminWindow = window.open('admin-dashboard.php', '_blank');
            
            setTimeout(() => {
                try {
                    const contentArea = adminWindow.document.getElementById('contentArea');
                    const dashboardContent = adminWindow.document.getElementById('dashboardContent');
                    const dashboardLayout = adminWindow.document.querySelector('.dashboard-layout');
                    const shortcuts = adminWindow.document.querySelector('.management-shortcuts');
                    
                    let report = [];
                    
                    if (contentArea) {
                        report.push('✅ 找到内容区域 (#contentArea)');
                        report.push(`   显示状态: ${adminWindow.getComputedStyle(contentArea).display}`);
                    }
                    
                    if (dashboardContent) {
                        report.push('✅ 找到仪表板内容 (#dashboardContent)');
                        report.push(`   显示状态: ${adminWindow.getComputedStyle(dashboardContent).display}`);
                    }
                    
                    if (dashboardLayout) {
                        report.push('✅ 找到仪表板布局 (.dashboard-layout)');
                        const style = adminWindow.getComputedStyle(dashboardLayout);
                        report.push(`   网格列: ${style.gridTemplateColumns}`);
                        report.push(`   显示状态: ${style.display}`);
                    }
                    
                    if (shortcuts) {
                        report.push('✅ 找到快捷入口 (.management-shortcuts)');
                        const parent = shortcuts.parentElement;
                        report.push(`   父元素: ${parent.tagName}${parent.id ? '#' + parent.id : ''}${parent.className ? '.' + parent.className.split(' ').join('.') : ''}`);
                    } else {
                        report.push('❌ 未找到快捷入口');
                    }
                    
                    result.innerHTML = `
                        <p class="success">结构分析完成</p>
                        <pre>${report.join('\n')}</pre>
                    `;
                } catch (error) {
                    result.innerHTML = `<p class="error">分析失败: ${error.message}</p>`;
                }
            }, 2000);
        }

        function addTestShortcuts() {
            const result = document.getElementById('fixResult');
            result.innerHTML = '<p class="info">正在添加测试快捷入口...</p>';
            
            const adminWindow = window.open('admin-dashboard.php', '_blank');
            
            setTimeout(() => {
                try {
                    const dashboardContent = adminWindow.document.getElementById('dashboardContent');
                    
                    if (dashboardContent) {
                        const testShortcuts = adminWindow.document.createElement('div');
                        testShortcuts.innerHTML = `
                            <div style="background: #ff6b6b; color: white; padding: 20px; margin: 20px 0; border-radius: 8px; text-align: center;">
                                <h3>🧪 测试快捷入口</h3>
                                <p>如果您能看到这个红色区域，说明JavaScript可以正常添加元素到仪表板中。</p>
                                <p>这意味着快捷入口的位置和显示逻辑是正确的。</p>
                            </div>
                        `;
                        
                        dashboardContent.appendChild(testShortcuts);
                        
                        result.innerHTML = '<p class="success">✅ 测试快捷入口已添加到仪表板</p>';
                    } else {
                        result.innerHTML = '<p class="error">❌ 未找到仪表板内容区域</p>';
                    }
                } catch (error) {
                    result.innerHTML = `<p class="error">添加失败: ${error.message}</p>`;
                }
            }, 2000);
        }

        function forceShowShortcuts() {
            const result = document.getElementById('fixResult');
            result.innerHTML = '<p class="info">正在强制显示快捷入口...</p>';
            
            const adminWindow = window.open('admin-dashboard.php', '_blank');
            
            setTimeout(() => {
                try {
                    const shortcuts = adminWindow.document.querySelector('.management-shortcuts');
                    
                    if (shortcuts) {
                        // 强制设置样式
                        shortcuts.style.display = 'block';
                        shortcuts.style.visibility = 'visible';
                        shortcuts.style.opacity = '1';
                        shortcuts.style.background = '#ffffff';
                        shortcuts.style.border = '2px solid #ff6b6b';
                        shortcuts.style.padding = '20px';
                        shortcuts.style.margin = '20px 0';
                        
                        result.innerHTML = '<p class="success">✅ 已强制显示快捷入口（红色边框）</p>';
                    } else {
                        result.innerHTML = '<p class="error">❌ 未找到快捷入口元素</p>';
                    }
                } catch (error) {
                    result.innerHTML = `<p class="error">强制显示失败: ${error.message}</p>`;
                }
            }, 2000);
        }

        function checkVisibility() {
            const result = document.getElementById('layoutResult');
            result.innerHTML = '<p class="info">正在检查元素可见性...</p>';
            
            const adminWindow = window.open('admin-dashboard.php', '_blank');
            
            setTimeout(() => {
                try {
                    const elements = [
                        { name: '内容区域', selector: '#contentArea' },
                        { name: '仪表板内容', selector: '#dashboardContent' },
                        { name: '仪表板布局', selector: '.dashboard-layout' },
                        { name: '快捷入口', selector: '.management-shortcuts' },
                        { name: '快捷网格', selector: '.shortcuts-grid' },
                        { name: '快捷卡片', selector: '.shortcut-card' }
                    ];
                    
                    let report = [];
                    
                    elements.forEach(element => {
                        const el = adminWindow.document.querySelector(element.selector);
                        if (el) {
                            const style = adminWindow.getComputedStyle(el);
                            const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
                            const rect = el.getBoundingClientRect();
                            
                            report.push(`${isVisible ? '✅' : '❌'} ${element.name}:`);
                            report.push(`   display: ${style.display}`);
                            report.push(`   visibility: ${style.visibility}`);
                            report.push(`   opacity: ${style.opacity}`);
                            report.push(`   尺寸: ${rect.width}x${rect.height}`);
                            report.push(`   位置: (${rect.left}, ${rect.top})`);
                            report.push('');
                        } else {
                            report.push(`❌ ${element.name}: 元素不存在`);
                            report.push('');
                        }
                    });
                    
                    result.innerHTML = `
                        <p class="success">可见性检查完成</p>
                        <pre>${report.join('\n')}</pre>
                    `;
                } catch (error) {
                    result.innerHTML = `<p class="error">检查失败: ${error.message}</p>`;
                }
            }, 2000);
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('仪表板调试工具已加载');
            console.log('使用这些工具来诊断快捷入口显示问题');
        };
    </script>
</body>
</html>
