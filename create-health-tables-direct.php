<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>直接创建健康管理数据表</h2>";

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    echo "<h3>创建 health_fields 表...</h3>";
    
    // 创建健康字段表
    $sql1 = "
    CREATE TABLE IF NOT EXISTS health_fields (
        id INT PRIMARY KEY AUTO_INCREMENT,
        field_name VARCHAR(100) NOT NULL UNIQUE,
        field_type ENUM('number', 'text', 'boolean', 'select') DEFAULT 'number',
        field_unit VARCHAR(20),
        field_options TEXT,
        display_order INT DEFAULT 0,
        is_system BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_display_order (display_order),
        INDEX idx_is_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql1);
    echo "<p style='color: green;'>✓ health_fields 表创建成功</p>";
    
    echo "<h3>创建 health_logs 表...</h3>";
    
    // 创建健康日志表
    $sql2 = "
    CREATE TABLE IF NOT EXISTS health_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        log_date DATE NOT NULL,
        field_id INT NOT NULL,
        field_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_date_field (user_id, log_date, field_id),
        INDEX idx_user_date (user_id, log_date),
        INDEX idx_field_id (field_id),
        FOREIGN KEY (field_id) REFERENCES health_fields(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql2);
    echo "<p style='color: green;'>✓ health_logs 表创建成功</p>";
    
    echo "<h3>插入默认健康字段...</h3>";
    
    // 检查是否已有数据
    $stmt = $db->query("SELECT COUNT(*) FROM health_fields");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // 插入默认健康字段
        $defaultFields = [
            ['发热', 'boolean', '', null, 1],
            ['咳嗽', 'boolean', '', null, 2],
            ['呼吸不通畅', 'boolean', '', null, 3],
            ['最大心率数', 'number', 'bpm', null, 4],
            ['最小心率数', 'number', 'bpm', null, 5],
            ['静息心率数', 'number', 'bpm', null, 6],
            ['心率异常总数', 'number', '次', null, 7],
            ['运动步数', 'number', '步', null, 8],
            ['房颤数', 'number', '次', null, 9],
            ['早搏数', 'number', '次', null, 10],
            ['疑似房颤标识', 'boolean', '', null, 11],
            ['疑似早搏标识', 'boolean', '', null, 12],
            ['血氧饱和度', 'number', '%', null, 13],
            ['其他', 'text', '', null, 14]
        ];
        
        $insertSql = "INSERT INTO health_fields (field_name, field_type, field_unit, field_options, display_order, is_system, is_active) VALUES (?, ?, ?, ?, ?, 1, 1)";
        $stmt = $db->prepare($insertSql);
        
        foreach ($defaultFields as $field) {
            $stmt->execute($field);
        }
        
        echo "<p style='color: green;'>✓ 插入了 " . count($defaultFields) . " 个默认健康字段</p>";
    } else {
        echo "<p style='color: blue;'>ℹ 健康字段已存在，跳过插入</p>";
    }
    
    echo "<h3>验证表创建结果</h3>";
    
    // 验证表
    $tables = ['health_fields', 'health_logs'];
    foreach ($tables as $table) {
        $stmt = $db->query("SELECT COUNT(*) FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ 表 $table 存在，记录数: $count</p>";
    }
    
    // 显示字段列表
    echo "<h3>健康字段列表</h3>";
    $stmt = $db->query("SELECT * FROM health_fields ORDER BY display_order");
    $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($fields)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>字段名</th><th>类型</th><th>单位</th><th>顺序</th><th>系统字段</th><th>状态</th></tr>";
        foreach ($fields as $field) {
            echo "<tr>";
            echo "<td>" . $field['id'] . "</td>";
            echo "<td>" . $field['field_name'] . "</td>";
            echo "<td>" . $field['field_type'] . "</td>";
            echo "<td>" . ($field['field_unit'] ?: '-') . "</td>";
            echo "<td>" . $field['display_order'] . "</td>";
            echo "<td>" . ($field['is_system'] ? '是' : '否') . "</td>";
            echo "<td>" . ($field['is_active'] ? '启用' : '禁用') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>🎉 健康管理数据表创建完成！</h3>";
    echo "<p><a href='health-management.php' style='background: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>立即体验健康管理</a></p>";
    echo "<p><a href='admin-dashboard.php'>返回管理后台</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red; background: #fee; padding: 10px; border: 1px solid #fcc;'>错误: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接和权限设置</p>";
}
?>
