<?php
/**
 * 服务器环境诊断脚本
 * 请将此文件上传到服务器根目录，然后访问测试
 */

echo "<h1>🔍 服务器环境诊断报告</h1>";
echo "<hr>";

// 1. 基本环境信息
echo "<h2>📊 基本环境信息</h2>";
echo "<ul>";
echo "<li><strong>PHP版本:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>服务器软件:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</li>";
echo "<li><strong>文档根目录:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>当前目录:</strong> " . __DIR__ . "</li>";
echo "<li><strong>服务器时间:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// 2. 关键文件检查
echo "<h2>📁 关键文件检查</h2>";
$critical_files = [
    'admin-dashboard.php',
    'config/database.php',
    'assets/css/admin.css',
    'assets/js/admin.js',
    'api/stats.php',
    'api/notifications.php',
    'api/activities.php'
];

echo "<ul>";
foreach ($critical_files as $file) {
    $exists = file_exists($file);
    $readable = $exists ? is_readable($file) : false;
    $status = $exists ? ($readable ? '✅ 存在且可读' : '⚠️ 存在但不可读') : '❌ 不存在';
    echo "<li><strong>{$file}:</strong> {$status}";
    if ($exists) {
        echo " (大小: " . filesize($file) . " 字节)";
    }
    echo "</li>";
}
echo "</ul>";

// 3. 数据库连接测试
echo "<h2>🗄️ 数据库连接测试</h2>";
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    if ($connection) {
        echo "<span style='color: green;'>✅ 数据库连接成功</span><br>";
        
        // 测试关键表
        $tables = ['users', 'user_roles', 'posts', 'comments', 'system_settings'];
        echo "<h3>📋 数据库表检查</h3>";
        echo "<ul>";
        foreach ($tables as $table) {
            try {
                $stmt = $connection->query("SELECT COUNT(*) FROM {$table}");
                $count = $stmt->fetchColumn();
                echo "<li><strong>{$table}:</strong> ✅ 存在 ({$count} 条记录)</li>";
            } catch (Exception $e) {
                echo "<li><strong>{$table}:</strong> ❌ 不存在或无法访问</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<span style='color: red;'>❌ 数据库连接失败</span><br>";
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ 数据库配置错误: " . $e->getMessage() . "</span><br>";
}

// 4. PHP扩展检查
echo "<h2>🔧 PHP扩展检查</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'json', 'curl', 'gd', 'fileinfo'];
echo "<ul>";
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅ 已加载' : '❌ 未加载';
    echo "<li><strong>{$ext}:</strong> {$status}</li>";
}
echo "</ul>";

// 5. 文件权限检查
echo "<h2>🔐 文件权限检查</h2>";
$permission_dirs = [
    '.',
    'uploads',
    'assets',
    'config',
    'api'
];

echo "<ul>";
foreach ($permission_dirs as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir);
        $status = $writable ? "✅ 可写 ({$perms})" : "⚠️ 不可写 ({$perms})";
        echo "<li><strong>{$dir}/:</strong> {$status}</li>";
    } else {
        echo "<li><strong>{$dir}/:</strong> ❌ 目录不存在</li>";
    }
}
echo "</ul>";

// 6. 静态资源检查
echo "<h2>🎨 静态资源检查</h2>";
$static_files = [
    'assets/css/admin.css',
    'assets/js/admin.js',
    'assets/js/jquery.min.js',
    'assets/css/bootstrap.min.css',
    'image/bit.png'
];

echo "<ul>";
foreach ($static_files as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    $status = $exists ? "✅ 存在 ({$size} 字节)" : "❌ 不存在";
    echo "<li><strong>{$file}:</strong> {$status}</li>";
}
echo "</ul>";

// 7. 错误日志检查
echo "<h2>📝 错误日志信息</h2>";
$error_log = ini_get('error_log');
echo "<p><strong>错误日志位置:</strong> " . ($error_log ?: '未设置') . "</p>";

// 显示最近的PHP错误
if (function_exists('error_get_last')) {
    $last_error = error_get_last();
    if ($last_error) {
        echo "<h3>最近的PHP错误:</h3>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
        echo "类型: " . $last_error['type'] . "\n";
        echo "消息: " . $last_error['message'] . "\n";
        echo "文件: " . $last_error['file'] . "\n";
        echo "行号: " . $last_error['line'] . "\n";
        echo "</pre>";
    } else {
        echo "<p>✅ 没有检测到最近的PHP错误</p>";
    }
}

// 8. 建议的修复步骤
echo "<h2>💡 建议的修复步骤</h2>";
echo "<ol>";
echo "<li><strong>检查浏览器控制台</strong> - 查看JavaScript错误和网络请求失败</li>";
echo "<li><strong>验证文件完整性</strong> - 确保所有文件都已正确上传</li>";
echo "<li><strong>检查文件权限</strong> - 设置正确的文件和目录权限</li>";
echo "<li><strong>清除浏览器缓存</strong> - 强制刷新页面资源</li>";
echo "<li><strong>检查数据库配置</strong> - 确保数据库连接参数正确</li>";
echo "<li><strong>查看服务器错误日志</strong> - 在宝塔面板中查看详细错误信息</li>";
echo "</ol>";

echo "<hr>";
echo "<p><strong>诊断完成时间:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>建议:</strong> 请将此报告截图发送给技术支持以获得进一步帮助。</p>";
?>
