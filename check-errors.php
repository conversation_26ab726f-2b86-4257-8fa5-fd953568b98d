<?php
/**
 * 错误检查页面
 * 检查系统中的常见错误
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>系统错误检查</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style></head><body>";

echo "<h1>系统错误检查</h1>";

// 1. 检查认证初始化
echo "<h2>1. 认证系统检查</h2>";
try {
    require_once 'includes/auth-init.php';
    
    echo "<div class='success'>✓ 认证初始化文件加载成功</div>";
    
    // 检查变量
    if (isset($currentUser)) {
        echo "<div class='success'>✓ \$currentUser 变量已定义</div>";
        if ($currentUser) {
            echo "<div class='info'>当前用户: " . ($currentUser['username'] ?? '未知') . "</div>";
        } else {
            echo "<div class='info'>当前用户: 未登录</div>";
        }
    } else {
        echo "<div class='error'>✗ \$currentUser 变量未定义</div>";
    }
    
    if (isset($isAuthenticated)) {
        echo "<div class='success'>✓ \$isAuthenticated 变量已定义: " . ($isAuthenticated ? 'true' : 'false') . "</div>";
    } else {
        echo "<div class='error'>✗ \$isAuthenticated 变量未定义</div>";
    }
    
    if (isset($auth)) {
        echo "<div class='success'>✓ \$auth 对象已定义</div>";
        if ($auth) {
            echo "<div class='info'>Auth对象类型: " . get_class($auth) . "</div>";
        }
    } else {
        echo "<div class='error'>✗ \$auth 对象未定义</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 认证初始化失败: " . $e->getMessage() . "</div>";
}

// 2. 检查数据库连接
echo "<h2>2. 数据库连接检查</h2>";
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    echo "<div class='success'>✓ 数据库连接成功</div>";
    
    // 测试查询
    $result = $db->query("SELECT 1 as test");
    $row = $result->fetch();
    if ($row && $row['test'] == 1) {
        echo "<div class='success'>✓ 数据库查询测试成功</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 数据库连接失败: " . $e->getMessage() . "</div>";
}

// 3. 检查导航栏组件
echo "<h2>3. 导航栏组件检查</h2>";
try {
    require_once 'components/navbar.php';
    echo "<div class='success'>✓ 导航栏组件加载成功</div>";
    
    $navbarData = getNavbarData();
    if (is_array($navbarData) && !empty($navbarData)) {
        echo "<div class='success'>✓ 导航栏数据获取成功，共 " . count($navbarData) . " 个项目</div>";
        
        foreach ($navbarData as $index => $item) {
            if (isset($item['name']) && isset($item['url'])) {
                echo "<div class='info'>  - " . $item['name'] . " (" . $item['url'] . ")</div>";
            } else {
                echo "<div class='warning'>  - 项目 {$index} 缺少必要字段</div>";
            }
        }
    } else {
        echo "<div class='warning'>⚠ 导航栏数据为空或格式错误</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 导航栏组件加载失败: " . $e->getMessage() . "</div>";
}

// 4. 检查关键文件
echo "<h2>4. 关键文件检查</h2>";
$files = [
    'index.php' => '首页文件',
    'login.php' => '登录页面',
    'register.php' => '注册页面',
    'admin-dashboard.php' => '管理后台',
    'classes/Auth.php' => '认证类',
    'config/database.php' => '数据库配置',
    'includes/auth-init.php' => '认证初始化',
    'components/navbar.php' => '导航栏组件',
    'api/login.php' => '登录API',
    'api/register.php' => '注册API'
];

foreach ($files as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✓ {$description} ({$file})</div>";
    } else {
        echo "<div class='error'>✗ {$description} ({$file}) 不存在</div>";
    }
}

// 5. 检查PHP错误日志
echo "<h2>5. PHP错误日志检查</h2>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    echo "<div class='info'>错误日志文件: {$errorLog}</div>";
    
    // 读取最后几行错误日志
    $lines = file($errorLog);
    if ($lines) {
        $recentLines = array_slice($lines, -10); // 最后10行
        echo "<div class='info'>最近的错误日志:</div>";
        echo "<pre>" . htmlspecialchars(implode('', $recentLines)) . "</pre>";
    }
} else {
    echo "<div class='info'>未找到PHP错误日志文件</div>";
}

// 6. JavaScript错误检查脚本
echo "<h2>6. JavaScript错误监控</h2>";
echo "<div class='info'>在浏览器控制台中检查JavaScript错误</div>";
echo "<script>
    window.addEventListener('error', function(e) {
        console.error('JavaScript错误:', e.error);
        document.body.innerHTML += '<div class=\"error\">JavaScript错误: ' + e.message + ' (文件: ' + e.filename + ', 行: ' + e.lineno + ')</div>';
    });
    
    // 检查关键变量
    if (typeof currentUser !== 'undefined') {
        console.log('✓ currentUser 变量已定义');
    } else {
        console.warn('⚠ currentUser 变量未定义');
    }
</script>";

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='test-db-connection.php'>数据库测试</a> | <a href='admin-dashboard.php'>管理后台</a></p>";
echo "</body></html>";
?>
