# 🚀 比特熊项目 - PHP服务器启动指南

## 📋 概述

本项目包含动态专家展示功能，需要PHP服务器来运行完整功能。以下是多种启动方法：

## 🎯 快速启动（推荐方法）

### 方法1：便携版PHP（最简单）

1. **下载PHP**
   ```
   访问：https://windows.php.net/download/
   选择：PHP 8.2 或 8.3
   版本：Non Thread Safe (x64)
   ```

2. **安装步骤**
   - 在项目目录创建 `php` 文件夹
   - 解压下载的zip到 `php` 文件夹
   - 确保路径：`项目目录\php\php.exe`

3. **启动服务器**
   ```bash
   # 方法A：双击运行
   start-server.bat
   
   # 方法B：命令行
   cd C:\Users\<USER>\Desktop\bitbear
   php\php.exe -S localhost:8000
   ```

### 方法2：XAMPP（功能最全）

1. **下载安装**
   ```
   访问：https://www.apachefriends.org/download.html
   下载：XAMPP for Windows
   安装到：C:\xampp（默认）
   ```

2. **启动服务器**
   ```bash
   cd C:\Users\<USER>\Desktop\bitbear
   C:\xampp\php\php.exe -S localhost:8000
   ```

### 方法3：系统PHP

如果已安装PHP到系统PATH：
```bash
cd C:\Users\<USER>\Desktop\bitbear
php -S localhost:8000
```

## 🌐 访问地址

启动成功后，访问以下地址：

- **主页**：http://localhost:8000/index.php
- **管理后台**：http://localhost:8000/admin.php
- **静态版本**：http://localhost:8000/index-standalone.html

## 📁 项目文件说明

### 核心文件
- `index.php` - 主页面（PHP版本，支持动态专家数据）
- `admin.php` - 管理后台（添加/删除专家）
- `experts-data.php` - 专家数据存储
- `index-style.css` - 样式文件

### 静态版本
- `index.html` - 静态主页面
- `index-standalone.html` - 完全独立版本

### 启动脚本
- `start-server.bat` - 自动检测并启动PHP服务器
- `quick-php-setup.bat` - 自动下载安装PHP
- `setup-php.ps1` - PowerShell版本安装脚本

## 🔧 功能特性

### 动态专家展示区域
- ✨ O'Reilly风格设计
- 🎭 滚动动画效果
- 📱 响应式布局
- 🎨 悬停3D效果

### 后台管理功能
- ➕ 添加新专家
- 🗑️ 删除专家
- 👁️ 查看专家列表
- 🖼️ 自定义头像

## 🚨 故障排除

### 问题1：PHP未找到
**解决方案**：
1. 确认PHP已正确安装
2. 检查路径是否正确
3. 尝试重新下载PHP

### 问题2：端口8000被占用
**解决方案**：
```bash
# 使用其他端口
php -S localhost:8080
php -S localhost:3000
```

### 问题3：权限问题
**解决方案**：
1. 以管理员身份运行命令提示符
2. 检查文件夹权限

### 问题4：无法访问网页
**解决方案**：
1. 确认服务器正在运行
2. 检查防火墙设置
3. 尝试 http://127.0.0.1:8000

## 🎨 自定义专家数据

### 通过管理后台
1. 访问 http://localhost:8000/admin.php
2. 填写专家信息
3. 点击"添加专家"

### 直接编辑文件
编辑 `experts-data.php`：
```php
$experts = [
    [
        'id' => 7,
        'name' => '新专家姓名',
        'title' => '专家职位',
        'image' => 'image/expert-7.jpg',
        'alt' => '新专家姓名 - 专家职位'
    ]
];
```

## 📞 技术支持

如果遇到问题：
1. 检查PHP版本（推荐8.2+）
2. 确认所有文件完整
3. 查看浏览器控制台错误信息

## 🎉 享受使用！

现在您可以体验完整的动态专家展示功能了！
