@echo off
chcp 65001 >nul
title 比特熊智慧系统 - 后台服务器
color 0A

echo.
echo     ╔══════════════════════════════════════╗
echo     ║        比特熊智慧系统后台服务器        ║
echo     ╚══════════════════════════════════════╝
echo.

cd /d "%~dp0"

:: 检查PHP环境
echo [1/4] 检查PHP环境...

:: 尝试多个可能的PHP路径
set "php_exe="
set "found=0"

:: 检查XAMPP
if exist "C:\xampp\php\php.exe" (
    set "php_exe=C:\xampp\php\php.exe"
    set "found=1"
    echo     ✓ 发现XAMPP PHP
)

:: 检查WAMP
if %found%==0 if exist "C:\wamp64\bin\php\php8.2.0\php.exe" (
    set "php_exe=C:\wamp64\bin\php\php8.2.0\php.exe"
    set "found=1"
    echo     ✓ 发现WAMP PHP
)

:: 检查系统PATH
if %found%==0 (
    php --version >nul 2>&1
    if !errorlevel! equ 0 (
        set "php_exe=php"
        set "found=1"
        echo     ✓ 发现系统PHP
    )
)

if %found%==0 (
    echo     ✗ 未找到PHP环境
    echo.
    echo [解决方案]
    echo 1. 安装XAMPP: https://www.apachefriends.org/download.html
    echo 2. 或者下载便携版PHP并解压到项目目录
    echo.
    echo 正在为您打开XAMPP下载页面...
    start "" "https://www.apachefriends.org/zh_cn/download.html"
    echo.
    echo 安装完成后请重新运行此脚本
    pause
    exit /b 1
)

echo [2/4] 检查MySQL连接...
echo     ℹ 如果遇到数据库连接问题，请确保MySQL服务已启动

echo [3/4] 准备启动服务器...
set "port=8000"
set "host=localhost"

echo     服务器地址: http://%host%:%port%
echo     后台地址: http://%host%:%port%/admin/
echo.

echo [4/4] 启动服务器...
echo     按 Ctrl+C 停止服务器
echo     ════════════════════════════════════════
echo.

:: 延迟3秒后自动打开浏览器
start /b timeout /t 3 /nobreak >nul && start "" "http://%host%:%port%/admin/init-database.php"

:: 启动PHP内置服务器
"%php_exe%" -S %host%:%port%

echo.
echo 服务器已停止
pause
