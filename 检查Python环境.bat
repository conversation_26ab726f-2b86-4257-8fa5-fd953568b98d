@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🔍 Python 环境诊断
echo ========================================
echo.

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查 Python 命令...
echo.

:: 检查 python 命令
echo [1] 检查 python 命令:
python --version 2>nul
if %errorlevel% == 0 (
    echo ✅ python 命令可用
    python --version
) else (
    echo ❌ python 命令不可用
)
echo.

:: 检查 python3 命令
echo [2] 检查 python3 命令:
python3 --version 2>nul
if %errorlevel% == 0 (
    echo ✅ python3 命令可用
    python3 --version
) else (
    echo ❌ python3 命令不可用
)
echo.

:: 检查 py 命令
echo [3] 检查 py 命令:
py --version 2>nul
if %errorlevel% == 0 (
    echo ✅ py 命令可用
    py --version
) else (
    echo ❌ py 命令不可用
)
echo.

:: 检查 PATH 环境变量
echo [4] 检查 PATH 中的 Python:
echo %PATH% | findstr /i python >nul
if %errorlevel% == 0 (
    echo ✅ PATH 中包含 Python 路径
    echo %PATH% | findstr /i python
) else (
    echo ❌ PATH 中未找到 Python 路径
)
echo.

:: 检查常见 Python 安装位置
echo [5] 检查常见安装位置:
if exist "C:\Python*" (
    echo ✅ 找到 C:\Python* 目录
    dir C:\Python* /b
) else (
    echo ❌ 未找到 C:\Python* 目录
)

if exist "%LOCALAPPDATA%\Programs\Python" (
    echo ✅ 找到用户安装的 Python
    dir "%LOCALAPPDATA%\Programs\Python" /b
) else (
    echo ❌ 未找到用户安装的 Python
)

if exist "%PROGRAMFILES%\Python*" (
    echo ✅ 找到系统安装的 Python
    dir "%PROGRAMFILES%\Python*" /b
) else (
    echo ❌ 未找到系统安装的 Python
)
echo.

:: 检查项目文件
echo [6] 检查项目文件:
if exist "index.html" (
    echo ✅ index.html 存在
) else (
    echo ❌ index.html 不存在
)

if exist "index-style.css" (
    echo ✅ index-style.css 存在
) else (
    echo ❌ index-style.css 不存在
)

if exist "启动页面.html" (
    echo ✅ 启动页面.html 存在
) else (
    echo ❌ 启动页面.html 不存在
)
echo.

:: 尝试启动简单的HTTP服务器
echo [7] 尝试启动服务器测试:
echo.

:: 测试 python 命令
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 🔄 尝试使用 python 命令启动服务器...
    echo 如果成功，服务器将在 http://localhost:8001 运行
    echo 按 Ctrl+C 停止测试服务器
    echo.
    timeout /t 3 >nul
    python -m http.server 8001
    goto :end
)

:: 测试 py 命令
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo 🔄 尝试使用 py 命令启动服务器...
    echo 如果成功，服务器将在 http://localhost:8001 运行
    echo 按 Ctrl+C 停止测试服务器
    echo.
    timeout /t 3 >nul
    py -m http.server 8001
    goto :end
)

echo ❌ 无法启动 Python HTTP 服务器
echo.
echo 💡 建议解决方案:
echo.
echo 1. 安装 Python:
echo    • 访问 https://www.python.org/downloads/
echo    • 下载最新版本
echo    • 安装时勾选 "Add Python to PATH"
echo.
echo 2. 重新启动命令提示符
echo.
echo 3. 使用替代方案:
echo    • 直接打开 index-standalone.html
echo    • 使用其他 HTTP 服务器
echo.

:end
echo.
echo ========================================
echo 🎯 诊断完成
echo ========================================
pause
