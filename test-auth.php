<?php
session_start();

echo "<h2>认证状态测试</h2>";

echo "<h3>Session 信息:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>认证方式测试:</h3>";

// 测试方式1: Auth类
echo "<h4>方式1: Auth类认证</h4>";
if (file_exists('classes/Auth.php')) {
    require_once 'classes/Auth.php';
    try {
        $auth = new Auth();
        echo "Auth类加载成功<br>";
        
        if ($auth->isLoggedIn()) {
            echo "✓ 用户已登录<br>";
            
            if (method_exists($auth, 'isAdmin') && $auth->isAdmin()) {
                echo "✓ 用户是管理员<br>";
            } else {
                echo "✗ 用户不是管理员<br>";
            }
            
            $currentUser = $auth->getCurrentUser();
            echo "当前用户信息:<br>";
            echo "<pre>";
            print_r($currentUser);
            echo "</pre>";
        } else {
            echo "✗ 用户未登录<br>";
        }
    } catch (Exception $e) {
        echo "Auth类错误: " . $e->getMessage() . "<br>";
    }
} else {
    echo "Auth类文件不存在<br>";
}

// 测试方式2: 管理后台认证
echo "<h4>方式2: 管理后台认证</h4>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    echo "✓ 管理后台已登录<br>";
    echo "用户: " . ($_SESSION['admin_user'] ?? 'unknown') . "<br>";
    echo "类型: " . ($_SESSION['user_type'] ?? 'unknown') . "<br>";
} else {
    echo "✗ 管理后台未登录<br>";
}

echo "<h3>数据库连接测试:</h3>";
try {
    require_once 'config/database.php';
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    echo "✓ 数据库连接成功<br>";
    
    // 测试健康表是否存在
    $tables = ['health_fields', 'health_logs'];
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "✓ 表 $table 存在，记录数: $count<br>";
        } catch (Exception $e) {
            echo "✗ 表 $table 不存在或查询失败: " . $e->getMessage() . "<br>";
        }
    }
} catch (Exception $e) {
    echo "✗ 数据库连接失败: " . $e->getMessage() . "<br>";
}

echo "<h3>建议操作:</h3>";
echo "<ul>";
echo "<li><a href='setup-health-tables.php' target='_blank'>创建健康管理数据表</a></li>";
echo "<li><a href='admin-dashboard.php'>返回管理后台</a></li>";
echo "<li><a href='health-management.php'>测试健康管理页面</a></li>";
echo "</ul>";
?>
