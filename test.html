<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 { color: #fff; margin-bottom: 2rem; }
        .status { font-size: 1.2rem; margin: 1rem 0; }
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .links { margin-top: 2rem; }
        .links a {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 服务器连接测试</h1>
        
        <div class="status success">
            ✅ HTML页面加载成功
        </div>
        
        <div class="status" id="phpStatus">
            🔄 正在测试PHP服务器...
        </div>
        
        <div class="status" id="timeStatus">
            ⏰ 当前时间: <span id="currentTime"></span>
        </div>
        
        <div class="links">
            <a href="test.php">测试PHP</a>
            <a href="quick_start.php">快速启动</a>
            <a href="index_lite.php">轻量版首页</a>
            <a href="diagnostic.php">系统诊断</a>
            <a href="admin-dashboard.php">管理后台</a>
        </div>
        
        <div style="margin-top: 2rem; font-size: 0.9rem; opacity: 0.8;">
            <p>如果您能看到这个页面，说明Web服务器正在正常运行。</p>
            <p>如果PHP链接无法访问，请检查PHP服务器是否启动。</p>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }
        
        // 测试PHP服务器
        async function testPHP() {
            try {
                const response = await fetch('test.php');
                const text = await response.text();
                
                if (response.ok && text.includes('PHP服务器正常运行')) {
                    document.getElementById('phpStatus').innerHTML = 
                        '<span class="success">✅ PHP服务器运行正常</span>';
                } else {
                    document.getElementById('phpStatus').innerHTML = 
                        '<span class="error">❌ PHP服务器响应异常</span>';
                }
            } catch (error) {
                document.getElementById('phpStatus').innerHTML = 
                    '<span class="error">❌ 无法连接到PHP服务器</span>';
                console.error('PHP测试失败:', error);
            }
        }
        
        // 页面加载时执行
        updateTime();
        setInterval(updateTime, 1000);
        
        // 延迟测试PHP，给服务器启动时间
        setTimeout(testPHP, 1000);
        
        console.log('测试页面加载完成');
    </script>
</body>
</html>
