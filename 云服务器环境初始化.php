<?php
/**
 * 云服务器环境初始化脚本
 * 专门用于初始化云服务器环境下的数据库和系统配置
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器环境初始化 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .step.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .step.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .step.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #11998e;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background: #0f7c75;
        }
        .env-info {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 云服务器环境初始化</h1>
            <p>检测并初始化云服务器环境配置</p>
        </div>
        
        <div class="content">
            <?php
            
            // 1. 环境检测
            echo "<div class='step info'>";
            echo "<h3>🔍 环境检测</h3>";
            
            $serverInfo = [
                'SERVER_NAME' => $_SERVER['SERVER_NAME'] ?? '未知',
                'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? '未知',
                'SERVER_ADDR' => $_SERVER['SERVER_ADDR'] ?? '未知',
                'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'] ?? '未知',
                'PHP_VERSION' => PHP_VERSION,
                'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'] ?? '未知'
            ];
            
            echo "<div class='env-info'>";
            echo "<h4>服务器信息：</h4>";
            foreach ($serverInfo as $key => $value) {
                echo "<p><strong>{$key}:</strong> {$value}</p>";
            }
            echo "</div>";
            
            // 判断是否为云服务器环境
            $isCloudServer = (strpos($_SERVER['SERVER_NAME'] ?? '', 'bitbear.top') !== false ||
                             strpos($_SERVER['HTTP_HOST'] ?? '', 'bitbear.top') !== false ||
                             ($_SERVER['SERVER_ADDR'] ?? '') === '*************');
            
            if ($isCloudServer) {
                echo "<p>✅ 检测到云服务器环境</p>";
            } else {
                echo "<p>⚠️ 检测到本地开发环境</p>";
            }
            echo "</div>";
            
            // 2. 数据库连接测试
            echo "<div class='step info'>";
            echo "<h3>🔗 数据库连接测试</h3>";
            
            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<p>✅ 数据库连接成功</p>";
                
                // 显示数据库信息
                $dbInfo = $db->query("SELECT DATABASE() as db_name, VERSION() as version")->fetch();
                echo "<div class='code-block'>";
                echo "数据库名称: " . ($dbInfo['db_name'] ?? '未知') . "\n";
                echo "MySQL版本: " . ($dbInfo['version'] ?? '未知') . "\n";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<p>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
            }
            echo "</div>";
            
            // 3. 数据库表初始化
            if (isset($db)) {
                echo "<div class='step info'>";
                echo "<h3>🗄️ 数据库表初始化</h3>";
                
                try {
                    // 获取现有表列表
                    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                    echo "<p>现有表数量: " . count($tables) . "</p>";
                    
                    if (!empty($tables)) {
                        echo "<div class='code-block'>";
                        echo "现有表:\n" . implode("\n", $tables);
                        echo "</div>";
                    }
                    
                    // 检查必要的表
                    $requiredTables = [
                        'user_roles' => "
                            CREATE TABLE user_roles (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                role_code VARCHAR(50) NOT NULL UNIQUE,
                                role_name VARCHAR(100) NOT NULL,
                                description TEXT,
                                permissions TEXT,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            )",
                        'users' => "
                            CREATE TABLE users (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                username VARCHAR(50) NOT NULL UNIQUE,
                                email VARCHAR(100) NOT NULL UNIQUE,
                                password_hash VARCHAR(255) NOT NULL,
                                full_name VARCHAR(100),
                                avatar VARCHAR(255),
                                role_id INT DEFAULT 3,
                                status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                                last_login TIMESTAMP NULL,
                                login_count INT DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
                            )",
                        'user_profiles' => "
                            CREATE TABLE user_profiles (
                                id INT PRIMARY KEY AUTO_INCREMENT,
                                user_id INT NOT NULL,
                                nickname VARCHAR(100),
                                bio TEXT,
                                signature VARCHAR(500),
                                avatar_url VARCHAR(255),
                                location VARCHAR(100),
                                website VARCHAR(255),
                                social_links JSON,
                                post_count INT DEFAULT 0,
                                comment_count INT DEFAULT 0,
                                like_received_count INT DEFAULT 0,
                                reputation_score INT DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                                INDEX idx_user_id (user_id)
                            )"
                    ];
                    
                    $createdTables = [];
                    foreach ($requiredTables as $tableName => $createSql) {
                        if (!in_array($tableName, $tables)) {
                            try {
                                $db->exec($createSql);
                                echo "<p>✅ 创建表: {$tableName}</p>";
                                $createdTables[] = $tableName;
                            } catch (Exception $e) {
                                echo "<p>❌ 创建表 {$tableName} 失败: " . $e->getMessage() . "</p>";
                            }
                        } else {
                            echo "<p>✅ 表已存在: {$tableName}</p>";
                        }
                    }
                    
                    // 初始化用户角色数据
                    if (in_array('user_roles', $createdTables) || in_array('user_roles', $tables)) {
                        $roleCount = $db->query("SELECT COUNT(*) FROM user_roles")->fetchColumn();
                        if ($roleCount == 0) {
                            try {
                                $db->exec("
                                    INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                                    ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                                    ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                                    ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                                ");
                                echo "<p>✅ 初始化用户角色数据</p>";
                            } catch (Exception $e) {
                                echo "<p>❌ 初始化用户角色数据失败: " . $e->getMessage() . "</p>";
                            }
                        } else {
                            echo "<p>✅ 用户角色数据已存在 ({$roleCount} 个角色)</p>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<p>❌ 数据库表初始化失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                // 4. 创建管理员账户
                echo "<div class='step info'>";
                echo "<h3>👤 管理员账户检查</h3>";
                
                try {
                    $adminCount = $db->query("
                        SELECT COUNT(*) FROM users u 
                        JOIN user_roles r ON u.role_id = r.id 
                        WHERE r.role_code IN ('admin', 'super_admin')
                    ")->fetchColumn();
                    
                    if ($adminCount == 0) {
                        echo "<p>⚠️ 未找到管理员账户，建议创建管理员账户</p>";
                        echo "<p><a href='创建管理员账户.php' class='btn'>创建管理员账户</a></p>";
                    } else {
                        echo "<p>✅ 已存在 {$adminCount} 个管理员账户</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p>❌ 检查管理员账户失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
            }
            
            // 5. 目录权限检查
            echo "<div class='step info'>";
            echo "<h3>📁 目录权限检查</h3>";
            
            $directories = [
                'uploads/' => '上传目录',
                'uploads/avatars/' => '头像上传目录',
                'uploads/temp/' => '临时文件目录',
                'logs/' => '日志目录'
            ];
            
            foreach ($directories as $dir => $desc) {
                if (!is_dir($dir)) {
                    if (mkdir($dir, 0755, true)) {
                        echo "<p>✅ 创建{$desc}: {$dir}</p>";
                    } else {
                        echo "<p>❌ 创建{$desc}失败: {$dir}</p>";
                    }
                } else {
                    echo "<p>✅ {$desc}已存在: {$dir}</p>";
                }
                
                if (is_dir($dir)) {
                    if (is_writable($dir)) {
                        echo "<p>✅ {$desc}可写</p>";
                    } else {
                        echo "<p>⚠️ {$desc}不可写，尝试修改权限...</p>";
                        if (chmod($dir, 0755)) {
                            echo "<p>✅ 权限修改成功</p>";
                        } else {
                            echo "<p>❌ 权限修改失败</p>";
                        }
                    }
                }
            }
            echo "</div>";
            
            // 6. 初始化完成
            echo "<div class='step success'>";
            echo "<h3>🎉 初始化完成</h3>";
            echo "<p>云服务器环境初始化已完成！现在可以正常使用系统功能了。</p>";
            echo "</div>";
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="注册测试.php" class="btn">测试注册功能</a>
                <a href="云服务器注册修复.php" class="btn">注册修复工具</a>
                <a href="register.php" class="btn">用户注册</a>
                <a href="admin-dashboard.php" class="btn">管理后台</a>
            </div>
        </div>
    </div>
</body>
</html>
