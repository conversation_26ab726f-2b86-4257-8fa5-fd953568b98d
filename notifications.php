<?php
// 重定向到新的通知中心布局
header('Location: notifications_new.php' . (isset($_GET['type']) ? '?type=' . $_GET['type'] : ''));
exit;
?>
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .notifications-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .notifications-header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .notifications-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .notifications-subtitle {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .notifications-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .notification-item {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-item:hover {
            background-color: #f9fafb;
        }
        
        .notification-item.unread {
            background-color: #eff6ff;
            border-left: 4px solid #3b82f6;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .notification-icon.info {
            background-color: #dbeafe;
            color: #3b82f6;
        }
        
        .notification-icon.success {
            background-color: #dcfce7;
            color: #16a34a;
        }
        
        .notification-icon.warning {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .notification-message {
            color: #6b7280;
            line-height: 1.5;
            margin-bottom: 0.5rem;
        }
        
        .notification-time {
            font-size: 0.875rem;
            color: #9ca3af;
        }
        
        .notification-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .btn-mark-read {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn-mark-read:hover {
            background: #2563eb;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #d1d5db;
        }
        
        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #3b82f6;
            text-decoration: none;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .back-link:hover {
            color: #2563eb;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="notifications-container">
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i>
            返回首页
        </a>
        
        <div class="notifications-header">
            <h1 class="notifications-title">通知中心</h1>
            <p class="notifications-subtitle">查看您的最新通知和消息</p>
        </div>
        
        <div class="notifications-list" id="notificationsList">
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                正在加载通知...
            </div>
        </div>
    </div>

    <script>
        // 加载通知列表
        function loadNotifications() {
            fetch('api/notifications.php?action=list')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('notificationsList');
                    
                    if (data.success && data.notifications.length > 0) {
                        container.innerHTML = data.notifications.map(notification => `
                            <div class="notification-item ${notification.is_read ? '' : 'unread'}" data-id="${notification.id}">
                                <div class="notification-icon ${getNotificationIconType(notification.type)}">
                                    <i class="fas ${getNotificationIcon(notification.type)}"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">${escapeHtml(notification.title)}</div>
                                    <div class="notification-message">${escapeHtml(notification.content)}</div>
                                    <div class="notification-time">${formatTime(notification.created_at)}</div>
                                    ${!notification.is_read ? `
                                        <div class="notification-actions">
                                            <button class="btn-mark-read" onclick="markAsRead(${notification.id})">
                                                标记为已读
                                            </button>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="fas fa-bell-slash"></i>
                                <h3>暂无通知</h3>
                                <p>您目前没有任何通知消息</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载通知失败:', error);
                    document.getElementById('notificationsList').innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>加载失败</h3>
                            <p>无法加载通知列表，请稍后重试</p>
                        </div>
                    `;
                });
        }

        // 标记通知为已读
        function markAsRead(notificationId) {
            fetch('api/notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'mark_read',
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 重新加载通知列表
                    loadNotifications();
                }
            })
            .catch(error => {
                console.error('标记已读失败:', error);
            });
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            switch (type) {
                case 'system': return 'fa-cog';
                case 'post': return 'fa-file-alt';
                case 'comment': return 'fa-comment';
                case 'like': return 'fa-heart';
                case 'follow': return 'fa-user-plus';
                default: return 'fa-bell';
            }
        }

        // 获取通知图标类型
        function getNotificationIconType(type) {
            switch (type) {
                case 'system': return 'info';
                case 'post': return 'success';
                case 'comment': return 'info';
                case 'like': return 'success';
                case 'follow': return 'success';
                default: return 'info';
            }
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString('zh-CN');
            }
        }

        // 页面加载完成后加载通知
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();
        });
    </script>
</body>
</html>
