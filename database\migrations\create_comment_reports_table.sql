-- 创建评论举报表
CREATE TABLE IF NOT EXISTS comment_reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL,
    reporter_user_id INT NOT NULL,
    report_type VARCHAR(50) NOT NULL,
    report_reason TEXT,
    status ENUM('pending', 'reviewed', 'dismissed') DEFAULT 'pending',
    admin_user_id INT NULL,
    admin_action VARCHAR(50) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (comment_id) REFERENCES post_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (reporter_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_comment_id (comment_id),
    INDEX idx_reporter_user_id (reporter_user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 创建评论点赞/踩表（如果不存在）
CREATE TABLE IF NOT EXISTS comment_likes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    comment_id INT NOT NULL,
    user_id INT NOT NULL,
    type ENUM('like', 'dislike') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (comment_id) REFERENCES post_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_comment_user_like (comment_id, user_id),
    INDEX idx_comment_id (comment_id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type)
);

-- 插入举报类型的预设数据
INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('comment_report_types', '["违法违规", "色情", "低俗", "赌博诈骗", "违法信息外链", "涉政谣言", "虚假不实信息", "涉社会事件谣言", "人身攻击", "侵犯隐私", "垃圾广告", "引战", "刷屏", "剧透", "视频不相关", "违规抽奖", "青少年不良信息", "其他"]', '评论举报类型列表');
