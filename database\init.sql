-- 比特熊智慧系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS bitbear_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bitbear_system;

-- 用户角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100),
    avatar VARCHAR(255),
    role_id INT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 日程表
CREATE TABLE IF NOT EXISTS events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 通知表
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL, -- NULL表示系统通知，发给所有用户
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    type ENUM('user_register', 'admin_login', 'system_update', 'server_warning', 'application_process') DEFAULT 'system_update',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户活动表（用于仪表盘显示）
CREATE TABLE IF NOT EXISTS user_activities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    activity_type ENUM('user_register', 'admin_login', 'user_login', 'application_submit', 'application_process', 'system_update') NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入默认角色
INSERT INTO user_roles (role_name, role_code, description, permissions) VALUES
('超级管理员', 'super_admin', '拥有系统所有权限的超级管理员', '["*"]'),
('普通管理员', 'admin', '拥有部分管理权限的管理员', '["user.view", "user.create", "user.edit", "content.manage"]'),
('普通用户', 'user', '系统普通用户', '["profile.view", "profile.edit"]');

-- 插入默认超级管理员用户 (密码: admin123)
INSERT INTO users (username, email, password_hash, full_name, role_id, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 1, 'active');

-- 插入更多测试用户
INSERT INTO users (username, email, password_hash, full_name, role_id, status) VALUES
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '普通管理员', 2, 'active'),
('user1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', 3, 'active'),
('user2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', 3, 'active'),
('user3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', 3, 'active');

-- 插入默认系统设置
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', '比特熊智慧系统', 'string', '网站名称', TRUE),
('site_description', '专业的在线学习平台', 'string', '网站描述', TRUE),
('admin_email', '<EMAIL>', 'string', '管理员邮箱', FALSE),
('maintenance_mode', 'false', 'boolean', '维护模式', FALSE),
('user_registration', 'true', 'boolean', '允许用户注册', TRUE),
('max_login_attempts', '5', 'number', '最大登录尝试次数', FALSE),
('session_timeout', '3600', 'number', '会话超时时间(秒)', FALSE);

-- 插入默认通知数据
INSERT INTO notifications (title, content, type, created_at) VALUES
('新用户注册', '用户 "张三" 刚刚注册了账号', 'user_register', DATE_SUB(NOW(), INTERVAL 2 MINUTE)),
('系统更新', '系统已成功更新到版本 v1.2.0', 'system_update', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
('服务器警告', '服务器CPU使用率达到85%', 'server_warning', DATE_SUB(NOW(), INTERVAL 3 HOUR));

-- 插入默认活动日志
INSERT INTO user_activities (user_id, activity_type, title, description, created_at) VALUES
(3, 'user_register', '新用户注册', '用户 "李小明" 完成注册', DATE_SUB(NOW(), INTERVAL 5 MINUTE)),
(1, 'admin_login', '管理员登录', '管理员 "admin" 登录系统', DATE_SUB(NOW(), INTERVAL 15 MINUTE)),
(NULL, 'application_process', '申请处理', '处理了用户升级申请', DATE_SUB(NOW(), INTERVAL 1 HOUR)),
(NULL, 'system_update', '系统更新', '系统已更新到最新版本', DATE_SUB(NOW(), INTERVAL 2 HOUR)),
(4, 'user_register', '用户注册', '用户 "王小红" 完成注册', DATE_SUB(NOW(), INTERVAL 3 HOUR));

-- 插入默认日程数据
INSERT INTO events (user_id, title, description, event_date, start_time, end_time) VALUES
(1, '团队会议', '讨论项目进度和下一步计划', '2025-01-15', '09:00:00', '10:30:00'),
(1, '项目评审', '对当前项目进行全面评审', '2025-01-20', '14:00:00', '16:00:00'),
(1, '系统维护', '定期系统维护和更新', '2025-01-25', '02:00:00', '04:00:00');

-- 导航栏菜单表
CREATE TABLE IF NOT EXISTS navbar_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    url VARCHAR(255) NOT NULL,
    type ENUM('link', 'dropdown', 'submenu') DEFAULT 'link',
    parent_id INT NULL,
    icon VARCHAR(255) NULL,
    sort_order INT DEFAULT 0,
    visible BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES navbar_items(id) ON DELETE CASCADE,
    INDEX idx_parent (parent_id),
    INDEX idx_order (sort_order)
);

-- 插入默认导航栏数据
INSERT INTO navbar_items (name, url, type, parent_id, sort_order, visible) VALUES
('首页', '/index.php', 'link', NULL, 1, TRUE),
('组织', '#', 'dropdown', NULL, 2, TRUE),
('关于我们', '/about.php', 'submenu', 2, 1, TRUE),
('团队介绍', '/team.php', 'submenu', 2, 2, TRUE),
('联系我们', '/contact.php', 'submenu', 2, 3, TRUE),
('服务', '/services.php', 'link', NULL, 3, TRUE),
('新闻', '/news.php', 'link', NULL, 4, TRUE),
('课程', '#', 'dropdown', NULL, 5, TRUE),
('在线课程', '/courses/online.php', 'submenu', 8, 1, TRUE),
('课程超市', '/courses/market.php', 'submenu', 8, 2, TRUE),
('程序设计', '/courses/programming.php', 'submenu', 8, 3, TRUE),
('游戏', '#', 'dropdown', NULL, 6, TRUE),
('联机游戏', '/games/online.php', 'submenu', 12, 1, TRUE),
('单机游戏', '/games/single.php', 'submenu', 12, 2, TRUE),
('游戏社区', '/games/community.php', 'submenu', 12, 3, TRUE);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role_id);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_logs_user ON activity_logs(user_id);
CREATE INDEX idx_logs_action ON activity_logs(action);
CREATE INDEX idx_settings_key ON system_settings(setting_key);
CREATE INDEX idx_events_user_date ON events(user_id, event_date);
CREATE INDEX idx_events_date ON events(event_date);
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_notifications_created ON notifications(created_at);
CREATE INDEX idx_activities_user ON user_activities(user_id);
CREATE INDEX idx_activities_type ON user_activities(activity_type);
CREATE INDEX idx_activities_created ON user_activities(created_at);
CREATE INDEX idx_navbar_parent ON navbar_items(parent_id);
CREATE INDEX idx_navbar_sort ON navbar_items(sort_order);
CREATE INDEX idx_navbar_visible ON navbar_items(visible);
