<?php
/**
 * 检测JavaScript错误和链接问题
 */

require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>检测JavaScript错误</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #764ba2; }
    .test-link { display: block; padding: 15px; margin: 10px 0; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; text-decoration: none; color: #333; transition: all 0.3s; }
    .test-link:hover { background: #e9ecef; transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
    .log-area { background: #000; color: #0f0; padding: 15px; border-radius: 8px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; margin: 10px 0; }
    .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🐛 检测JavaScript错误和链接问题</h1>";

// 1. 获取测试帖子
echo "<div class='info'>";
echo "<h3>📋 获取测试帖子</h3>";

try {
    $db = db();
    $posts = $db->fetchAll("SELECT id, title, status FROM posts WHERE status = 'published' ORDER BY id DESC LIMIT 3");
    
    if ($posts) {
        echo "<div class='success'>✅ 找到 " . count($posts) . " 个可测试的帖子</div>";
        
        echo "<h4>🔗 测试链接（请在开发者工具中观察）</h4>";
        foreach ($posts as $post) {
            echo "<a href='community-post-detail.php?id={$post['id']}' class='test-link' data-post-id='{$post['id']}'>";
            echo "📄 帖子 {$post['id']}: " . htmlspecialchars(mb_substr($post['title'], 0, 50));
            echo "</a>";
        }
        
        echo "<h4>🆕 新窗口测试链接</h4>";
        foreach ($posts as $post) {
            echo "<a href='community-post-detail.php?id={$post['id']}' class='test-link' target='_blank' data-post-id='{$post['id']}'>";
            echo "🔗 新窗口 - 帖子 {$post['id']}: " . htmlspecialchars(mb_substr($post['title'], 0, 50));
            echo "</a>";
        }
        
    } else {
        echo "<div class='warning'>⚠️ 没有找到已发布的帖子</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 获取帖子失败: " . $e->getMessage() . "</div>";
}

echo "</div>";

// 2. JavaScript错误日志区域
echo "<div class='info'>";
echo "<h3>📊 JavaScript错误日志</h3>";
echo "<div id='errorLog' class='log-area'>等待JavaScript错误信息...</div>";
echo "<button onclick='clearLog()' class='btn'>清空日志</button>";
echo "</div>";

// 3. 网络请求监控
echo "<div class='info'>";
echo "<h3>🌐 网络请求监控</h3>";
echo "<div id='networkLog' class='log-area'>等待网络请求信息...</div>";
echo "<button onclick='clearNetworkLog()' class='btn'>清空网络日志</button>";
echo "</div>";

// 4. 解决方案建议
echo "<div class='warning'>";
echo "<h3>💡 可能的解决方案</h3>";
echo "<ol>";
echo "<li><strong>检查服务器路径</strong>：确认community-post-detail.php文件在服务器上存在且可访问</li>";
echo "<li><strong>检查文件权限</strong>：确保PHP文件有正确的读取权限</li>";
echo "<li><strong>检查URL重写</strong>：.htaccess规则可能影响链接跳转</li>";
echo "<li><strong>检查JavaScript冲突</strong>：全局事件监听器可能阻止默认行为</li>";
echo "<li><strong>检查服务器配置</strong>：Apache/Nginx配置可能影响PHP文件访问</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='community.php' class='btn'>返回社区测试</a>";
echo "<a href='诊断帖子链接问题.php' class='btn'>详细诊断</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";

// JavaScript诊断和监控脚本
echo "<script>
let errorCount = 0;
let networkCount = 0;

function logError(message, type = 'error') {
    errorCount++;
    const timestamp = new Date().toLocaleTimeString();
    const logArea = document.getElementById('errorLog');
    const color = type === 'error' ? '#ff6b6b' : type === 'warning' ? '#ffc107' : '#28a745';
    logArea.innerHTML += `<div style='color: ${color};'>[${timestamp}] ${message}</div>`;
    logArea.scrollTop = logArea.scrollHeight;
}

function logNetwork(message) {
    networkCount++;
    const timestamp = new Date().toLocaleTimeString();
    const logArea = document.getElementById('networkLog');
    logArea.innerHTML += `<div style='color: #17a2b8;'>[${timestamp}] ${message}</div>`;
    logArea.scrollTop = logArea.scrollHeight;
}

function clearLog() {
    document.getElementById('errorLog').innerHTML = '日志已清空...';
    errorCount = 0;
}

function clearNetworkLog() {
    document.getElementById('networkLog').innerHTML = '网络日志已清空...';
    networkCount = 0;
}

// 监听JavaScript错误
window.addEventListener('error', function(e) {
    logError(`❌ JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`);
});

// 监听Promise错误
window.addEventListener('unhandledrejection', function(e) {
    logError(`❌ Promise错误: ${e.reason}`);
});

// 监听资源加载错误
window.addEventListener('error', function(e) {
    if (e.target !== window) {
        logError(`❌ 资源加载失败: ${e.target.src || e.target.href || e.target.tagName}`);
    }
}, true);

// 监听所有链接点击事件
document.addEventListener('click', function(e) {
    const target = e.target.closest('a');
    if (target) {
        logNetwork(`🔗 链接点击: ${target.href}`);
        logNetwork(`   - 目标: ${target.target || '当前窗口'}`);
        logNetwork(`   - 文本: ${target.textContent.trim()}`);
        logNetwork(`   - 默认行为被阻止: ${e.defaultPrevented}`);
        
        // 检查是否有阻止默认行为的情况
        if (e.defaultPrevented) {
            logError(`⚠️ 链接默认行为被阻止: ${target.href}`, 'warning');
        }
    }
}, true);

// 监听页面跳转
let originalLocation = window.location.href;
setInterval(function() {
    if (window.location.href !== originalLocation) {
        logNetwork(`🔄 页面跳转: ${originalLocation} -> ${window.location.href}`);
        originalLocation = window.location.href;
    }
}, 100);

// 监听fetch请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    logNetwork(`📡 Fetch请求: ${args[0]}`);
    return originalFetch.apply(this, args)
        .then(response => {
            logNetwork(`📡 Fetch响应: ${response.status} ${response.statusText}`);
            return response;
        })
        .catch(error => {
            logError(`❌ Fetch错误: ${error.message}`);
            throw error;
        });
};

// 页面加载完成后的检查
document.addEventListener('DOMContentLoaded', function() {
    logNetwork('✅ 页面DOM加载完成');
    
    // 检查是否有全局的点击事件监听器
    const allElements = document.querySelectorAll('*');
    let elementsWithClickHandlers = 0;
    
    allElements.forEach(el => {
        if (el.onclick || el.getAttribute('onclick')) {
            elementsWithClickHandlers++;
        }
    });
    
    logNetwork(`📊 页面统计: 总元素${allElements.length}个，有点击处理器${elementsWithClickHandlers}个`);
});

logNetwork('🚀 JavaScript错误检测工具已启动');
</script>";

echo "</body></html>";
?>
