# 🚀 比特熊智慧系统 - 腾讯云部署检查清单

## 📋 部署前准备 ✅
- [x] 本地项目运行正常 (http://localhost:8000)
- [x] 服务器连接信息确认
- [x] 部署脚本准备完成

## 🔗 第一步：连接服务器
**执行方式**: 双击运行 `connect-to-server.bat` 或手动SSH连接

**服务器信息**:
- IP: *************
- 用户: root
- 密码: ZbDX7%=]?H2(LAUz

**连接后立即执行的检查命令**:
```bash
# 检查项目目录
ls -la /www/wwwroot/www.bitbear.top

# 检查PHP版本
php -v

# 检查服务状态
systemctl status nginx
systemctl status mysql

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 🔍 第二步：环境检查
**需要确认的信息**:
- [ ] PHP版本 (推荐8.1+)
- [ ] MySQL服务运行状态
- [ ] Nginx服务运行状态
- [ ] 项目目录是否存在
- [ ] 磁盘空间是否充足

## 🗄️ 第三步：数据库配置
**执行命令**:
```bash
# 连接MySQL
mysql -u root -p309290133q

# 在MySQL中执行
SHOW DATABASES;
CREATE DATABASE IF NOT EXISTS bitbear_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE bitbear_website;
SHOW TABLES;
EXIT;
```

**检查项**:
- [ ] 数据库连接成功
- [ ] bitbear_website数据库存在
- [ ] 数据库字符集正确

## 📁 第四步：文件上传
**需要上传的关键文件**:
- [ ] 所有PHP文件
- [ ] config/目录
- [ ] database/目录
- [ ] assets/目录
- [ ] uploads/目录
- [ ] .htaccess文件

**上传方式选择**:
1. 使用SCP命令
2. 使用FTP工具 (如FileZilla)
3. 使用宝塔面板文件管理

## ⚙️ 第五步：权限设置
**执行命令**:
```bash
cd /www/wwwroot/www.bitbear.top

# 设置所有者
chown -R www-data:www-data .

# 设置目录权限
find . -type d -exec chmod 755 {} \;

# 设置文件权限
find . -type f -exec chmod 644 {} \;

# 设置uploads目录可写
chmod -R 777 uploads/
```

## 🌐 第六步：Web服务器配置
**Nginx配置文件位置**: `/etc/nginx/sites-available/www.bitbear.top`

**关键配置检查**:
- [ ] 域名配置正确
- [ ] 根目录指向正确
- [ ] PHP处理配置正确
- [ ] 重写规则配置

## 🧪 第七步：测试验证
**测试项目**:
- [ ] 网站首页访问 (http://*************)
- [ ] 管理后台访问
- [ ] 数据库连接测试
- [ ] 文件上传功能
- [ ] 用户注册登录

**测试命令**:
```bash
# 测试数据库连接
cd /www/wwwroot/www.bitbear.top
php test_db_connection.php

# 检查错误日志
tail -f /var/log/nginx/error.log
```

## 🔒 第八步：安全优化
**安全配置**:
- [ ] 配置SSL证书 (HTTPS)
- [ ] 设置防火墙规则
- [ ] 隐藏服务器版本信息
- [ ] 配置访问限制

## 📞 故障排除
**常见问题及解决方案**:

1. **连接被拒绝**
   - 检查服务器IP和端口
   - 确认SSH服务运行状态

2. **权限错误**
   - 重新设置文件权限
   - 检查SELinux状态

3. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证数据库密码

4. **网页无法访问**
   - 检查Nginx配置
   - 查看错误日志

## 🎯 部署完成标志
- [ ] 网站正常访问
- [ ] 所有功能正常工作
- [ ] 性能测试通过
- [ ] 安全检查完成

---
**下一步**: 请先执行第一步连接服务器，然后我们逐步进行部署！
