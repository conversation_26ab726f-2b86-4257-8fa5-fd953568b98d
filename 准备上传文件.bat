@echo off
chcp 65001 >nul
title 准备服务器上传文件
color 0A

echo.
echo     ╔══════════════════════════════════════╗
echo     ║        准备服务器上传文件            ║
echo     ╚══════════════════════════════════════╝
echo.

:: 创建上传文件夹
set "upload_dir=待上传文件"
if not exist "%upload_dir%" mkdir "%upload_dir%"
if not exist "%upload_dir%\api" mkdir "%upload_dir%\api"

echo [1/5] 复制修改过的文件...

:: 复制主要文件
if exist "admin-dashboard.php" (
    copy "admin-dashboard.php" "%upload_dir%\" >nul
    echo     ✓ admin-dashboard.php
) else (
    echo     ✗ admin-dashboard.php 未找到
)

if exist "服务器环境诊断.php" (
    copy "服务器环境诊断.php" "%upload_dir%\" >nul
    echo     ✓ 服务器环境诊断.php
) else (
    echo     ✗ 服务器环境诊断.php 未找到
)

if exist "云服务器兼容性检查.php" (
    copy "云服务器兼容性检查.php" "%upload_dir%\" >nul
    echo     ✓ 云服务器兼容性检查.php
) else (
    echo     ✗ 云服务器兼容性检查.php 未找到
)

:: 复制API文件
if exist "api\stats.php" (
    copy "api\stats.php" "%upload_dir%\api\" >nul
    echo     ✓ api\stats.php
) else (
    echo     ✗ api\stats.php 未找到
)

:: 复制配置文件
if exist "config\database.php" (
    if not exist "%upload_dir%\config" mkdir "%upload_dir%\config"
    copy "config\database.php" "%upload_dir%\config\" >nul
    echo     ✓ config\database.php (已预配置服务器数据库)
) else (
    echo     ✗ config\database.php 未找到
)

echo.
echo [2/5] 创建上传说明文件...
echo 创建上传说明... > "%upload_dir%\上传说明.txt"
echo. >> "%upload_dir%\上传说明.txt"
echo 上传目标服务器: www.bitbear.top >> "%upload_dir%\上传说明.txt"
echo 服务器路径: /www/wwwroot/www.bitbear.top/ >> "%upload_dir%\上传说明.txt"
echo. >> "%upload_dir%\上传说明.txt"
echo 文件列表: >> "%upload_dir%\上传说明.txt"
echo - admin-dashboard.php >> "%upload_dir%\上传说明.txt"
echo - 服务器环境诊断.php >> "%upload_dir%\上传说明.txt"
echo - 云服务器兼容性检查.php >> "%upload_dir%\上传说明.txt"
echo - api/stats.php >> "%upload_dir%\上传说明.txt"
echo - config/database.php >> "%upload_dir%\上传说明.txt"
echo. >> "%upload_dir%\上传说明.txt"
echo 上传后测试链接: >> "%upload_dir%\上传说明.txt"
echo http://www.bitbear.top/云服务器兼容性检查.php >> "%upload_dir%\上传说明.txt"
echo http://www.bitbear.top/admin-dashboard.php >> "%upload_dir%\上传说明.txt"

echo     ✓ 上传说明.txt

echo.
echo [3/5] 检查文件完整性...
set "missing=0"
if not exist "%upload_dir%\admin-dashboard.php" set /a missing+=1
if not exist "%upload_dir%\服务器环境诊断.php" set /a missing+=1
if not exist "%upload_dir%\云服务器兼容性检查.php" set /a missing+=1
if not exist "%upload_dir%\api\stats.php" set /a missing+=1
if not exist "%upload_dir%\config\database.php" set /a missing+=1

if %missing% equ 0 (
    echo     ✓ 所有文件准备完成
) else (
    echo     ⚠ 缺少 %missing% 个文件
)

echo.
echo [4/5] 显示文件信息...
echo     文件夹位置: %cd%\%upload_dir%
echo     文件数量: 
dir "%upload_dir%" /s /-c | find "个文件"

echo.
echo [5/5] 完成准备工作
echo.
echo ════════════════════════════════════════
echo 📁 上传文件已准备完成！
echo.
echo 📂 文件位置: %upload_dir%\
echo.
echo 🚀 下一步操作:
echo    1. 使用FTP工具上传 "%upload_dir%" 文件夹中的所有文件
echo    2. 保持目录结构不变
echo    3. 上传到服务器路径: /www/wwwroot/www.bitbear.top/
echo.
echo 🧪 上传后测试:
echo    http://www.bitbear.top/云服务器兼容性检查.php
echo.
echo ════════════════════════════════════════

echo.
echo 是否打开上传文件夹? (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    explorer "%upload_dir%"
)

echo.
pause
