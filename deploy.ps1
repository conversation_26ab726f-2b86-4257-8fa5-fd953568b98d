# PowerShell部署脚本
param(
    [string]$ServerIP = "*************",
    [string]$Username = "root",
    [string]$Password = "ZbDX7%=]?H2(LAUz",
    [string]$RemotePath = "/www/wwwroot/比特熊组织网站项目(v0.0.1)",
    [string]$LocalPath = "."
)

Write-Host "开始部署比特熊智慧系统到服务器..." -ForegroundColor Green

# 1. 测试SSH连接
Write-Host "测试SSH连接..." -ForegroundColor Yellow
try {
    $securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential ($Username, $securePassword)
    
    # 使用plink测试连接（如果可用）
    $testCommand = "echo 'SSH连接测试成功'"
    $result = & plink -ssh -batch -pw $Password $Username@$ServerIP $testCommand 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SSH连接成功!" -ForegroundColor Green
    } else {
        Write-Host "SSH连接失败，错误: $result" -ForegroundColor Red
        Write-Host "请确保服务器可访问且SSH服务正常运行" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "尝试手动连接..." -ForegroundColor Yellow
}

# 2. 创建远程目录
Write-Host "创建远程目录..." -ForegroundColor Yellow
$createDirCommand = "mkdir -p '$RemotePath'"
& plink -ssh -batch -pw $Password $Username@$ServerIP $createDirCommand

# 3. 上传文件
Write-Host "正在上传文件到服务器..." -ForegroundColor Yellow

# 获取所有需要上传的文件
$filesToUpload = Get-ChildItem -Path $LocalPath -Recurse | Where-Object { 
    $_.Name -notmatch '\.(git|log)$' -and 
    $_.FullName -notmatch '\\\.git\\' -and
    $_.FullName -notmatch '\\node_modules\\' 
}

# 使用pscp上传文件
foreach ($file in $filesToUpload) {
    if ($file.PSIsContainer) {
        # 创建目录
        $relativePath = $file.FullName.Substring($LocalPath.Length + 1).Replace('\', '/')
        $remoteDir = "$RemotePath/$relativePath"
        & plink -ssh -batch -pw $Password $Username@$ServerIP "mkdir -p '$remoteDir'"
    } else {
        # 上传文件
        $relativePath = $file.FullName.Substring($LocalPath.Length + 1).Replace('\', '/')
        $remoteFile = "$RemotePath/$relativePath"
        $remoteDir = Split-Path $remoteFile -Parent
        
        # 确保远程目录存在
        & plink -ssh -batch -pw $Password $Username@$ServerIP "mkdir -p '$remoteDir'"
        
        # 上传文件
        & pscp -pw $Password $file.FullName $Username@$ServerIP`:$remoteFile
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "已上传: $relativePath" -ForegroundColor Gray
        } else {
            Write-Host "上传失败: $relativePath" -ForegroundColor Red
        }
    }
}

# 4. 执行服务器端配置
Write-Host "执行服务器端配置..." -ForegroundColor Yellow

$serverCommands = @"
cd '$RemotePath'

# 设置文件权限
chmod -R 755 .
mkdir -p uploads
chmod -R 777 uploads/

# 检查并启动MySQL
if ! systemctl is-active --quiet mysql && ! systemctl is-active --quiet mysqld; then
    echo '启动MySQL服务...'
    systemctl start mysql || systemctl start mysqld
fi

# 创建数据库
echo '创建数据库...'
mysql -u root -e "CREATE DATABASE IF NOT EXISTS bitbear_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || echo '数据库可能已存在'

# 导入数据库结构
if [ -f 'database/init.sql' ]; then
    echo '导入数据库结构...'
    mysql -u root bitbear_system < database/init.sql
    echo '数据库结构导入完成'
else
    echo '警告: database/init.sql 文件不存在'
fi

# 检查Web服务器
if systemctl is-active --quiet apache2; then
    echo '重启Apache服务...'
    systemctl reload apache2
elif systemctl is-active --quiet nginx; then
    echo '重启Nginx服务...'
    systemctl reload nginx
else
    echo '警告: 未检测到Apache或Nginx服务'
fi

echo '服务器配置完成!'
"@

# 执行服务器命令
& plink -ssh -batch -pw $Password $Username@$ServerIP $serverCommands

if ($LASTEXITCODE -eq 0) {
    Write-Host "部署完成!" -ForegroundColor Green
    Write-Host "项目已部署到: http://$ServerIP" -ForegroundColor Cyan
} else {
    Write-Host "部署过程中出现错误" -ForegroundColor Red
}
