<?php
/**
 * 用户管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取用户列表
 */
function handleGet($db) {
    $search = $_GET['search'] ?? '';
    $role = $_GET['role'] ?? '';
    $status = $_GET['status'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where = ['1=1'];
    $params = [];
    
    if (!empty($search)) {
        $where[] = '(u.username LIKE ? OR u.email LIKE ? OR u.full_name LIKE ?)';
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    if (!empty($role)) {
        $where[] = 'ur.role_code = ?';
        $params[] = $role;
    }
    
    if (!empty($status)) {
        $where[] = 'u.status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $where);
    
    // 获取用户列表
    $sql = "SELECT u.id, u.username, u.email, u.full_name, u.status, u.created_at, u.last_login, u.login_count,
                   ur.role_name, ur.role_code
            FROM users u
            LEFT JOIN user_roles ur ON u.role_id = ur.id
            WHERE {$whereClause}
            ORDER BY u.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $users = $db->fetchAll($sql, $params);
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total
                 FROM users u
                 LEFT JOIN user_roles ur ON u.role_id = ur.id
                 WHERE {$whereClause}";
    
    $countParams = array_slice($params, 0, -2); // 移除limit和offset参数
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];
    
    echo json_encode([
        'success' => true,
        'data' => [
            'users' => $users,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);
}

/**
 * 处理POST请求 - 创建用户
 */
function handlePost($db, $input) {
    if (!$input || !isset($input['username'], $input['email'], $input['role_id'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        return;
    }
    
    // 检查用户名是否已存在
    $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ? OR email = ?", 
                                  [$input['username'], $input['email']]);
    if ($existingUser) {
        http_response_code(400);
        echo json_encode(['error' => '用户名或邮箱已存在']);
        return;
    }
    
    // 生成默认密码
    $defaultPassword = $input['password'] ?? 'password123';
    $passwordHash = password_hash($defaultPassword, PASSWORD_DEFAULT);
    
    $data = [
        'username' => $input['username'],
        'email' => $input['email'],
        'password_hash' => $passwordHash,
        'full_name' => $input['full_name'] ?? '',
        'role_id' => $input['role_id'],
        'status' => $input['status'] ?? 'active'
    ];
    
    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $db->query($sql, [
        $data['username'],
        $data['email'],
        $data['password_hash'],
        $data['full_name'],
        $data['role_id'],
        $data['status']
    ]);
    
    $userId = $db->lastInsertId();
    
    // 记录活动
    $db->query("INSERT INTO user_activities (user_id, activity_type, title, description) VALUES (?, ?, ?, ?)", [
        null,
        'user_register',
        '新用户创建',
        "管理员创建了新用户: {$data['username']}"
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '用户创建成功',
        'data' => ['id' => $userId]
    ]);
}

/**
 * 处理PUT请求 - 更新用户
 */
function handlePut($db, $input) {
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少用户ID']);
        return;
    }
    
    $userId = $input['id'];
    
    // 检查用户是否存在
    $user = $db->fetchOne("SELECT id FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        http_response_code(404);
        echo json_encode(['error' => '用户不存在']);
        return;
    }
    
    // 构建更新数据
    $updateFields = [];
    $params = [];
    
    if (isset($input['username'])) {
        $updateFields[] = 'username = ?';
        $params[] = $input['username'];
    }
    if (isset($input['email'])) {
        $updateFields[] = 'email = ?';
        $params[] = $input['email'];
    }
    if (isset($input['full_name'])) {
        $updateFields[] = 'full_name = ?';
        $params[] = $input['full_name'];
    }
    if (isset($input['role_id'])) {
        $updateFields[] = 'role_id = ?';
        $params[] = $input['role_id'];
    }
    if (isset($input['status'])) {
        $updateFields[] = 'status = ?';
        $params[] = $input['status'];
    }
    if (isset($input['password']) && !empty($input['password'])) {
        $updateFields[] = 'password_hash = ?';
        $params[] = password_hash($input['password'], PASSWORD_DEFAULT);
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => '没有要更新的字段']);
        return;
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $userId;
    
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->query($sql, $params);
    
    echo json_encode(['success' => true, 'message' => '用户更新成功']);
}

/**
 * 处理DELETE请求 - 删除用户
 */
function handleDelete($db) {
    $userId = $_GET['id'] ?? null;
    
    if (!$userId) {
        http_response_code(400);
        echo json_encode(['error' => '缺少用户ID']);
        return;
    }
    
    // 检查用户是否存在
    $user = $db->fetchOne("SELECT username FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        http_response_code(404);
        echo json_encode(['error' => '用户不存在']);
        return;
    }
    
    // 软删除（更改状态为inactive）而不是物理删除
    $sql = "UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ?";
    $db->query($sql, [$userId]);
    
    // 记录活动
    $db->query("INSERT INTO user_activities (activity_type, title, description) VALUES (?, ?, ?)", [
        'user_register',
        '用户删除',
        "管理员删除了用户: {$user['username']}"
    ]);
    
    echo json_encode(['success' => true, 'message' => '用户删除成功']);
}
?>
