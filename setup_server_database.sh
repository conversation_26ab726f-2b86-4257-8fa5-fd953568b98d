#!/bin/bash

# 比特熊智慧系统 - 服务器数据库配置脚本
echo "=========================================="
echo "比特熊智慧系统 - 数据库配置"
echo "=========================================="
echo ""

# 数据库配置
DB_NAME="bitbear_website"
DB_USER="bitbear_website"
DB_PASS="309290133q"
ROOT_PASS="309290133q"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL服务
check_mysql() {
    log_info "检查MySQL服务状态..."
    
    if systemctl is-active --quiet mysql; then
        log_success "MySQL服务正在运行"
    elif systemctl is-active --quiet mysqld; then
        log_success "MySQL服务正在运行 (mysqld)"
    else
        log_warning "MySQL服务未运行，尝试启动..."
        systemctl start mysql || systemctl start mysqld
        
        if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
            log_success "MySQL服务启动成功"
        else
            log_error "MySQL服务启动失败"
            exit 1
        fi
    fi
}

# 测试MySQL连接
test_mysql_connection() {
    log_info "测试MySQL root连接..."
    
    if mysql -u root -p${ROOT_PASS} -e "SELECT 1;" &>/dev/null; then
        log_success "MySQL root连接成功"
        return 0
    else
        log_error "MySQL root连接失败"
        log_info "尝试无密码连接..."
        
        if mysql -u root -e "SELECT 1;" &>/dev/null; then
            log_warning "MySQL root无密码，正在设置密码..."
            mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED BY '${ROOT_PASS}';"
            mysql -u root -e "FLUSH PRIVILEGES;"
            log_success "MySQL root密码设置完成"
            return 0
        else
            log_error "无法连接到MySQL"
            return 1
        fi
    fi
}

# 创建数据库和用户
create_database() {
    log_info "创建数据库和用户..."
    
    # 创建数据库
    mysql -u root -p${ROOT_PASS} -e "CREATE DATABASE IF NOT EXISTS \`${DB_NAME}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
    if [ $? -eq 0 ]; then
        log_success "数据库 ${DB_NAME} 创建成功"
    else
        log_error "数据库创建失败"
        return 1
    fi
    
    # 创建用户
    mysql -u root -p${ROOT_PASS} -e "CREATE USER IF NOT EXISTS '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';" 2>/dev/null
    if [ $? -eq 0 ]; then
        log_success "用户 ${DB_USER} 创建成功"
    else
        log_warning "用户可能已存在，更新密码..."
        mysql -u root -p${ROOT_PASS} -e "ALTER USER '${DB_USER}'@'localhost' IDENTIFIED BY '${DB_PASS}';" 2>/dev/null
    fi
    
    # 授权
    mysql -u root -p${ROOT_PASS} -e "GRANT ALL PRIVILEGES ON \`${DB_NAME}\`.* TO '${DB_USER}'@'localhost';" 2>/dev/null
    mysql -u root -p${ROOT_PASS} -e "FLUSH PRIVILEGES;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        log_success "用户权限设置完成"
    else
        log_error "用户权限设置失败"
        return 1
    fi
}

# 测试新用户连接
test_user_connection() {
    log_info "测试新用户连接..."
    
    if mysql -u ${DB_USER} -p${DB_PASS} -D ${DB_NAME} -e "SELECT 1;" &>/dev/null; then
        log_success "用户 ${DB_USER} 连接测试成功"
        return 0
    else
        log_error "用户连接测试失败"
        return 1
    fi
}

# 显示数据库信息
show_database_info() {
    log_info "数据库配置信息:"
    echo ""
    echo "数据库名: ${DB_NAME}"
    echo "用户名: ${DB_USER}"
    echo "密码: ${DB_PASS}"
    echo "主机: localhost"
    echo ""
    
    # 显示现有表
    table_count=$(mysql -u ${DB_USER} -p${DB_PASS} -D ${DB_NAME} -e "SHOW TABLES;" 2>/dev/null | wc -l)
    if [ $table_count -gt 1 ]; then
        log_info "数据库包含 $((table_count-1)) 个表"
        mysql -u ${DB_USER} -p${DB_PASS} -D ${DB_NAME} -e "SHOW TABLES;" 2>/dev/null
    else
        log_warning "数据库为空，需要导入数据"
    fi
}

# 主函数
main() {
    check_mysql
    
    if test_mysql_connection; then
        create_database
        
        if test_user_connection; then
            show_database_info
            log_success "数据库配置完成！"
            echo ""
            echo "下一步: 导入数据库数据"
            echo "命令: mysql -u ${DB_USER} -p${DB_PASS} ${DB_NAME} < database_export.sql"
        else
            log_error "数据库配置失败"
            exit 1
        fi
    else
        log_error "MySQL连接失败，请检查MySQL服务"
        exit 1
    fi
}

# 运行主函数
main
