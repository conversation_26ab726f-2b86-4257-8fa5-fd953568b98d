<?php
// 调试注册问题
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>注册调试测试</h1>";

// 1. 测试数据库连接
echo "<h2>1. 数据库连接测试</h2>";
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 测试基本查询
    $result = $db->fetchOne("SELECT 1 as test");
    if ($result && $result['test'] == 1) {
        echo "<p style='color: green;'>✓ 数据库查询测试成功</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
    exit;
}

// 2. 检查必要的表是否存在
echo "<h2>2. 检查数据库表</h2>";
$requiredTables = ['users', 'user_roles', 'user_profiles'];
foreach ($requiredTables as $table) {
    try {
        $result = $db->fetchOne("SELECT COUNT(*) as count FROM $table");
        echo "<p style='color: green;'>✓ 表 '$table' 存在，记录数: {$result['count']}</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 表 '$table' 不存在或有问题: " . $e->getMessage() . "</p>";
    }
}

// 3. 检查用户角色
echo "<h2>3. 检查用户角色</h2>";
try {
    $roles = $db->fetchAll("SELECT * FROM user_roles");
    if (empty($roles)) {
        echo "<p style='color: orange;'>⚠ 用户角色表为空，正在创建默认角色...</p>";
        
        // 创建默认角色
        $defaultRoles = [
            ['role_code' => 'admin', 'role_name' => '管理员', 'permissions' => '["all"]'],
            ['role_code' => 'moderator', 'role_name' => '版主', 'permissions' => '["moderate"]'],
            ['role_code' => 'user', 'role_name' => '普通用户', 'permissions' => '["read","write"]']
        ];
        
        foreach ($defaultRoles as $role) {
            $db->execute(
                "INSERT INTO user_roles (role_code, role_name, permissions) VALUES (?, ?, ?)",
                [$role['role_code'], $role['role_name'], $role['permissions']]
            );
        }
        echo "<p style='color: green;'>✓ 默认角色创建完成</p>";
    } else {
        echo "<p style='color: green;'>✓ 用户角色表正常，共 " . count($roles) . " 个角色</p>";
        foreach ($roles as $role) {
            echo "<p>- ID: {$role['id']}, 代码: {$role['role_code']}, 名称: {$role['role_name']}</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 检查用户角色失败: " . $e->getMessage() . "</p>";
}

// 4. 模拟注册过程
echo "<h2>4. 模拟注册过程</h2>";
try {
    // 模拟注册数据
    $testUsername = 'test_user_' . time();
    $testEmail = 'test_' . time() . '@example.com';
    $testNickname = '测试用户';
    $testPassword = 'test123456';
    
    echo "<p>测试数据:</p>";
    echo "<p>- 用户名: $testUsername</p>";
    echo "<p>- 邮箱: $testEmail</p>";
    echo "<p>- 昵称: $testNickname</p>";
    
    // 开始事务
    $db->execute("BEGIN");
    
    // 获取普通用户角色ID
    $userRole = $db->fetchOne("SELECT id FROM user_roles WHERE role_code = 'user'");
    $roleId = $userRole ? $userRole['id'] : 3;
    
    echo "<p>使用角色ID: $roleId</p>";
    
    // 创建用户记录
    $passwordHash = password_hash($testPassword, PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
    
    $result = $db->execute($sql, [
        $testUsername,
        $testEmail,
        $passwordHash,
        $testNickname,
        $roleId
    ]);
    
    if (!$result) {
        throw new Exception('创建用户失败');
    }
    
    // 获取插入的ID
    $userId = $db->lastInsertId();
    echo "<p>用户ID: $userId</p>";
    
    if (!$userId || $userId == 0) {
        throw new Exception('获取用户ID失败');
    }
    
    // 创建用户个人资料记录
    $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                   VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
    
    $profileResult = $db->execute($profileSql, [
        $userId,
        $testNickname,
        'assets/images/default-avatar.png'
    ]);
    
    if (!$profileResult) {
        throw new Exception('创建用户资料失败');
    }
    
    // 提交事务
    $db->execute("COMMIT");
    
    echo "<p style='color: green;'>✓ 模拟注册成功！用户ID: $userId</p>";
    
    // 验证数据
    $user = $db->fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);
    $profile = $db->fetchOne("SELECT * FROM user_profiles WHERE user_id = ?", [$userId]);
    
    echo "<p>用户记录: " . ($user ? "存在" : "不存在") . "</p>";
    echo "<p>用户资料: " . ($profile ? "存在" : "不存在") . "</p>";
    
} catch (Exception $e) {
    // 回滚事务
    $db->execute("ROLLBACK");
    echo "<p style='color: red;'>✗ 模拟注册失败: " . $e->getMessage() . "</p>";
    
    // 显示详细错误信息
    echo "<p>错误详情:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// 5. 测试API端点
echo "<h2>5. 测试注册API端点</h2>";
if (file_exists('api/register.php')) {
    echo "<p style='color: green;'>✓ 注册API文件存在</p>";
    
    // 检查文件权限
    if (is_readable('api/register.php')) {
        echo "<p style='color: green;'>✓ 注册API文件可读</p>";
    } else {
        echo "<p style='color: red;'>✗ 注册API文件不可读</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 注册API文件不存在</p>";
}

echo "<h2>调试完成</h2>";
?>
