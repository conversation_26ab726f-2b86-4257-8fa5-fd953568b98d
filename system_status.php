<?php
/**
 * 系统状态检查页面
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊智慧系统 - 系统状态</title>
    <link rel="icon" type="image/png" href="image/bit.png">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 20px;
            background: #f8fafc;
            color: #1e293b;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f1f5f9;
        }
        .status-ok { background: #dcfce7; color: #166534; }
        .status-warning { background: #fef3c7; color: #92400e; }
        .status-error { background: #fecaca; color: #991b1b; }
        .status-info { background: #dbeafe; color: #1e40af; }
        .icon { font-size: 20px; margin-right: 10px; }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .btn:hover { background: #2563eb; }
        .btn-success { background: #16a34a; }
        .btn-success:hover { background: #15803d; }
        .btn-warning { background: #ea580c; }
        .btn-warning:hover { background: #c2410c; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐻 比特熊智慧系统</h1>
            <h2>系统状态检查</h2>
            <p>版本 v0.0.1</p>
        </div>

        <?php
        $checks = [];
        
        // 检查PHP版本
        $php_version = phpversion();
        $checks[] = [
            'name' => 'PHP版本',
            'status' => version_compare($php_version, '7.4', '>=') ? 'ok' : 'warning',
            'message' => "PHP {$php_version}",
            'icon' => '🐘'
        ];
        
        // 检查数据库连接
        try {
            require_once 'config/database.php';
            $db = db();
            $connection = $db->getConnection();
            $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
            
            $checks[] = [
                'name' => '数据库连接',
                'status' => 'ok',
                'message' => "已连接 ({$driver})",
                'icon' => '🗄️'
            ];
            
            // 检查用户表
            $user_count = $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
            $checks[] = [
                'name' => '用户数据',
                'status' => $user_count > 0 ? 'ok' : 'warning',
                'message' => "{$user_count} 个用户",
                'icon' => '👥'
            ];
            
            // 检查角色表
            $role_count = $db->fetchOne("SELECT COUNT(*) as count FROM user_roles")['count'];
            $checks[] = [
                'name' => '角色数据',
                'status' => $role_count > 0 ? 'ok' : 'warning',
                'message' => "{$role_count} 个角色",
                'icon' => '🔐'
            ];
            
        } catch (Exception $e) {
            $checks[] = [
                'name' => '数据库连接',
                'status' => 'error',
                'message' => '连接失败: ' . $e->getMessage(),
                'icon' => '❌'
            ];
        }
        
        // 检查关键文件
        $files = [
            'admin-login.php' => '登录页面',
            'admin-auth.php' => '认证处理',
            'admin-dashboard.php' => '管理后台',
            'config/database.php' => '数据库配置'
        ];
        
        foreach ($files as $file => $name) {
            $checks[] = [
                'name' => $name,
                'status' => file_exists($file) ? 'ok' : 'error',
                'message' => file_exists($file) ? '文件存在' : '文件缺失',
                'icon' => '📄'
            ];
        }
        
        // 检查会话支持
        $checks[] = [
            'name' => '会话支持',
            'status' => function_exists('session_start') ? 'ok' : 'error',
            'message' => function_exists('session_start') ? '支持' : '不支持',
            'icon' => '🔒'
        ];
        
        // 检查PDO支持
        $pdo_drivers = PDO::getAvailableDrivers();
        $checks[] = [
            'name' => 'PDO驱动',
            'status' => !empty($pdo_drivers) ? 'ok' : 'error',
            'message' => implode(', ', $pdo_drivers),
            'icon' => '🔌'
        ];
        
        // 显示检查结果
        foreach ($checks as $check) {
            $class = 'status-' . $check['status'];
            echo "<div class='status-item {$class}'>";
            echo "<span><span class='icon'>{$check['icon']}</span>{$check['name']}</span>";
            echo "<span>{$check['message']}</span>";
            echo "</div>";
        }
        
        // 统计状态
        $ok_count = count(array_filter($checks, function($c) { return $c['status'] === 'ok'; }));
        $total_count = count($checks);
        $health_percentage = round(($ok_count / $total_count) * 100);
        
        echo "<div class='status-item status-info'>";
        echo "<span><span class='icon'>📊</span>系统健康度</span>";
        echo "<span>{$health_percentage}% ({$ok_count}/{$total_count})</span>";
        echo "</div>";
        ?>

        <div class="actions">
            <h3>快速操作</h3>
            <a href="admin-login.php" class="btn">🔑 管理员登录</a>
            <a href="test_new_db.php" class="btn btn-warning">🔍 数据库测试</a>
            <a href="test_login_flow.php" class="btn btn-warning">🧪 登录流程测试</a>
            
            <?php if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']): ?>
            <a href="admin-dashboard.php" class="btn btn-success">🏠 管理后台</a>
            <?php endif; ?>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center; color: #64748b;">
            <p>比特熊智慧系统 v0.0.1 | 系统状态检查完成</p>
            <p>默认管理员账号：admin / admin123</p>
        </div>
    </div>
</body>
</html>
