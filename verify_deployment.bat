@echo off
echo ========================================
echo 比特熊智慧系统 - 部署验证工具
echo ========================================
echo.

echo 正在验证服务器部署状态...
echo.

echo [1/6] 检查数据库连接...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "sudo mysql -u root -D bitbear_website -e 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema=\"bitbear_website\";'"
echo.

echo [2/6] 检查项目文件...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "ls -la /www/wwwroot/www.bitbear.top/index.php"
echo.

echo [3/6] 检查Web服务器状态...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "systemctl is-active nginx"
echo.

echo [4/6] 检查PHP状态...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "php --version | head -1"
echo.

echo [5/6] 测试网站访问...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "curl -s -o /dev/null -w 'HTTP Status: %%{http_code}\n' http://localhost"
echo.

echo [6/6] 检查数据库表数据...
putty\plink.exe -ssh -batch -l root -pw "ZbDX7%=]?H2(LAUz" ************* "sudo mysql -u root -D bitbear_website -e 'SELECT \"users\" as table_name, COUNT(*) as record_count FROM users UNION SELECT \"posts\", COUNT(*) FROM posts UNION SELECT \"comments\", COUNT(*) FROM comments;'"
echo.

echo ========================================
echo 验证完成！
echo ========================================
echo.
echo 网站访问地址:
echo http://*************
echo http://www.bitbear.top
echo.
echo 管理后台:
echo http://*************/admin/
echo.
echo 数据库信息:
echo 主机: localhost
echo 数据库: bitbear_website
echo 用户名: bitbear_website
echo 密码: 309290133q
echo.

pause
