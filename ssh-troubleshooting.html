<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH连接问题排查 - 腾讯云服务器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid;
        }
        
        .alert-error {
            background: #fef2f2;
            border-color: #dc2626;
            color: #dc2626;
        }
        
        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }
        
        .alert-success {
            background: #dcfce7;
            border-color: #16a34a;
            color: #15803d;
        }
        
        .alert-info {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .status-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-ok { color: #059669; font-weight: 600; }
        .status-error { color: #dc2626; font-weight: 600; }
        
        .solution {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #3b82f6;
        }
        
        .solution-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .command-box {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 0.5rem 0;
            overflow-x: auto;
            position: relative;
        }
        
        .copy-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .copy-button:hover {
            background: #2563eb;
        }
        
        .test-button {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 SSH连接问题排查</h1>
        
        <div class="alert alert-error">
            <strong>❌ 问题：</strong> SSH连接时密码认证失败 (Permission denied)
        </div>
        
        <div class="alert alert-success">
            <strong>✅ 确认：</strong> 网络连接正常，SSH端口22可达
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <h3>🔍 连接状态检查</h3>
                <div class="status-item">
                    <span>网络连通性</span>
                    <span class="status-ok">✅ 正常</span>
                </div>
                <div class="status-item">
                    <span>SSH端口22</span>
                    <span class="status-ok">✅ 可达</span>
                </div>
                <div class="status-item">
                    <span>主机密钥</span>
                    <span class="status-ok">✅ 已清除</span>
                </div>
                <div class="status-item">
                    <span>密码认证</span>
                    <span class="status-error">❌ 失败</span>
                </div>
            </div>
            
            <div class="status-card">
                <h3>📋 服务器信息</h3>
                <div class="status-item">
                    <span>IP地址</span>
                    <span>*************</span>
                </div>
                <div class="status-item">
                    <span>用户名</span>
                    <span>root</span>
                </div>
                <div class="status-item">
                    <span>提供的密码</span>
                    <span>ZbDX7%=]?H2(LAUz</span>
                </div>
                <div class="status-item">
                    <span>操作系统</span>
                    <span>CentOS 7.8</span>
                </div>
            </div>
        </div>
        
        <h2>🛠️ 可能的原因和解决方案</h2>
        
        <div class="solution">
            <span class="solution-number">1</span>
            <strong>密码可能已更改</strong>
            <p>服务器的root密码可能已经被修改。</p>
            <div class="alert alert-info">
                <strong>解决方案：</strong>
                <ul>
                    <li>登录腾讯云控制台重置密码</li>
                    <li>使用腾讯云控制台的"登录"功能直接访问</li>
                    <li>检查是否有其他管理员修改了密码</li>
                </ul>
            </div>
        </div>
        
        <div class="solution">
            <span class="solution-number">2</span>
            <strong>SSH配置限制root登录</strong>
            <p>服务器可能配置了禁止root用户通过SSH登录。</p>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('ssh-config-check')">复制</button>
                <div id="ssh-config-check"># 通过腾讯云控制台登录后检查SSH配置
cat /etc/ssh/sshd_config | grep PermitRootLogin</div>
            </div>
            <div class="alert alert-info">
                <strong>如果显示 "PermitRootLogin no"，需要修改为：</strong>
                <div class="command-box">
                    <div>PermitRootLogin yes</div>
                </div>
                <p>然后重启SSH服务：<code>systemctl restart sshd</code></p>
            </div>
        </div>
        
        <div class="solution">
            <span class="solution-number">3</span>
            <strong>使用腾讯云控制台登录</strong>
            <p>最可靠的方法是通过腾讯云控制台直接登录服务器。</p>
            <div class="alert alert-success">
                <strong>步骤：</strong>
                <ol>
                    <li>登录腾讯云控制台</li>
                    <li>进入云服务器CVM管理页面</li>
                    <li>找到您的服务器实例</li>
                    <li>点击"登录"按钮</li>
                    <li>选择"标准登录方式"</li>
                    <li>使用用户名root和密码登录</li>
                </ol>
            </div>
        </div>
        
        <div class="solution">
            <span class="solution-number">4</span>
            <strong>重置服务器密码</strong>
            <p>通过腾讯云控制台重置root密码。</p>
            <div class="alert alert-warning">
                <strong>注意：</strong> 重置密码需要重启服务器，可能会影响正在运行的服务。
            </div>
            <div class="alert alert-info">
                <strong>步骤：</strong>
                <ol>
                    <li>在腾讯云控制台找到您的服务器</li>
                    <li>点击"更多" → "密码/密钥" → "重置密码"</li>
                    <li>设置新密码</li>
                    <li>重启服务器使密码生效</li>
                </ol>
            </div>
        </div>
        
        <div class="solution">
            <span class="solution-number">5</span>
            <strong>尝试其他SSH客户端</strong>
            <p>使用不同的SSH客户端进行测试。</p>
            <div style="text-align: center; margin: 1rem 0;">
                <button class="test-button" onclick="downloadPutty()">下载PuTTY</button>
                <button class="test-button" onclick="tryMobaXterm()">尝试MobaXterm</button>
                <button class="test-button" onclick="useWebSSH()">使用Web SSH</button>
            </div>
        </div>
        
        <h2>🔍 进一步诊断</h2>
        
        <div class="solution">
            <span class="solution-number">6</span>
            <strong>检查SSH服务日志</strong>
            <p>通过腾讯云控制台登录后，查看SSH服务日志：</p>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('check-ssh-logs')">复制</button>
                <div id="check-ssh-logs"># 查看SSH服务状态
systemctl status sshd

# 查看SSH日志
tail -f /var/log/secure

# 查看最近的认证失败记录
grep "authentication failure" /var/log/secure | tail -10</div>
            </div>
        </div>
        
        <div class="solution">
            <span class="solution-number">7</span>
            <strong>临时启用密码认证</strong>
            <p>如果SSH配置禁用了密码认证，需要启用：</p>
            <div class="command-box">
                <button class="copy-button" onclick="copyToClipboard('enable-password-auth')">复制</button>
                <div id="enable-password-auth"># 编辑SSH配置文件
vi /etc/ssh/sshd_config

# 确保以下设置：
PasswordAuthentication yes
PermitRootLogin yes
PubkeyAuthentication yes

# 重启SSH服务
systemctl restart sshd</div>
            </div>
        </div>
        
        <h2>🚀 推荐的下一步操作</h2>
        
        <div class="alert alert-info">
            <strong>建议按以下顺序尝试：</strong>
            <ol>
                <li><strong>使用腾讯云控制台登录</strong> - 最直接可靠的方法</li>
                <li><strong>检查SSH配置</strong> - 确认是否允许root登录</li>
                <li><strong>重置密码</strong> - 如果密码确实已更改</li>
                <li><strong>修复SSH配置</strong> - 启用必要的认证方式</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 2rem 0;">
            <button class="test-button" onclick="openTencentConsole()">打开腾讯云控制台</button>
            <button class="test-button danger" onclick="showManualSteps()">显示手动操作步骤</button>
        </div>
        
        <div id="manualSteps" style="display: none;" class="alert alert-warning">
            <h4>手动操作步骤：</h4>
            <ol>
                <li>访问 <a href="https://console.cloud.tencent.com/cvm" target="_blank">腾讯云CVM控制台</a></li>
                <li>找到服务器实例 (IP: *************)</li>
                <li>点击"登录"按钮</li>
                <li>选择"标准登录方式"</li>
                <li>输入用户名: root</li>
                <li>输入密码: ZbDX7%=]?H2(LAUz</li>
                <li>如果密码错误，使用"重置密码"功能</li>
            </ol>
        </div>
    </div>
    
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(function() {
                const button = element.parentNode.querySelector('.copy-button');
                const originalText = button.textContent;
                button.textContent = '已复制!';
                button.style.background = '#16a34a';
                
                setTimeout(function() {
                    button.textContent = originalText;
                    button.style.background = '#3b82f6';
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败: ', err);
                alert('复制失败，请手动复制内容');
            });
        }
        
        function downloadPutty() {
            window.open('https://www.putty.org/', '_blank');
        }
        
        function tryMobaXterm() {
            window.open('https://mobaxterm.mobatek.net/', '_blank');
        }
        
        function useWebSSH() {
            alert('推荐使用腾讯云控制台的Web SSH功能，更加安全可靠。');
        }
        
        function openTencentConsole() {
            window.open('https://console.cloud.tencent.com/cvm', '_blank');
        }
        
        function showManualSteps() {
            const steps = document.getElementById('manualSteps');
            steps.style.display = steps.style.display === 'none' ? 'block' : 'none';
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 SSH问题排查页面已加载');
            console.log('📋 建议使用腾讯云控制台登录');
        });
    </script>
</body>
</html>
