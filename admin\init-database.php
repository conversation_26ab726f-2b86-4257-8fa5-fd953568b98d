<?php
/**
 * 数据库初始化脚本
 * 用于创建数据库表结构和初始数据
 */

require_once __DIR__ . '/../config/database.php';

try {
    $db = DatabaseConfig::getInstance();
    
    echo "<h2>正在初始化数据库...</h2>";
    
    // 初始化数据库结构
    $db->initializeDatabase();
    
    echo "<p>✅ 数据库表结构创建成功</p>";
    echo "<p>✅ 默认数据插入成功</p>";
    echo "<p>✅ 索引创建成功</p>";
    
    echo "<h3>默认管理员账户信息：</h3>";
    echo "<ul>";
    echo "<li>用户名: admin</li>";
    echo "<li>邮箱: <EMAIL></li>";
    echo "<li>密码: admin123</li>";
    echo "<li>角色: 超级管理员</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php'>前往登录页面</a></p>";
    
} catch (Exception $e) {
    echo "<h2>数据库初始化失败</h2>";
    echo "<p>错误信息: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>请检查数据库连接配置和MySQL服务状态</p>";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库初始化 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        h2, h3 {
            color: #1e293b;
        }
        
        p {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #10b981;
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
        }
        
        a {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
</body>
</html>
