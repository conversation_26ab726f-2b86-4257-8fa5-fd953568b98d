<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速启动 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .quick-link {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            display: block;
        }
        
        .quick-link:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .quick-link-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .quick-link-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .quick-link-desc {
            opacity: 0.9;
            font-size: 0.875rem;
        }
        
        .system-status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .status-label {
            opacity: 0.8;
            font-size: 0.875rem;
        }
        
        .status-ok { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-error { color: #ef4444; }
        
        .tips {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .tips-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .tips-list {
            list-style: none;
            padding: 0;
        }
        
        .tips-list li {
            padding: 0.5rem 0;
            opacity: 0.9;
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .quick-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速启动</h1>
        
        <!-- 系统状态 -->
        <div class="system-status">
            <div class="status-title">📊 系统状态</div>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="phpStatus">检查中...</div>
                    <div class="status-label">PHP服务</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="dbStatus">检查中...</div>
                    <div class="status-label">数据库</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="navbarStatus">检查中...</div>
                    <div class="status-label">导航栏</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="loadTime">计算中...</div>
                    <div class="status-label">加载时间</div>
                </div>
            </div>
        </div>
        
        <!-- 快速链接 -->
        <div class="quick-links">
            <a href="index_lite.php" class="quick-link">
                <div class="quick-link-icon">🏠</div>
                <div class="quick-link-title">轻量版首页</div>
                <div class="quick-link-desc">快速加载的简化版首页</div>
            </a>
            
            <a href="index.php" class="quick-link">
                <div class="quick-link-icon">🌟</div>
                <div class="quick-link-title">完整版首页</div>
                <div class="quick-link-desc">功能完整的原版首页</div>
            </a>
            
            <a href="admin-dashboard.php" class="quick-link">
                <div class="quick-link-icon">⚙️</div>
                <div class="quick-link-title">管理后台</div>
                <div class="quick-link-desc">系统管理和配置</div>
            </a>
            
            <a href="diagnostic.php" class="quick-link">
                <div class="quick-link-icon">🔍</div>
                <div class="quick-link-title">系统诊断</div>
                <div class="quick-link-desc">性能分析和问题诊断</div>
            </a>
            
            <a href="test_navbar.php" class="quick-link">
                <div class="quick-link-icon">🧪</div>
                <div class="quick-link-title">功能测试</div>
                <div class="quick-link-desc">导航栏功能测试</div>
            </a>
            
            <a href="preview_navbar.php" class="quick-link">
                <div class="quick-link-icon">👁️</div>
                <div class="quick-link-title">导航预览</div>
                <div class="quick-link-desc">导航栏效果预览</div>
            </a>
        </div>
        
        <!-- 使用提示 -->
        <div class="tips">
            <div class="tips-title">
                💡 使用提示
            </div>
            <ul class="tips-list">
                <li>
                    <span>🔧</span>
                    <span>如果网站加载缓慢，建议先访问<strong>轻量版首页</strong>或<strong>系统诊断</strong></span>
                </li>
                <li>
                    <span>🗄️</span>
                    <span>管理后台默认账号: <code>admin</code> 密码: <code>admin123</code></span>
                </li>
                <li>
                    <span>🧭</span>
                    <span>在管理后台的"页面管理 → 导航栏管理"中可以自定义导航栏</span>
                </li>
                <li>
                    <span>⚡</span>
                    <span>系统会自动缓存导航栏数据以提高性能</span>
                </li>
                <li>
                    <span>🔄</span>
                    <span>如果遇到问题，可以刷新页面或重启PHP服务器</span>
                </li>
            </ul>
        </div>
    </div>
    
    <script>
        const startTime = performance.now();
        
        // 检查系统状态
        async function checkSystemStatus() {
            // PHP状态
            document.getElementById('phpStatus').textContent = '✅ 正常';
            document.getElementById('phpStatus').className = 'status-value status-ok';
            
            // 数据库状态
            try {
                const response = await fetch('api/navbar.php');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('dbStatus').textContent = '✅ 已连接';
                    document.getElementById('dbStatus').className = 'status-value status-ok';
                    
                    document.getElementById('navbarStatus').textContent = `✅ ${data.data.length}项`;
                    document.getElementById('navbarStatus').className = 'status-value status-ok';
                } else {
                    document.getElementById('dbStatus').textContent = '❌ 连接失败';
                    document.getElementById('dbStatus').className = 'status-value status-error';
                    
                    document.getElementById('navbarStatus').textContent = '❌ 加载失败';
                    document.getElementById('navbarStatus').className = 'status-value status-error';
                }
            } catch (error) {
                document.getElementById('dbStatus').textContent = '⚠️ 检查失败';
                document.getElementById('dbStatus').className = 'status-value status-warning';
                
                document.getElementById('navbarStatus').textContent = '⚠️ 未知';
                document.getElementById('navbarStatus').className = 'status-value status-warning';
            }
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = performance.now() - startTime;
            const loadTimeElement = document.getElementById('loadTime');
            
            loadTimeElement.textContent = `${loadTime.toFixed(0)}ms`;
            
            if (loadTime < 100) {
                loadTimeElement.className = 'status-value status-ok';
            } else if (loadTime < 500) {
                loadTimeElement.className = 'status-value status-warning';
            } else {
                loadTimeElement.className = 'status-value status-error';
            }
            
            // 检查系统状态
            checkSystemStatus();
            
            console.log('🚀 快速启动页面加载完成');
            console.log(`⚡ 加载时间: ${loadTime.toFixed(2)}ms`);
        });
        
        // 添加点击效果
        document.querySelectorAll('.quick-link').forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
