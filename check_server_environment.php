<?php
/**
 * 服务器环境检测脚本
 * 用于检查服务器环境是否满足项目运行要求
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>服务器环境检测</h1>";
echo "<hr>";

// 检查函数
function checkItem($name, $condition, $required = true) {
    $status = $condition ? '✓' : '✗';
    $color = $condition ? 'green' : ($required ? 'red' : 'orange');
    $level = $required ? '必需' : '推荐';
    echo "<p style='color: {$color};'>{$status} {$name} ({$level})</p>";
    return $condition;
}

// 1. PHP环境检查
echo "<h2>1. PHP环境检查</h2>";
$phpVersion = PHP_VERSION;
echo "<p>PHP版本: {$phpVersion}</p>";

checkItem("PHP版本 >= 7.4", version_compare($phpVersion, '7.4.0', '>='));
checkItem("PDO扩展", extension_loaded('pdo'));
checkItem("PDO MySQL扩展", extension_loaded('pdo_mysql'));
checkItem("MySQLi扩展", extension_loaded('mysqli'));
checkItem("JSON扩展", extension_loaded('json'));
checkItem("MBString扩展", extension_loaded('mbstring'));
checkItem("OpenSSL扩展", extension_loaded('openssl'));
checkItem("CURL扩展", extension_loaded('curl'));
checkItem("GD扩展", extension_loaded('gd'), false);
checkItem("Zip扩展", extension_loaded('zip'), false);

// 2. PHP配置检查
echo "<h2>2. PHP配置检查</h2>";
$uploadMaxFilesize = ini_get('upload_max_filesize');
$postMaxSize = ini_get('post_max_size');
$maxExecutionTime = ini_get('max_execution_time');
$memoryLimit = ini_get('memory_limit');

echo "<p>上传文件大小限制: {$uploadMaxFilesize}</p>";
echo "<p>POST数据大小限制: {$postMaxSize}</p>";
echo "<p>脚本执行时间限制: {$maxExecutionTime}秒</p>";
echo "<p>内存限制: {$memoryLimit}</p>";

checkItem("文件上传已启用", ini_get('file_uploads'));
checkItem("会话支持", function_exists('session_start'));

// 3. 服务器信息
echo "<h2>3. 服务器信息</h2>";
echo "<p>服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>服务器IP: " . $_SERVER['SERVER_ADDR'] . "</p>";
echo "<p>服务器名称: " . $_SERVER['SERVER_NAME'] . "</p>";
echo "<p>文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>当前脚本路径: " . __FILE__ . "</p>";

// 4. 目录权限检查
echo "<h2>4. 目录权限检查</h2>";
$checkDirs = [
    __DIR__ => '项目根目录',
    __DIR__ . '/uploads' => '上传目录',
    __DIR__ . '/uploads/avatars' => '头像目录',
    __DIR__ . '/uploads/posts' => '帖子图片目录',
    __DIR__ . '/config' => '配置目录',
    '/tmp' => '临时目录'
];

foreach ($checkDirs as $dir => $name) {
    if (is_dir($dir)) {
        $readable = is_readable($dir);
        $writable = is_writable($dir);
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        
        echo "<p>{$name} ({$dir}):</p>";
        echo "<ul>";
        echo "<li>权限: {$perms}</li>";
        checkItem("可读", $readable);
        checkItem("可写", $writable);
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>{$name} ({$dir}): 目录不存在</p>";
    }
}

// 5. 数据库连接检查
echo "<h2>5. 数据库连接检查</h2>";

// 检查MySQL连接
try {
    $host = 'localhost';
    $username = 'root';
    $password = '309290133q';
    $database = 'bitbear_website';
    
    // 基本连接测试
    $dsn = "mysql:host={$host};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    
    checkItem("MySQL基本连接", true);
    
    // 获取MySQL版本
    $version = $pdo->query("SELECT VERSION() as version")->fetch();
    echo "<p>MySQL版本: " . $version['version'] . "</p>";
    
    // 检查数据库
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    $dbExists = in_array($database, $databases);
    checkItem("数据库 '{$database}' 存在", $dbExists);
    
    if ($dbExists) {
        // 连接到指定数据库
        $dsn = "mysql:host={$host};dbname={$database};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // 检查表
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>数据库包含 " . count($tables) . " 个表</p>";
        
        if (!empty($tables)) {
            echo "<details><summary>表列表</summary><ul>";
            foreach ($tables as $table) {
                echo "<li>{$table}</li>";
            }
            echo "</ul></details>";
        }
    }
    
} catch (PDOException $e) {
    checkItem("数据库连接", false);
    echo "<p style='color: red;'>数据库连接错误: " . $e->getMessage() . "</p>";
}

// 6. Web服务器检查
echo "<h2>6. Web服务器检查</h2>";
$serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
echo "<p>Web服务器: {$serverSoftware}</p>";

checkItem("HTTPS支持", isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on', false);
checkItem("URL重写支持", function_exists('apache_get_modules') ? in_array('mod_rewrite', apache_get_modules()) : true, false);

// 7. 安全检查
echo "<h2>7. 安全检查</h2>";
checkItem("register_globals已禁用", !ini_get('register_globals'));
checkItem("magic_quotes已禁用", !get_magic_quotes_gpc());
checkItem("allow_url_fopen已禁用", !ini_get('allow_url_fopen'), false);
checkItem("display_errors在生产环境中已禁用", !ini_get('display_errors'), false);

// 8. 性能检查
echo "<h2>8. 性能检查</h2>";
checkItem("OPcache已启用", extension_loaded('opcache') && ini_get('opcache.enable'), false);
checkItem("APCu已启用", extension_loaded('apcu'), false);

// 9. 时区和编码检查
echo "<h2>9. 时区和编码检查</h2>";
echo "<p>默认时区: " . date_default_timezone_get() . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>内部编码: " . mb_internal_encoding() . "</p>";

checkItem("时区设置为Asia/Shanghai", date_default_timezone_get() === 'Asia/Shanghai');

// 10. 配置文件检查
echo "<h2>10. 配置文件检查</h2>";
$configFiles = [
    'config/database.php' => '数据库配置文件',
    'database/init.sql' => '数据库初始化文件',
    'index.php' => '主页文件'
];

foreach ($configFiles as $file => $name) {
    $exists = file_exists(__DIR__ . '/' . $file);
    checkItem($name, $exists);
    
    if ($exists) {
        $size = filesize(__DIR__ . '/' . $file);
        echo "<p style='margin-left: 20px;'>文件大小: " . number_format($size) . " 字节</p>";
    }
}

echo "<hr>";
echo "<h2>检测完成</h2>";
echo "<p>请根据检测结果修复任何标记为红色的问题。</p>";
echo "<p>橙色项目为推荐配置，不是必需的。</p>";
echo "<p>如果所有必需项目都显示为绿色，说明服务器环境配置正确。</p>";
?>
