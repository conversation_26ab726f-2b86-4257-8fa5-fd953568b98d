<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器连接测试 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .result.success {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }
        
        .result.error {
            background: #fef2f2;
            border: 1px solid #dc2626;
            color: #dc2626;
        }
        
        .result.info {
            background: #dbeafe;
            border: 1px solid #2563eb;
            color: #1d4ed8;
        }
        
        .url-list {
            list-style: none;
            padding: 0;
        }
        
        .url-list li {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
        }
        
        .url-list a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }
        
        .url-list a:hover {
            text-decoration: underline;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #16a34a; }
        .status-error { background: #dc2626; }
        .status-pending { background: #f59e0b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 服务器连接测试</h1>
        
        <div class="test-section">
            <h3>📋 诊断结果总结</h3>
            <div class="result info">
根据PowerShell诊断结果：

✅ 端口8888可达 - 服务器正在运行
❌ 所有HTTP路径返回404 - 路径配置问题
⚠️  网络连接不稳定 - 存在丢包

<strong>结论：这不是网络连接问题，而是服务器配置问题！</strong>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🌐 浏览器测试</h3>
            <p>点击下面的按钮测试不同的URL路径：</p>
            
            <button class="test-button" onclick="testUrl('http://*************:8888/')">测试根路径</button>
            <button class="test-button" onclick="testUrl('http://*************:8888/tencentcloud')">测试目标路径</button>
            <button class="test-button" onclick="testUrl('http://*************:8888/index.html')">测试index.html</button>
            <button class="test-button" onclick="testUrl('http://*************:8888/index.php')">测试index.php</button>
            
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 可能的解决方案</h3>
            <div class="result info">
<strong>1. 服务器端检查：</strong>
   • 确认Web服务器（Apache/Nginx）是否正确配置
   • 检查文档根目录是否包含所需文件
   • 验证/tencentcloud路径是否存在
   • 检查文件权限设置

<strong>2. 配置建议：</strong>
   • 在服务器上创建/tencentcloud目录
   • 添加默认的index.html或index.php文件
   • 检查Web服务器配置文件
   • 重启Web服务器服务

<strong>3. 临时解决方案：</strong>
   • 尝试访问服务器的其他已知路径
   • 联系服务器管理员确认配置
   • 检查服务器日志文件
            </div>
        </div>
        
        <div class="test-section">
            <h3>📱 手机vs电脑差异分析</h3>
            <div class="result info">
<strong>为什么手机能访问而电脑不能？</strong>

可能的原因：
1. <strong>缓存差异</strong> - 手机可能缓存了旧版本的页面
2. <strong>网络路径</strong> - 手机使用移动网络，电脑使用WiFi
3. <strong>DNS解析</strong> - 不同设备可能使用不同的DNS服务器
4. <strong>代理设置</strong> - 电脑可能配置了代理服务器
5. <strong>防火墙</strong> - 电脑防火墙可能阻止了连接

<strong>建议测试：</strong>
• 在电脑上尝试使用手机热点
• 清除电脑浏览器的所有缓存
• 尝试使用不同的浏览器
• 检查电脑的代理设置
            </div>
        </div>
        
        <div class="test-section">
            <h3>🚀 快速测试链接</h3>
            <ul class="url-list">
                <li>
                    <span class="status-indicator status-pending"></span>
                    <a href="http://*************:8888/" target="_blank">http://*************:8888/</a>
                    <span style="color: #6b7280; font-size: 0.875rem;">- 服务器根路径</span>
                </li>
                <li>
                    <span class="status-indicator status-error"></span>
                    <a href="http://*************:8888/tencentcloud" target="_blank">http://*************:8888/tencentcloud</a>
                    <span style="color: #6b7280; font-size: 0.875rem;">- 目标路径（当前404）</span>
                </li>
                <li>
                    <span class="status-indicator status-pending"></span>
                    <a href="http://*************:8888/admin" target="_blank">http://*************:8888/admin</a>
                    <span style="color: #6b7280; font-size: 0.875rem;">- 管理后台</span>
                </li>
            </ul>
        </div>
    </div>
    
    <script>
        function testUrl(url) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = `正在测试: ${url}\n请稍候...`;
            
            // 使用fetch API测试URL
            fetch(url, { 
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            })
            .then(response => {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ 连接成功！\nURL: ${url}\n状态: 可访问\n\n点击下面的链接在新窗口中打开：`;
                
                const link = document.createElement('a');
                link.href = url;
                link.target = '_blank';
                link.textContent = url;
                link.style.color = '#3b82f6';
                link.style.textDecoration = 'underline';
                
                resultDiv.appendChild(document.createElement('br'));
                resultDiv.appendChild(document.createElement('br'));
                resultDiv.appendChild(link);
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 连接测试\nURL: ${url}\n错误: ${error.message}\n\n注意：由于CORS限制，这个测试可能不准确。\n建议直接点击上面的链接进行测试。`;
            });
            
            // 同时在新窗口中打开URL进行实际测试
            setTimeout(() => {
                window.open(url, '_blank');
            }, 1000);
        }
        
        // 页面加载完成后显示诊断信息
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 服务器连接测试页面已加载');
            console.log('📊 根据PowerShell诊断：端口8888可达，但所有路径返回404');
            console.log('💡 建议：检查服务器配置和文件结构');
        });
    </script>
</body>
</html>
