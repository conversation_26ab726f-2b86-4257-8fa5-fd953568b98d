@echo off
chcp 65001 >nul
echo ========================================
echo 腾讯云服务器SSH连接工具 (已修复)
echo ========================================
echo.
echo 服务器信息:
echo   IP地址: *************
echo   用户名: root  
echo   密码: ZbDX7%=]?H2(LAUz
echo   操作系统: CentOS 7.8 64bit
echo.

echo ✓ SSH主机密钥问题已修复
echo.

echo 正在连接到服务器...
echo.
echo 注意事项:
echo 1. 首次连接会询问是否信任服务器，请输入 yes
echo 2. 输入密码时不会显示字符，这是正常的安全特性
echo 3. 密码: ZbDX7%=]?H2(LAUz
echo.

pause

echo 正在建立SSH连接...
C:\Windows\System32\OpenSSH\ssh.exe root@*************

echo.
echo 连接已断开
pause
