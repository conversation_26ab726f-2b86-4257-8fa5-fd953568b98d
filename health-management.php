<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once 'config/database.php';

// 检查登录状态 - 兼容两种认证方式
$isAuthenticated = false;
$currentUser = null;

// 方式1: 新的Auth类认证
if (class_exists('Auth')) {
    require_once 'classes/Auth.php';
    $auth = new Auth();
    if ($auth->isLoggedIn() && $auth->isAdmin()) {
        $isAuthenticated = true;
        $currentUser = $auth->getCurrentUser();
    }
}

// 方式2: 管理后台认证（回退方案）
if (!$isAuthenticated && isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $isAuthenticated = true;
    $currentUser = [
        'id' => $_SESSION['user_id'] ?? 1,
        'username' => $_SESSION['admin_user'] ?? 'admin',
        'role_code' => $_SESSION['user_type'] ?? 'admin',
        'full_name' => '管理员'
    ];
}

if (!$isAuthenticated) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康管理 - 比特熊智慧系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #10b981;
            --secondary-color: #059669;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --success-color: #10b981;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .page-title {
            margin: 0;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-description {
            margin: 0.5rem 0 0 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .date-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .date-selector label {
            font-weight: 500;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .date-selector select {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .date-selector select option {
            background: rgba(0, 0, 0, 0.8);
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: rgba(16, 185, 129, 0.3);
            border-color: rgba(16, 185, 129, 0.5);
        }

        .btn-primary:hover {
            background: rgba(16, 185, 129, 0.5);
        }

        .btn-secondary {
            background: rgba(107, 114, 128, 0.3);
            border-color: rgba(107, 114, 128, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.5);
        }

        .btn-success {
            background: rgba(34, 197, 94, 0.3);
            border-color: rgba(34, 197, 94, 0.5);
        }

        .btn-success:hover {
            background: rgba(34, 197, 94, 0.5);
        }

        .btn-info {
            background: rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
        }

        .btn-info:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        .health-table-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 0;
            padding: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .health-table {
            width: 100vw;
            min-width: 100vw;
            border-collapse: collapse;
            margin: 0;
            table-layout: fixed;
        }

        .health-table th,
        .health-table td {
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.75rem;
            text-align: center;
            position: relative;
            font-size: 1rem;
            min-width: 60px;
            min-height: 45px;
            width: auto;
        }

        .health-table th {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            font-weight: 600;
            color: white;
            position: sticky;
            top: 0;
            z-index: 10;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .health-table th:first-child {
            left: 0;
            z-index: 11;
            background: rgba(255, 255, 255, 0.3);
            width: 180px;
            min-width: 180px;
            max-width: 180px;
        }

        .health-table td:first-child {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            font-weight: 500;
            text-align: left;
            position: sticky;
            left: 0;
            z-index: 9;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            width: 180px;
            min-width: 180px;
            max-width: 180px;
        }

        /* 日期列宽度设置 */
        .health-table th:not(:first-child),
        .health-table td:not(:first-child) {
            width: calc((100vw - 180px) / 31);
            min-width: 70px;
        }

        .health-table td.editable {
            cursor: pointer;
            transition: all 0.2s;
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.9);
        }

        .health-table td.editable:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: scale(1.02);
        }

        .trend-up {
            color: var(--danger-color);
        }

        .trend-down {
            color: var(--success-color);
        }

        .trend-arrow {
            font-size: 0.8em;
            margin-left: 0.25rem;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #64748b;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .pagination button {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--border-color);
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination .current-page {
            font-weight: 600;
            color: #374151;
        }

        @media (max-width: 768px) {
            .date-selector {
                flex-direction: column;
                align-items: flex-start;
            }

            .action-buttons {
                justify-content: center;
            }

            .health-table-container {
                padding: 1rem;
            }

            .health-table th,
            .health-table td {
                padding: 0.5rem;
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="admin-dashboard.php">
                <i class="fas fa-heartbeat me-2"></i>
                健康管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="admin-dashboard.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回管理后台
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">健康管理</h1>
            <p class="page-description">管理和分析个人健康数据，支持自定义字段和趋势分析</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="date-selector">
                <label for="yearSelect">年份:</label>
                <select id="yearSelect" onchange="loadHealthData()">
                    <!-- 年份选项将通过JavaScript生成 -->
                </select>
                
                <label for="monthSelect">月份:</label>
                <select id="monthSelect" onchange="loadHealthData()">
                    <option value="1">1月</option>
                    <option value="2">2月</option>
                    <option value="3">3月</option>
                    <option value="4">4月</option>
                    <option value="5">5月</option>
                    <option value="6">6月</option>
                    <option value="7">7月</option>
                    <option value="8">8月</option>
                    <option value="9">9月</option>
                    <option value="10">10月</option>
                    <option value="11">11月</option>
                    <option value="12">12月</option>
                </select>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="loadHealthData()">
                    <i class="fas fa-sync-alt me-1"></i>
                    刷新数据
                </button>
                <button class="btn btn-success" onclick="showFieldManager()">
                    <i class="fas fa-cog me-1"></i>
                    字段管理
                </button>
                <button class="btn btn-info" onclick="showStatistics()">
                    <i class="fas fa-chart-line me-1"></i>
                    统计分析
                </button>
                <button class="btn btn-secondary" onclick="exportData()">
                    <i class="fas fa-download me-1"></i>
                    导出数据
                </button>
            </div>
        </div>

        <!-- 健康日志表格 -->
        <div class="health-table-container">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-spinner fa-spin me-2"></i>
                正在加载健康数据...
            </div>
            
            <div id="healthTableWrapper" style="display: none;">
                <table class="health-table" id="healthTable">
                    <thead>
                        <tr id="tableHeader">
                            <th>健康指标</th>
                            <!-- 日期列将通过JavaScript生成 -->
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- 数据行将通过JavaScript生成 -->
                    </tbody>
                </table>
                
                <div class="pagination" id="pagination">
                    <!-- 分页控件将通过JavaScript生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let healthFields = [];
        let healthData = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        function initializePage() {
            // 生成年份选项
            generateYearOptions();
            
            // 设置当前年月
            document.getElementById('yearSelect').value = currentYear;
            document.getElementById('monthSelect').value = currentMonth;
            
            // 加载健康数据
            loadHealthData();
        }

        function generateYearOptions() {
            const yearSelect = document.getElementById('yearSelect');
            const currentYear = new Date().getFullYear();
            
            // 生成从5年前到明年的年份选项
            for (let year = currentYear - 5; year <= currentYear + 1; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                if (year === currentYear) {
                    option.selected = true;
                }
                yearSelect.appendChild(option);
            }
        }

        function loadHealthData() {
            const year = document.getElementById('yearSelect').value;
            const month = document.getElementById('monthSelect').value;

            currentYear = parseInt(year);
            currentMonth = parseInt(month);

            showLoading(true);

            // 调用API加载数据
            fetch(`api/health-management.php?action=get_data&year=${year}&month=${month}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        healthFields = data.fields;
                        healthData = data.data;
                        renderHealthTable();
                    } else {
                        console.error('加载数据失败:', data.error);
                        alert('加载数据失败: ' + data.error);
                        // 如果API失败，使用模拟数据
                        loadMockData();
                    }
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    alert('网络错误，使用模拟数据');
                    loadMockData();
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const tableWrapper = document.getElementById('healthTableWrapper');
            
            if (show) {
                loadingIndicator.style.display = 'block';
                tableWrapper.style.display = 'none';
            } else {
                loadingIndicator.style.display = 'none';
                tableWrapper.style.display = 'block';
            }
        }

        function loadMockData() {
            // 模拟健康字段数据
            healthFields = [
                {id: 1, field_name: '发热', field_type: 'boolean', field_unit: ''},
                {id: 2, field_name: '咳嗽', field_type: 'boolean', field_unit: ''},
                {id: 3, field_name: '呼吸不通畅', field_type: 'boolean', field_unit: ''},
                {id: 4, field_name: '最大心率数', field_type: 'number', field_unit: 'bpm'},
                {id: 5, field_name: '最小心率数', field_type: 'number', field_unit: 'bpm'},
                {id: 6, field_name: '静息心率数', field_type: 'number', field_unit: 'bpm'},
                {id: 7, field_name: '心率异常总数', field_type: 'number', field_unit: '次'},
                {id: 8, field_name: '运动步数', field_type: 'number', field_unit: '步'},
                {id: 9, field_name: '房颤数', field_type: 'number', field_unit: '次'},
                {id: 10, field_name: '早搏数', field_type: 'number', field_unit: '次'},
                {id: 11, field_name: '疑似房颤标识', field_type: 'boolean', field_unit: ''},
                {id: 12, field_name: '疑似早搏标识', field_type: 'boolean', field_unit: ''},
                {id: 13, field_name: '血氧饱和度', field_type: 'number', field_unit: '%'},
                {id: 14, field_name: '其他', field_type: 'text', field_unit: ''}
            ];
            
            // 生成模拟数据
            generateMockHealthData();
            
            // 渲染表格
            renderHealthTable();
        }

        function generateMockHealthData() {
            healthData = {};
            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
            
            for (let day = 1; day <= daysInMonth; day++) {
                const dateKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                healthData[dateKey] = {};
                
                healthFields.forEach(field => {
                    if (field.type === 'boolean') {
                        healthData[dateKey][field.id] = Math.random() > 0.8 ? '是' : '否';
                    } else if (field.type === 'number') {
                        if (field.name.includes('心率')) {
                            healthData[dateKey][field.id] = Math.floor(Math.random() * 40) + 60;
                        } else if (field.name.includes('步数')) {
                            healthData[dateKey][field.id] = Math.floor(Math.random() * 5000) + 3000;
                        } else if (field.name.includes('血氧')) {
                            healthData[dateKey][field.id] = Math.floor(Math.random() * 5) + 95;
                        } else {
                            healthData[dateKey][field.id] = Math.floor(Math.random() * 10);
                        }
                    } else {
                        healthData[dateKey][field.id] = Math.random() > 0.7 ? '正常' : '';
                    }
                });
            }
        }

        function renderHealthTable() {
            const tableHeader = document.getElementById('tableHeader');
            const tableBody = document.getElementById('tableBody');
            
            // 清空现有内容
            tableHeader.innerHTML = '<th>健康指标</th>';
            tableBody.innerHTML = '';
            
            // 生成日期列标题
            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
            for (let day = 1; day <= daysInMonth; day++) {
                const th = document.createElement('th');
                th.textContent = day; // 只显示数字
                tableHeader.appendChild(th);
            }
            
            // 生成数据行
            healthFields.forEach(field => {
                const row = document.createElement('tr');
                
                // 字段名称列
                const fieldCell = document.createElement('td');
                const fieldName = field.field_name || field.name || 'undefined';
                fieldCell.textContent = fieldName + (field.field_unit || field.unit ? ` (${field.field_unit || field.unit})` : '');
                row.appendChild(fieldCell);
                
                // 数据列
                for (let day = 1; day <= daysInMonth; day++) {
                    const dateKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                    const dataCell = document.createElement('td');
                    dataCell.className = 'editable';
                    dataCell.setAttribute('data-date', dateKey);
                    dataCell.setAttribute('data-field', field.id);
                    
                    const value = healthData[dateKey] && healthData[dateKey][field.id] !== undefined ? 
                                 healthData[dateKey][field.id] : '';
                    
                    dataCell.textContent = value;
                    
                    // 添加趋势箭头（仅数字类型）
                    if ((field.field_type === 'number' || field.type === 'number') && value && day > 1) {
                        const prevDateKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${(day-1).toString().padStart(2, '0')}`;
                        const prevValue = healthData[prevDateKey] && healthData[prevDateKey][field.id];
                        
                        if (prevValue && !isNaN(value) && !isNaN(prevValue)) {
                            const currentVal = parseFloat(value);
                            const prevVal = parseFloat(prevValue);
                            
                            if (currentVal > prevVal) {
                                const arrow = document.createElement('span');
                                arrow.className = 'trend-arrow trend-up';
                                arrow.innerHTML = ' ↑';
                                dataCell.appendChild(arrow);
                                dataCell.classList.add('trend-up');
                            } else if (currentVal < prevVal) {
                                const arrow = document.createElement('span');
                                arrow.className = 'trend-arrow trend-down';
                                arrow.innerHTML = ' ↓';
                                dataCell.appendChild(arrow);
                                dataCell.classList.add('trend-down');
                            }
                        }
                    }
                    
                    // 添加双击编辑事件
                    dataCell.addEventListener('dblclick', function() {
                        editCell(this);
                    });
                    
                    row.appendChild(dataCell);
                }
                
                tableBody.appendChild(row);
            });
        }

        function editCell(cell) {
            const currentValue = cell.textContent.replace(/[↑↓]/g, '').trim();
            const fieldId = cell.getAttribute('data-field');
            const date = cell.getAttribute('data-date');
            
            // 找到对应的字段信息
            const field = healthFields.find(f => f.id == fieldId);
            if (!field) return;
            
            let input;
            
            if (field.type === 'boolean') {
                input = document.createElement('select');
                input.innerHTML = '<option value="">请选择</option><option value="是">是</option><option value="否">否</option>';
                input.value = currentValue;
            } else if (field.type === 'number') {
                input = document.createElement('input');
                input.type = 'number';
                input.value = currentValue;
            } else {
                input = document.createElement('input');
                input.type = 'text';
                input.value = currentValue;
            }
            
            input.style.width = '100%';
            input.style.border = 'none';
            input.style.background = 'transparent';
            input.style.textAlign = 'center';
            
            // 替换单元格内容
            cell.innerHTML = '';
            cell.appendChild(input);
            input.focus();
            
            // 处理保存
            function saveValue() {
                const newValue = input.value;

                // 调用API保存数据
                const saveData = {
                    date: date,
                    field_id: fieldId,
                    value: newValue
                };

                fetch('api/health-management.php?action=save_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(saveData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新本地数据
                        if (!healthData[date]) healthData[date] = {};
                        healthData[date][fieldId] = newValue;

                        // 重新渲染表格
                        renderHealthTable();
                    } else {
                        alert('保存失败: ' + data.error);
                        // 重新渲染表格以恢复原值
                        renderHealthTable();
                    }
                })
                .catch(error => {
                    console.error('保存失败:', error);
                    alert('网络错误，保存失败');
                    // 重新渲染表格以恢复原值
                    renderHealthTable();
                });
            }
            
            // 绑定事件
            input.addEventListener('blur', saveValue);
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    saveValue();
                }
            });
        }

        // 其他功能函数
        function showFieldManager() {
            window.open('health-field-manager.php', '_blank', 'width=800,height=600');
        }

        function showStatistics() {
            window.open('health-statistics.php', '_blank', 'width=1000,height=700');
        }

        function exportData() {
            const year = document.getElementById('yearSelect').value;
            const month = document.getElementById('monthSelect').value;

            // 生成CSV数据
            let csvContent = "data:text/csv;charset=utf-8,";

            // 添加标题行
            const headers = ['健康指标'];
            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
            for (let day = 1; day <= daysInMonth; day++) {
                headers.push(`${currentMonth}月${day}日`);
            }
            csvContent += headers.join(',') + '\n';

            // 添加数据行
            healthFields.forEach(field => {
                const row = [field.field_name + (field.field_unit ? ` (${field.field_unit})` : '')];

                for (let day = 1; day <= daysInMonth; day++) {
                    const dateKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                    const value = healthData[dateKey] && healthData[dateKey][field.id] !== undefined ?
                                 healthData[dateKey][field.id] : '';
                    row.push(value);
                }

                csvContent += row.join(',') + '\n';
            });

            // 下载文件
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement('a');
            link.setAttribute('href', encodedUri);
            link.setAttribute('download', `健康数据_${year}年${month}月.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
