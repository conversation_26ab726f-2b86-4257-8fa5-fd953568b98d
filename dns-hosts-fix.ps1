# DNS and Hosts File Fix Script
# Fix DNS resolution issues for BitBear System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DNS & Hosts File Fix Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$serverDomain = "bitbear.panel"  # 假设的域名，可以自定义
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "❌ 需要管理员权限来修改Hosts文件" -ForegroundColor Red
    Write-Host "请以管理员身份重新运行此脚本" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "操作步骤：" -ForegroundColor Cyan
    Write-Host "1. 右键点击PowerShell" -ForegroundColor White
    Write-Host "2. 选择'以管理员身份运行'" -ForegroundColor White
    Write-Host "3. 重新执行此脚本" -ForegroundColor White
    pause
    exit 1
}

Write-Host "✅ 管理员权限确认" -ForegroundColor Green
Write-Host ""

# 1. DNS解析测试
Write-Host "1. 测试当前DNS解析速度..." -ForegroundColor Yellow
Write-Host ""

$dnsServers = @(
    @{Name="本地DNS"; Server=""}
    @{Name="Google DNS"; Server="*******"}
    @{Name="Cloudflare DNS"; Server="*******"}
    @{Name="阿里DNS"; Server="*********"}
    @{Name="腾讯DNS"; Server="************"}
)

foreach ($dns in $dnsServers) {
    Write-Host "测试 $($dns.Name)..." -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        if ($dns.Server -eq "") {
            $result = Resolve-DnsName -Name $serverIP -ErrorAction Stop
        } else {
            $result = Resolve-DnsName -Name $serverIP -Server $dns.Server -ErrorAction Stop
        }
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  ✅ 解析成功 - 耗时: $([math]::Round($duration, 2))ms" -ForegroundColor Green
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  ❌ 解析失败 - 耗时: $([math]::Round($duration, 2))ms" -ForegroundColor Red
        Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Gray
    }
}

Write-Host ""

# 2. 检查当前Hosts文件
Write-Host "2. 检查当前Hosts文件..." -ForegroundColor Yellow
Write-Host "Hosts文件路径: $hostsPath" -ForegroundColor Gray
Write-Host ""

if (Test-Path $hostsPath) {
    Write-Host "✅ Hosts文件存在" -ForegroundColor Green
    
    # 读取现有内容
    $hostsContent = Get-Content $hostsPath -ErrorAction SilentlyContinue
    
    # 检查是否已有相关条目
    $existingEntries = $hostsContent | Where-Object { $_ -match $serverIP }
    
    if ($existingEntries) {
        Write-Host "发现现有条目：" -ForegroundColor Yellow
        foreach ($entry in $existingEntries) {
            Write-Host "  $entry" -ForegroundColor Gray
        }
    } else {
        Write-Host "未发现相关的Hosts条目" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ Hosts文件不存在" -ForegroundColor Red
}

Write-Host ""

# 3. 备份Hosts文件
Write-Host "3. 备份当前Hosts文件..." -ForegroundColor Yellow
$backupPath = "$hostsPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

try {
    Copy-Item $hostsPath $backupPath -ErrorAction Stop
    Write-Host "✅ 备份成功: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "❌ 备份失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "继续执行可能有风险，是否继续？(y/N)" -ForegroundColor Yellow
    $continue = Read-Host
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 1
    }
}

Write-Host ""

# 4. 添加Hosts条目
Write-Host "4. 添加Hosts条目..." -ForegroundColor Yellow

$hostsEntries = @(
    "$serverIP $serverDomain",
    "$serverIP bitbear.local",
    "$serverIP panel.bitbear.com",
    "$serverIP $serverIP.local"
)

Write-Host "准备添加以下条目：" -ForegroundColor Cyan
foreach ($entry in $hostsEntries) {
    Write-Host "  $entry" -ForegroundColor White
}

Write-Host ""
Write-Host "是否添加这些Hosts条目？(Y/n)" -ForegroundColor Yellow
$addHosts = Read-Host
if ($addHosts -eq "" -or $addHosts -eq "y" -or $addHosts -eq "Y") {
    try {
        # 读取现有内容
        $currentContent = Get-Content $hostsPath -ErrorAction SilentlyContinue
        
        # 添加分隔符和新条目
        $newContent = $currentContent + @(
            "",
            "# BitBear System - Added by DNS Fix Tool $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')",
            $hostsEntries,
            "# End BitBear System entries"
        )
        
        # 写入文件
        $newContent | Out-File -FilePath $hostsPath -Encoding ASCII -Force
        Write-Host "✅ Hosts条目添加成功" -ForegroundColor Green
        
        # 刷新DNS缓存
        Write-Host "正在刷新DNS缓存..." -ForegroundColor Cyan
        ipconfig /flushdns | Out-Null
        Write-Host "✅ DNS缓存已刷新" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ 添加Hosts条目失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "跳过Hosts条目添加" -ForegroundColor Yellow
}

Write-Host ""

# 5. 测试修复效果
Write-Host "5. 测试修复效果..." -ForegroundColor Yellow
Write-Host ""

$testUrls = @(
    "http://$serverIP`:8888/tencentcloud",
    "http://$serverDomain`:8888/tencentcloud",
    "http://bitbear.local:8888/tencentcloud"
)

foreach ($url in $testUrls) {
    Write-Host "测试: $url" -ForegroundColor Cyan
    $startTime = Get-Date
    
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  ✅ 响应成功 - 状态码: $($response.StatusCode), 耗时: $([math]::Round($duration, 2))ms" -ForegroundColor Green
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        $statusCode = $_.Exception.Response.StatusCode.value__
        if ($statusCode) {
            Write-Host "  ⚠️  HTTP $statusCode - 耗时: $([math]::Round($duration, 2))ms" -ForegroundColor Yellow
        } else {
            Write-Host "  ❌ 连接失败 - 耗时: $([math]::Round($duration, 2))ms" -ForegroundColor Red
        }
    }
}

Write-Host ""

# 6. 优化DNS设置建议
Write-Host "6. DNS优化建议..." -ForegroundColor Yellow
Write-Host ""

Write-Host "建议的DNS服务器设置：" -ForegroundColor Cyan
Write-Host "主DNS: ********* (阿里DNS)" -ForegroundColor White
Write-Host "备DNS: ******* (Google DNS)" -ForegroundColor White
Write-Host ""

Write-Host "是否要修改DNS设置？(y/N)" -ForegroundColor Yellow
$changeDNS = Read-Host
if ($changeDNS -eq "y" -or $changeDNS -eq "Y") {
    try {
        # 获取活动网络适配器
        $adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" -and $_.InterfaceDescription -notlike "*Loopback*" -and $_.InterfaceDescription -notlike "*VMware*" } | Select-Object -First 1
        
        if ($adapter) {
            Write-Host "正在修改网络适配器: $($adapter.Name)" -ForegroundColor Cyan
            
            # 设置DNS服务器
            Set-DnsClientServerAddress -InterfaceIndex $adapter.InterfaceIndex -ServerAddresses @("*********", "*******")
            Write-Host "✅ DNS服务器设置成功" -ForegroundColor Green
            
            # 刷新DNS缓存
            ipconfig /flushdns | Out-Null
            Write-Host "✅ DNS缓存已刷新" -ForegroundColor Green
        } else {
            Write-Host "❌ 未找到合适的网络适配器" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ DNS设置失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""

# 7. 生成测试页面
Write-Host "7. 生成测试页面..." -ForegroundColor Yellow

$testPageContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>DNS修复测试页面</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>DNS修复测试页面</h1>
    <p>点击下面的链接测试不同的访问方式：</p>
    
    <a href="http://$serverIP`:8888/tencentcloud" class="test-link" target="_blank">
        直接IP访问: http://$serverIP`:8888/tencentcloud
    </a>
    
    <a href="http://$serverDomain`:8888/tencentcloud" class="test-link" target="_blank">
        域名访问: http://$serverDomain`:8888/tencentcloud
    </a>
    
    <a href="http://bitbear.local:8888/tencentcloud" class="test-link" target="_blank">
        本地域名访问: http://bitbear.local:8888/tencentcloud
    </a>
    
    <script>
        console.log('DNS修复测试页面加载完成');
        console.log('服务器IP: $serverIP');
        console.log('测试时间: ' + new Date().toISOString());
    </script>
</body>
</html>
"@

$testPagePath = "dns-fix-test.html"
$testPageContent | Out-File -FilePath $testPagePath -Encoding UTF8
Write-Host "✅ 测试页面已生成: $testPagePath" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "修复完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "修复摘要：" -ForegroundColor White
Write-Host "• Hosts文件已备份" -ForegroundColor Green
Write-Host "• 已添加服务器IP解析条目" -ForegroundColor Green
Write-Host "• DNS缓存已刷新" -ForegroundColor Green
Write-Host "• 测试页面已生成" -ForegroundColor Green
Write-Host ""

Write-Host "下一步操作：" -ForegroundColor Yellow
Write-Host "1. 打开生成的测试页面进行测试" -ForegroundColor White
Write-Host "2. 如果仍有问题，尝试重启浏览器" -ForegroundColor White
Write-Host "3. 如果需要恢复，使用备份文件: $backupPath" -ForegroundColor White

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
