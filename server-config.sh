#!/bin/bash

# 服务器环境配置脚本
# 在服务器上执行此脚本来配置Web服务器

set -e

PROJECT_NAME="比特熊组织网站项目(v0.0.1)"
PROJECT_PATH="/www/wwwroot/${PROJECT_NAME}"
DOMAIN="your-domain.com"  # 替换为您的域名

echo "=== 服务器环境配置开始 ==="

# 1. 更新系统
echo "更新系统包..."
apt update && apt upgrade -y

# 2. 安装必要软件
echo "安装必要软件..."
apt install -y nginx mysql-server php8.1-fpm php8.1-mysql php8.1-curl php8.1-gd php8.1-mbstring php8.1-xml php8.1-zip unzip curl wget

# 3. 配置PHP
echo "配置PHP..."
sed -i 's/upload_max_filesize = 2M/upload_max_filesize = 50M/' /etc/php/8.1/fpm/php.ini
sed -i 's/post_max_size = 8M/post_max_size = 50M/' /etc/php/8.1/fpm/php.ini
sed -i 's/max_execution_time = 30/max_execution_time = 300/' /etc/php/8.1/fpm/php.ini

# 4. 配置Nginx
echo "配置Nginx..."
cat > /etc/nginx/sites-available/${PROJECT_NAME} << 'NGINX_CONFIG'
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /www/wwwroot/比特熊组织网站项目(v0.0.1);
    index index.php index.html index.htm;

    # 安全配置
    server_tokens off;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # PHP处理
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 隐藏敏感文件
    location ~ /\. {
        deny all;
    }

    location ~ /(config|database|logs)/ {
        deny all;
    }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
NGINX_CONFIG

# 5. 启用站点
echo "启用站点..."
ln -sf /etc/nginx/sites-available/${PROJECT_NAME} /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 6. 测试Nginx配置
echo "测试Nginx配置..."
nginx -t

# 7. 创建必要目录
echo "创建必要目录..."
mkdir -p ${PROJECT_PATH}/{uploads,logs,cache}
chown -R www-data:www-data ${PROJECT_PATH}
chmod -R 755 ${PROJECT_PATH}
chmod -R 777 ${PROJECT_PATH}/{uploads,logs,cache}

# 8. 配置MySQL安全
echo "配置MySQL..."
mysql_secure_installation

# 9. 配置防火墙
echo "配置防火墙..."
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw --force enable

# 10. 启动服务
echo "启动服务..."
systemctl restart nginx
systemctl restart php8.1-fpm
systemctl restart mysql

systemctl enable nginx
systemctl enable php8.1-fpm
systemctl enable mysql

# 11. 创建SSL证书（Let's Encrypt）
echo "安装Certbot..."
apt install -y certbot python3-certbot-nginx

echo "=== 服务器配置完成 ==="
echo ""
echo "接下来的步骤:"
echo "1. 将域名解析到服务器IP: *************"
echo "2. 运行: certbot --nginx -d your-domain.com"
echo "3. 测试网站访问"
echo ""
echo "重要文件位置:"
echo "  项目目录: ${PROJECT_PATH}"
echo "  Nginx配置: /etc/nginx/sites-available/${PROJECT_NAME}"
echo "  PHP配置: /etc/php/8.1/fpm/php.ini"
echo "  错误日志: /var/log/nginx/error.log"
