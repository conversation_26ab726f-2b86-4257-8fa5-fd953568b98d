<?php
session_start();

// 记录退出登录活动
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $username = $_SESSION['admin_user'] ?? 'Unknown';
    $user_id = $_SESSION['user_id'] ?? null;
    
    // 尝试记录到数据库
    try {
        require_once 'config/database.php';
        $db = db();
        
        $db->query("INSERT INTO user_activities (user_id, activity_type, title, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)", [
            $user_id,
            'admin_login',
            '管理员退出',
            '用户 "' . $username . '" 退出系统',
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("记录退出活动失败: " . $e->getMessage());
    }
}

// 清除所有会话数据
$_SESSION = array();

// 如果使用了cookie会话，也删除cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// 销毁会话
session_destroy();

// 重定向到登录页面
header('Location: admin-login.php?logout=1');
exit;
?>
