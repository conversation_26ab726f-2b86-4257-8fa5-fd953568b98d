<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊项目 - 快速启动</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            text-align: center;
            max-width: 800px;
            padding: 2rem;
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .btn {
            display: block;
            padding: 1.5rem 2rem;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status h3 {
            margin-bottom: 1rem;
            color: #4ade80;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            text-align: left;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-icon {
            font-size: 1.2rem;
        }
        
        .instructions {
            margin-top: 2rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
        }
        
        .instructions h3 {
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .instructions ol {
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🐻</div>
        <h1>比特熊项目</h1>
        <p class="subtitle">O'Reilly风格专家展示区域 - 已准备就绪</p>
        
        <div class="buttons">
            <a href="index-standalone.html" class="btn btn-primary">
                🌟 查看静态版本<br>
                <small>推荐 - 完整效果，无需服务器</small>
            </a>
            
            <a href="index.html" class="btn">
                📄 标准HTML版本<br>
                <small>基础版本，包含所有功能</small>
            </a>
            
            <a href="项目演示.html" class="btn">
                🎯 项目演示页面<br>
                <small>功能介绍和使用指南</small>
            </a>
        </div>
        
        <div class="status">
            <h3>✅ 项目状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-icon">🎨</span>
                    <span>O'Reilly风格设计</span>
                </div>
                <div class="status-item">
                    <span class="status-icon">🎭</span>
                    <span>动画效果完成</span>
                </div>
                <div class="status-item">
                    <span class="status-icon">📱</span>
                    <span>响应式布局</span>
                </div>
                <div class="status-item">
                    <span class="status-icon">⚡</span>
                    <span>即开即用</span>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🚀 快速开始</h3>
            <ol>
                <li><strong>推荐方式</strong>：点击上方"查看静态版本"按钮</li>
                <li><strong>完整功能</strong>：如需PHP动态功能，请安装PHP环境</li>
                <li><strong>管理后台</strong>：PHP环境下可访问 admin.php</li>
                <li><strong>自定义</strong>：编辑 experts-data.php 修改专家信息</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 检查文件是否存在
        function checkFile(url) {
            return fetch(url, { method: 'HEAD' })
                .then(response => response.ok)
                .catch(() => false);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 比特熊项目启动页面加载完成！');
            console.log('📁 项目文件已准备就绪');
            console.log('🌟 推荐使用静态版本获得最佳体验');
            
            // 为按钮添加点击统计
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    console.log(`🔗 用户点击了: ${this.textContent.trim()}`);
                });
            });
        });
    </script>
</body>
</html>
