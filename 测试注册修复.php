<?php
/**
 * 测试注册修复效果
 */

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>测试注册修复</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #764ba2; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    .test-form { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
    input, button { padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
    button { background: #667eea; color: white; border: none; cursor: pointer; }
    button:hover { background: #764ba2; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🧪 测试注册修复效果</h1>";

// 测试注册API
echo "<div class='info'>";
echo "<h3>📝 注册测试</h3>";
echo "<div class='test-form'>";
echo "<h4>自动测试注册功能</h4>";

// 生成测试数据
$testUsername = 'testuser_' . time();
$testEmail = 'test_' . time() . '@example.com';
$testNickname = '测试用户_' . time();
$testPassword = 'test123456';

echo "<p><strong>测试数据：</strong></p>";
echo "<ul>";
echo "<li>用户名: {$testUsername}</li>";
echo "<li>邮箱: {$testEmail}</li>";
echo "<li>昵称: {$testNickname}</li>";
echo "<li>密码: {$testPassword}</li>";
echo "</ul>";

// 准备POST数据
$postData = [
    'username' => $testUsername,
    'email' => $testEmail,
    'nickname' => $testNickname,
    'password' => $testPassword,
    'confirm_password' => $testPassword,
    'agree_terms' => 'on'
];

// 发送注册请求
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/api/register.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'X-Requested-With: XMLHttpRequest'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<h4>📡 API响应结果</h4>";
echo "<p><strong>HTTP状态码:</strong> {$httpCode}</p>";

if ($error) {
    echo "<div class='error'>❌ CURL错误: {$error}</div>";
} else {
    echo "<p><strong>响应内容:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // 尝试解析JSON响应
    $responseData = json_decode($response, true);
    if ($responseData) {
        if (isset($responseData['success']) && $responseData['success']) {
            echo "<div class='success'>✅ 注册成功！</div>";
            if (isset($responseData['user_id'])) {
                echo "<p><strong>用户ID:</strong> {$responseData['user_id']}</p>";
            }
            if (isset($responseData['message'])) {
                echo "<p><strong>消息:</strong> {$responseData['message']}</p>";
            }
        } else {
            echo "<div class='error'>❌ 注册失败</div>";
            if (isset($responseData['message'])) {
                echo "<p><strong>错误信息:</strong> {$responseData['message']}</p>";
            }
        }
    } else {
        echo "<div class='error'>❌ 无法解析JSON响应</div>";
    }
}

echo "</div>";
echo "</div>";

// 检查错误日志
echo "<div class='info'>";
echo "<h3>📋 错误日志检查</h3>";

$logFiles = [
    'error.log',
    'php_errors.log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log'
];

$foundLogs = false;
foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        $foundLogs = true;
        echo "<h4>日志文件: {$logFile}</h4>";
        
        // 读取最后50行
        $lines = file($logFile);
        $recentLines = array_slice($lines, -50);
        
        // 过滤与注册相关的日志
        $relevantLines = array_filter($recentLines, function($line) use ($testUsername, $testEmail) {
            return strpos($line, $testUsername) !== false || 
                   strpos($line, $testEmail) !== false ||
                   strpos($line, '用户ID') !== false ||
                   strpos($line, 'lastInsertId') !== false ||
                   strpos($line, 'register.php') !== false;
        });
        
        if (!empty($relevantLines)) {
            echo "<pre>" . htmlspecialchars(implode('', $relevantLines)) . "</pre>";
        } else {
            echo "<p>没有找到相关的日志条目</p>";
        }
    }
}

if (!$foundLogs) {
    echo "<p>未找到错误日志文件</p>";
}

echo "</div>";

// 手动测试表单
echo "<div class='info'>";
echo "<h3>🖱️ 手动测试</h3>";
echo "<div class='test-form'>";
echo "<h4>手动注册测试</h4>";
echo "<form method='post' action='api/register.php' target='_blank'>";
echo "<p><input type='text' name='username' placeholder='用户名' value='manual_" . time() . "' required></p>";
echo "<p><input type='email' name='email' placeholder='邮箱' value='manual_" . time() . "@example.com' required></p>";
echo "<p><input type='text' name='nickname' placeholder='昵称' value='手动测试用户' required></p>";
echo "<p><input type='password' name='password' placeholder='密码' value='test123456' required></p>";
echo "<p><input type='password' name='confirm_password' placeholder='确认密码' value='test123456' required></p>";
echo "<p><label><input type='checkbox' name='agree_terms' checked> 同意服务条款</label></p>";
echo "<p><button type='submit'>手动测试注册</button></p>";
echo "</form>";
echo "</div>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' class='btn'>前往注册页面</a>";
echo "<a href='修复用户ID获取问题.php' class='btn'>查看诊断工具</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
