<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>健康管理模块数据库表创建</h2>";

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    // 读取SQL文件
    $sql = file_get_contents('sql/create_health_tables.sql');
    
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句（按分号分割）
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<h3>执行SQL语句...</h3>";
    
    foreach ($statements as $index => $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        echo "<p>执行语句 " . ($index + 1) . "...</p>";
        
        try {
            $db->exec($statement);
            echo "<p style='color: green;'>✓ 执行成功</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ 执行失败: " . $e->getMessage() . "</p>";
            echo "<pre>" . htmlspecialchars($statement) . "</pre>";
        }
    }
    
    echo "<h3>验证表创建结果</h3>";
    
    // 检查表是否创建成功
    $tables = ['health_fields', 'health_logs'];
    
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ 表 $table 创建成功</p>";
            
            // 显示表结构
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>表 $table 结构:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>备注</th></tr>";
            foreach ($columns as $col) {
                echo "<tr>";
                echo "<td>" . $col['Field'] . "</td>";
                echo "<td>" . $col['Type'] . "</td>";
                echo "<td>" . $col['Null'] . "</td>";
                echo "<td>" . $col['Key'] . "</td>";
                echo "<td>" . $col['Default'] . "</td>";
                echo "<td>" . ($col['Extra'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 显示数据量
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<p>数据量: " . $result['count'] . " 条</p>";
            
        } else {
            echo "<p style='color: red;'>✗ 表 $table 创建失败</p>";
        }
        
        echo "<hr>";
    }
    
    // 显示默认字段
    echo "<h3>默认健康字段</h3>";
    $stmt = $db->query("SELECT * FROM health_fields ORDER BY display_order");
    $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($fields)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>字段名</th><th>类型</th><th>单位</th><th>顺序</th><th>系统字段</th><th>状态</th></tr>";
        foreach ($fields as $field) {
            echo "<tr>";
            echo "<td>" . $field['id'] . "</td>";
            echo "<td>" . $field['field_name'] . "</td>";
            echo "<td>" . $field['field_type'] . "</td>";
            echo "<td>" . $field['field_unit'] . "</td>";
            echo "<td>" . $field['display_order'] . "</td>";
            echo "<td>" . ($field['is_system'] ? '是' : '否') . "</td>";
            echo "<td>" . ($field['is_active'] ? '启用' : '禁用') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3 style='color: green;'>✓ 健康管理模块数据库表创建完成！</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
}
?>
