# PHP 快速设置脚本 - PowerShell版本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PHP 快速设置 - 比特熊项目" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查当前目录下是否有PHP
if (Test-Path "php\php.exe") {
    Write-Host "✅ 发现本地 PHP 安装！" -ForegroundColor Green
    & "php\php.exe" --version
    Write-Host ""
    $useLocal = $true
} else {
    # 检查系统PHP
    try {
        php --version | Out-Null
        Write-Host "✅ 发现系统 PHP 安装！" -ForegroundColor Green
        php --version
        Write-Host ""
        $useSystem = $true
    } catch {
        Write-Host "📥 正在下载便携版 PHP..." -ForegroundColor Yellow
        Write-Host "这是一个轻量级的PHP版本，无需安装。" -ForegroundColor Yellow
        Write-Host ""

        # 创建php目录
        if (!(Test-Path "php")) {
            New-Item -ItemType Directory -Path "php" | Out-Null
        }

        # 下载便携版PHP
        Write-Host "正在下载 PHP 8.3 便携版..." -ForegroundColor Yellow
        $url = "https://windows.php.net/downloads/releases/php-8.3.14-nts-Win32-vs16-x64.zip"
        $output = "php.zip"
        
        try {
            $ProgressPreference = 'SilentlyContinue'
            Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
            Write-Host "✅ 下载完成！" -ForegroundColor Green
        } catch {
            Write-Host "❌ 下载失败，尝试备用链接..." -ForegroundColor Red
            $url2 = "https://windows.php.net/downloads/releases/php-8.2.26-nts-Win32-vs16-x64.zip"
            try {
                Invoke-WebRequest -Uri $url2 -OutFile $output -UseBasicParsing
                Write-Host "✅ 备用下载完成！" -ForegroundColor Green
            } catch {
                Write-Host "❌ 所有下载链接都失败了" -ForegroundColor Red
                Write-Host ""
                Write-Host "🔧 手动解决方案：" -ForegroundColor Yellow
                Write-Host "1. 访问 https://windows.php.net/download/"
                Write-Host "2. 下载 'Non Thread Safe' 版本的 PHP"
                Write-Host "3. 解压到当前目录的 'php' 文件夹中"
                Write-Host "4. 重新运行此脚本"
                Write-Host ""
                Read-Host "按回车键退出"
                exit 1
            }
        }

        if (!(Test-Path "php.zip")) {
            Write-Host "❌ 下载失败！" -ForegroundColor Red
            Read-Host "按回车键退出"
            exit 1
        }

        # 解压PHP
        Write-Host "📦 正在解压 PHP..." -ForegroundColor Yellow
        try {
            Expand-Archive -Path "php.zip" -DestinationPath "php" -Force
            Write-Host "✅ 解压完成！" -ForegroundColor Green
        } catch {
            Write-Host "❌ 解压失败！" -ForegroundColor Red
            Read-Host "按回车键退出"
            exit 1
        }

        # 清理
        Remove-Item "php.zip" -Force

        # 创建基本配置
        Write-Host "🔧 配置 PHP..." -ForegroundColor Yellow
        if (Test-Path "php\php.ini-development") {
            Copy-Item "php\php.ini-development" "php\php.ini"
        }

        # 验证安装
        Write-Host ""
        Write-Host "🔍 验证 PHP 安装..." -ForegroundColor Yellow
        try {
            & "php\php.exe" --version
            Write-Host "✅ PHP 设置成功！" -ForegroundColor Green
            $useLocal = $true
        } catch {
            Write-Host "❌ PHP 设置失败！" -ForegroundColor Red
            Read-Host "按回车键退出"
            exit 1
        }
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 启动 PHP 开发服务器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 服务器地址: http://localhost:8000" -ForegroundColor Green
Write-Host ""
Write-Host "📄 可用页面:" -ForegroundColor Yellow
Write-Host "  • 主页: http://localhost:8000/index.php" -ForegroundColor White
Write-Host "  • 管理后台: http://localhost:8000/admin.php" -ForegroundColor White
Write-Host "  • 静态版本: http://localhost:8000/index.html" -ForegroundColor White
Write-Host ""
Write-Host "💡 提示: 按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

# 启动服务器并自动打开浏览器
Start-Process "http://localhost:8000/index.php"

if ($useLocal) {
    & "php\php.exe" -S localhost:8000
} elseif ($useSystem) {
    php -S localhost:8000
}

Read-Host "按回车键退出"
