<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏测试页面</title>
    <link rel="stylesheet" href="index-style.css?v=20250731v2">
    <style>
        .test-content {
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            background: #10b981;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="center">
            <img src="image/bit.png" width="130px" height="60px">
            <ul class="list-left" id="dynamicNavbar">
            <?php
            require_once 'components/navbar.php';
            $navbarData = getNavbarData();

            foreach ($navbarData as $item) {
                $hasChildren = !empty($item['children']);
                if ($hasChildren) {
                    echo '<li class="nav-item dropdown">';
                    echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle">';
                    echo htmlspecialchars($item['name']);
                    echo '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
                    echo '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>';
                    echo '</svg>';
                    echo '</a>';
                    echo '<div class="dropdown-menu">';
                    foreach ($item['children'] as $child) {
                        echo '<a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">' . htmlspecialchars($child['name']) . '</a>';
                    }
                    echo '</div>';
                    echo '</li>';
                } else {
                    echo '<li class="nav-item">';
                    echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">' . htmlspecialchars($item['name']) . '</a>';
                    echo '</li>';
                }
            }
            ?>
            </ul>
            
            <div class="search-container">
                <form class="search-form" role="search">
                    <div class="search-input-wrapper">
                        <input type="search" class="search-input" placeholder="搜索..." aria-label="搜索内容">
                        <button type="submit" class="search-btn" aria-label="搜索">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>

            <ul class="list-right">
                <li class="zhuche">
                    <a href="register.php">注册试试</a>
                </li>
                <li class="loading">
                    <a href="login.php">登录</a>
                </li>
                <li class="admin-link">
                    <a href="admin" title="管理后台">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        管理后台
                    </a>
                </li>
            </ul>
        </div>
    </header>

    <div class="test-content">
        <h1>导航栏测试页面</h1>
        
        <div class="test-section">
            <h2><span class="status-indicator"></span>导航栏加载状态</h2>
            <p>导航栏已成功从数据库加载。当前包含 <?php echo count($navbarData); ?> 个主菜单项。</p>
        </div>

        <div class="test-section">
            <h2><span class="status-indicator"></span>下拉菜单测试</h2>
            <p>请将鼠标悬停在带有下拉箭头的菜单项上（如"组织"、"分管领导"、"课程"、"游戏"），下拉菜单应该会平滑显示。</p>
        </div>

        <div class="test-section">
            <h2>导航栏结构</h2>
            <ul>
                <?php foreach ($navbarData as $item): ?>
                    <li>
                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                        <?php if (!empty($item['children'])): ?>
                            <ul>
                                <?php foreach ($item['children'] as $child): ?>
                                    <li><?php echo htmlspecialchars($child['name']); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownItems = document.querySelectorAll('.nav-item.dropdown');

            dropdownItems.forEach(item => {
                const dropdownToggle = item.querySelector('.dropdown-toggle');
                const dropdownMenu = item.querySelector('.dropdown-menu');
                const dropdownArrow = item.querySelector('.dropdown-arrow');

                // 鼠标悬停显示下拉菜单
                item.addEventListener('mouseenter', function() {
                    dropdownMenu.classList.add('show');
                    dropdownArrow.classList.add('rotated');
                });

                // 鼠标离开隐藏下拉菜单
                item.addEventListener('mouseleave', function() {
                    dropdownMenu.classList.remove('show');
                    dropdownArrow.classList.remove('rotated');
                });

                // 点击切换下拉菜单
                dropdownToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    dropdownMenu.classList.toggle('show');
                    dropdownArrow.classList.toggle('rotated');
                });
            });
        });
    </script>
</body>
</html>
