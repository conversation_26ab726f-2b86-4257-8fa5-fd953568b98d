<?php
/**
 * 诊断帖子链接问题
 * 检查为什么点击帖子链接会刷新页面而不是跳转
 */

require_once 'config/database.php';

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>诊断帖子链接问题</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); min-height: 100vh; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    .btn { display: inline-block; padding: 12px 24px; background: #ff6b6b; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #ee5a24; }
    .btn.test { background: #28a745; }
    .btn.test:hover { background: #1e7e34; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .test-link { display: block; padding: 10px; margin: 5px 0; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; text-decoration: none; color: #333; }
    .test-link:hover { background: #e9ecef; }
    .code-block { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔍 诊断帖子链接问题</h1>";

// 1. 检查帖子数据
echo "<div class='info'>";
echo "<h3>📋 检查帖子数据</h3>";

try {
    $db = db();
    
    // 获取最新的几个帖子
    $posts = $db->fetchAll("SELECT id, title, status, created_at FROM posts ORDER BY id DESC LIMIT 5");
    
    if ($posts) {
        echo "<table>";
        echo "<tr><th>ID</th><th>标题</th><th>状态</th><th>创建时间</th><th>测试链接</th></tr>";
        
        foreach ($posts as $post) {
            echo "<tr>";
            echo "<td>{$post['id']}</td>";
            echo "<td>" . htmlspecialchars(mb_substr($post['title'], 0, 30)) . "</td>";
            echo "<td>{$post['status']}</td>";
            echo "<td>{$post['created_at']}</td>";
            echo "<td>";
            echo "<a href='community-post-detail.php?id={$post['id']}' class='test-link' target='_blank'>测试链接</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div class='success'>✅ 找到 " . count($posts) . " 个帖子</div>";
    } else {
        echo "<div class='warning'>⚠️ 没有找到帖子数据</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库查询失败: " . $e->getMessage() . "</div>";
}

echo "</div>";

// 2. 检查文件存在性
echo "<div class='info'>";
echo "<h3>📁 检查关键文件</h3>";

$files = [
    'community.php' => '社区首页',
    'community-post-detail.php' => '帖子详情页',
    'community-post.php' => '发帖页面',
    'assets/js/community-post-detail.js' => '帖子详情JS',
    'assets/css/community.css' => '社区样式'
];

echo "<table>";
echo "<tr><th>文件</th><th>状态</th><th>大小</th><th>修改时间</th></tr>";

foreach ($files as $file => $description) {
    $exists = file_exists($file);
    echo "<tr>";
    echo "<td>{$file}<br><small>{$description}</small></td>";
    echo "<td>" . ($exists ? "<span style='color: green;'>✅ 存在</span>" : "<span style='color: red;'>❌ 不存在</span>") . "</td>";
    
    if ($exists) {
        $size = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "<td>" . formatBytes($size) . "</td>";
        echo "<td>{$modified}</td>";
    } else {
        echo "<td>-</td><td>-</td>";
    }
    echo "</tr>";
}
echo "</table>";

echo "</div>";

// 3. 检查服务器配置
echo "<div class='info'>";
echo "<h3>⚙️ 服务器配置检查</h3>";

echo "<table>";
echo "<tr><th>配置项</th><th>值</th><th>说明</th></tr>";

$configs = [
    'PHP版本' => phpversion(),
    '服务器软件' => $_SERVER['SERVER_SOFTWARE'] ?? '未知',
    '文档根目录' => $_SERVER['DOCUMENT_ROOT'] ?? '未知',
    '当前脚本路径' => $_SERVER['SCRIPT_FILENAME'] ?? '未知',
    'URL重写' => (function_exists('apache_get_modules') && in_array('mod_rewrite', apache_get_modules())) ? '支持' : '未知',
    '错误显示' => ini_get('display_errors') ? '开启' : '关闭',
    '错误报告级别' => error_reporting(),
    'mbstring扩展' => extension_loaded('mbstring') ? '已安装' : '未安装'
];

foreach ($configs as $key => $value) {
    echo "<tr>";
    echo "<td>{$key}</td>";
    echo "<td>{$value}</td>";
    echo "<td>";
    switch ($key) {
        case 'mbstring扩展':
            echo $value === '已安装' ? '✅ 正常' : '❌ 需要安装';
            break;
        case '错误显示':
            echo $value === '开启' ? '⚠️ 生产环境建议关闭' : '✅ 正常';
            break;
        default:
            echo '信息';
    }
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

echo "</div>";

// 4. JavaScript错误检测
echo "<div class='info'>";
echo "<h3>🐛 JavaScript错误检测</h3>";

echo "<p>在浏览器中打开开发者工具（F12），检查Console标签页是否有JavaScript错误。</p>";

echo "<div class='code-block'>";
echo "常见的JavaScript错误类型：<br>";
echo "1. Uncaught ReferenceError: xxx is not defined<br>";
echo "2. Uncaught TypeError: Cannot read property 'xxx' of null<br>";
echo "3. 404错误：无法加载JS/CSS文件<br>";
echo "4. CORS错误：跨域请求被阻止<br>";
echo "5. 语法错误：SyntaxError";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>⚠️ 调试步骤</h4>";
echo "<ol>";
echo "<li>打开社区页面：<a href='community.php' target='_blank'>community.php</a></li>";
echo "<li>按F12打开开发者工具</li>";
echo "<li>切换到Console标签页</li>";
echo "<li>点击任意帖子链接</li>";
echo "<li>观察是否有错误信息</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

// 5. 创建测试链接
echo "<div class='info'>";
echo "<h3>🔗 测试链接功能</h3>";

if (isset($_POST['create_test_page'])) {
    $testContent = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>链接测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-link { display: block; padding: 10px; margin: 10px 0; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 4px; }
        .test-link:hover { background: #e0e0e0; }
    </style>
</head>
<body>
    <h1>链接测试页面</h1>
    <p>测试不同类型的链接是否正常工作：</p>
    
    <a href='community-post-detail.php?id=1' class='test-link'>普通链接 - 帖子1</a>
    <a href='community-post-detail.php?id=2' class='test-link'>普通链接 - 帖子2</a>
    <a href='community-post-detail.php?id=3' class='test-link'>普通链接 - 帖子3</a>
    
    <a href='community-post-detail.php?id=1' class='test-link' target='_blank'>新窗口链接 - 帖子1</a>
    <a href='community-post-detail.php?id=2' class='test-link' target='_blank'>新窗口链接 - 帖子2</a>
    
    <script>
        console.log('测试页面JavaScript加载成功');
        
        // 监听链接点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                console.log('链接被点击:', e.target.href);
                console.log('目标:', e.target.target);
            }
        });
        
        // 检查是否有阻止默认行为的事件
        document.addEventListener('click', function(e) {
            console.log('点击事件详情:', {
                target: e.target,
                defaultPrevented: e.defaultPrevented,
                bubbles: e.bubbles,
                cancelable: e.cancelable
            });
        }, true);
    </script>
</body>
</html>";
    
    if (file_put_contents('测试链接页面.html', $testContent)) {
        echo "<div class='success'>✅ 测试页面创建成功</div>";
        echo "<p><a href='测试链接页面.html' target='_blank' class='btn test'>打开测试页面</a></p>";
    } else {
        echo "<div class='error'>❌ 无法创建测试页面</div>";
    }
} else {
    echo "<form method='post'>";
    echo "<button type='submit' name='create_test_page' class='btn test'>创建测试页面</button>";
    echo "</form>";
}

echo "</div>";

// 6. 检查.htaccess文件
echo "<div class='info'>";
echo "<h3>📄 检查.htaccess配置</h3>";

if (file_exists('.htaccess')) {
    echo "<div class='success'>✅ .htaccess文件存在</div>";
    $htaccessContent = file_get_contents('.htaccess');
    echo "<h4>当前.htaccess内容：</h4>";
    echo "<pre>" . htmlspecialchars($htaccessContent) . "</pre>";
    
    // 检查是否有可能影响链接的规则
    if (strpos($htaccessContent, 'RewriteRule') !== false) {
        echo "<div class='warning'>⚠️ 发现URL重写规则，可能影响链接跳转</div>";
    }
} else {
    echo "<div class='info'>ℹ️ 没有.htaccess文件</div>";
}

echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='community.php' class='btn'>返回社区</a>";
echo "<a href='修复mbstring扩展问题.php' class='btn'>mbstring修复</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";

// JavaScript诊断脚本
echo "<script>
console.log('🔍 帖子链接诊断工具已加载');

// 检查页面加载错误
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    console.error('错误文件:', e.filename);
    console.error('错误行号:', e.lineno);
});

// 检查资源加载错误
window.addEventListener('error', function(e) {
    if (e.target !== window) {
        console.error('资源加载失败:', e.target.src || e.target.href);
    }
}, true);

// 监听所有链接点击
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' || e.target.closest('a')) {
        const link = e.target.tagName === 'A' ? e.target : e.target.closest('a');
        console.log('🔗 链接点击事件:', {
            href: link.href,
            target: link.target,
            text: link.textContent.trim(),
            defaultPrevented: e.defaultPrevented
        });
    }
});

// 检查是否有全局的点击事件监听器
console.log('🎯 当前页面的事件监听器数量:', document.querySelectorAll('*').length);
</script>";

echo "</body></html>";

// 辅助函数
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
?>
