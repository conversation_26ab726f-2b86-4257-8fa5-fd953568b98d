<?php
require_once 'config/database.php';

try {
    $db = db();
    
    echo "=== 检查帖子点赞数据 ===\n";
    $posts = $db->fetchAll('SELECT id, title, like_count, dislike_count FROM posts LIMIT 3');
    foreach ($posts as $post) {
        echo "帖子 {$post['id']}: {$post['title']} - 点赞: {$post['like_count']}, 反对: {$post['dislike_count']}\n";
    }
    
    echo "\n=== 检查评论点赞数据 ===\n";
    $comments = $db->fetchAll('SELECT id, content, like_count, dislike_count FROM comments LIMIT 3');
    foreach ($comments as $comment) {
        $shortContent = substr($comment['content'], 0, 30) . '...';
        echo "评论 {$comment['id']}: {$shortContent} - 点赞: {$comment['like_count']}, 反对: {$comment['dislike_count']}\n";
    }
    
    echo "\n=== 检查点赞记录表 ===\n";
    $likes = $db->fetchAll('SELECT * FROM likes LIMIT 5');
    if (empty($likes)) {
        echo "暂无点赞记录\n";
    } else {
        foreach ($likes as $like) {
            echo "用户 {$like['user_id']} 对 {$like['target_type']} {$like['target_id']} 进行了 {$like['type']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
