<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏测试页面</title>
    <link rel="stylesheet" href="index-style.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-content {
            padding: 100px 20px 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #374151;
        }
        
        .status-value {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-error {
            background: #fef2f2;
            color: #dc2626;
        }
        
        .navbar-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .navbar-item {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .navbar-item:last-child {
            border-bottom: none;
        }
        
        .navbar-item strong {
            color: #1e293b;
        }
        
        .children-list {
            margin-left: 20px;
            margin-top: 5px;
            font-size: 0.9rem;
            color: #64748b;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="center">
            <img src="image/bit.png" width="130px" height="60px">
            <ul class="list-left" id="dynamicNavbar">
            <?php
            // 包含导航栏组件并获取数据
            require_once 'components/navbar.php';
            $navbarData = getNavbarData();

            // 渲染导航菜单项
            foreach ($navbarData as $item) {
                $hasChildren = !empty($item['children']);
                if ($hasChildren) {
                    echo '<li>';
                    echo '<a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['name']) . '</a>';
                    echo '<ul class="dropdown-menu">';
                    foreach ($item['children'] as $child) {
                        echo '<li><a href="' . htmlspecialchars($child['url']) . '">' . htmlspecialchars($child['name']) . '</a></li>';
                    }
                    echo '</ul>';
                    echo '</li>';
                } else {
                    echo '<li><a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['name']) . '</a></li>';
                }
            }
            ?>
            </ul>
            
            <ul class="list-right">
                <li class="zhuche">
                    <a href="register.php">注册试试</a>
                </li>
                <li class="loading">
                    <a href="login.php">登录</a>
                </li>
                <li class="admin-link">
                    <a href="admin" title="管理后台 (需要登录)">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        管理后台
                    </a>
                </li>
            </ul>
        </div>
    </header>

    <div class="test-content">
        <div class="test-section">
            <h2>🧭 导航栏测试报告</h2>
            
            <div class="status-item">
                <span class="status-label">导航栏加载状态</span>
                <span class="status-value status-success">✅ 正常</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">菜单项数量</span>
                <span class="status-value status-success"><?php echo count($navbarData); ?> 个</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">下拉菜单功能</span>
                <span class="status-value status-success">✅ 已启用</span>
            </div>
            
            <div class="status-item">
                <span class="status-label">响应式设计</span>
                <span class="status-value status-success">✅ 支持</span>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 当前导航菜单结构</h3>
            <div class="navbar-info">
                <?php foreach ($navbarData as $item): ?>
                    <div class="navbar-item">
                        <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                        <span style="color: #64748b; font-size: 0.9rem;">
                            (<?php echo htmlspecialchars($item['url']); ?>)
                        </span>
                        
                        <?php if (!empty($item['children'])): ?>
                            <div class="children-list">
                                <?php foreach ($item['children'] as $child): ?>
                                    <div>↳ <?php echo htmlspecialchars($child['name']); ?> (<?php echo htmlspecialchars($child['url']); ?>)</div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 测试说明</h3>
            <ul>
                <li><strong>悬停测试：</strong>将鼠标悬停在有下拉菜单的导航项上，应该显示下拉菜单</li>
                <li><strong>样式测试：</strong>下拉菜单应该有半透明背景和模糊效果</li>
                <li><strong>响应式测试：</strong>在移动设备上查看导航栏的显示效果</li>
                <li><strong>链接测试：</strong>点击导航项应该能够正确跳转</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 管理功能</h3>
            <p>
                <a href="admin/login.php" style="display: inline-block; padding: 10px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 6px; margin-right: 10px;">
                    进入后台管理
                </a>
                <a href="index.php" style="display: inline-block; padding: 10px 20px; background: #f3f4f6; color: #374151; text-decoration: none; border-radius: 6px;">
                    返回首页
                </a>
            </p>
        </div>
    </div>

    <script>
        // 检查导航栏功能
        document.addEventListener('DOMContentLoaded', function() {
            const navItems = document.querySelectorAll('.list-left li');
            const dropdownMenus = document.querySelectorAll('.dropdown-menu');
            
            console.log(`🧭 导航栏测试：发现 ${navItems.length} 个导航项`);
            console.log(`📋 下拉菜单测试：发现 ${dropdownMenus.length} 个下拉菜单`);
            
            // 测试下拉菜单功能
            navItems.forEach((item, index) => {
                const dropdown = item.querySelector('.dropdown-menu');
                if (dropdown) {
                    console.log(`✅ 导航项 ${index + 1} 包含下拉菜单`);
                }
            });
        });
    </script>
</body>
</html>
