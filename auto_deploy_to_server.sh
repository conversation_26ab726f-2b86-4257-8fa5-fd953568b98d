#!/bin/bash

# 比特熊智慧系统 - 腾讯云自动部署脚本
# 使用方法: bash auto_deploy_to_server.sh

echo "========================================="
echo "比特熊智慧系统 - 腾讯云自动部署脚本"
echo "========================================="
echo

# 服务器配置
SERVER_IP="*************"
SERVER_USER="root"
SERVER_PASS="ZbDX7%=]?H2(LAUz"
PROJECT_DIR="/www/wwwroot/www.bitbear.top"
DB_NAME="bitbear_website"
DB_PASS="309290133q"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查本地环境
check_local_environment() {
    log_info "检查本地环境..."
    
    # 检查SSH客户端
    if ! command -v ssh &> /dev/null; then
        log_error "SSH客户端未安装"
        exit 1
    fi
    
    # 检查SCP客户端
    if ! command -v scp &> /dev/null; then
        log_error "SCP客户端未安装"
        exit 1
    fi
    
    log_info "本地环境检查完成"
}

# 连接测试
test_connection() {
    log_info "测试服务器连接..."
    
    # 使用sshpass进行自动化连接（如果可用）
    if command -v sshpass &> /dev/null; then
        if sshpass -p "$SERVER_PASS" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "echo 'Connection test successful'"; then
            log_info "服务器连接测试成功"
            return 0
        else
            log_error "服务器连接测试失败"
            return 1
        fi
    else
        log_warn "sshpass未安装，将需要手动输入密码"
        log_info "请手动测试连接: ssh $SERVER_USER@$SERVER_IP"
        return 0
    fi
}

# 检查服务器环境
check_server_environment() {
    log_info "检查服务器环境..."
    
    # 创建检查脚本
    cat > server_check.sh << 'EOF'
#!/bin/bash
echo "=== 服务器环境检查 ==="

echo "1. PHP版本:"
php -v | head -1

echo "2. MySQL服务状态:"
systemctl is-active mysql

echo "3. Nginx服务状态:"
systemctl is-active nginx

echo "4. 项目目录:"
ls -la /www/wwwroot/www.bitbear.top 2>/dev/null || echo "目录不存在"

echo "5. 磁盘空间:"
df -h /

echo "6. 内存使用:"
free -h

echo "=== 检查完成 ==="
EOF

    # 上传并执行检查脚本
    if command -v sshpass &> /dev/null; then
        scp -o StrictHostKeyChecking=no server_check.sh "$SERVER_USER@$SERVER_IP:/tmp/"
        sshpass -p "$SERVER_PASS" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "bash /tmp/server_check.sh"
    else
        log_info "请手动上传并执行server_check.sh脚本"
    fi
    
    rm -f server_check.sh
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 创建数据库配置脚本
    cat > setup_db.sh << EOF
#!/bin/bash
echo "配置数据库..."

# 创建数据库
mysql -u root -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 检查数据库
mysql -u root -p$DB_PASS -e "SHOW DATABASES;" | grep $DB_NAME

if [ \$? -eq 0 ]; then
    echo "数据库 $DB_NAME 创建成功"
else
    echo "数据库创建失败"
    exit 1
fi
EOF

    # 上传并执行数据库配置
    if command -v sshpass &> /dev/null; then
        scp -o StrictHostKeyChecking=no setup_db.sh "$SERVER_USER@$SERVER_IP:/tmp/"
        sshpass -p "$SERVER_PASS" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "bash /tmp/setup_db.sh"
    else
        log_info "请手动执行数据库配置"
    fi
    
    rm -f setup_db.sh
}

# 上传项目文件
upload_project_files() {
    log_info "准备上传项目文件..."
    
    # 创建要排除的文件列表
    cat > .rsync_exclude << 'EOF'
.git/
.gitignore
node_modules/
*.log
.env
.DS_Store
Thumbs.db
*.tmp
*.bak
putty/
EOF

    log_info "开始上传项目文件到服务器..."
    
    if command -v rsync &> /dev/null && command -v sshpass &> /dev/null; then
        # 使用rsync同步文件
        sshpass -p "$SERVER_PASS" rsync -avz --exclude-from=.rsync_exclude \
            -e "ssh -o StrictHostKeyChecking=no" \
            ./ "$SERVER_USER@$SERVER_IP:$PROJECT_DIR/"
        
        if [ $? -eq 0 ]; then
            log_info "文件上传完成"
        else
            log_error "文件上传失败"
            return 1
        fi
    else
        log_warn "rsync或sshpass未安装，请手动上传文件"
        log_info "建议使用以下命令:"
        echo "scp -r ./* $SERVER_USER@$SERVER_IP:$PROJECT_DIR/"
    fi
    
    rm -f .rsync_exclude
}

# 设置文件权限
set_permissions() {
    log_info "设置文件权限..."
    
    # 创建权限设置脚本
    cat > set_permissions.sh << EOF
#!/bin/bash
cd $PROJECT_DIR

echo "设置文件所有者..."
chown -R www-data:www-data .

echo "设置目录权限..."
find . -type d -exec chmod 755 {} \;

echo "设置文件权限..."
find . -type f -exec chmod 644 {} \;

echo "设置uploads目录可写权限..."
chmod -R 777 uploads/ 2>/dev/null || echo "uploads目录不存在，跳过"

echo "权限设置完成"
EOF

    # 上传并执行权限设置
    if command -v sshpass &> /dev/null; then
        scp -o StrictHostKeyChecking=no set_permissions.sh "$SERVER_USER@$SERVER_IP:/tmp/"
        sshpass -p "$SERVER_PASS" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_IP" "bash /tmp/set_permissions.sh"
    else
        log_info "请手动设置文件权限"
    fi
    
    rm -f set_permissions.sh
}

# 主函数
main() {
    echo "开始自动部署流程..."
    echo
    
    # 检查本地环境
    check_local_environment
    
    # 测试连接
    if ! test_connection; then
        log_error "无法连接到服务器，请检查网络和服务器状态"
        exit 1
    fi
    
    # 检查服务器环境
    check_server_environment
    
    echo
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 配置数据库
    setup_database
    
    # 上传项目文件
    upload_project_files
    
    # 设置权限
    set_permissions
    
    log_info "自动部署完成！"
    log_info "请访问 http://$SERVER_IP 测试网站"
    
    echo
    echo "后续手动步骤："
    echo "1. 配置Nginx虚拟主机"
    echo "2. 导入数据库结构"
    echo "3. 测试网站功能"
    echo "4. 配置SSL证书"
}

# 执行主函数
main "$@"
