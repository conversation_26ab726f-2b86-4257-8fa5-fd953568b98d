<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区管理 - 比特熊智慧系统</title>
    <link rel="icon" href="image/bit.png" type="image/png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .header .back-btn:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .container {
            width: 100%;
            margin: 0;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }

        .management-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 0.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 1rem;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #666;
        }

        .tab-btn.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .tab-content.active {
            display: block;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }

        .page-header h2 {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #4a5568;
        }

        .btn-secondary:hover {
            background: #cbd5e0;
        }

        .btn-danger {
            background: #e53e3e;
            color: white;
        }

        .btn-danger:hover {
            background: #c53030;
        }

        .search-filter-bar {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .search-box {
            position: relative;
            flex: 1;
            min-width: 250px;
        }

        .search-box input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }

        .search-box svg {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }

        .filter-select {
            padding: 0.75rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            cursor: pointer;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-1px);
        }

        .stat-icon {
            width: 32px;
            height: 32px;
            margin: 0 auto 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-icon svg {
            width: 18px;
            height: 18px;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
            color: #333;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .posts-table-container,
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .posts-table,
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .posts-table th,
        .posts-table td,
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .posts-table th,
        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #4a5568;
        }

        .posts-table tbody tr:hover,
        .data-table tbody tr:hover {
            background: #f8fafc;
        }

        /* 评论管理特定样式 */
        .comment-content {
            max-width: 300px;
        }

        .comment-text {
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 0.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .reply-to {
            font-size: 0.8rem;
            color: #666;
            font-style: italic;
            padding: 0.25rem 0.5rem;
            background: #f1f5f9;
            border-radius: 4px;
            border-left: 3px solid #3b82f6;
        }

        .user-info {
            min-width: 120px;
        }

        .user-details {
            min-width: 0;
        }

        .user-name {
            font-weight: 500;
            color: #1e293b;
        }

        .user-meta {
            font-size: 0.8rem;
            color: #64748b;
        }

        /* 筛选和搜索区域样式 */
        .filters-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filters-row {
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 200px;
        }

        .filter-group label {
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s;
            background: white;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .filter-group input::placeholder {
            color: #9ca3af;
        }

        .filter-group select {
            cursor: pointer;
        }

        .post-info {
            max-width: 200px;
        }

        .post-title {
            color: #3b82f6;
            text-decoration: none;
            font-size: 0.9rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .post-title:hover {
            text-decoration: underline;
        }

        .comment-stats {
            display: flex;
            flex-direction: column;
            gap: 6px;
            min-width: 80px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;
            color: #6b7280;
        }

        .like-stat {
            color: #059669;
        }

        .like-stat svg {
            color: #059669;
        }

        .dislike-stat {
            color: #dc2626;
        }

        .dislike-stat svg {
            color: #dc2626;
        }

        .report-count {
            color: #f59e0b;
        }

        .report-count svg {
            color: #f59e0b;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.8rem;
            color: #64748b;
        }

        .stat-item svg {
            width: 14px;
            height: 14px;
        }

        .report-count {
            color: #dc2626;
            font-weight: 500;
        }

        .time-info {
            min-width: 120px;
            font-size: 0.85rem;
        }

        .created-time {
            color: #64748b;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            min-width: 100px;
        }

        .bulk-actions {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            padding: 1rem 2rem;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .bulk-actions-content {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .bulk-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-published {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-draft {
            background: #fed7d7;
            color: #742a2a;
        }

        .status-pending {
            background: #feebc8;
            color: #7b341e;
        }

        .status-rejected {
            background: #fed7d7;
            color: #742a2a;
        }

        .post-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-small {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
        }

        .btn-success {
            background: #38a169;
            color: white;
        }

        .btn-success:hover {
            background: #2f855a;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-numbers {
            display: flex;
            gap: 0.5rem;
        }

        .page-number {
            padding: 0.5rem 0.75rem;
            border: 1px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-number:hover {
            background: #f8fafc;
        }

        .page-number.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading, .no-data {
            text-align: center;
            padding: 3rem;
            color: #666;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .management-tabs {
                flex-direction: column;
            }

            .search-filter-bar {
                flex-direction: column;
            }

            .header {
                padding: 1rem;
            }

            .header h1 {
                font-size: 1.2rem;
            }

            .posts-table-container {
                overflow-x: auto;
            }

            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .post-actions {
                flex-direction: column;
            }
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .modal-header .close {
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .modal-header .close:hover {
            opacity: 1;
        }

        .modal-body {
            padding: 25px;
        }

        .modal-body .form-group {
            margin-bottom: 20px;
        }

        .modal-body label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .modal-body select,
        .modal-body textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }

        .modal-body select:focus,
        .modal-body textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modal-body textarea {
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .modal-footer {
            padding: 20px 25px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }

        .modal-footer .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
        }

        .modal-footer .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .modal-footer .btn-secondary:hover {
            background-color: #5a6268;
        }

        .modal-footer .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .modal-footer .btn-danger:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>社区管理</h1>
        <a href="admin-dashboard.php" class="back-btn">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2"/>
            </svg>
            返回仪表板
        </a>
    </div>

    <div class="container">
        <div class="management-tabs">
            <button class="tab-btn active" onclick="switchTab('posts')">帖子管理</button>
            <button class="tab-btn" onclick="switchTab('comments')">评论管理</button>
            <button class="tab-btn" onclick="switchTab('categories')">分类管理</button>
            <button class="tab-btn" onclick="switchTab('stats')">社区统计</button>
            <button class="tab-btn" onclick="switchTab('reports')">举报管理</button>
        </div>

        <!-- 帖子管理 -->
        <div id="posts-tab" class="tab-content active">
            <div class="page-header">
                <h2>帖子管理</h2>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="refreshPosts()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2"/>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M3.51 15A9 9 0 0 0 18.36 18.36L23 14" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        刷新
                    </button>
                    <button class="btn btn-secondary" onclick="exportPosts()">导出数据</button>
                    <button class="btn btn-primary" onclick="showAddPostModal()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        新建帖子
                    </button>
                </div>
            </div>

            <div class="search-filter-bar">
                <div class="search-box">
                    <input type="text" placeholder="搜索帖子标题、内容或作者..." id="postSearchInput">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <select class="filter-select" id="postStatusFilter">
                    <option value="all">全部状态</option>
                    <option value="published">已发布</option>
                    <option value="draft">草稿</option>
                    <option value="pending">待审核</option>
                </select>
                <select class="filter-select" id="postCategoryFilter">
                    <option value="all">全部分类</option>
                    <option value="tech">技术分享</option>
                    <option value="study">学习心得</option>
                    <option value="qa">问答求助</option>
                </select>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6A2 2 0 0 0 4 4V20A2 2 0 0 0 6 22H18A2 2 0 0 0 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-number" id="totalPosts">-</div>
                    <div class="stat-label">总帖子数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-number" id="categoriesCount">-</div>
                    <div class="stat-label">分类数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.54 21H20.46A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 9V13" stroke="currentColor" stroke-width="2"/>
                            <path d="M12 17H12.01" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-number" id="violationPosts">-</div>
                    <div class="stat-label">违规帖子数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-number" id="yesterdayPosts">-</div>
                    <div class="stat-label">昨日新增</div>
                </div>
            </div>

            <div id="postsContent">
                <div class="posts-table-container">
                    <table class="posts-table" id="postsTable">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>作者</th>
                                <th>分类</th>
                                <th>状态</th>
                                <th>发布时间</th>
                                <th>浏览量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="postsTableBody">
                            <!-- 帖子数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>

                    <div class="loading" id="postsLoading" style="display: none;">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>

                    <div class="no-data" id="postsNoData" style="display: none;">
                        <p>暂无帖子数据</p>
                    </div>
                </div>

                <!-- 打回帖子模态框 -->
                <div id="rejectModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>打回帖子</h3>
                            <span class="close" onclick="closeRejectModal()">&times;</span>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="rejectReason">打回原因：</label>
                                <select id="rejectReasonSelect" onchange="handleReasonChange()">
                                    <option value="">请选择打回原因</option>
                                    <option value="内容违规">内容违规</option>
                                    <option value="标题不当">标题不当</option>
                                    <option value="垃圾信息">垃圾信息</option>
                                    <option value="重复发帖">重复发帖</option>
                                    <option value="格式错误">格式错误</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="rejectReason">详细说明：</label>
                                <textarea id="rejectReason" rows="4" placeholder="请详细说明打回原因，帮助用户改进内容..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-secondary" onclick="closeRejectModal()">取消</button>
                            <button class="btn btn-danger" onclick="confirmReject()">确认打回</button>
                        </div>
                    </div>
                </div>

                <div class="pagination-container" id="postsPagination">
                    <div class="pagination-info">
                        <span id="postsPageInfo">第 1 页，共 1 页</span>
                    </div>
                    <div class="pagination-controls">
                        <button class="btn btn-secondary" id="postsPrevBtn" onclick="changePage('prev')" disabled>上一页</button>
                        <div class="page-numbers" id="postsPageNumbers"></div>
                        <button class="btn btn-secondary" id="postsNextBtn" onclick="changePage('next')" disabled>下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他标签页内容将通过JavaScript动态加载 -->
        <div id="comments-tab" class="tab-content">
            <div class="page-header">
                <h2>评论管理</h2>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="exportComments()">导出数据</button>
                    <button class="btn btn-primary" onclick="refreshComments()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M1 4V10H7M23 20V14H17" stroke="currentColor" stroke-width="2"/>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 筛选和搜索 -->
            <div class="filters-section">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>状态筛选：</label>
                        <select id="commentStatusFilter" onchange="filterComments()">
                            <option value="">全部状态</option>
                            <option value="published">已发布</option>
                            <option value="hidden">已隐藏</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>搜索：</label>
                        <input type="text" id="commentSearchInput" placeholder="搜索评论内容、用户名或帖子标题..." onkeyup="searchComments(event)" oninput="searchComments(event)">
                    </div>
                </div>
            </div>

            <!-- 评论统计 -->
            <div class="stats-grid" id="commentStats">
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15A2 2 0 0 1 19 17H7L4 20V5A2 2 0 0 1 6 3H19A2 2 0 0 1 21 5Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalComments">0</div>
                        <div class="stat-label">总评论数</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="publishedComments">0</div>
                        <div class="stat-label">已发布</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M18.364 18.364A9 9 0 0 0 5.636 5.636M12 17L17 12L12 7M7 12H17" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="hiddenComments">0</div>
                        <div class="stat-label">已隐藏</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" stroke-width="2"/>
                            <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="reportedComments">0</div>
                        <div class="stat-label">被举报</div>
                    </div>
                </div>
            </div>

            <!-- 评论列表 -->
            <div class="table-container">
                <table class="data-table" id="commentsTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAllComments" onchange="toggleSelectAllComments()">
                            </th>
                            <th>评论内容</th>
                            <th>用户</th>
                            <th>帖子</th>
                            <th>状态</th>
                            <th>统计</th>
                            <th>时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="commentsTableBody">
                        <!-- 评论数据将通过JavaScript加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 批量操作 -->
            <div class="bulk-actions" id="commentsBulkActions" style="display: none;">
                <div class="bulk-actions-content">
                    <span id="selectedCommentsCount">已选择 0 条评论</span>
                    <div class="bulk-buttons">
                        <button class="btn btn-success" onclick="bulkPublishComments()">批量发布</button>
                        <button class="btn btn-warning" onclick="bulkHideComments()">批量隐藏</button>
                        <button class="btn btn-danger" onclick="bulkDeleteComments()">批量删除</button>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    <span id="commentsInfo">显示第 1-10 条，共 0 条记录</span>
                </div>
                <div class="pagination-controls">
                    <button class="btn btn-secondary" id="commentsPrevBtn" onclick="changeCommentsPage('prev')" disabled>上一页</button>
                    <div class="page-numbers" id="commentsPageNumbers"></div>
                    <button class="btn btn-secondary" id="commentsNextBtn" onclick="changeCommentsPage('next')" disabled>下一页</button>
                </div>
            </div>
        </div>

        <div id="categories-tab" class="tab-content">
            <div class="page-header">
                <h2>分类管理</h2>
            </div>
            <p style="text-align: center; color: #666; padding: 2rem;">分类管理功能正在开发中...</p>
        </div>

        <div id="stats-tab" class="tab-content">
            <div class="page-header">
                <h2>社区统计</h2>
            </div>
            <p style="text-align: center; color: #666; padding: 2rem;">社区统计功能正在开发中...</p>
        </div>

        <div id="reports-tab" class="tab-content">
            <div class="page-header">
                <h2>举报管理</h2>
            </div>
            <p style="text-align: center; color: #666; padding: 2rem;">举报管理功能正在开发中...</p>
        </div>
    </div>

    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {
            search: '',
            status: 'all',
            category: 'all'
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载帖子管理数据
            loadPostsStats();
            loadPostsList();

            // 绑定搜索和筛选事件
            const searchInput = document.getElementById('postSearchInput');
            const statusFilter = document.getElementById('postStatusFilter');
            const categoryFilter = document.getElementById('postCategoryFilter');

            if (searchInput) {
                searchInput.addEventListener('input', debounce(handleSearch, 500));
            }
            if (statusFilter) {
                statusFilter.addEventListener('change', handleFilter);
            }
            if (categoryFilter) {
                categoryFilter.addEventListener('change', handleFilter);
            }
        });

        function switchTab(tabName) {
            // 隐藏所有标签页内容
            const allTabs = document.querySelectorAll('.tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 移除所有按钮的active状态
            const allBtns = document.querySelectorAll('.tab-btn');
            allBtns.forEach(btn => btn.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活对应的按钮
            event.target.classList.add('active');

            // 如果切换到帖子管理，重新加载数据
            if (tabName === 'posts') {
                setTimeout(() => {
                    loadPostsStats();
                    loadPostsList();
                }, 100);
            }
        }

        // 加载帖子统计数据
        function loadPostsStats() {
            console.log('开始加载统计数据...');
            fetch('api/posts-management.php?action=stats')
                .then(response => {
                    console.log('统计数据响应状态:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('统计数据响应:', data);
                    if (data.success) {
                        document.getElementById('totalPosts').textContent = data.data.totalPosts;
                        document.getElementById('categoriesCount').textContent = data.data.categoriesCount;
                        document.getElementById('violationPosts').textContent = data.data.violationPosts;
                        document.getElementById('yesterdayPosts').textContent = data.data.yesterdayPosts;
                    } else {
                        console.error('加载统计数据失败:', data.error);
                        // 显示错误信息
                        document.getElementById('totalPosts').textContent = 'Error';
                        document.getElementById('categoriesCount').textContent = 'Error';
                        document.getElementById('violationPosts').textContent = 'Error';
                        document.getElementById('yesterdayPosts').textContent = 'Error';
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    // 显示错误信息
                    document.getElementById('totalPosts').textContent = 'Error';
                    document.getElementById('categoriesCount').textContent = 'Error';
                    document.getElementById('violationPosts').textContent = 'Error';
                    document.getElementById('yesterdayPosts').textContent = 'Error';
                });
        }

        // 加载帖子列表
        function loadPostsList() {
            showLoading(true);

            const params = new URLSearchParams({
                action: 'list',
                page: currentPage,
                search: currentFilters.search,
                status: currentFilters.status,
                category: currentFilters.category
            });

            fetch(`api/posts-management.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    showLoading(false);
                    if (data.success) {
                        renderPostsList(data.data.posts);
                        updatePagination(data.data.pagination);
                    } else {
                        showNoData(true);
                        console.error('加载帖子列表失败:', data.error);
                    }
                })
                .catch(error => {
                    showLoading(false);
                    showNoData(true);
                    console.error('请求失败:', error);
                });
        }

        // 渲染帖子列表
        function renderPostsList(posts) {
            const tbody = document.getElementById('postsTableBody');

            if (!posts || posts.length === 0) {
                showNoData(true);
                return;
            }

            showNoData(false);

            tbody.innerHTML = posts.map(post => `
                <tr>
                    <td>
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${post.title}">
                            ${post.is_pinned ? '<i class="fas fa-thumbtack" style="color: #ff6b6b; margin-right: 5px;" title="置顶"></i>' : ''}
                            ${post.title}
                        </div>
                        ${post.status === 'rejected' && post.rejection_reason ? `
                            <div style="font-size: 12px; color: #dc3545; margin-top: 2px;">
                                <i class="fas fa-exclamation-triangle"></i> ${post.rejection_reason}
                                ${post.rejected_at ? `<br><small>打回时间: ${post.rejected_at}</small>` : ''}
                                ${post.rejected_by_name ? `<br><small>操作者: ${post.rejected_by_name}</small>` : ''}
                            </div>
                        ` : ''}
                    </td>
                    <td>${post.author_name}</td>
                    <td>${post.category_name}</td>
                    <td>
                        <span class="status-badge status-${post.status}">
                            ${getStatusText(post.status)}
                        </span>
                    </td>
                    <td>${post.created_at}</td>
                    <td>${post.views}</td>
                    <td>
                        <div class="post-actions">
                            <button class="btn btn-small btn-primary" onclick="viewPost(${post.id})">查看</button>
                            <button class="btn btn-small btn-danger" onclick="deletePost(${post.id})">删除</button>
                            <button class="btn btn-small ${post.is_pinned ? 'btn-warning' : 'btn-info'}" onclick="togglePin(${post.id}, ${post.is_pinned})">
                                ${post.is_pinned ? '取消置顶' : '置顶'}
                            </button>
                            ${post.status !== 'rejected' ? `<button class="btn btn-small btn-danger" onclick="showRejectModal(${post.id})">打回</button>` : ''}
                            ${post.status === 'pending' ? `<button class="btn btn-small btn-success" onclick="approvePost(${post.id})">通过</button>` : ''}
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'published': '已发布',
                'draft': '草稿',
                'pending': '待审核',
                'rejected': '已打回'
            };
            return statusMap[status] || status;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            document.getElementById('postsLoading').style.display = show ? 'block' : 'none';
            document.getElementById('postsTable').style.display = show ? 'none' : 'table';
        }

        // 显示/隐藏无数据状态
        function showNoData(show) {
            document.getElementById('postsNoData').style.display = show ? 'block' : 'none';
            document.getElementById('postsTable').style.display = show ? 'none' : 'table';
        }

        // 更新分页
        function updatePagination(pagination) {
            currentPage = pagination.currentPage;
            totalPages = pagination.totalPages;

            document.getElementById('postsPageInfo').textContent =
                `第 ${pagination.currentPage} 页，共 ${pagination.totalPages} 页 (总计 ${pagination.totalCount} 条)`;

            // 更新按钮状态
            document.getElementById('postsPrevBtn').disabled = currentPage <= 1;
            document.getElementById('postsNextBtn').disabled = currentPage >= totalPages;

            // 生成页码
            generatePageNumbers(pagination);
        }

        // 生成页码按钮
        function generatePageNumbers(pagination) {
            const container = document.getElementById('postsPageNumbers');
            const maxVisible = 5;
            let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
            let end = Math.min(totalPages, start + maxVisible - 1);

            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }

            let html = '';
            for (let i = start; i <= end; i++) {
                html += `<button class="page-number ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }

            container.innerHTML = html;
        }

        // 跳转到指定页面
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadPostsList();
            }
        }

        // 翻页
        function changePage(direction) {
            if (direction === 'prev' && currentPage > 1) {
                currentPage--;
                loadPostsList();
            } else if (direction === 'next' && currentPage < totalPages) {
                currentPage++;
                loadPostsList();
            }
        }

        // 搜索处理
        function handleSearch(event) {
            currentFilters.search = event.target.value;
            currentPage = 1;
            loadPostsList();
        }

        // 筛选处理
        function handleFilter(event) {
            const filterId = event.target.id;
            if (filterId === 'postStatusFilter') {
                currentFilters.status = event.target.value;
            } else if (filterId === 'postCategoryFilter') {
                currentFilters.category = event.target.value;
            }
            currentPage = 1;
            loadPostsList();
        }

        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 帖子管理功能
        function viewPost(postId) {
            // 跳转到社区帖子详情页
            window.open(`community-post-detail.php?id=${postId}`, '_blank');
        }

        function deletePost(postId) {
            if (confirm('确定要删除这个帖子吗？删除后无法恢复。')) {
                fetch('api/posts-management.php?action=delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        postId: postId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('帖子已删除');
                        loadPostsList();
                        loadPostsStats();
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('删除失败，请重试');
                });
            }
        }

        function togglePin(postId, currentPinned) {
            const action = currentPinned ? '取消置顶' : '置顶';
            if (confirm(`确定要${action}这个帖子吗？`)) {
                fetch('api/posts-management.php?action=pin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        postId: postId,
                        isPinned: !currentPinned
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        loadPostsList();
                    } else {
                        alert('操作失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('操作失败，请重试');
                });
            }
        }

        function approvePost(postId) {
            if (confirm('确定要通过这篇帖子吗？')) {
                updatePostStatus(postId, 'published');
            }
        }

        function showRejectModal(postId) {
            const reason = prompt('请输入打回原因：');
            if (reason && reason.trim()) {
                rejectPost(postId, reason.trim());
            }
        }

        // 更新帖子状态
        function updatePostStatus(postId, status) {
            fetch('api/posts-management.php?action=update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    postId: postId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('状态更新成功');
                    loadPostsList();
                    loadPostsStats();
                } else {
                    alert('更新失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                alert('更新失败，请重试');
            });
        }

        // 打回帖子相关功能
        let currentRejectPostId = null;

        function showRejectModal(postId) {
            currentRejectPostId = postId;
            document.getElementById('rejectModal').style.display = 'block';
            document.getElementById('rejectReasonSelect').value = '';
            document.getElementById('rejectReason').value = '';
        }

        function closeRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
            currentRejectPostId = null;
        }

        function handleReasonChange() {
            const select = document.getElementById('rejectReasonSelect');
            const textarea = document.getElementById('rejectReason');

            if (select.value && select.value !== '其他') {
                textarea.value = select.value + '：';
                textarea.focus();
                // 将光标移到末尾
                textarea.setSelectionRange(textarea.value.length, textarea.value.length);
            }
        }

        function confirmReject() {
            const reason = document.getElementById('rejectReason').value.trim();

            if (!reason) {
                alert('请填写打回原因');
                return;
            }

            if (reason.length < 5) {
                alert('打回原因至少需要5个字符');
                return;
            }

            fetch('api/posts-management.php?action=reject', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    postId: currentRejectPostId,
                    reason: reason,
                    adminId: 1 // 这里应该从session获取当前管理员ID
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('帖子已打回，已通知用户');
                    closeRejectModal();
                    loadPostsList();
                    loadPostsStats();
                } else {
                    alert('打回失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                alert('打回失败，请重试');
            });
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('rejectModal');
            if (event.target === modal) {
                closeRejectModal();
            }
        }

        // 刷新帖子列表
        function refreshPosts() {
            console.log('刷新帖子列表...');
            // 重置筛选条件
            document.getElementById('postSearchInput').value = '';
            document.getElementById('postStatusFilter').value = 'all';
            document.getElementById('postCategoryFilter').value = 'all';
            currentPostsFilters = { search: '', status: 'all', category: 'all' };
            currentPostsPage = 1;

            loadPostsStats();
            loadPostsList();
        }

        // 导出功能
        function exportPosts() {
            alert('导出功能正在开发中...');
        }

        function showAddPostModal() {
            alert('新建帖子功能正在开发中...');
        }

        // ==================== 评论管理功能 ====================
        let currentCommentsPage = 1;
        let totalCommentsPages = 1;
        let currentCommentsFilters = {
            search: '',
            status: ''
        };

        // 加载评论列表
        function loadCommentsList() {
            const params = new URLSearchParams({
                page: currentCommentsPage,
                search: currentCommentsFilters.search,
                status: currentCommentsFilters.status
            });

            console.log('正在加载评论列表...', params.toString());

            fetch(`api/comments-management.php?action=list&${params}`)
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('API原始响应:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            displayCommentsList(data.comments);
                            updateCommentsPagination(data.pagination);
                        } else {
                            console.error('加载评论失败:', data);
                            document.getElementById('commentsTableBody').innerHTML =
                                `<tr><td colspan="8" style="text-align: center; color: #666;">加载评论失败: ${data.error}</td></tr>`;
                        }
                    } catch (parseError) {
                        console.error('JSON解析失败:', parseError, 'Raw text:', text);
                        document.getElementById('commentsTableBody').innerHTML =
                            '<tr><td colspan="8" style="text-align: center; color: #666;">服务器响应格式错误</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    document.getElementById('commentsTableBody').innerHTML =
                        `<tr><td colspan="8" style="text-align: center; color: #666;">网络错误: ${error.message}</td></tr>`;
                });
        }

        // 加载评论统计
        function loadCommentsStats() {
            console.log('正在加载评论统计...');

            fetch('api/comments-management.php?action=stats')
                .then(response => {
                    console.log('统计API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('统计API原始响应:', text);
                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            document.getElementById('totalComments').textContent = data.stats.total || 0;
                            document.getElementById('publishedComments').textContent = data.stats.published || 0;
                            document.getElementById('hiddenComments').textContent = data.stats.hidden || 0;
                            document.getElementById('reportedComments').textContent = data.stats.reported || 0;
                        } else {
                            console.error('加载统计失败:', data);
                        }
                    } catch (parseError) {
                        console.error('统计JSON解析失败:', parseError, 'Raw text:', text);
                    }
                })
                .catch(error => {
                    console.error('加载统计失败:', error);
                });
        }

        // 显示评论列表
        function displayCommentsList(comments) {
            const tbody = document.getElementById('commentsTableBody');

            if (!comments || comments.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">暂无评论数据</td></tr>';
                return;
            }

            tbody.innerHTML = comments.map(comment => `
                <tr>
                    <td>
                        <input type="checkbox" class="comment-checkbox" value="${comment.id}" onchange="updateCommentsBulkActions()">
                    </td>
                    <td>
                        <div class="comment-content">
                            <div class="comment-text">${escapeHtml(comment.content)}</div>
                            ${comment.parent_id ? `<div class="reply-to">回复: ${escapeHtml(comment.parent_content || '原评论已删除')}</div>` : ''}
                        </div>
                    </td>
                    <td>
                        <div class="user-info">
                            <div class="user-details">
                                <div class="user-name">${escapeHtml(comment.nickname || comment.username)}</div>
                                <div class="user-meta">@${escapeHtml(comment.username)}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="post-info">
                            <a href="community-post-detail.php?id=${comment.post_id}" target="_blank" class="post-title">
                                ${escapeHtml(comment.post_title)}
                            </a>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge status-${comment.status}">
                            ${comment.status === 'published' ? '已发布' : '已隐藏'}
                        </span>
                    </td>
                    <td>
                        <div class="comment-stats">
                            <span class="stat-item like-stat">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M7 14L12 9L17 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                ${comment.like_count || 0}
                            </span>
                            <span class="stat-item dislike-stat">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                ${comment.dislike_count || 0}
                            </span>
                            ${comment.report_count > 0 ? `
                                <span class="stat-item report-count">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <path d="M10.29 3.86L1.82 18A2 2 0 0 0 3.64 21H20.36A2 2 0 0 0 22.18 18L13.71 3.86A2 2 0 0 0 10.29 3.86Z" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                    ${comment.report_count}
                                </span>
                            ` : ''}
                        </div>
                    </td>
                    <td>
                        <div class="time-info">
                            <div class="created-time">${formatDateTime(comment.created_at)}</div>
                        </div>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-info" onclick="viewCommentInPost(${comment.id}, ${comment.post_id})" title="查看">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                            ${comment.status === 'published' ?
                                `<button class="btn btn-sm btn-warning" onclick="hideComment(${comment.id})" title="隐藏">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.7 18.7 0 0 1 6.06 6.06L17.94 17.94Z" stroke="currentColor" stroke-width="2"/>
                                        <path d="M1 1L23 23" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>` :
                                `<button class="btn btn-sm btn-success" onclick="publishComment(${comment.id})" title="发布">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                        <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>`
                            }
                            <button class="btn btn-sm btn-danger" onclick="deleteComment(${comment.id})" title="删除">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                    <path d="M3 6H5H21" stroke="currentColor" stroke-width="2"/>
                                    <path d="M8 6V4A2 2 0 0 1 10 2H14A2 2 0 0 1 16 4V6M19 6V20A2 2 0 0 1 17 22H7A2 2 0 0 1 5 20V6H19Z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // 更新评论分页
        function updateCommentsPagination(pagination) {
            totalCommentsPages = pagination.totalPages;

            // 更新信息显示
            const start = (pagination.currentPage - 1) * pagination.perPage + 1;
            const end = Math.min(start + pagination.perPage - 1, pagination.totalRecords);
            document.getElementById('commentsInfo').textContent =
                `显示第 ${start}-${end} 条，共 ${pagination.totalRecords} 条记录`;

            // 更新按钮状态
            document.getElementById('commentsPrevBtn').disabled = pagination.currentPage <= 1;
            document.getElementById('commentsNextBtn').disabled = pagination.currentPage >= pagination.totalPages;

            // 更新页码
            const pageNumbers = document.getElementById('commentsPageNumbers');
            pageNumbers.innerHTML = '';

            const startPage = Math.max(1, pagination.currentPage - 2);
            const endPage = Math.min(pagination.totalPages, pagination.currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `btn btn-sm ${i === pagination.currentPage ? 'btn-primary' : 'btn-secondary'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => {
                    currentCommentsPage = i;
                    loadCommentsList();
                };
                pageNumbers.appendChild(pageBtn);
            }
        }

        // 评论分页控制
        function changeCommentsPage(direction) {
            if (direction === 'prev' && currentCommentsPage > 1) {
                currentCommentsPage--;
                loadCommentsList();
            } else if (direction === 'next' && currentCommentsPage < totalCommentsPages) {
                currentCommentsPage++;
                loadCommentsList();
            }
        }

        // 筛选评论
        function filterComments() {
            currentCommentsFilters.status = document.getElementById('commentStatusFilter').value;
            currentCommentsPage = 1;
            loadCommentsList();
        }

        // 搜索防抖定时器
        let searchTimeout;

        // 搜索评论
        function searchComments(event) {
            if (event.key === 'Enter') {
                // 立即搜索
                clearTimeout(searchTimeout);
                currentCommentsFilters.search = event.target.value.trim();
                currentCommentsPage = 1;
                loadCommentsList();
            } else if (event.type === 'input') {
                // 防抖搜索
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    currentCommentsFilters.search = event.target.value.trim();
                    currentCommentsPage = 1;
                    loadCommentsList();
                }, 500);
            }
        }

        // 刷新评论
        function refreshComments() {
            console.log('刷新评论列表...');
            // 重置筛选条件
            document.getElementById('commentStatusFilter').value = '';
            document.getElementById('commentSearchInput').value = '';
            currentCommentsFilters = { status: '', search: '' };
            currentCommentsPage = 1;

            loadCommentsList();
            loadCommentsStats();
        }

        // 查看评论在帖子中的位置
        function viewCommentInPost(commentId, postId) {
            // 跳转到帖子详情页，并定位到对应评论
            const url = `community-post-detail.php?id=${postId}&comment=${commentId}`;
            window.open(url, '_blank');
        }

        // 导出评论
        function exportComments() {
            alert('导出评论功能正在开发中...');
        }

        // 发布评论
        function publishComment(commentId) {
            if (confirm('确定要发布这条评论吗？')) {
                updateCommentStatus(commentId, 'published');
            }
        }

        // 隐藏评论
        function hideComment(commentId) {
            if (confirm('确定要隐藏这条评论吗？')) {
                updateCommentStatus(commentId, 'hidden');
            }
        }

        // 删除评论
        function deleteComment(commentId) {
            if (confirm('确定要删除这条评论吗？此操作不可恢复！')) {
                fetch('api/comments-management.php?action=delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        commentId: commentId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('评论删除成功');
                        loadCommentsList();
                        loadCommentsStats();
                    } else {
                        alert('删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('删除失败，请重试');
                });
            }
        }

        // 更新评论状态
        function updateCommentStatus(commentId, status) {
            fetch('api/comments-management.php?action=update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    commentId: commentId,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('状态更新成功');
                    loadCommentsList();
                    loadCommentsStats();
                } else {
                    alert('更新失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                alert('更新失败，请重试');
            });
        }

        // 批量操作相关
        function toggleSelectAllComments() {
            const selectAll = document.getElementById('selectAllComments');
            const checkboxes = document.querySelectorAll('.comment-checkbox');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });

            updateCommentsBulkActions();
        }

        function updateCommentsBulkActions() {
            const checkboxes = document.querySelectorAll('.comment-checkbox:checked');
            const bulkActions = document.getElementById('commentsBulkActions');
            const countSpan = document.getElementById('selectedCommentsCount');

            if (checkboxes.length > 0) {
                bulkActions.style.display = 'block';
                countSpan.textContent = `已选择 ${checkboxes.length} 条评论`;
            } else {
                bulkActions.style.display = 'none';
            }
        }

        function bulkPublishComments() {
            const selectedIds = Array.from(document.querySelectorAll('.comment-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('请先选择要发布的评论');
                return;
            }

            if (confirm(`确定要发布选中的 ${selectedIds.length} 条评论吗？`)) {
                bulkUpdateCommentsStatus(selectedIds, 'published');
            }
        }

        function bulkHideComments() {
            const selectedIds = Array.from(document.querySelectorAll('.comment-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('请先选择要隐藏的评论');
                return;
            }

            if (confirm(`确定要隐藏选中的 ${selectedIds.length} 条评论吗？`)) {
                bulkUpdateCommentsStatus(selectedIds, 'hidden');
            }
        }

        function bulkDeleteComments() {
            const selectedIds = Array.from(document.querySelectorAll('.comment-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedIds.length === 0) {
                alert('请先选择要删除的评论');
                return;
            }

            if (confirm(`确定要删除选中的 ${selectedIds.length} 条评论吗？此操作不可恢复！`)) {
                fetch('api/comments-management.php?action=bulk_delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        commentIds: selectedIds
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`成功删除 ${data.deletedCount} 条评论`);
                        document.getElementById('selectAllComments').checked = false;
                        loadCommentsList();
                        loadCommentsStats();
                        updateCommentsBulkActions();
                    } else {
                        alert('批量删除失败: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('请求失败:', error);
                    alert('批量删除失败，请重试');
                });
            }
        }

        function bulkUpdateCommentsStatus(commentIds, status) {
            fetch('api/comments-management.php?action=bulk_update_status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    commentIds: commentIds,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`成功更新 ${data.updatedCount} 条评论状态`);
                    document.getElementById('selectAllComments').checked = false;
                    loadCommentsList();
                    loadCommentsStats();
                    updateCommentsBulkActions();
                } else {
                    alert('批量更新失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                alert('批量更新失败，请重试');
            });
        }

        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 修改switchTab函数以支持评论管理
        function switchTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // 移除所有按钮的active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活对应的按钮
            event.target.classList.add('active');

            // 根据标签页加载对应数据
            if (tabName === 'posts') {
                loadPostsList();
                loadPostsStats();
            } else if (tabName === 'comments') {
                loadCommentsList();
                loadCommentsStats();
            }
        }
    </script>
</body>
</html>
