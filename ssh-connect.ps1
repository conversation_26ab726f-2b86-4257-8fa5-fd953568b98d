# SSH Connection Script for Tencent Cloud Server
# Server: CentOS 7.8 64bit

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Tencent Cloud Server SSH Connection" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$username = "root"
$password = "ZbDX7%=]?H2(LAUz"

Write-Host "Server Information:" -ForegroundColor Yellow
Write-Host "  IP Address: $serverIP" -ForegroundColor White
Write-Host "  Username: $username" -ForegroundColor White
Write-Host "  OS: CentOS 7.8 64bit" -ForegroundColor White
Write-Host ""

# Check for SSH clients
Write-Host "Checking for SSH clients..." -ForegroundColor Yellow
Write-Host ""

$sshFound = $false
$sshPath = ""

# Method 1: Check Windows built-in SSH
if (Test-Path "C:\Windows\System32\OpenSSH\ssh.exe") {
    Write-Host "✅ Windows built-in SSH found" -ForegroundColor Green
    $sshPath = "C:\Windows\System32\OpenSSH\ssh.exe"
    $sshFound = $true
}
# Method 2: Check Git SSH
elseif (Test-Path "C:\Program Files\Git\usr\bin\ssh.exe") {
    Write-Host "✅ Git SSH found" -ForegroundColor Green
    $sshPath = "C:\Program Files\Git\usr\bin\ssh.exe"
    $sshFound = $true
}
# Method 3: Check system PATH SSH
else {
    try {
        $null = Get-Command ssh -ErrorAction Stop
        Write-Host "✅ SSH found in system PATH" -ForegroundColor Green
        $sshPath = "ssh"
        $sshFound = $true
    } catch {
        Write-Host "❌ SSH client not found" -ForegroundColor Red
    }
}

if (-not $sshFound) {
    Write-Host ""
    Write-Host "SSH client installation options:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Install Windows OpenSSH:" -ForegroundColor White
    Write-Host "   - Open Settings > Apps > Optional Features" -ForegroundColor Gray
    Write-Host "   - Click 'Add a feature'" -ForegroundColor Gray
    Write-Host "   - Search and install 'OpenSSH Client'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Install Git for Windows:" -ForegroundColor White
    Write-Host "   - Download from: https://git-scm.com/download/win" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Use PuTTY:" -ForegroundColor White
    Write-Host "   - Download from: https://www.putty.org/" -ForegroundColor Gray
    Write-Host ""
    pause
    exit 1
}

Write-Host ""
Write-Host "Connection options:" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Connect with password (interactive)" -ForegroundColor White
Write-Host "2. Show connection command" -ForegroundColor White
Write-Host "3. Test connection" -ForegroundColor White
Write-Host "4. Generate SSH key pair" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Select option (1-4)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "Connecting to server..." -ForegroundColor Green
        Write-Host "Password: $password" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Note: Password will not be visible when typing" -ForegroundColor Gray
        Write-Host ""
        
        # Connect using SSH
        if ($sshPath -eq "ssh") {
            & ssh "$username@$serverIP"
        } else {
            & "$sshPath" "$username@$serverIP"
        }
    }
    
    "2" {
        Write-Host ""
        Write-Host "SSH Connection Command:" -ForegroundColor Green
        Write-Host ""
        if ($sshPath -eq "ssh") {
            Write-Host "ssh $username@$serverIP" -ForegroundColor Cyan
        } else {
            Write-Host "`"$sshPath`" $username@$serverIP" -ForegroundColor Cyan
        }
        Write-Host ""
        Write-Host "Password: $password" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "Copy the command above and run it in your terminal" -ForegroundColor Gray
    }
    
    "3" {
        Write-Host ""
        Write-Host "Testing connection..." -ForegroundColor Green
        
        try {
            $testResult = Test-NetConnection -ComputerName $serverIP -Port 22 -WarningAction SilentlyContinue
            if ($testResult.TcpTestSucceeded) {
                Write-Host "✅ SSH port 22 is accessible" -ForegroundColor Green
                Write-Host "Server is ready for SSH connection" -ForegroundColor Green
            } else {
                Write-Host "❌ SSH port 22 is not accessible" -ForegroundColor Red
                Write-Host "Check server firewall settings" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "❌ Connection test failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    "4" {
        Write-Host ""
        Write-Host "Generating SSH key pair..." -ForegroundColor Green
        Write-Host ""
        
        $keyPath = "$env:USERPROFILE\.ssh\id_rsa"
        $keyDir = Split-Path $keyPath -Parent
        
        # Create .ssh directory if it doesn't exist
        if (-not (Test-Path $keyDir)) {
            New-Item -ItemType Directory -Path $keyDir -Force | Out-Null
            Write-Host "Created .ssh directory: $keyDir" -ForegroundColor Green
        }
        
        # Generate SSH key
        if ($sshPath -eq "ssh") {
            & ssh-keygen -t rsa -b 4096 -f $keyPath -N '""'
        } else {
            $sshKeygenPath = Join-Path (Split-Path $sshPath -Parent) "ssh-keygen.exe"
            if (Test-Path $sshKeygenPath) {
                & "$sshKeygenPath" -t rsa -b 4096 -f $keyPath -N '""'
            } else {
                Write-Host "❌ ssh-keygen not found" -ForegroundColor Red
                Write-Host "Please use option 1 for password authentication" -ForegroundColor Yellow
            }
        }
        
        if (Test-Path "$keyPath.pub") {
            Write-Host ""
            Write-Host "✅ SSH key pair generated successfully!" -ForegroundColor Green
            Write-Host "Private key: $keyPath" -ForegroundColor Gray
            Write-Host "Public key: $keyPath.pub" -ForegroundColor Gray
            Write-Host ""
            Write-Host "To use key-based authentication:" -ForegroundColor Yellow
            Write-Host "1. Copy the public key to the server:" -ForegroundColor White
            Write-Host "   ssh-copy-id $username@$serverIP" -ForegroundColor Cyan
            Write-Host "2. Or manually add the public key to ~/.ssh/authorized_keys on the server" -ForegroundColor White
        }
    }
    
    default {
        Write-Host "Invalid option selected" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Connection Information Summary:" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow
Write-Host "Server IP: $serverIP" -ForegroundColor White
Write-Host "Username: $username" -ForegroundColor White
Write-Host "Password: $password" -ForegroundColor White
Write-Host "SSH Port: 22 (default)" -ForegroundColor White
Write-Host ""
Write-Host "Useful SSH commands after connection:" -ForegroundColor Green
Write-Host "  ls -la          # List files" -ForegroundColor Gray
Write-Host "  pwd             # Show current directory" -ForegroundColor Gray
Write-Host "  cd /var/www     # Change to web directory" -ForegroundColor Gray
Write-Host "  systemctl status httpd    # Check Apache status" -ForegroundColor Gray
Write-Host "  systemctl status nginx    # Check Nginx status" -ForegroundColor Gray
Write-Host "  netstat -tlnp   # Show listening ports" -ForegroundColor Gray
Write-Host "  exit            # Disconnect from server" -ForegroundColor Gray
Write-Host ""

pause
