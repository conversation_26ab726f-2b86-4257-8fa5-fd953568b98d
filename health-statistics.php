<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
require_once 'config/database.php';

// 检查登录状态 - 兼容两种认证方式
$isAuthenticated = false;
$currentUser = null;

// 方式1: 新的Auth类认证
if (class_exists('Auth')) {
    require_once 'classes/Auth.php';
    $auth = new Auth();
    if ($auth->isLoggedIn() && $auth->isAdmin()) {
        $isAuthenticated = true;
        $currentUser = $auth->getCurrentUser();
    }
}

// 方式2: 管理后台认证（回退方案）
if (!$isAuthenticated && isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $isAuthenticated = true;
    $currentUser = [
        'id' => $_SESSION['user_id'] ?? 1,
        'username' => $_SESSION['admin_user'] ?? 'admin',
        'role_code' => $_SESSION['user_type'] ?? 'admin',
        'full_name' => '管理员'
    ];
}

if (!$isAuthenticated) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康统计分析 - 比特熊智慧系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #10b981;
            --secondary-color: #059669;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --success-color: #10b981;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
        }

        body {
            background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: auto;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-title {
            margin: 0;
            color: white;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .page-description {
            margin: 0.5rem 0 0 0;
            color: rgba(255, 255, 255, 0.8);
        }

        .controls-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .date-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .date-selector label {
            font-weight: 500;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .date-selector select {
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .date-selector select option {
            background: rgba(0, 0, 0, 0.8);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
        }

        .stat-card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-card-title {
            font-weight: 600;
            color: white;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .stat-card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-card-icon.primary {
            background: var(--primary-color);
        }

        .stat-card-icon.info {
            background: var(--info-color);
        }

        .stat-card-icon.warning {
            background: var(--warning-color);
        }

        .stat-card-icon.danger {
            background: var(--danger-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .summary-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .summary-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
            margin-bottom: 0.25rem;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .summary-label {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .trend-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .trend-up {
            color: var(--danger-color);
        }

        .trend-down {
            color: var(--success-color);
        }

        .trend-stable {
            color: #6b7280;
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: rgba(16, 185, 129, 0.3);
            border-color: rgba(16, 185, 129, 0.5);
        }

        .btn-primary:hover {
            background: rgba(16, 185, 129, 0.5);
        }

        .btn-secondary {
            background: rgba(107, 114, 128, 0.3);
            border-color: rgba(107, 114, 128, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(107, 114, 128, 0.5);
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: #64748b;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: #64748b;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .date-selector {
                flex-direction: column;
                align-items: flex-start;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">健康统计分析</h1>
            <p class="page-description">分析健康数据趋势，提供周统计和月统计报告</p>
            <div class="mt-3">
                <button class="btn btn-primary" onclick="loadStatistics()">
                    <i class="fas fa-sync-alt me-1"></i>
                    刷新数据
                </button>
                <button class="btn btn-secondary" onclick="window.close()">
                    <i class="fas fa-times me-1"></i>
                    关闭
                </button>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="date-selector">
                <label for="yearSelect">年份:</label>
                <select id="yearSelect" onchange="loadStatistics()">
                    <!-- 年份选项将通过JavaScript生成 -->
                </select>
                
                <label for="monthSelect">月份:</label>
                <select id="monthSelect" onchange="loadStatistics()">
                    <option value="1">1月</option>
                    <option value="2">2月</option>
                    <option value="3">3月</option>
                    <option value="4">4月</option>
                    <option value="5">5月</option>
                    <option value="6">6月</option>
                    <option value="7">7月</option>
                    <option value="8">8月</option>
                    <option value="9">9月</option>
                    <option value="10">10月</option>
                    <option value="11">11月</option>
                    <option value="12">12月</option>
                </select>
                
                <label for="typeSelect">统计类型:</label>
                <select id="typeSelect" onchange="loadStatistics()">
                    <option value="month">月统计</option>
                    <option value="week">周统计</option>
                </select>
            </div>
        </div>

        <!-- 统计内容 -->
        <div id="loadingIndicator" class="loading">
            <i class="fas fa-spinner fa-spin me-2"></i>
            正在加载统计数据...
        </div>
        
        <div id="statisticsContainer" style="display: none;">
            <!-- 统计卡片网格 -->
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将通过JavaScript生成 -->
            </div>
        </div>
        
        <div id="emptyState" class="empty-state" style="display: none;">
            <i class="fas fa-chart-line"></i>
            <h5>暂无统计数据</h5>
            <p>请先在健康管理页面录入一些数据</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth() + 1;
        let statisticsData = [];
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        function initializePage() {
            // 生成年份选项
            generateYearOptions();
            
            // 设置当前年月
            document.getElementById('yearSelect').value = currentYear;
            document.getElementById('monthSelect').value = currentMonth;
            
            // 加载统计数据
            loadStatistics();
        }

        function generateYearOptions() {
            const yearSelect = document.getElementById('yearSelect');
            const currentYear = new Date().getFullYear();
            
            // 生成从5年前到明年的年份选项
            for (let year = currentYear - 5; year <= currentYear + 1; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                if (year === currentYear) {
                    option.selected = true;
                }
                yearSelect.appendChild(option);
            }
        }

        function loadStatistics() {
            const year = document.getElementById('yearSelect').value;
            const month = document.getElementById('monthSelect').value;
            const type = document.getElementById('typeSelect').value;
            
            currentYear = parseInt(year);
            currentMonth = parseInt(month);
            
            showLoading(true);
            
            fetch(`api/health-management.php?action=get_statistics&year=${year}&month=${month}&type=${type}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statisticsData = data.statistics;
                        renderStatistics();
                    } else {
                        console.error('加载统计数据失败:', data.error);
                        alert('加载统计数据失败: ' + data.error);
                        showEmptyState();
                    }
                })
                .catch(error => {
                    console.error('API调用失败:', error);
                    alert('网络错误，请稍后重试');
                    // 使用模拟数据
                    loadMockStatistics();
                })
                .finally(() => {
                    showLoading(false);
                });
        }

        function loadMockStatistics() {
            // 模拟统计数据
            statisticsData = [
                {
                    id: 4,
                    field_name: '最大心率数',
                    field_unit: 'bpm',
                    current_avg: 85.5,
                    prev_avg: 82.3,
                    current_count: 25,
                    prev_count: 28
                },
                {
                    id: 5,
                    field_name: '最小心率数',
                    field_unit: 'bpm',
                    current_avg: 65.2,
                    prev_avg: 67.1,
                    current_count: 25,
                    prev_count: 28
                },
                {
                    id: 6,
                    field_name: '静息心率数',
                    field_unit: 'bpm',
                    current_avg: 72.8,
                    prev_avg: 74.2,
                    current_count: 25,
                    prev_count: 28
                },
                {
                    id: 7,
                    field_name: '心率异常总数',
                    field_unit: '次',
                    current_avg: 3.2,
                    prev_avg: 4.1,
                    current_count: 25,
                    prev_count: 28
                },
                {
                    id: 8,
                    field_name: '运动步数',
                    field_unit: '步',
                    current_avg: 6850,
                    prev_avg: 6200,
                    current_count: 25,
                    prev_count: 28
                },
                {
                    id: 9,
                    field_name: '房颤数',
                    field_unit: '次',
                    current_avg: 0.8,
                    prev_avg: 1.2,
                    current_count: 15,
                    prev_count: 18
                },
                {
                    id: 10,
                    field_name: '早搏数',
                    field_unit: '次',
                    current_avg: 2.1,
                    prev_avg: 2.8,
                    current_count: 20,
                    prev_count: 22
                },
                {
                    id: 13,
                    field_name: '血氧饱和度',
                    field_unit: '%',
                    current_avg: 97.2,
                    prev_avg: 96.8,
                    current_count: 20,
                    prev_count: 22
                }
            ];
            
            renderStatistics();
        }

        function renderStatistics() {
            const container = document.getElementById('statisticsContainer');
            const emptyState = document.getElementById('emptyState');
            const statsGrid = document.getElementById('statsGrid');
            
            if (statisticsData.length === 0) {
                showEmptyState();
                return;
            }
            
            container.style.display = 'block';
            emptyState.style.display = 'none';
            
            // 清空现有内容
            statsGrid.innerHTML = '';
            
            // 销毁现有图表
            Object.values(charts).forEach(chart => {
                if (chart) chart.destroy();
            });
            charts = {};
            
            // 生成统计卡片
            statisticsData.forEach((stat, index) => {
                const card = createStatCard(stat, index);
                statsGrid.appendChild(card);
            });
        }

        function createStatCard(stat, index) {
            const card = document.createElement('div');
            card.className = 'stat-card';
            
            // 计算趋势
            const trend = calculateTrend(stat.current_avg, stat.prev_avg);
            const trendClass = trend > 0 ? 'trend-up' : trend < 0 ? 'trend-down' : 'trend-stable';
            const trendIcon = trend > 0 ? 'fa-arrow-up' : trend < 0 ? 'fa-arrow-down' : 'fa-minus';
            const trendText = trend > 0 ? '上升' : trend < 0 ? '下降' : '稳定';
            
            const iconColors = ['primary', 'info', 'warning', 'danger'];
            const iconColor = iconColors[index % iconColors.length];
            
            card.innerHTML = `
                <div class="stat-card-header">
                    <h5 class="stat-card-title">${stat.field_name}</h5>
                    <div class="stat-card-icon ${iconColor}">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                </div>
                
                <div class="summary-stats">
                    <div class="summary-item">
                        <div class="summary-value">${formatValue(stat.current_avg, stat.field_unit)}</div>
                        <div class="summary-label">本月平均</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${formatValue(stat.prev_avg, stat.field_unit)}</div>
                        <div class="summary-label">上月平均</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">${stat.current_count}</div>
                        <div class="summary-label">记录天数</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-value">
                            ${Math.abs(trend).toFixed(1)}%
                            <span class="trend-indicator ${trendClass}">
                                <i class="fas ${trendIcon}"></i>
                                ${trendText}
                            </span>
                        </div>
                        <div class="summary-label">变化趋势</div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="chart-${stat.id}"></canvas>
                </div>
            `;
            
            // 创建图表
            setTimeout(() => {
                createChart(stat);
            }, 100);
            
            return card;
        }

        function createChart(stat) {
            const ctx = document.getElementById(`chart-${stat.id}`);
            if (!ctx) return;
            
            // 生成模拟的月度数据
            const labels = [];
            const currentData = [];
            const prevData = [];
            
            const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
            const prevDaysInMonth = new Date(currentYear, currentMonth - 1, 0).getDate();
            
            // 生成当月数据
            for (let day = 1; day <= Math.min(daysInMonth, 15); day++) {
                labels.push(`${currentMonth}/${day}`);
                currentData.push(generateRandomValue(stat.current_avg, stat.field_unit));
            }
            
            // 生成上月数据（后15天）
            for (let day = Math.max(1, prevDaysInMonth - 14); day <= prevDaysInMonth; day++) {
                prevData.push(generateRandomValue(stat.prev_avg, stat.field_unit));
            }
            
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: `本月${stat.field_name}`,
                            data: currentData,
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: `上月${stat.field_name}`,
                            data: prevData.slice(-labels.length),
                            borderColor: '#6b7280',
                            backgroundColor: 'rgba(107, 114, 128, 0.1)',
                            tension: 0.4,
                            fill: false,
                            borderDash: [5, 5]
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: `${stat.field_name}趋势图`
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            title: {
                                display: true,
                                text: stat.field_unit || '值'
                            }
                        }
                    }
                }
            });
            
            charts[stat.id] = chart;
        }

        function generateRandomValue(avg, unit) {
            const variance = avg * 0.1; // 10%的变化范围
            const value = avg + (Math.random() - 0.5) * 2 * variance;
            
            if (unit === '%') {
                return Math.max(0, Math.min(100, value));
            } else if (unit === 'bpm') {
                return Math.max(40, Math.min(200, value));
            } else if (unit === '步') {
                return Math.max(0, value);
            }
            
            return Math.max(0, value);
        }

        function calculateTrend(current, previous) {
            if (!previous || previous === 0) return 0;
            return ((current - previous) / previous) * 100;
        }

        function formatValue(value, unit) {
            if (!value) return '-';
            
            if (unit === '步') {
                return Math.round(value).toLocaleString();
            } else {
                return parseFloat(value).toFixed(1);
            }
        }

        function showLoading(show) {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statisticsContainer = document.getElementById('statisticsContainer');
            const emptyState = document.getElementById('emptyState');
            
            if (show) {
                loadingIndicator.style.display = 'block';
                statisticsContainer.style.display = 'none';
                emptyState.style.display = 'none';
            } else {
                loadingIndicator.style.display = 'none';
            }
        }

        function showEmptyState() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const statisticsContainer = document.getElementById('statisticsContainer');
            const emptyState = document.getElementById('emptyState');
            
            loadingIndicator.style.display = 'none';
            statisticsContainer.style.display = 'none';
            emptyState.style.display = 'block';
        }
    </script>
</body>
</html>
