<?php
/**
 * 云服务器注册问题一键修复脚本
 * 解决云服务器环境下的用户注册问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器注册问题修复</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .step {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }
        .step.success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .step.error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .step.warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .step.info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 云服务器注册问题修复</h1>
            <p>一键修复云服务器环境下的用户注册问题</p>
        </div>
        
        <div class="content">
            <?php
            
            $fixActions = [];
            $hasErrors = false;
            
            try {
                // 1. 检查并修复数据库连接
                echo "<div class='step info'>";
                echo "<h3>🗄️ 检查数据库连接</h3>";
                
                require_once 'config/database.php';
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<p>✅ 数据库连接成功</p>";
                
                // 测试数据库查询
                $result = $db->query("SELECT 1 as test, NOW() as current_time")->fetch();
                echo "<p>✅ 数据库查询测试成功</p>";
                echo "<div class='code-block'>当前时间: " . $result['current_time'] . "</div>";
                
                echo "</div>";
                
                // 2. 检查并创建必要的表
                echo "<div class='step info'>";
                echo "<h3>📋 检查数据库表结构</h3>";
                
                // 检查用户角色表
                $result = $db->query("SHOW TABLES LIKE 'user_roles'")->fetch();
                if (!$result) {
                    echo "<p>⚠️ user_roles 表不存在，正在创建...</p>";
                    $db->query("
                        CREATE TABLE user_roles (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            role_code VARCHAR(50) UNIQUE NOT NULL,
                            role_name VARCHAR(100) NOT NULL,
                            description TEXT,
                            permissions TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        )
                    ");
                    
                    // 插入默认角色
                    $db->query("
                        INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                        ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                        ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                        ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                    ");
                    
                    echo "<p>✅ user_roles 表创建成功</p>";
                    $fixActions[] = "创建用户角色表";
                } else {
                    echo "<p>✅ user_roles 表已存在</p>";
                }
                
                // 检查用户表
                $result = $db->query("SHOW TABLES LIKE 'users'")->fetch();
                if (!$result) {
                    echo "<p>⚠️ users 表不存在，正在创建...</p>";
                    $db->query("
                        CREATE TABLE users (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            username VARCHAR(50) NOT NULL UNIQUE,
                            email VARCHAR(100) NOT NULL UNIQUE,
                            password_hash VARCHAR(255) NOT NULL,
                            full_name VARCHAR(100),
                            avatar VARCHAR(255),
                            role_id INT DEFAULT 3,
                            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                            last_login TIMESTAMP NULL,
                            login_count INT DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
                        )
                    ");
                    echo "<p>✅ users 表创建成功</p>";
                    $fixActions[] = "创建用户表";
                } else {
                    echo "<p>✅ users 表已存在</p>";
                }
                
                // 检查用户资料表
                $result = $db->query("SHOW TABLES LIKE 'user_profiles'")->fetch();
                if (!$result) {
                    echo "<p>⚠️ user_profiles 表不存在，正在创建...</p>";
                    $db->query("
                        CREATE TABLE user_profiles (
                            id INT PRIMARY KEY AUTO_INCREMENT,
                            user_id INT NOT NULL,
                            nickname VARCHAR(100),
                            avatar_url VARCHAR(500),
                            bio TEXT,
                            location VARCHAR(100),
                            website VARCHAR(200),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                        )
                    ");
                    echo "<p>✅ user_profiles 表创建成功</p>";
                    $fixActions[] = "创建用户资料表";
                } else {
                    echo "<p>✅ user_profiles 表已存在</p>";
                }
                
                echo "</div>";
                
                // 3. 检查用户角色数据
                echo "<div class='step info'>";
                echo "<h3>👥 检查用户角色数据</h3>";
                
                $roles = $db->query("SELECT * FROM user_roles")->fetchAll();
                if (empty($roles)) {
                    echo "<p>⚠️ 用户角色数据为空，正在插入默认数据...</p>";
                    $db->query("
                        INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                        ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                        ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                        ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                    ");
                    echo "<p>✅ 默认用户角色数据插入成功</p>";
                    $fixActions[] = "插入默认用户角色数据";
                } else {
                    echo "<p>✅ 找到 " . count($roles) . " 个用户角色</p>";
                }
                
                echo "</div>";
                
                // 4. 检查上传目录权限
                echo "<div class='step info'>";
                echo "<h3>📁 检查上传目录权限</h3>";
                
                $uploadDir = 'uploads/avatars';
                if (!is_dir($uploadDir)) {
                    echo "<p>⚠️ 头像上传目录不存在，正在创建...</p>";
                    if (mkdir($uploadDir, 0755, true)) {
                        echo "<p>✅ 头像上传目录创建成功</p>";
                        $fixActions[] = "创建头像上传目录";
                    } else {
                        echo "<p>❌ 头像上传目录创建失败</p>";
                        $hasErrors = true;
                    }
                } else {
                    echo "<p>✅ 头像上传目录已存在</p>";
                }
                
                if (is_dir($uploadDir)) {
                    if (is_writable($uploadDir)) {
                        echo "<p>✅ 头像上传目录可写</p>";
                    } else {
                        echo "<p>⚠️ 头像上传目录不可写，正在修复权限...</p>";
                        if (chmod($uploadDir, 0755)) {
                            echo "<p>✅ 头像上传目录权限修复成功</p>";
                            $fixActions[] = "修复头像上传目录权限";
                        } else {
                            echo "<p>❌ 头像上传目录权限修复失败</p>";
                            $hasErrors = true;
                        }
                    }
                }
                
                echo "</div>";
                
                // 5. 测试注册流程
                echo "<div class='step info'>";
                echo "<h3>🧪 测试注册流程</h3>";
                
                try {
                    // 开始事务
                    $db->query("BEGIN");
                    
                    $testData = [
                        'username' => 'test_user_' . time(),
                        'email' => 'test_' . time() . '@example.com',
                        'nickname' => '测试用户' . time(),
                        'password' => 'test123456'
                    ];
                    
                    echo "<p>使用测试数据进行注册流程测试...</p>";
                    
                    // 获取用户角色ID
                    $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
                    $roleId = $userRole ? $userRole['id'] : 3;
                    
                    // 创建用户记录
                    $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
                    $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                            VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
                    
                    $stmt = $db->getConnection()->prepare($sql);
                    $result = $stmt->execute([
                        $testData['username'],
                        $testData['email'],
                        $passwordHash,
                        $testData['nickname'],
                        $roleId
                    ]);
                    
                    if ($result) {
                        $userId = $db->getConnection()->lastInsertId();
                        echo "<p>✅ 用户创建成功，ID: {$userId}</p>";
                        
                        // 创建用户资料
                        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                        
                        $profileStmt = $db->getConnection()->prepare($profileSql);
                        $profileResult = $profileStmt->execute([
                            $userId,
                            $testData['nickname'],
                            'assets/images/default-avatar.png'
                        ]);
                        
                        if ($profileResult) {
                            echo "<p>✅ 用户资料创建成功</p>";
                            echo "<p><strong>🎉 注册流程测试完全成功！</strong></p>";
                            $fixActions[] = "注册流程测试通过";
                        } else {
                            echo "<p>❌ 用户资料创建失败</p>";
                            $hasErrors = true;
                        }
                        
                        // 清理测试数据
                        $db->query("DELETE FROM user_profiles WHERE user_id = ?", [$userId]);
                        $db->query("DELETE FROM users WHERE id = ?", [$userId]);
                        echo "<p>✅ 测试数据清理完成</p>";
                        
                    } else {
                        echo "<p>❌ 用户创建失败</p>";
                        $hasErrors = true;
                    }
                    
                    // 回滚事务
                    $db->query("ROLLBACK");
                    
                } catch (Exception $e) {
                    $db->query("ROLLBACK");
                    echo "<p>❌ 注册流程测试失败: " . $e->getMessage() . "</p>";
                    echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                    $hasErrors = true;
                }
                
                echo "</div>";
                
                // 6. 显示修复总结
                if ($hasErrors) {
                    echo "<div class='step error'>";
                    echo "<h3>⚠️ 修复完成但存在问题</h3>";
                } else {
                    echo "<div class='step success'>";
                    echo "<h3>🎯 修复完成</h3>";
                }
                
                if (!empty($fixActions)) {
                    echo "<p>已完成以下修复操作：</p>";
                    echo "<ul>";
                    foreach ($fixActions as $action) {
                        echo "<li>{$action}</li>";
                    }
                    echo "</ul>";
                } else {
                    echo "<p>系统检查正常，无需修复操作。</p>";
                }
                
                if (!$hasErrors) {
                    echo "<p><strong>✅ 现在可以正常使用用户注册功能了！</strong></p>";
                } else {
                    echo "<p><strong>⚠️ 部分问题需要手动处理，请检查上述错误信息。</strong></p>";
                }
                
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ 修复过程出错</h3>";
                echo "<p>错误信息: " . $e->getMessage() . "</p>";
                echo "<div class='code-block'>" . $e->getTraceAsString() . "</div>";
                echo "</div>";
            }
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="register.php" class="btn success">测试注册功能</a>
                <a href="debug_register_server.php" class="btn">详细诊断</a>
                <a href="index.php" class="btn">返回首页</a>
            </div>
        </div>
    </div>
</body>
</html>
