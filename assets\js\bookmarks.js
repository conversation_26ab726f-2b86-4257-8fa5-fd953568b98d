// 收藏页面交互功能

document.addEventListener('DOMContentLoaded', function() {
    initRemoveBookmarks();
});

// 初始化取消收藏功能
function initRemoveBookmarks() {
    const removeButtons = document.querySelectorAll('.remove-bookmark-btn');
    
    removeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const postId = this.dataset.postId;
            const bookmarkItem = this.closest('.bookmark-item');
            
            // 显示确认对话框
            if (confirm('确定要取消收藏这个帖子吗？')) {
                removeBookmark(postId, bookmarkItem);
            }
        });
    });
}

// 取消收藏
async function removeBookmark(postId, bookmarkItem) {
    try {
        const response = await fetch('api/post-actions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'bookmark',
                post_id: postId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 添加移除动画
            bookmarkItem.style.transition = 'all 0.3s ease';
            bookmarkItem.style.opacity = '0';
            bookmarkItem.style.transform = 'translateX(-20px)';
            
            // 动画完成后移除元素
            setTimeout(() => {
                bookmarkItem.remove();
                
                // 检查是否还有收藏项
                const remainingItems = document.querySelectorAll('.bookmark-item');
                if (remainingItems.length === 0) {
                    showEmptyState();
                }
                
                // 更新统计数字
                updateBookmarkCount(-1);
            }, 300);
            
            showToast('已取消收藏', 'success');
        } else {
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('取消收藏失败:', error);
        showToast('网络错误，请稍后重试', 'error');
    }
}

// 显示空状态
function showEmptyState() {
    const bookmarksContainer = document.querySelector('.bookmarks-container');
    bookmarksContainer.innerHTML = `
        <div class="empty-bookmarks">
            <div class="empty-icon">
                <i class="fas fa-bookmark"></i>
            </div>
            <h3>还没有收藏任何帖子</h3>
            <p>发现感兴趣的帖子时，点击收藏按钮保存到这里</p>
            <a href="community.php" class="btn btn-primary">
                <i class="fas fa-search"></i> 去发现好内容
            </a>
        </div>
    `;
}

// 更新收藏数量
function updateBookmarkCount(change) {
    const countElement = document.querySelector('.page-stats .stat-item');
    if (countElement) {
        const currentText = countElement.textContent;
        const currentCount = parseInt(currentText.match(/\d+/)[0]);
        const newCount = Math.max(0, currentCount + change);
        countElement.innerHTML = `
            <i class="fas fa-heart"></i>
            共收藏了 ${newCount} 个帖子
        `;
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '9999',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        backgroundColor: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'
    });
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    // ESC键返回社区
    if (e.key === 'Escape') {
        window.location.href = 'community.php';
    }
    
    // Ctrl+F 搜索（浏览器默认行为）
    if (e.ctrlKey && e.key === 'f') {
        // 让浏览器处理默认搜索
        return;
    }
});

// 平滑滚动到顶部
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 懒加载图片
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 初始化懒加载（如果需要）
if ('IntersectionObserver' in window) {
    initLazyLoading();
}

// 处理页面可见性变化
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，可以刷新一些数据
        console.log('收藏页面重新可见');
    }
});
