<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏预览 - 比特熊智慧系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .preview-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem;
            text-align: center;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .preview-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .preview-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.8;
            font-size: 0.875rem;
        }

        .preview-content {
            background: white;
            margin: 2rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .demo-content {
            padding: 3rem 2rem;
            text-align: center;
            background: #f8fafc;
        }

        .demo-content h2 {
            color: #1e293b;
            margin-bottom: 1rem;
        }

        .demo-content p {
            color: #64748b;
            margin-bottom: 2rem;
        }

        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .demo-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
        }

        .demo-card h3 {
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .demo-card p {
            color: #64748b;
            font-size: 0.875rem;
            margin: 0;
        }

        .refresh-notice {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            font-size: 0.875rem;
            max-width: 300px;
        }

        .refresh-notice button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 0.5rem;
            transition: background 0.2s ease;
        }

        .refresh-notice button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 确保导航栏样式正确加载 */
        .navbar {
            position: relative !important;
            top: 0 !important;
        }

        @media (max-width: 768px) {
            .preview-content {
                margin: 1rem;
            }
            
            .demo-content {
                padding: 2rem 1rem;
            }
            
            .refresh-notice {
                bottom: 1rem;
                right: 1rem;
                left: 1rem;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h1>🔍 导航栏预览</h1>
        <p>这是根据后台配置生成的实际导航栏效果</p>
    </div>

    <div class="preview-content">
        <!-- 包含动态导航栏 -->
        <?php include 'components/navbar.php'; ?>
        
        <!-- 演示内容 -->
        <div class="demo-content">
            <h2>导航栏预览演示</h2>
            <p>这是一个完整的前台页面预览，展示了导航栏在实际网站中的效果。</p>
            
            <div class="demo-cards">
                <div class="demo-card">
                    <h3>🎨 样式效果</h3>
                    <p>导航栏使用了毛玻璃效果和平滑动画，提供现代化的视觉体验。</p>
                </div>
                
                <div class="demo-card">
                    <h3>📱 响应式设计</h3>
                    <p>导航栏在不同设备上都能完美显示，支持移动端折叠菜单。</p>
                </div>
                
                <div class="demo-card">
                    <h3>🔗 动态链接</h3>
                    <p>所有菜单项都是从后台数据库动态生成，支持实时更新。</p>
                </div>
                
                <div class="demo-card">
                    <h3>📋 下拉菜单</h3>
                    <p>支持多级下拉菜单，鼠标悬停时显示子菜单项。</p>
                </div>
            </div>
        </div>
    </div>

    <div class="refresh-notice">
        <strong>💡 提示</strong><br>
        在后台修改导航栏后，刷新此页面即可看到最新效果。
        <br>
        <button onclick="window.location.reload()">🔄 刷新预览</button>
        <button onclick="window.close()" style="margin-left: 0.5rem;">❌ 关闭</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 自动刷新功能
        let autoRefresh = false;
        
        // 监听来自父窗口的消息
        window.addEventListener('message', function(event) {
            if (event.data === 'refresh-navbar') {
                window.location.reload();
            }
        });
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('导航栏预览页面已加载');
            
            // 检查导航栏是否正确加载
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                console.log('✅ 导航栏组件加载成功');
                
                // 统计菜单项数量
                const navItems = document.querySelectorAll('.nav-item');
                const dropdownItems = document.querySelectorAll('.dropdown-item');
                
                console.log(`📊 导航栏统计:`);
                console.log(`   主菜单项: ${navItems.length}`);
                console.log(`   下拉菜单项: ${dropdownItems.length}`);
                
                // 测试下拉菜单功能
                const dropdowns = document.querySelectorAll('.dropdown');
                dropdowns.forEach((dropdown, index) => {
                    console.log(`🔽 下拉菜单 ${index + 1}: ${dropdown.querySelector('.nav-link').textContent.trim()}`);
                });
            } else {
                console.error('❌ 导航栏组件加载失败');
            }
            
            // 添加点击事件监听
            document.querySelectorAll('.nav-link, .dropdown-item').forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    if (href === '#' || href.startsWith('#')) {
                        e.preventDefault();
                        console.log(`🔗 点击了菜单项: ${this.textContent.trim()}`);
                        
                        // 显示提示
                        showClickNotice(this.textContent.trim());
                    }
                });
            });
        });
        
        function showClickNotice(menuName) {
            // 创建点击提示
            const notice = document.createElement('div');
            notice.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(59, 130, 246, 0.95);
                color: white;
                padding: 1rem 2rem;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
                z-index: 10000;
                font-weight: 500;
                backdrop-filter: blur(10px);
            `;
            notice.textContent = `点击了菜单: ${menuName}`;
            
            document.body.appendChild(notice);
            
            // 2秒后移除
            setTimeout(() => {
                document.body.removeChild(notice);
            }, 2000);
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                console.log('🔄 手动刷新预览');
            }
            
            if (e.key === 'Escape') {
                window.close();
            }
        });
        
        // 定期检查是否需要刷新
        setInterval(() => {
            if (autoRefresh) {
                fetch('api/navbar.php?timestamp=' + Date.now())
                .then(response => response.json())
                .then(data => {
                    // 这里可以比较数据是否有变化
                    // 如果有变化则自动刷新
                })
                .catch(error => {
                    console.log('检查更新失败:', error);
                });
            }
        }, 5000);
    </script>
</body>
</html>
