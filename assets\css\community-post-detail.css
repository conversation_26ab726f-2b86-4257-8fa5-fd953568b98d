/* 帖子详情页面样式 */

/* 面包屑导航 */
.breadcrumb {
    margin-bottom: 24px;
}

.breadcrumb-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.2s;
}

.breadcrumb-link:hover {
    color: #3b82f6;
    background: rgba(255, 255, 255, 0.95);
    transform: translateX(-4px);
}

/* 帖子详情 */
.post-detail {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    margin-bottom: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.post-header {
    margin-bottom: 32px;
}

.post-meta {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.post-category {
    background: rgb(var(--category-color, 59, 130, 246));
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.pin-badge {
    background: #f59e0b;
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.post-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.3;
    margin-bottom: 24px;
}

.post-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
    border-top: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
}

.author-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.author-avatar-link {
    display: block;
    border-radius: 50%;
    transition: transform 0.2s;
}

.author-avatar-link:hover {
    transform: scale(1.05);
}

.author-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
}

.author-name-link {
    text-decoration: none;
    color: inherit;
}

.author-name-link:hover .author-name {
    color: #3b82f6;
}

.author-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a1a1a;
}

.post-meta-info {
    display: flex;
    gap: 16px;
    font-size: 14px;
    color: #6b7280;
}

.post-stats {
    display: flex;
    gap: 24px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 14px;
}

.stat-item i {
    font-size: 16px;
}

/* 帖子内容 */
.post-content {
    margin-bottom: 32px;
}

.featured-image {
    margin-bottom: 32px;
    border-radius: 12px;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
}

.post-body {
    font-size: 16px;
    line-height: 1.8;
    color: #374151;
}

.post-body h1,
.post-body h2,
.post-body h3,
.post-body h4,
.post-body h5,
.post-body h6 {
    margin: 32px 0 16px 0;
    color: #1a1a1a;
    font-weight: 600;
}

.post-body h1 { font-size: 2rem; }
.post-body h2 { font-size: 1.75rem; }
.post-body h3 { font-size: 1.5rem; }
.post-body h4 { font-size: 1.25rem; }
.post-body h5 { font-size: 1.125rem; }
.post-body h6 { font-size: 1rem; }

.post-body p {
    margin-bottom: 16px;
}

.post-body img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 16px 0;
}

.post-body pre {
    background: #f8fafc;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    overflow-x: auto;
    margin: 16px 0;
    font-family: 'Courier New', monospace;
}

.post-body blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 16px;
    margin: 16px 0;
    color: #6b7280;
    font-style: italic;
}

.post-body ul,
.post-body ol {
    margin: 16px 0;
    padding-left: 24px;
}

.post-body li {
    margin-bottom: 8px;
}

/* 帖子操作按钮 */
.post-actions {
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.action-buttons {
    display: flex;
    gap: 16px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    color: #6b7280;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
}

.action-btn:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-1px);
}

.action-btn.active {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

.like-btn.active {
    border-color: #ef4444;
    background: #ef4444;
}

.dislike-btn.active {
    border-color: #6b7280;
    background: #6b7280;
}

/* 评论区 */
.comments-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.comments-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.comments-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 12px;
}

.comments-sort select {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    cursor: pointer;
}

/* 评论表单 */
.comment-form-container {
    margin-bottom: 32px;
}

.comment-form {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    border: 2px solid #e5e7eb;
    transition: border-color 0.2s;
}

.comment-form:focus-within {
    border-color: #3b82f6;
}

.form-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.form-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #1a1a1a;
}

.form-label {
    font-size: 14px;
    color: #6b7280;
}

.comment-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    background: white;
}

.comment-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 16px;
}

.login-prompt {
    text-align: center;
    padding: 32px;
    background: #f8fafc;
    border-radius: 12px;
    margin-bottom: 32px;
}

.login-prompt a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.login-prompt a:hover {
    text-decoration: underline;
}

/* 评论列表 */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.empty-comments {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #d1d5db;
}

/* 评论时间线样式 */
.comments-timeline {
    position: relative;
    padding-left: 45px;
}

.comments-timeline::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 评论项 */
.comment-item {
    position: relative;
    margin-bottom: 24px;
    transition: all 0.3s ease;
}

.comment-item::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 24px;
    width: 14px;
    height: 14px;
    border: 3px solid #ffffff;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 2;
    transition: all 0.3s ease;
}

/* 默认圆圈颜色 */
.comment-item::before {
    background: #4a90e2;
}

.comment-item:hover::before {
    transform: scale(1.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
}

.comment-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(74, 144, 226, 0.1);
    transition: all 0.3s ease;
}

.comment-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(74, 144, 226, 0.2);
}

.comment-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(74, 144, 226, 0.1);
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 16px;
}

.comment-avatar-link {
    display: block;
    border-radius: 50%;
    transition: transform 0.2s;
}

.comment-avatar-link:hover {
    transform: scale(1.1);
}

.comment-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
    border: 3px solid #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 16px;
}

.author-signature {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.comment-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 13px;
    color: #6b7280;
}

.floor-number {
    background: linear-gradient(135deg, #4a90e2, #7bb3f0);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.comment-time {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #6b7280;
}

.comment-body {
    margin-bottom: 16px;
}

.comment-text {
    color: #374151;
    line-height: 1.6;
}

.comment-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(74, 144, 226, 0.1);
}

.comment-actions .action-btn {
    padding: 8px 16px;
    font-size: 13px;
    border: 1px solid rgba(74, 144, 226, 0.2);
    border-radius: 20px;
    background: rgba(74, 144, 226, 0.05);
    color: #4a90e2;
    transition: all 0.2s ease;
}

.comment-actions .action-btn:hover {
    background: rgba(74, 144, 226, 0.1);
    border-color: rgba(74, 144, 226, 0.3);
    transform: translateY(-1px);
}

.comment-actions .action-btn.active {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.comment-actions .delete-btn {
    background: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.comment-actions .delete-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

.comment-replies {
    margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .post-detail {
        padding: 24px 20px;
    }
    
    .post-title {
        font-size: 1.75rem;
    }
    
    .post-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .action-buttons {
        flex-wrap: wrap;
    }
    
    .comments-section {
        padding: 24px 20px;
    }
    
    .comments-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    /* 移动端时间线样式调整 */
    .comments-timeline {
        padding-left: 35px;
    }

    .comments-timeline::before {
        left: 18px;
        width: 2px;
    }

    .comment-item::before {
        left: -25px;
        top: 20px;
        width: 12px;
        height: 12px;
    }

    .comment-item {
        margin-left: 0 !important;
    }

    .comment-replies .comment-item {
        margin-left: 20px !important;
    }

    .comment-content {
        padding: 16px;
    }

    .comment-author {
        gap: 12px;
    }

    .comment-avatar {
        width: 40px;
        height: 40px;
    }

    .comment-actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .comment-actions .action-btn {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
