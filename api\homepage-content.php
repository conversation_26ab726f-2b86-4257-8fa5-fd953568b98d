<?php
session_start();
header('Content-Type: application/json');

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

// 引入数据库配置
try {
    require_once __DIR__ . '/../config/database.php';
    $db = db();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '数据库连接失败']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'GET':
            handleGet($db, $action);
            break;
        case 'POST':
            handlePost($db, $action);
            break;
        case 'PUT':
            handlePut($db, $action);
            break;
        case 'DELETE':
            handleDelete($db, $action);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}

function handleGet($db, $action) {
    switch ($action) {
        case 'hero':
            getHeroContent($db);
            break;
        case 'experts':
            getExpertsContent($db);
            break;
        case 'video':
            getVideoContent($db);
            break;
        case 'sections':
            getSectionsContent($db);
            break;
        case 'all':
            getAllContent($db);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function handlePost($db, $action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'hero':
            saveHeroContent($db, $input);
            break;
        case 'expert':
            addExpert($db, $input);
            break;
        case 'video':
            saveVideoContent($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function handlePut($db, $action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'expert':
            updateExpert($db, $input);
            break;
        case 'section':
            updateSection($db, $input);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

function handleDelete($db, $action) {
    $id = $_GET['id'] ?? null;
    
    switch ($action) {
        case 'expert':
            deleteExpert($db, $id);
            break;
        default:
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
}

// 获取英雄区域内容
function getHeroContent($db) {
    $sql = "SELECT * FROM homepage_hero WHERE is_active = 1 ORDER BY id DESC LIMIT 1";
    $result = $db->fetchOne($sql);
    
    if ($result) {
        echo json_encode(['success' => true, 'data' => $result]);
    } else {
        // 返回默认数据
        $defaultData = [
            'title' => '欢迎访问比特熊极简门户网站',
            'subtitle' => '在这里一起和小熊彼彼度过美好的时光',
            'description' => '也许你会好奇，这个网站是干嘛的？比特熊极简门户网站是比特熊爱生活团队开发的一个综合应用型社交网站，你可以在这里学习、加入在线联机游戏、分享生活中的美好事物、或者一起学习，读书等',
            'primary_button_text' => '立即体验',
            'primary_button_url' => '#',
            'secondary_button_text' => '了解更多',
            'secondary_button_url' => '#features',
            'hero_image' => 'image/bitlogo.png'
        ];
        echo json_encode(['success' => true, 'data' => $defaultData]);
    }
}

// 保存英雄区域内容
function saveHeroContent($db, $data) {
    // 验证必填字段
    $required = ['title', 'subtitle', 'description', 'primary_button_text', 'primary_button_url', 'secondary_button_text', 'secondary_button_url'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => "字段 {$field} 不能为空"]);
            return;
        }
    }
    
    // 检查是否已存在记录
    $existing = $db->fetchOne("SELECT id FROM homepage_hero WHERE is_active = 1 LIMIT 1");
    
    if ($existing) {
        // 更新现有记录
        $sql = "UPDATE homepage_hero SET 
                title = ?, subtitle = ?, description = ?, 
                primary_button_text = ?, primary_button_url = ?, 
                secondary_button_text = ?, secondary_button_url = ?, 
                hero_image = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        $params = [
            $data['title'], $data['subtitle'], $data['description'],
            $data['primary_button_text'], $data['primary_button_url'],
            $data['secondary_button_text'], $data['secondary_button_url'],
            $data['hero_image'] ?? 'image/bitlogo.png',
            $existing['id']
        ];
    } else {
        // 插入新记录
        $sql = "INSERT INTO homepage_hero 
                (title, subtitle, description, primary_button_text, primary_button_url, 
                 secondary_button_text, secondary_button_url, hero_image) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $params = [
            $data['title'], $data['subtitle'], $data['description'],
            $data['primary_button_text'], $data['primary_button_url'],
            $data['secondary_button_text'], $data['secondary_button_url'],
            $data['hero_image'] ?? 'image/bitlogo.png'
        ];
    }
    
    $result = $db->execute($sql, $params);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => '英雄区域内容保存成功']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '保存失败']);
    }
}

// 获取专家列表
function getExpertsContent($db) {
    $sql = "SELECT * FROM homepage_experts WHERE is_active = 1 ORDER BY sort_order ASC, id ASC";
    $result = $db->fetchAll($sql);
    echo json_encode(['success' => true, 'data' => $result]);
}

// 添加专家
function addExpert($db, $data) {
    $required = ['name', 'title'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => "字段 {$field} 不能为空"]);
            return;
        }
    }
    
    $sql = "INSERT INTO homepage_experts (name, title, description, image, sort_order) 
            VALUES (?, ?, ?, ?, ?)";
    $params = [
        $data['name'],
        $data['title'],
        $data['description'] ?? '',
        $data['image'] ?? 'image/default-avatar.svg',
        $data['sort_order'] ?? 0
    ];
    
    $result = $db->execute($sql, $params);
    
    if ($result !== false) {
        $id = $db->lastInsertId();
        echo json_encode(['success' => true, 'message' => '专家添加成功', 'id' => $id]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '添加失败']);
    }
}

// 更新专家
function updateExpert($db, $data) {
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '专家ID不能为空']);
        return;
    }
    
    $sql = "UPDATE homepage_experts SET 
            name = ?, title = ?, description = ?, image = ?, sort_order = ?, 
            updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?";
    $params = [
        $data['name'],
        $data['title'],
        $data['description'] ?? '',
        $data['image'] ?? 'image/default-avatar.svg',
        $data['sort_order'] ?? 0,
        $data['id']
    ];
    
    $result = $db->execute($sql, $params);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => '专家信息更新成功']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
}

// 删除专家
function deleteExpert($db, $id) {
    if (empty($id)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '专家ID不能为空']);
        return;
    }
    
    $sql = "UPDATE homepage_experts SET is_active = 0 WHERE id = ?";
    $result = $db->execute($sql, [$id]);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => '专家删除成功']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '删除失败']);
    }
}

// 获取视频内容
function getVideoContent($db) {
    $sql = "SELECT * FROM homepage_videos WHERE is_active = 1 AND is_featured = 1 ORDER BY id DESC LIMIT 1";
    $result = $db->fetchOne($sql);
    
    if ($result) {
        echo json_encode(['success' => true, 'data' => $result]);
    } else {
        // 返回默认数据
        $defaultData = [
            'title' => 'Why Jose uses O\'Reilly every day',
            'description' => 'As a principal software engineer, I rely on O\'Reilly\'s platform to keep my team updated with the latest technologies and best practices.',
            'speaker_name' => 'Jose Dunio',
            'speaker_role' => 'Principal Software Engineer',
            'company_badge' => 'O\'REILLY',
            'video_url' => '#'
        ];
        echo json_encode(['success' => true, 'data' => $defaultData]);
    }
}

// 保存视频内容
function saveVideoContent($db, $data) {
    $required = ['title', 'speaker_name', 'speaker_role'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => "字段 {$field} 不能为空"]);
            return;
        }
    }
    
    // 检查是否已存在特色视频
    $existing = $db->fetchOne("SELECT id FROM homepage_videos WHERE is_active = 1 AND is_featured = 1 LIMIT 1");
    
    if ($existing) {
        // 更新现有记录
        $sql = "UPDATE homepage_videos SET 
                title = ?, description = ?, speaker_name = ?, speaker_role = ?, 
                video_url = ?, company_badge = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?";
        $params = [
            $data['title'], $data['description'] ?? '', $data['speaker_name'], $data['speaker_role'],
            $data['video_url'] ?? '#', $data['company_badge'] ?? 'O\'REILLY',
            $existing['id']
        ];
    } else {
        // 插入新记录
        $sql = "INSERT INTO homepage_videos 
                (title, description, speaker_name, speaker_role, video_url, company_badge, is_featured) 
                VALUES (?, ?, ?, ?, ?, ?, 1)";
        $params = [
            $data['title'], $data['description'] ?? '', $data['speaker_name'], $data['speaker_role'],
            $data['video_url'] ?? '#', $data['company_badge'] ?? 'O\'REILLY'
        ];
    }
    
    $result = $db->execute($sql, $params);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => '视频内容保存成功']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '保存失败']);
    }
}

// 获取区域设置
function getSectionsContent($db) {
    $sql = "SELECT * FROM homepage_sections ORDER BY sort_order ASC, id ASC";
    $result = $db->fetchAll($sql);
    echo json_encode(['success' => true, 'data' => $result]);
}

// 更新区域设置
function updateSection($db, $data) {
    if (empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => '区域ID不能为空']);
        return;
    }
    
    $sql = "UPDATE homepage_sections SET 
            is_visible = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?";
    $params = [$data['is_visible'] ? 1 : 0, $data['id']];
    
    $result = $db->execute($sql, $params);
    
    if ($result !== false) {
        echo json_encode(['success' => true, 'message' => '区域设置更新成功']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => '更新失败']);
    }
}

// 获取所有内容
function getAllContent($db) {
    $hero = $db->fetchOne("SELECT * FROM homepage_hero WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
    $experts = $db->fetchAll("SELECT * FROM homepage_experts WHERE is_active = 1 ORDER BY sort_order ASC, id ASC");
    $video = $db->fetchOne("SELECT * FROM homepage_videos WHERE is_active = 1 AND is_featured = 1 ORDER BY id DESC LIMIT 1");
    $sections = $db->fetchAll("SELECT * FROM homepage_sections ORDER BY sort_order ASC, id ASC");
    
    echo json_encode([
        'success' => true,
        'data' => [
            'hero' => $hero,
            'experts' => $experts,
            'video' => $video,
            'sections' => $sections
        ]
    ]);
}
?>
