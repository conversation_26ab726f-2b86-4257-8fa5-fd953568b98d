<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/auth.php';

header('Content-Type: application/json');

// 检查用户是否登录
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$currentUser = $auth->getCurrentUser();

// 只处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    $db = db();
    
    if ($action === 'report') {
        $commentId = intval($input['comment_id'] ?? 0);
        $reportType = trim($input['report_type'] ?? '');
        $reportReason = trim($input['report_reason'] ?? '');
        
        // 验证输入
        if ($commentId <= 0) {
            throw new Exception('无效的评论ID');
        }
        
        if (empty($reportType)) {
            throw new Exception('请选择举报类型');
        }
        
        // 检查评论是否存在
        $comment = $db->fetchOne("SELECT id, user_id FROM post_comments WHERE id = ? AND status != 'deleted'", [$commentId]);
        if (!$comment) {
            throw new Exception('评论不存在或已被删除');
        }
        
        // 检查是否已经举报过
        $existingReport = $db->fetchOne("SELECT id FROM comment_reports WHERE comment_id = ? AND reporter_user_id = ?", 
                                       [$commentId, $currentUser['id']]);
        if ($existingReport) {
            throw new Exception('您已经举报过这条评论');
        }
        
        // 不能举报自己的评论
        if ($comment['user_id'] == $currentUser['id']) {
            throw new Exception('不能举报自己的评论');
        }
        
        // 插入举报记录
        $sql = "INSERT INTO comment_reports (comment_id, reporter_user_id, report_type, report_reason, created_at) 
                VALUES (?, ?, ?, ?, NOW())";
        $db->execute($sql, [$commentId, $currentUser['id'], $reportType, $reportReason]);
        
        echo json_encode(['success' => true, 'message' => '举报提交成功，我们会尽快处理']);
        
    } elseif ($action === 'get_report_types') {
        // 获取举报类型列表
        $reportTypes = [
            '违法违规',
            '色情',
            '低俗', 
            '赌博诈骗',
            '违法信息外链',
            '涉政谣言',
            '虚假不实信息',
            '涉社会事件谣言',
            '人身攻击',
            '侵犯隐私',
            '垃圾广告',
            '引战',
            '刷屏',
            '剧透',
            '视频不相关',
            '违规抽奖',
            '青少年不良信息',
            '其他'
        ];
        
        echo json_encode(['success' => true, 'data' => $reportTypes]);
        
    } else {
        throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
