<?php
/**
 * 公告管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    
    // 确保公告表存在
    $db->query("CREATE TABLE IF NOT EXISTS announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        type ENUM('important', 'normal', 'urgent') DEFAULT 'normal',
        status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
        is_pinned BOOLEAN DEFAULT FALSE,
        author_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NULL,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )");
    
    switch ($method) {
        case 'GET':
            handleGet($db);
            break;
        case 'POST':
            handlePost($db, $input);
            break;
        case 'PUT':
            handlePut($db, $input);
            break;
        case 'DELETE':
            handleDelete($db);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * 处理GET请求 - 获取公告列表
 */
function handleGet($db) {
    $type = $_GET['type'] ?? '';
    $status = $_GET['status'] ?? 'active';
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where = ['1=1'];
    $params = [];
    
    if (!empty($type)) {
        $where[] = 'a.type = ?';
        $params[] = $type;
    }
    
    if (!empty($status)) {
        $where[] = 'a.status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $where);
    
    // 获取公告列表
    $sql = "SELECT a.id, a.title, a.content, a.type, a.status, a.is_pinned, a.created_at, a.updated_at, a.expires_at,
                   u.username as author_name,
                   CASE 
                       WHEN TIMESTAMPDIFF(MINUTE, a.created_at, NOW()) < 60 THEN CONCAT(TIMESTAMPDIFF(MINUTE, a.created_at, NOW()), '分钟前')
                       WHEN TIMESTAMPDIFF(HOUR, a.created_at, NOW()) < 24 THEN CONCAT(TIMESTAMPDIFF(HOUR, a.created_at, NOW()), '小时前')
                       ELSE CONCAT(TIMESTAMPDIFF(DAY, a.created_at, NOW()), '天前')
                   END as time_ago
            FROM announcements a
            LEFT JOIN users u ON a.author_id = u.id
            WHERE {$whereClause}
            ORDER BY a.is_pinned DESC, a.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $announcements = $db->fetchAll($sql, $params);
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM announcements a WHERE {$whereClause}";
    $countParams = array_slice($params, 0, -2);
    $totalResult = $db->fetchOne($countSql, $countParams);
    $total = $totalResult['total'];
    
    echo json_encode([
        'success' => true,
        'data' => [
            'announcements' => $announcements,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]
    ]);
}

/**
 * 处理POST请求 - 创建公告
 */
function handlePost($db, $input) {
    if (!$input || !isset($input['title'], $input['content'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        return;
    }
    
    $data = [
        'title' => $input['title'],
        'content' => $input['content'],
        'type' => $input['type'] ?? 'normal',
        'status' => $input['status'] ?? 'active',
        'is_pinned' => $input['is_pinned'] ?? false,
        'author_id' => $input['author_id'] ?? 1, // 默认管理员
        'expires_at' => $input['expires_at'] ?? null
    ];
    
    $sql = "INSERT INTO announcements (title, content, type, status, is_pinned, author_id, expires_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    $db->query($sql, [
        $data['title'],
        $data['content'],
        $data['type'],
        $data['status'],
        $data['is_pinned'],
        $data['author_id'],
        $data['expires_at']
    ]);
    
    $announcementId = $db->lastInsertId();
    
    // 记录活动
    $db->query("INSERT INTO user_activities (user_id, activity_type, title, description) VALUES (?, ?, ?, ?)", [
        $data['author_id'],
        'system_update',
        '发布公告',
        "发布了新公告: {$data['title']}"
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => '公告创建成功',
        'data' => ['id' => $announcementId]
    ]);
}

/**
 * 处理PUT请求 - 更新公告
 */
function handlePut($db, $input) {
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少公告ID']);
        return;
    }
    
    $announcementId = $input['id'];
    
    // 检查公告是否存在
    $announcement = $db->fetchOne("SELECT id, title FROM announcements WHERE id = ?", [$announcementId]);
    if (!$announcement) {
        http_response_code(404);
        echo json_encode(['error' => '公告不存在']);
        return;
    }
    
    // 构建更新数据
    $updateFields = [];
    $params = [];
    
    if (isset($input['title'])) {
        $updateFields[] = 'title = ?';
        $params[] = $input['title'];
    }
    if (isset($input['content'])) {
        $updateFields[] = 'content = ?';
        $params[] = $input['content'];
    }
    if (isset($input['type'])) {
        $updateFields[] = 'type = ?';
        $params[] = $input['type'];
    }
    if (isset($input['status'])) {
        $updateFields[] = 'status = ?';
        $params[] = $input['status'];
    }
    if (isset($input['is_pinned'])) {
        $updateFields[] = 'is_pinned = ?';
        $params[] = $input['is_pinned'];
    }
    if (isset($input['expires_at'])) {
        $updateFields[] = 'expires_at = ?';
        $params[] = $input['expires_at'];
    }
    
    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['error' => '没有要更新的字段']);
        return;
    }
    
    $updateFields[] = 'updated_at = NOW()';
    $params[] = $announcementId;
    
    $sql = "UPDATE announcements SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $db->query($sql, $params);
    
    echo json_encode(['success' => true, 'message' => '公告更新成功']);
}

/**
 * 处理DELETE请求 - 删除公告
 */
function handleDelete($db) {
    $announcementId = $_GET['id'] ?? null;
    
    if (!$announcementId) {
        http_response_code(400);
        echo json_encode(['error' => '缺少公告ID']);
        return;
    }
    
    // 检查公告是否存在
    $announcement = $db->fetchOne("SELECT title FROM announcements WHERE id = ?", [$announcementId]);
    if (!$announcement) {
        http_response_code(404);
        echo json_encode(['error' => '公告不存在']);
        return;
    }
    
    $sql = "DELETE FROM announcements WHERE id = ?";
    $db->query($sql, [$announcementId]);
    
    // 记录活动
    $db->query("INSERT INTO user_activities (activity_type, title, description) VALUES (?, ?, ?)", [
        'system_update',
        '删除公告',
        "删除了公告: {$announcement['title']}"
    ]);
    
    echo json_encode(['success' => true, 'message' => '公告删除成功']);
}

// 插入一些示例数据
try {
    $db = db();
    
    // 检查是否已有数据
    $count = $db->fetchOne("SELECT COUNT(*) as count FROM announcements");
    if ($count['count'] == 0) {
        // 插入示例公告
        $sampleAnnouncements = [
            [
                'title' => '系统维护通知',
                'content' => '系统将于2025年1月20日凌晨2:00-4:00进行维护升级，期间可能无法正常访问，请提前做好准备。',
                'type' => 'important',
                'is_pinned' => true,
                'author_id' => 1
            ],
            [
                'title' => '新功能上线',
                'content' => '我们很高兴地宣布，新的日程管理功能已经上线，用户可以更方便地管理自己的学习计划。',
                'type' => 'normal',
                'is_pinned' => false,
                'author_id' => 1
            ],
            [
                'title' => '用户协议更新',
                'content' => '我们已更新用户协议和隐私政策，请查看最新版本。',
                'type' => 'normal',
                'is_pinned' => false,
                'author_id' => 1
            ]
        ];
        
        foreach ($sampleAnnouncements as $announcement) {
            $db->query("INSERT INTO announcements (title, content, type, is_pinned, author_id) VALUES (?, ?, ?, ?, ?)", [
                $announcement['title'],
                $announcement['content'],
                $announcement['type'],
                $announcement['is_pinned'],
                $announcement['author_id']
            ]);
        }
    }
} catch (Exception $e) {
    // 忽略错误，可能是在API调用中
}
?>
