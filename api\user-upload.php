<?php
// 设置session配置
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0);
ini_set('session.cookie_samesite', 'Lax');
ini_set('session.use_strict_mode', 1);

session_start();
header('Content-Type: application/json');

// 引入Auth类
require_once '../classes/Auth.php';

// 检查用户是否已登录
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '请先登录']);
    exit;
}

$currentUser = $auth->getCurrentUser();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => '不支持的请求方法']);
    exit;
}

try {
    // 检查是否有文件上传
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('文件上传失败');
    }
    
    $file = $_FILES['file'];
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmpName = $file['tmp_name'];
    $fileType = $file['type'];
    
    // 文件大小限制 (10MB)
    $maxSize = 10 * 1024 * 1024;
    if ($fileSize > $maxSize) {
        throw new Exception('文件大小不能超过10MB');
    }
    
    // 允许的文件类型
    $allowedTypes = [
        // 图片
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
        // 音频
        'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg',
        // 视频
        'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
        // 文档
        'application/pdf', 'text/plain',
        'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    if (!in_array($fileType, $allowedTypes)) {
        throw new Exception('不支持的文件类型');
    }
    
    // 生成安全的文件名
    $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
    $safeFileName = uniqid() . '_' . time() . '.' . $fileExtension;
    
    // 确定上传目录
    $uploadDir = '../uploads/posts/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $uploadPath = $uploadDir . $safeFileName;
    
    // 移动文件
    if (!move_uploaded_file($fileTmpName, $uploadPath)) {
        throw new Exception('文件保存失败');
    }
    
    // 返回文件URL
    $fileUrl = 'uploads/posts/' . $safeFileName;
    
    // 记录上传日志（可选）
    try {
        require_once '../config/database.php';
        $db = db();
        $db->execute(
            "INSERT INTO file_uploads (user_id, original_name, file_name, file_path, file_size, file_type, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, datetime('now'))",
            [$currentUser['id'], $fileName, $safeFileName, $fileUrl, $fileSize, $fileType]
        );
    } catch (Exception $e) {
        // 日志记录失败不影响文件上传
        error_log("文件上传日志记录失败: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'location' => $fileUrl,
        'filename' => $fileName,
        'size' => $fileSize,
        'type' => $fileType
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
