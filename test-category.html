<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; }
        .modal-overlay {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000;
        }
        .modal { background: white; padding: 20px; border-radius: 8px; width: 400px; max-width: 90%; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input, .form-group textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>分类功能自动测试</h1>
    
    <div class="test-section">
        <h2>测试控制面板</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="testModalCreation()">测试模态框创建</button>
        <button onclick="testFormSubmission()">测试表单提交</button>
        <button onclick="clearResults()">清除结果</button>
    </div>
    
    <div id="testResults"></div>
    
    <script>
        // 复制分类相关函数
        window.showAddCategoryModal = function() {
            console.log('showAddCategoryModal 函数被调用');
            
            // 移除已存在的模态框
            const existingModal = document.querySelector('.modal-overlay');
            if (existingModal) {
                existingModal.remove();
            }
            
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal">
                    <h3>添加分类</h3>
                    <form id="addCategoryForm">
                        <div class="form-group">
                            <label>分类名称 *</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>分类描述</label>
                            <textarea name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>分类颜色</label>
                            <input type="color" name="color" value="#007bff">
                        </div>
                        <div class="form-group">
                            <label>图标 (emoji)</label>
                            <input type="text" name="icon" placeholder="📁">
                        </div>
                        <div class="form-group">
                            <label>排序</label>
                            <input type="number" name="sort_order" value="0" min="0">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" name="is_active" checked> 启用分类
                            </label>
                        </div>
                        <div style="text-align: right; margin-top: 20px;">
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="console.log('按钮被点击'); window.addCategory(); return false;">添加</button>
                        </div>
                    </form>
                </div>
            `;
            document.body.appendChild(modal);
            addTestResult('模态框已创建', 'success');
            return modal;
        }
        
        window.addCategory = function() {
            console.log('addCategory 函数被调用');
            addTestResult('addCategory 函数被成功调用', 'success');
            
            const form = document.getElementById('addCategoryForm');
            if (!form) {
                addTestResult('找不到表单元素', 'error');
                return;
            }
            
            const formData = new FormData(form);
            const categoryData = {
                action: 'add_category',
                name: formData.get('name'),
                description: formData.get('description'),
                color: formData.get('color'),
                icon: formData.get('icon'),
                sort_order: parseInt(formData.get('sort_order')),
                is_active: formData.has('is_active')
            };
            
            addTestResult('表单数据收集成功: ' + JSON.stringify(categoryData), 'success');
            
            // 模拟API调用
            setTimeout(() => {
                addTestResult('模拟API调用成功', 'success');
                document.querySelector('.modal-overlay').remove();
                addTestResult('模态框已关闭', 'info');
            }, 1000);
        }
        
        function addTestResult(message, type) {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            console.log(`[TEST] ${message}`);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        function testModalCreation() {
            addTestResult('开始测试模态框创建...', 'info');
            try {
                const modal = window.showAddCategoryModal();
                if (modal && modal.querySelector('.modal')) {
                    addTestResult('✓ 模态框创建成功', 'success');
                } else {
                    addTestResult('✗ 模态框创建失败', 'error');
                }
            } catch (error) {
                addTestResult('✗ 模态框创建出错: ' + error.message, 'error');
            }
        }
        
        function testFormSubmission() {
            addTestResult('开始测试表单提交...', 'info');
            
            // 先创建模态框
            const modal = window.showAddCategoryModal();
            
            setTimeout(() => {
                // 填写表单
                const nameInput = modal.querySelector('input[name="name"]');
                const descInput = modal.querySelector('textarea[name="description"]');
                
                if (nameInput) {
                    nameInput.value = '测试分类';
                    addTestResult('✓ 表单字段填写完成', 'success');
                    
                    // 点击添加按钮
                    const addBtn = modal.querySelector('.btn-primary');
                    if (addBtn) {
                        addBtn.click();
                        addTestResult('✓ 添加按钮点击成功', 'success');
                    } else {
                        addTestResult('✗ 找不到添加按钮', 'error');
                    }
                } else {
                    addTestResult('✗ 找不到表单字段', 'error');
                }
            }, 500);
        }
        
        function runAllTests() {
            clearResults();
            addTestResult('开始运行所有测试...', 'info');
            
            // 测试1: 函数定义检查
            addTestResult('测试1: 检查函数定义', 'info');
            if (typeof window.showAddCategoryModal === 'function') {
                addTestResult('✓ showAddCategoryModal 函数已定义', 'success');
            } else {
                addTestResult('✗ showAddCategoryModal 函数未定义', 'error');
            }
            
            if (typeof window.addCategory === 'function') {
                addTestResult('✓ addCategory 函数已定义', 'success');
            } else {
                addTestResult('✗ addCategory 函数未定义', 'error');
            }
            
            // 测试2: 模态框创建
            setTimeout(() => {
                testModalCreation();
                
                // 测试3: 表单提交
                setTimeout(() => {
                    testFormSubmission();
                }, 2000);
            }, 1000);
        }
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            addTestResult('页面加载完成，准备开始测试', 'info');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
