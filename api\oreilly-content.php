<?php
session_start();
header('Content-Type: application/json');

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

require_once '../config/database.php';

$db = db();
$action = $_GET['action'] ?? '';
$method = $_SERVER['REQUEST_METHOD'];

try {
    if ($method === 'GET') {
        // 获取O'Reilly区域内容
        switch ($action) {
            case 'hero':
                $data = $db->fetchOne("SELECT * FROM oreilly_hero_section WHERE is_active = 1");
                break;
                
            case 'courses':
                $liveCourses = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'live_courses' AND is_active = 1");
                $aiAnswers = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'ai_answers' AND is_active = 1");
                $data = [
                    'live_courses' => $liveCourses,
                    'ai_answers' => $aiAnswers
                ];
                break;
                
            case 'experts':
                $data = $db->fetchOne("SELECT * FROM oreilly_experts_section WHERE is_active = 1");
                break;
                
            case 'testimonial':
                $data = $db->fetchOne("SELECT * FROM oreilly_testimonial_section WHERE is_active = 1");
                break;
                
            case 'cta':
                $data = $db->fetchOne("SELECT * FROM oreilly_cta_section WHERE is_active = 1");
                break;
                
            case 'all':
                // 获取所有区域数据
                $hero = $db->fetchOne("SELECT * FROM oreilly_hero_section WHERE is_active = 1");
                $liveCourses = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'live_courses' AND is_active = 1");
                $aiAnswers = $db->fetchOne("SELECT * FROM oreilly_courses_section WHERE section_type = 'ai_answers' AND is_active = 1");
                $experts = $db->fetchOne("SELECT * FROM oreilly_experts_section WHERE is_active = 1");
                $testimonial = $db->fetchOne("SELECT * FROM oreilly_testimonial_section WHERE is_active = 1");
                $cta = $db->fetchOne("SELECT * FROM oreilly_cta_section WHERE is_active = 1");
                
                $data = [
                    'hero' => $hero,
                    'courses' => [
                        'live_courses' => $liveCourses,
                        'ai_answers' => $aiAnswers
                    ],
                    'experts' => $experts,
                    'testimonial' => $testimonial,
                    'cta' => $cta
                ];
                break;
                
            default:
                throw new Exception('无效的操作类型');
        }
        
        echo json_encode(['success' => true, 'data' => $data]);
        
    } elseif ($method === 'POST') {
        // 保存O'Reilly区域内容
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            throw new Exception('无效的JSON数据');
        }
        
        switch ($action) {
            case 'hero':
                $sql = "UPDATE oreilly_hero_section SET 
                        main_title = :main_title,
                        description = :description,
                        primary_button_text = :primary_button_text,
                        primary_button_url = :primary_button_url,
                        secondary_button_text = :secondary_button_text,
                        secondary_button_url = :secondary_button_url,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = 1";
                        
                $params = [
                    'main_title' => $input['main_title'],
                    'description' => $input['description'],
                    'primary_button_text' => $input['primary_button_text'],
                    'primary_button_url' => $input['primary_button_url'],
                    'secondary_button_text' => $input['secondary_button_text'],
                    'secondary_button_url' => $input['secondary_button_url']
                ];
                
                $db->execute($sql, $params);
                $message = 'O\'Reilly英雄区域内容保存成功';
                break;
                
            case 'courses':
                // 更新专家课程区域
                if (isset($input['live_courses'])) {
                    $sql = "UPDATE oreilly_courses_section SET 
                            title = :title,
                            description = :description,
                            button_text = :button_text,
                            button_url = :button_url,
                            updated_at = CURRENT_TIMESTAMP
                            WHERE section_type = 'live_courses'";
                            
                    $db->execute($sql, $input['live_courses']);
                }
                
                if (isset($input['ai_answers'])) {
                    $sql = "UPDATE oreilly_courses_section SET 
                            title = :title,
                            description = :description,
                            button_text = :button_text,
                            button_url = :button_url,
                            updated_at = CURRENT_TIMESTAMP
                            WHERE section_type = 'ai_answers'";
                            
                    $db->execute($sql, $input['ai_answers']);
                }
                
                $message = '专家课程区域内容保存成功';
                break;
                
            case 'experts':
                $sql = "UPDATE oreilly_experts_section SET 
                        main_title = :main_title,
                        description = :description,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = 1";
                        
                $params = [
                    'main_title' => $input['main_title'],
                    'description' => $input['description']
                ];
                
                $db->execute($sql, $params);
                $message = '专家展示区域内容保存成功';
                break;
                
            case 'testimonial':
                $sql = "UPDATE oreilly_testimonial_section SET 
                        title = :title,
                        description = :description,
                        stat1_number = :stat1_number,
                        stat1_label = :stat1_label,
                        stat2_number = :stat2_number,
                        stat2_label = :stat2_label,
                        stat3_number = :stat3_number,
                        stat3_label = :stat3_label,
                        button_text = :button_text,
                        button_url = :button_url,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = 1";
                        
                $db->execute($sql, $input);
                $message = '推荐区域内容保存成功';
                break;
                
            case 'cta':
                $sql = "UPDATE oreilly_cta_section SET 
                        main_title = :main_title,
                        primary_button_text = :primary_button_text,
                        primary_button_url = :primary_button_url,
                        secondary_button_text = :secondary_button_text,
                        secondary_button_url = :secondary_button_url,
                        updated_at = CURRENT_TIMESTAMP
                        WHERE id = 1";
                        
                $params = [
                    'main_title' => $input['main_title'],
                    'primary_button_text' => $input['primary_button_text'],
                    'primary_button_url' => $input['primary_button_url'],
                    'secondary_button_text' => $input['secondary_button_text'],
                    'secondary_button_url' => $input['secondary_button_url']
                ];
                
                $db->execute($sql, $params);
                $message = '行动号召区域内容保存成功';
                break;
                
            default:
                throw new Exception('无效的操作类型');
        }
        
        echo json_encode(['success' => true, 'message' => $message]);
        
    } else {
        throw new Exception('不支持的请求方法');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
