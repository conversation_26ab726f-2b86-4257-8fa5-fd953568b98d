@echo off
echo ========================================
echo 比特熊智慧系统 - 服务器连接工具
echo ========================================
echo.
echo 正在连接到服务器 43.134.80.134...
echo 用户名: root
echo 密码: ZbDX7%=]?H2(LAUz
echo.
echo 注意: 输入密码时不会显示字符，这是正常的安全特性
echo.

REM 尝试多种SSH客户端
echo 正在检测SSH客户端...

REM 方法1: 检查Windows内置SSH
if exist "C:\Windows\System32\OpenSSH\ssh.exe" (
    echo ✓ 使用Windows内置SSH客户端
    echo.
    echo 连接命令: C:\Windows\System32\OpenSSH\ssh.exe root@43.134.80.134
    echo.
    C:\Windows\System32\OpenSSH\ssh.exe root@43.134.80.134
    goto :end
)

REM 方法2: 检查Git SSH
if exist "C:\Program Files\Git\usr\bin\ssh.exe" (
    echo ✓ 使用Git SSH客户端
    echo.
    echo 连接命令: "C:\Program Files\Git\usr\bin\ssh.exe" root@43.134.80.134
    echo.
    "C:\Program Files\Git\usr\bin\ssh.exe" root@43.134.80.134
    goto :end
)

REM 方法3: 检查系统PATH中的SSH
ssh --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 使用系统PATH中的SSH客户端
    echo.
    echo 连接命令: ssh root@43.134.80.134
    echo.
    ssh root@43.134.80.134
    goto :end
)

REM 没有找到SSH客户端
echo ❌ 未找到SSH客户端
echo.
echo 请安装以下任一SSH客户端：
echo.
echo 1. Windows OpenSSH客户端:
echo    - 打开"设置" > "应用" > "可选功能"
echo    - 点击"添加功能"
echo    - 搜索并安装"OpenSSH客户端"
echo.
echo 2. Git for Windows:
echo    - 访问 https://git-scm.com/download/win
echo    - 下载并安装Git for Windows
echo.
echo 3. PuTTY:
echo    - 访问 https://www.putty.org/
echo    - 下载并安装PuTTY
echo.

:end
pause
