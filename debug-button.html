<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮调试工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .results { margin-top: 20px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px; }
        iframe { width: 100%; height: 600px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 按钮调试工具</h1>
        <p>这个工具将帮助诊断admin-dashboard.php中添加分类按钮的问题。</p>
        
        <div class="debug-section">
            <h2>调试控制</h2>
            <button onclick="openAdminDashboard()">🔗 打开管理面板</button>
            <button onclick="testButtonInFrame()">🧪 在框架中测试按钮</button>
            <button onclick="injectDebugScript()">💉 注入调试脚本</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
        </div>
        
        <div class="results" id="results"></div>
        
        <div class="debug-section">
            <h2>管理面板预览</h2>
            <iframe id="adminFrame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function openAdminDashboard() {
            log('🔗 正在加载管理面板...', 'info');
            const iframe = document.getElementById('adminFrame');
            iframe.src = '/admin-dashboard.php';
            
            iframe.onload = function() {
                log('✅ 管理面板已加载', 'success');
                log('💡 现在可以测试按钮功能', 'info');
            };
            
            iframe.onerror = function() {
                log('❌ 管理面板加载失败', 'error');
            };
        }

        function testButtonInFrame() {
            log('🧪 开始在框架中测试按钮...', 'info');
            const iframe = document.getElementById('adminFrame');
            
            if (!iframe.src || iframe.src === 'about:blank') {
                log('⚠️ 请先打开管理面板', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查是否能访问iframe内容
                if (!iframeDoc) {
                    log('❌ 无法访问iframe内容（可能是跨域问题）', 'error');
                    return;
                }
                
                // 切换到分类管理页面
                log('📋 切换到分类管理页面...', 'info');
                const switchPageFunc = iframe.contentWindow.switchPage;
                if (switchPageFunc) {
                    switchPageFunc('categories-management');
                    log('✅ 已切换到分类管理页面', 'success');
                } else {
                    log('❌ 找不到switchPage函数', 'error');
                    return;
                }
                
                // 等待页面切换完成
                setTimeout(() => {
                    // 查找添加分类按钮
                    const addButton = iframeDoc.querySelector('button[onclick*="showAddCategoryModal"]');
                    if (addButton) {
                        log('✅ 找到添加分类按钮', 'success');
                        log(`📝 按钮文本: "${addButton.textContent.trim()}"`, 'info');
                        log(`🔗 onclick属性: "${addButton.getAttribute('onclick')}"`, 'info');
                        
                        // 检查函数是否存在
                        const showAddCategoryModal = iframe.contentWindow.showAddCategoryModal;
                        if (showAddCategoryModal) {
                            log('✅ showAddCategoryModal函数存在', 'success');
                            
                            // 模拟点击
                            log('🖱️ 模拟点击按钮...', 'info');
                            try {
                                addButton.click();
                                log('✅ 按钮点击成功', 'success');
                                
                                // 检查是否出现模态框
                                setTimeout(() => {
                                    const modal = iframeDoc.querySelector('.modal-overlay');
                                    if (modal) {
                                        log('🎉 模态框已出现！', 'success');
                                        log(`📋 模态框类名: ${modal.className}`, 'info');
                                    } else {
                                        log('❌ 模态框未出现', 'error');
                                    }
                                }, 500);
                                
                            } catch (error) {
                                log(`❌ 点击按钮时出错: ${error.message}`, 'error');
                            }
                        } else {
                            log('❌ showAddCategoryModal函数不存在', 'error');
                        }
                    } else {
                        log('❌ 找不到添加分类按钮', 'error');
                        
                        // 列出所有按钮
                        const allButtons = iframeDoc.querySelectorAll('button');
                        log(`📊 页面中共有 ${allButtons.length} 个按钮`, 'info');
                        allButtons.forEach((btn, index) => {
                            if (btn.textContent.includes('添加') || btn.textContent.includes('分类')) {
                                log(`🔍 按钮${index}: "${btn.textContent.trim()}" - onclick: "${btn.getAttribute('onclick') || '无'}"`, 'info');
                            }
                        });
                    }
                }, 1000);
                
            } catch (error) {
                log(`❌ 测试过程中出错: ${error.message}`, 'error');
            }
        }

        function injectDebugScript() {
            log('💉 正在注入调试脚本...', 'info');
            const iframe = document.getElementById('adminFrame');
            
            if (!iframe.src || iframe.src === 'about:blank') {
                log('⚠️ 请先打开管理面板', 'error');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeWindow = iframe.contentWindow;
                
                // 注入调试脚本
                const script = iframeDoc.createElement('script');
                script.textContent = `
                    // 调试脚本
                    console.log('🔧 调试脚本已注入');
                    
                    // 重新定义showAddCategoryModal函数，添加更多调试信息
                    const originalShowAddCategoryModal = window.showAddCategoryModal;
                    window.showAddCategoryModal = function() {
                        console.log('🚀 showAddCategoryModal被调用');
                        console.log('📍 调用堆栈:', new Error().stack);
                        
                        try {
                            return originalShowAddCategoryModal.apply(this, arguments);
                        } catch (error) {
                            console.error('❌ showAddCategoryModal执行出错:', error);
                            alert('函数执行出错: ' + error.message);
                        }
                    };
                    
                    // 监听所有点击事件
                    document.addEventListener('click', function(e) {
                        if (e.target.tagName === 'BUTTON') {
                            console.log('🖱️ 按钮被点击:', e.target.textContent.trim());
                            console.log('🔗 onclick属性:', e.target.getAttribute('onclick'));
                        }
                    }, true);
                    
                    // 检查函数定义
                    console.log('🔍 函数检查:');
                    console.log('showAddCategoryModal:', typeof window.showAddCategoryModal);
                    console.log('addCategory:', typeof window.addCategory);
                `;
                
                iframeDoc.head.appendChild(script);
                log('✅ 调试脚本注入成功', 'success');
                log('💡 现在可以在iframe中测试按钮，查看控制台输出', 'info');
                
            } catch (error) {
                log(`❌ 注入调试脚本失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            log('🎯 按钮调试工具已加载', 'info');
            log('📋 使用步骤:', 'info');
            log('1. 点击"打开管理面板"加载admin-dashboard.php', 'info');
            log('2. 点击"在框架中测试按钮"自动测试按钮功能', 'info');
            log('3. 如需更详细调试，点击"注入调试脚本"', 'info');
        };
    </script>
</body>
</html>
