<?php
/**
 * 前台导航栏组件
 * 从数据库动态加载导航栏结构
 */

require_once __DIR__ . '/../config/database.php';

function getNavbarData() {
    // 使用缓存来提高性能
    static $cachedNavbar = null;
    static $cacheTime = null;

    // 缓存5分钟
    if ($cachedNavbar !== null && $cacheTime !== null && (time() - $cacheTime) < 300) {
        return $cachedNavbar;
    }

    try {
        $db = DatabaseConfig::getInstance();

        // 优化的SQL查询，只获取必要字段
        $sql = "SELECT id, name, url, type, parent_id, sort_order FROM navbar_items WHERE visible = 1 ORDER BY sort_order ASC, id ASC";
        $items = $db->fetchAll($sql);

        // 构建树形结构
        $result = buildNavbarTree($items);

        // 缓存结果
        $cachedNavbar = $result;
        $cacheTime = time();

        return $result;
    } catch (Exception $e) {
        // 如果数据库出错，返回默认导航
        error_log("导航栏数据加载失败: " . $e->getMessage());
        return getDefaultNavbar();
    }
}

function buildNavbarTree($items) {
    $tree = [];
    $itemsById = [];
    
    // 按ID索引所有项目
    foreach ($items as $item) {
        $itemsById[$item['id']] = $item;
        $itemsById[$item['id']]['children'] = [];
    }
    
    // 构建树形结构
    foreach ($items as $item) {
        if ($item['parent_id'] === null) {
            $tree[] = &$itemsById[$item['id']];
        } else {
            if (isset($itemsById[$item['parent_id']])) {
                $itemsById[$item['parent_id']]['children'][] = &$itemsById[$item['id']];
            }
        }
    }
    
    return $tree;
}

function getDefaultNavbar() {
    return [
        [
            'id' => 1,
            'name' => '首页',
            'url' => '/index.php',
            'type' => 'link',
            'children' => []
        ],
        [
            'id' => 2,
            'name' => '组织',
            'url' => '#',
            'type' => 'dropdown',
            'children' => [
                ['id' => 21, 'name' => '关于我们', 'url' => '/about.php', 'type' => 'submenu'],
                ['id' => 22, 'name' => '团队介绍', 'url' => '/team.php', 'type' => 'submenu'],
                ['id' => 23, 'name' => '联系我们', 'url' => '/contact.php', 'type' => 'submenu']
            ]
        ],
        [
            'id' => 3,
            'name' => '服务',
            'url' => '/services.php',
            'type' => 'link',
            'children' => []
        ],
        [
            'id' => 4,
            'name' => '新闻',
            'url' => '/news.php',
            'type' => 'link',
            'children' => []
        ]
    ];
}

function renderNavbar($navbarData = null) {
    if ($navbarData === null) {
        $navbarData = getNavbarData();
    }
    
    $html = '';
    
    foreach ($navbarData as $item) {
        $html .= renderNavItem($item);
    }
    
    return $html;
}

function renderNavItem($item) {
    $hasChildren = !empty($item['children']);
    $itemClass = $hasChildren ? 'nav-item dropdown' : 'nav-item';
    
    $html = '<li class="' . $itemClass . '">';
    
    if ($hasChildren) {
        // 下拉菜单
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">';
        $html .= htmlspecialchars($item['name']);
        $html .= '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
        $html .= '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>';
        $html .= '</svg>';
        $html .= '</a>';
        
        // 下拉菜单内容
        $html .= '<ul class="dropdown-menu">';
        foreach ($item['children'] as $child) {
            $html .= '<li>';
            $html .= '<a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">';
            $html .= htmlspecialchars($child['name']);
            $html .= '</a>';
            $html .= '</li>';
        }
        $html .= '</ul>';
    } else {
        // 普通链接
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">';
        $html .= htmlspecialchars($item['name']);
        $html .= '</a>';
    }
    
    $html .= '</li>';
    
    return $html;
}

// 这个文件只提供数据，不包含HTML结构
// HTML结构在index.php中处理
