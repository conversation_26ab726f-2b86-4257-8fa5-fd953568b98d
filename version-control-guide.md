# 比特熊智慧系统版本控制指南

## 分支策略

### 主要分支
- `main` - 生产环境分支，只包含稳定版本
- `develop` - 开发分支，用于集成新功能
- `feature/功能名` - 功能开发分支

### 工作流程

1. **开发新功能**
```bash
# 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/student-management

# 开发完成后
git add .
git commit -m "feat: 添加学生管理功能"
git push origin feature/student-management
```

2. **合并到开发分支**
```bash
git checkout develop
git merge feature/student-management
git push origin develop
```

3. **发布到生产环境**
```bash
# 测试通过后合并到main
git checkout main
git merge develop
git tag v1.2.0  # 打版本标签
git push origin main --tags
```

## 提交信息规范

- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 样式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

## 数据库变更管理

### 创建数据库更新文件
```sql
-- database/updates/v1.2.0_add_student_table.sql
CREATE TABLE IF NOT EXISTS students (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 版本记录表
```sql
CREATE TABLE IF NOT EXISTS version_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    description TEXT,
    deployed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
