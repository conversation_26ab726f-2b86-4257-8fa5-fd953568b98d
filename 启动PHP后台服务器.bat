@echo off
chcp 65001 >nul
echo ========================================
echo    比特熊智慧系统 - PHP后台服务器启动
echo ========================================
echo.

cd /d "%~dp0"

echo 正在检查PHP环境...
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到PHP环境！
    echo.
    echo 请先安装PHP或确保PHP已添加到系统PATH中
    echo 您可以：
    echo 1. 安装XAMPP（推荐）
    echo 2. 下载PHP并配置环境变量
    echo 3. 使用WAMP或其他集成环境
    echo.
    pause
    exit /b 1
)

echo [成功] PHP环境检查通过
php --version
echo.

echo 正在启动PHP内置服务器...
echo 服务器地址: http://localhost:8000
echo 后台地址: http://localhost:8000/admin/
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

start "" "http://localhost:8000/admin/init-database.php"

php -S localhost:8000

pause
