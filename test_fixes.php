<?php
/**
 * 测试修复的功能
 */

echo "=== 比特熊智慧系统 - 功能修复测试 ===\n\n";

// 测试1：检查评论排序JavaScript文件
echo "1. 检查评论排序功能修复...\n";
$jsFile = 'assets/js/community-post-detail.js';
if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    
    // 检查是否包含修复的排序函数
    if (strpos($jsContent, 'function sortComments(sortType)') !== false) {
        echo "   ✓ 排序函数存在\n";
        
        // 检查是否修复了选择器问题
        if (strpos($jsContent, '.like-btn\').textContent.trim()') !== false) {
            echo "   ✓ 点赞按钮选择器已修复\n";
        } else {
            echo "   ✗ 点赞按钮选择器未修复\n";
        }
        
        // 检查是否有错误处理
        if (strpos($jsContent, 'if (!commentsList) return;') !== false) {
            echo "   ✓ 错误处理已添加\n";
        } else {
            echo "   ✗ 缺少错误处理\n";
        }
    } else {
        echo "   ✗ 排序函数不存在\n";
    }
} else {
    echo "   ✗ JavaScript文件不存在\n";
}

echo "\n";

// 测试2：检查评论点赞API修复
echo "2. 检查评论点赞API修复...\n";
$apiFile = 'api/comment-actions.php';
if (file_exists($apiFile)) {
    $apiContent = file_get_contents($apiFile);
    
    // 检查是否修复了时间函数
    if (strpos($apiContent, "date('Y-m-d H:i:s')") !== false) {
        echo "   ✓ 时间函数已修复为兼容格式\n";
    } else if (strpos($apiContent, "datetime('now')") !== false) {
        echo "   ✗ 仍使用SQLite特有的时间函数\n";
    } else {
        echo "   ? 时间函数状态未知\n";
    }
    
    // 检查是否有错误处理
    if (strpos($apiContent, 'try {') !== false && strpos($apiContent, 'catch (Exception $e)') !== false) {
        echo "   ✓ 错误处理存在\n";
    } else {
        echo "   ✗ 缺少错误处理\n";
    }
} else {
    echo "   ✗ API文件不存在\n";
}

echo "\n";

// 测试3：检查退出登录功能修复
echo "3. 检查退出登录功能修复...\n";
$authFile = 'classes/Auth.php';
if (file_exists($authFile)) {
    $authContent = file_get_contents($authFile);
    
    // 检查logout方法是否返回结果
    if (strpos($authContent, "return [") !== false && strpos($authContent, "'success' => true") !== false) {
        echo "   ✓ logout方法已修复为返回结果\n";
    } else {
        echo "   ✗ logout方法未返回结果\n";
    }
} else {
    echo "   ✗ Auth类文件不存在\n";
}

// 检查index.php中的退出登录链接
$indexFile = 'index.php';
if (file_exists($indexFile)) {
    $indexContent = file_get_contents($indexFile);
    
    // 检查是否修改为AJAX调用
    if (strpos($indexContent, 'onclick="handleLogout(event)"') !== false) {
        echo "   ✓ 退出登录链接已修改为AJAX调用\n";
    } else {
        echo "   ✗ 退出登录链接仍为直接链接\n";
    }
    
    // 检查是否添加了handleLogout函数
    if (strpos($indexContent, 'function handleLogout(event)') !== false) {
        echo "   ✓ handleLogout函数已添加\n";
    } else {
        echo "   ✗ 缺少handleLogout函数\n";
    }
} else {
    echo "   ✗ index.php文件不存在\n";
}

echo "\n";

// 测试4：检查community-post-detail.php中的datetime属性
echo "4. 检查评论时间属性修复...\n";
$postDetailFile = 'community-post-detail.php';
if (file_exists($postDetailFile)) {
    $postContent = file_get_contents($postDetailFile);
    
    // 检查是否添加了datetime属性
    if (strpos($postContent, 'datetime="\'') !== false) {
        echo "   ✓ 评论时间datetime属性已添加\n";
    } else {
        echo "   ✗ 评论时间datetime属性未添加\n";
    }
} else {
    echo "   ✗ community-post-detail.php文件不存在\n";
}

echo "\n=== 测试完成 ===\n";
echo "请在浏览器中测试以下功能：\n";
echo "1. 访问社区帖子详情页，测试评论排序功能\n";
echo "2. 尝试给评论点赞，检查是否还有服务器错误\n";
echo "3. 尝试退出登录，检查是否还有数组访问错误\n";
?>
