<?php
/**
 * 用户认证初始化文件
 * 在所有需要用户认证的页面中包含此文件
 */

// 设置session配置（如果还没有启动session）
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // 本地开发环境设为0
    ini_set('session.cookie_samesite', 'Lax');
    ini_set('session.use_strict_mode', 1);
    session_start();
}

// 初始化用户变量
$currentUser = null;
$isAuthenticated = false;
$auth = null;

try {
    // 引入必要的文件
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../classes/Auth.php';

    // 检查用户登录状态
    $auth = new Auth();
    $currentUser = $auth->getCurrentUser();
    $isAuthenticated = $auth->isLoggedIn();

    // 确保 $currentUser 是数组格式
    if ($currentUser && !is_array($currentUser)) {
        $currentUser = (array) $currentUser;
    }

} catch (Exception $e) {
    // 如果认证过程出错，记录错误但不中断页面加载
    error_log("用户认证初始化错误: " . $e->getMessage());
    $currentUser = null;
    $isAuthenticated = false;
    $auth = null;
}

/**
 * 安全的HTML输出函数 (如果未定义)
 */
if (!function_exists('escapeHtml')) {
    function escapeHtml($text) {
        return htmlspecialchars($text ?? '', ENT_QUOTES, 'UTF-8');
    }
}

/**
 * 检查用户是否有特定权限
 */
function hasPermission($permission) {
    global $auth, $isAuthenticated;
    
    if (!$isAuthenticated || !isset($auth)) {
        return false;
    }
    
    try {
        return $auth->hasPermission($permission);
    } catch (Exception $e) {
        error_log("权限检查错误: " . $e->getMessage());
        return false;
    }
}

/**
 * 检查用户是否有特定角色
 */
function hasRole($role) {
    global $auth, $isAuthenticated;
    
    if (!$isAuthenticated || !isset($auth)) {
        return false;
    }
    
    try {
        return $auth->hasRole($role);
    } catch (Exception $e) {
        error_log("角色检查错误: " . $e->getMessage());
        return false;
    }
}

/**
 * 获取用户头像URL
 */
function getUserAvatar($user = null) {
    global $currentUser;
    
    $user = $user ?? $currentUser;
    
    if (!$user) {
        return 'assets/images/default-avatar.png';
    }
    
    return $user['avatar'] ?? 'assets/images/default-avatar.png';
}

/**
 * 获取用户显示名称
 */
function getUserDisplayName($user = null) {
    global $currentUser;
    
    $user = $user ?? $currentUser;
    
    if (!$user) {
        return '游客';
    }
    
    return $user['nickname'] ?? $user['username'] ?? '用户';
}
?>
