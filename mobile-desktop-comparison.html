<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机vs电脑访问差异分析 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        
        .alert-warning {
            background: #fef3c7;
            border-color: #f59e0b;
            color: #92400e;
        }
        
        .alert-success {
            background: #dcfce7;
            border-color: #16a34a;
            color: #15803d;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
        
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .comparison-table th {
            background: #f1f5f9;
            font-weight: 600;
        }
        
        .status-mobile { color: #16a34a; font-weight: 600; }
        .status-desktop { color: #dc2626; font-weight: 600; }
        
        .solution-steps {
            counter-reset: step-counter;
        }
        
        .solution-step {
            counter-increment: step-counter;
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .solution-step::before {
            content: "步骤 " counter(step-counter) ": ";
            font-weight: 600;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱💻 手机vs电脑访问差异分析</h1>
        
        <div class="alert alert-info">
            <strong>🔍 诊断结果：</strong> 根据PowerShell测试，从您的电脑发出的所有请求都返回404错误，包括模拟手机User-Agent的请求。这说明问题可能不在User-Agent，而在网络路径或缓存。
        </div>
        
        <div class="test-section">
            <h3>📊 访问状态对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>设备类型</th>
                        <th>网络类型</th>
                        <th>访问状态</th>
                        <th>可能原因</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>📱 手机</td>
                        <td>移动网络/WiFi</td>
                        <td class="status-mobile">✅ 可以访问</td>
                        <td>直接网络路径，无代理干扰</td>
                    </tr>
                    <tr>
                        <td>💻 电脑</td>
                        <td>WiFi/有线</td>
                        <td class="status-desktop">❌ 无法访问</td>
                        <td>网络路径差异，可能有缓存/代理</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h3>🧪 实时测试工具</h3>
            <p>点击下面的按钮进行各种测试：</p>
            
            <button class="test-button" onclick="testDirectAccess()">直接访问测试</button>
            <button class="test-button" onclick="testWithTimestamp()">带时间戳访问</button>
            <button class="test-button" onclick="testDifferentPorts()">测试其他端口</button>
            <button class="test-button danger" onclick="clearAllCache()">清除所有缓存</button>
            
            <div id="testResults" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 可能的原因分析</h3>
            
            <div class="alert alert-warning">
                <strong>⚠️ 主要怀疑原因：</strong>
                <ul>
                    <li><strong>网络路径差异</strong> - 手机使用移动网络，电脑使用WiFi，可能经过不同的路由</li>
                    <li><strong>DNS缓存差异</strong> - 电脑可能缓存了错误的DNS解析结果</li>
                    <li><strong>浏览器缓存</strong> - 电脑浏览器可能缓存了404错误页面</li>
                    <li><strong>防火墙/安全软件</strong> - 电脑上的安全软件可能阻止了访问</li>
                    <li><strong>代理设置</strong> - 电脑可能配置了代理服务器</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🛠️ 解决方案步骤</h3>
            <div class="solution-steps">
                <div class="solution-step">
                    <strong>使用手机热点测试</strong><br>
                    让电脑连接手机热点，然后尝试访问。如果成功，说明是网络路径问题。
                    <br><br>
                    <button class="test-button" onclick="window.open('http://*************:8888/tencentcloud', '_blank')">
                        通过热点测试访问
                    </button>
                </div>
                
                <div class="solution-step">
                    <strong>清除DNS缓存</strong><br>
                    打开命令提示符（管理员权限），运行：<code>ipconfig /flushdns</code>
                    <br><br>
                    <button class="test-button" onclick="showDNSFlushInstructions()">
                        显示DNS清除指令
                    </button>
                </div>
                
                <div class="solution-step">
                    <strong>清除浏览器缓存</strong><br>
                    完全清除浏览器的缓存、Cookie和浏览数据。
                    <br><br>
                    <button class="test-button" onclick="showCacheClearInstructions()">
                        显示缓存清除指令
                    </button>
                </div>
                
                <div class="solution-step">
                    <strong>尝试隐私模式</strong><br>
                    使用浏览器的隐私/无痕模式访问，避免缓存干扰。
                    <br><br>
                    <button class="test-button" onclick="openIncognito()">
                        在新隐私窗口中打开
                    </button>
                </div>
                
                <div class="solution-step">
                    <strong>检查代理设置</strong><br>
                    确保电脑没有配置HTTP代理服务器。
                    <br><br>
                    <button class="test-button" onclick="showProxyCheckInstructions()">
                        显示代理检查方法
                    </button>
                </div>
                
                <div class="solution-step">
                    <strong>尝试不同浏览器</strong><br>
                    使用Chrome、Firefox、Edge等不同浏览器测试。
                    <br><br>
                    <button class="test-button" onclick="testMultipleBrowsers()">
                        生成多浏览器测试链接
                    </button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 系统信息检查</h3>
            <div id="systemInfo">
                <p>正在检测您的系统信息...</p>
            </div>
        </div>
    </div>
    
    <script>
        // 获取系统信息
        function getSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                connection: navigator.connection ? navigator.connection.effectiveType : 'Unknown',
                screen: `${screen.width}x${screen.height}`,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timestamp: new Date().toISOString()
            };
            
            return info;
        }
        
        // 显示系统信息
        function displaySystemInfo() {
            const info = getSystemInfo();
            const systemInfoDiv = document.getElementById('systemInfo');
            
            let html = '<table class="comparison-table">';
            html += '<thead><tr><th>属性</th><th>值</th></tr></thead><tbody>';
            
            for (const [key, value] of Object.entries(info)) {
                html += `<tr><td>${key}</td><td>${value}</td></tr>`;
            }
            
            html += '</tbody></table>';
            systemInfoDiv.innerHTML = html;
        }
        
        // 测试直接访问
        function testDirectAccess() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = '正在测试直接访问...\n';
            
            const testUrl = 'http://*************:8888/tencentcloud';
            
            // 尝试fetch请求
            fetch(testUrl, { 
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            })
            .then(() => {
                resultsDiv.textContent += '✅ Fetch请求成功\n';
                resultsDiv.textContent += '正在新窗口中打开...\n';
                window.open(testUrl, '_blank');
            })
            .catch(error => {
                resultsDiv.textContent += `❌ Fetch请求失败: ${error.message}\n`;
                resultsDiv.textContent += '仍然尝试在新窗口中打开...\n';
                window.open(testUrl, '_blank');
            });
        }
        
        // 带时间戳测试
        function testWithTimestamp() {
            const timestamp = Date.now();
            const testUrl = `http://*************:8888/tencentcloud?t=${timestamp}`;
            
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `正在测试带时间戳的访问...\n时间戳: ${timestamp}\n`;
            
            window.open(testUrl, '_blank');
        }
        
        // 测试其他端口
        function testDifferentPorts() {
            const ports = [80, 8080, 8888, 3000, 5000];
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = '正在测试不同端口...\n';
            
            ports.forEach(port => {
                const testUrl = `http://*************:${port}/tencentcloud`;
                resultsDiv.textContent += `测试端口 ${port}: ${testUrl}\n`;
                setTimeout(() => {
                    window.open(testUrl, '_blank');
                }, port * 100); // 延迟打开避免浏览器阻止
            });
        }
        
        // 清除所有缓存
        function clearAllCache() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = '正在尝试清除缓存...\n';
            
            // 清除localStorage
            try {
                localStorage.clear();
                resultsDiv.textContent += '✅ localStorage已清除\n';
            } catch (e) {
                resultsDiv.textContent += '❌ localStorage清除失败\n';
            }
            
            // 清除sessionStorage
            try {
                sessionStorage.clear();
                resultsDiv.textContent += '✅ sessionStorage已清除\n';
            } catch (e) {
                resultsDiv.textContent += '❌ sessionStorage清除失败\n';
            }
            
            resultsDiv.textContent += '\n⚠️ 注意：浏览器缓存和Cookie需要手动清除\n';
            resultsDiv.textContent += '请按 Ctrl+Shift+Delete 打开清除数据对话框\n';
        }
        
        // 显示DNS清除指令
        function showDNSFlushInstructions() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `DNS缓存清除指令：

1. 按 Win+R 打开运行对话框
2. 输入 cmd 并按 Ctrl+Shift+Enter（以管理员身份运行）
3. 在命令提示符中输入以下命令：

   ipconfig /flushdns

4. 等待显示"已成功刷新 DNS 解析缓存"
5. 重新尝试访问网站`;
        }
        
        // 显示缓存清除指令
        function showCacheClearInstructions() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `浏览器缓存清除方法：

Chrome/Edge:
1. 按 Ctrl+Shift+Delete
2. 选择"所有时间"
3. 勾选所有选项
4. 点击"清除数据"

Firefox:
1. 按 Ctrl+Shift+Delete
2. 选择"全部"
3. 勾选所有选项
4. 点击"立即清除"

或者尝试硬刷新：Ctrl+F5`;
        }
        
        // 显示代理检查方法
        function showProxyCheckInstructions() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `代理设置检查方法：

Windows设置方法：
1. 打开"设置" > "网络和Internet"
2. 点击"代理"
3. 确保"使用代理服务器"是关闭状态

浏览器检查方法：
Chrome: 设置 > 高级 > 系统 > 打开代理设置
Firefox: 设置 > 网络设置 > 设置 > 无代理

如果发现有代理设置，请暂时禁用后重试`;
        }
        
        // 在隐私模式中打开
        function openIncognito() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `隐私模式访问：

由于安全限制，无法直接打开隐私窗口。
请手动操作：

1. 按 Ctrl+Shift+N (Chrome) 或 Ctrl+Shift+P (Firefox)
2. 在隐私窗口中访问：http://*************:8888/tencentcloud

或者点击下面的链接（会在普通窗口中打开）：`;
            
            const link = document.createElement('a');
            link.href = 'http://*************:8888/tencentcloud';
            link.target = '_blank';
            link.textContent = 'http://*************:8888/tencentcloud';
            link.style.color = '#3b82f6';
            link.style.textDecoration = 'underline';
            
            resultsDiv.appendChild(document.createElement('br'));
            resultsDiv.appendChild(document.createElement('br'));
            resultsDiv.appendChild(link);
        }
        
        // 生成多浏览器测试链接
        function testMultipleBrowsers() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = `多浏览器测试建议：

请在以下浏览器中分别测试：
• Chrome
• Firefox  
• Microsoft Edge
• Internet Explorer (如果可用)

测试URL: http://*************:8888/tencentcloud

如果某个浏览器可以访问，说明是特定浏览器的配置问题。`;
            
            // 打开测试链接
            window.open('http://*************:8888/tencentcloud', '_blank');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            displaySystemInfo();
            
            console.log('🔍 手机vs电脑访问差异分析页面已加载');
            console.log('📊 系统信息:', getSystemInfo());
        });
    </script>
</body>
</html>
