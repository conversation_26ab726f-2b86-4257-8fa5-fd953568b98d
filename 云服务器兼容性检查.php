<?php
/**
 * 云服务器兼容性检查脚本
 * 检查并修复云服务器环境中的常见问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云服务器兼容性检查 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .check-item {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .check-item.warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .check-item.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .check-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .check-title::before {
            content: '';
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
        }
        .success .check-title::before {
            background: #10b981;
            content: '✓';
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }
        .warning .check-title::before {
            background: #f59e0b;
            content: '⚠';
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }
        .error .check-title::before {
            background: #ef4444;
            content: '✗';
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }
        .check-details {
            margin-left: 30px;
            color: #666;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 云服务器兼容性检查</h1>
            <p>检查系统在云服务器环境中的兼容性</p>
        </div>
        
        <div class="content">
            <?php
            
            // 1. 检查PHP版本
            echo "<div class='check-item success'>";
            echo "<div class='check-title'>PHP版本检查</div>";
            echo "<div class='check-details'>当前版本: " . PHP_VERSION . " ✓</div>";
            echo "</div>";
            
            // 2. 检查禁用函数
            echo "<div class='check-item " . (checkDisabledFunctions() ? 'warning' : 'success') . "'>";
            echo "<div class='check-title'>危险函数检查</div>";
            echo "<div class='check-details'>";
            checkDisabledFunctions();
            echo "</div>";
            echo "</div>";
            
            // 3. 检查数据库连接
            echo "<div class='check-item " . (checkDatabaseConnection() ? 'success' : 'error') . "'>";
            echo "<div class='check-title'>数据库连接检查</div>";
            echo "<div class='check-details'>";
            checkDatabaseConnection();
            echo "</div>";
            echo "</div>";
            
            // 4. 检查文件权限
            echo "<div class='check-item " . (checkFilePermissions() ? 'success' : 'warning') . "'>";
            echo "<div class='check-title'>文件权限检查</div>";
            echo "<div class='check-details'>";
            checkFilePermissions();
            echo "</div>";
            echo "</div>";
            
            // 5. 检查扩展
            echo "<div class='check-item " . (checkExtensions() ? 'success' : 'warning') . "'>";
            echo "<div class='check-title'>PHP扩展检查</div>";
            echo "<div class='check-details'>";
            checkExtensions();
            echo "</div>";
            echo "</div>";
            
            // 6. 检查内存限制
            echo "<div class='check-item success'>";
            echo "<div class='check-title'>内存限制检查</div>";
            echo "<div class='check-details'>";
            checkMemoryLimit();
            echo "</div>";
            echo "</div>";
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="admin-dashboard.php" class="btn">进入管理后台</a>
                <a href="index.php" class="btn">返回首页</a>
                <a href="服务器环境诊断.php" class="btn">详细诊断</a>
            </div>
        </div>
    </div>
</body>
</html>

<?php

function checkDisabledFunctions() {
    $disabled = explode(',', ini_get('disable_functions'));
    $dangerous = ['exec', 'shell_exec', 'system', 'passthru', 'eval', 'file_get_contents'];
    $found = array_intersect($dangerous, array_map('trim', $disabled));
    
    if (!empty($found)) {
        echo "以下函数被禁用: " . implode(', ', $found) . "<br>";
        echo "这是正常的安全设置，系统已适配云服务器环境 ✓";
        return true;
    } else {
        echo "未发现被禁用的危险函数";
        return false;
    }
}

function checkDatabaseConnection() {
    try {
        require_once 'config/database.php';
        $db = DatabaseConfig::getInstance();
        $connection = $db->getConnection();
        
        if ($connection) {
            echo "数据库连接成功 ✓<br>";
            
            // 检查数据库类型
            $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
            echo "数据库类型: " . strtoupper($driver) . "<br>";
            
            // 测试查询
            $result = $connection->query("SELECT 1 as test")->fetch();
            if ($result['test'] == 1) {
                echo "数据库查询测试通过 ✓";
            }
            
            return true;
        }
    } catch (Exception $e) {
        echo "数据库连接失败: " . $e->getMessage();
        return false;
    }
}

function checkFilePermissions() {
    $dirs = ['database', 'uploads', 'cache'];
    $allOk = true;
    
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "创建目录 {$dir} ✓<br>";
            } else {
                echo "无法创建目录 {$dir} ✗<br>";
                $allOk = false;
            }
        } else {
            if (is_writable($dir)) {
                echo "目录 {$dir} 可写 ✓<br>";
            } else {
                echo "目录 {$dir} 不可写 ⚠<br>";
                $allOk = false;
            }
        }
    }
    
    return $allOk;
}

function checkExtensions() {
    $required = ['pdo', 'pdo_mysql', 'pdo_sqlite', 'json', 'mbstring'];
    $missing = [];
    
    foreach ($required as $ext) {
        if (extension_loaded($ext)) {
            echo "{$ext} ✓<br>";
        } else {
            echo "{$ext} ✗<br>";
            $missing[] = $ext;
        }
    }
    
    return empty($missing);
}

function checkMemoryLimit() {
    $limit = ini_get('memory_limit');
    $usage = round(memory_get_usage(true) / 1024 / 1024, 1);
    $peak = round(memory_get_peak_usage(true) / 1024 / 1024, 1);
    
    echo "内存限制: {$limit}<br>";
    echo "当前使用: {$usage}MB<br>";
    echo "峰值使用: {$peak}MB";
}

?>
