<?php
/**
 * 网站诊断页面 - 检查加载缓慢的原因
 */

// 记录开始时间
$start_time = microtime(true);

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 检查PHP版本
$php_version = phpversion();

// 检查内存使用
$memory_limit = ini_get('memory_limit');
$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);

// 检查数据库连接
$db_status = 'unknown';
$db_error = '';
$db_time = 0;

try {
    $db_start = microtime(true);
    require_once 'config/database.php';
    $db = db();
    $db_time = (microtime(true) - $db_start) * 1000; // 转换为毫秒
    $db_status = 'connected';
} catch (Exception $e) {
    $db_status = 'error';
    $db_error = $e->getMessage();
}

// 检查文件系统
$file_checks = [
    'index.php' => file_exists('index.php'),
    'admin-dashboard.php' => file_exists('admin-dashboard.php'),
    'components/navbar.php' => file_exists('components/navbar.php'),
    'api/navbar.php' => file_exists('api/navbar.php'),
    'config/database.php' => file_exists('config/database.php'),
];

// 检查导航栏数据
$navbar_status = 'unknown';
$navbar_count = 0;
$navbar_time = 0;

if ($db_status === 'connected') {
    try {
        $navbar_start = microtime(true);
        $result = $db->fetchAll("SELECT COUNT(*) as count FROM navbar_items");
        $navbar_count = $result[0]['count'] ?? 0;
        $navbar_time = (microtime(true) - $navbar_start) * 1000;
        $navbar_status = 'ok';
    } catch (Exception $e) {
        $navbar_status = 'error: ' . $e->getMessage();
    }
}

// 计算总执行时间
$total_time = (microtime(true) - $start_time) * 1000;

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站诊断 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            color: #1e293b;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .diagnostic-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #64748b;
        }
        
        .status-value {
            font-weight: 600;
        }
        
        .status-ok { color: #059669; }
        .status-warning { color: #d97706; }
        .status-error { color: #dc2626; }
        
        .performance-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .performance-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .performance-excellent { background: #059669; }
        .performance-good { background: #65a30d; }
        .performance-fair { background: #d97706; }
        .performance-poor { background: #dc2626; }
        
        .actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-success {
            background: #059669;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .refresh-info {
            text-align: center;
            margin-top: 1rem;
            color: #64748b;
            font-size: 0.875rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
                margin: 10px;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 网站诊断报告</h1>
        
        <div class="diagnostic-grid">
            <!-- 系统状态 -->
            <div class="diagnostic-card">
                <div class="card-title">
                    ⚙️ 系统状态
                </div>
                <div class="status-item">
                    <span class="status-label">PHP版本</span>
                    <span class="status-value status-ok"><?php echo $php_version; ?></span>
                </div>
                <div class="status-item">
                    <span class="status-label">内存限制</span>
                    <span class="status-value"><?php echo $memory_limit; ?></span>
                </div>
                <div class="status-item">
                    <span class="status-label">内存使用</span>
                    <span class="status-value"><?php echo round($memory_usage / 1024 / 1024, 2); ?> MB</span>
                </div>
                <div class="status-item">
                    <span class="status-label">峰值内存</span>
                    <span class="status-value"><?php echo round($memory_peak / 1024 / 1024, 2); ?> MB</span>
                </div>
            </div>
            
            <!-- 数据库状态 -->
            <div class="diagnostic-card">
                <div class="card-title">
                    🗄️ 数据库状态
                </div>
                <div class="status-item">
                    <span class="status-label">连接状态</span>
                    <span class="status-value <?php echo $db_status === 'connected' ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $db_status === 'connected' ? '✅ 已连接' : '❌ 连接失败'; ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">连接时间</span>
                    <span class="status-value <?php echo $db_time < 100 ? 'status-ok' : ($db_time < 500 ? 'status-warning' : 'status-error'); ?>">
                        <?php echo round($db_time, 2); ?> ms
                    </span>
                </div>
                <?php if ($db_error): ?>
                <div class="status-item">
                    <span class="status-label">错误信息</span>
                    <span class="status-value status-error"><?php echo htmlspecialchars($db_error); ?></span>
                </div>
                <?php endif; ?>
                <div class="status-item">
                    <span class="status-label">导航项数量</span>
                    <span class="status-value"><?php echo $navbar_count; ?></span>
                </div>
            </div>
            
            <!-- 文件系统 -->
            <div class="diagnostic-card">
                <div class="card-title">
                    📁 文件系统
                </div>
                <?php foreach ($file_checks as $file => $exists): ?>
                <div class="status-item">
                    <span class="status-label"><?php echo $file; ?></span>
                    <span class="status-value <?php echo $exists ? 'status-ok' : 'status-error'; ?>">
                        <?php echo $exists ? '✅ 存在' : '❌ 缺失'; ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- 性能指标 -->
            <div class="diagnostic-card">
                <div class="card-title">
                    ⚡ 性能指标
                </div>
                <div class="status-item">
                    <span class="status-label">页面加载时间</span>
                    <span class="status-value <?php echo $total_time < 100 ? 'status-ok' : ($total_time < 500 ? 'status-warning' : 'status-error'); ?>">
                        <?php echo round($total_time, 2); ?> ms
                    </span>
                </div>
                <div class="performance-bar">
                    <div class="performance-fill <?php 
                        echo $total_time < 100 ? 'performance-excellent' : 
                            ($total_time < 300 ? 'performance-good' : 
                            ($total_time < 1000 ? 'performance-fair' : 'performance-poor')); 
                    ?>" style="width: <?php echo min(100, ($total_time / 1000) * 100); ?>%"></div>
                </div>
                <div class="status-item">
                    <span class="status-label">数据库查询时间</span>
                    <span class="status-value"><?php echo round($navbar_time, 2); ?> ms</span>
                </div>
            </div>
        </div>
        
        <div class="actions">
            <a href="index.php" class="btn btn-primary">
                🏠 访问首页
            </a>
            <a href="admin-dashboard.php" class="btn btn-secondary">
                ⚙️ 管理后台
            </a>
            <a href="test_navbar.php" class="btn btn-success">
                🧪 功能测试
            </a>
            <button onclick="window.location.reload()" class="btn btn-secondary">
                🔄 刷新诊断
            </button>
        </div>
        
        <div class="refresh-info">
            诊断完成时间: <?php echo date('Y-m-d H:i:s'); ?> | 
            总执行时间: <?php echo round($total_time, 2); ?> ms
        </div>
    </div>
    
    <script>
        // 自动刷新功能
        let autoRefresh = false;
        
        // 检查性能并给出建议
        const totalTime = <?php echo $total_time; ?>;
        const dbTime = <?php echo $db_time; ?>;
        
        if (totalTime > 1000) {
            console.warn('⚠️ 页面加载时间较长，建议检查数据库连接和服务器性能');
        }
        
        if (dbTime > 500) {
            console.warn('⚠️ 数据库连接时间较长，建议检查数据库配置');
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 诊断页面加载完成');
            console.log(`📊 性能数据: 总时间 ${totalTime.toFixed(2)}ms, 数据库 ${dbTime.toFixed(2)}ms`);
            
            // 如果加载时间过长，显示建议
            if (totalTime > 2000) {
                const suggestion = document.createElement('div');
                suggestion.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #fef3c7;
                    border: 1px solid #f59e0b;
                    color: #92400e;
                    padding: 1rem;
                    border-radius: 8px;
                    max-width: 300px;
                    z-index: 1000;
                `;
                suggestion.innerHTML = `
                    <strong>⚠️ 性能建议</strong><br>
                    页面加载时间较长，建议：<br>
                    1. 检查数据库连接<br>
                    2. 优化PHP配置<br>
                    3. 检查服务器资源
                `;
                document.body.appendChild(suggestion);
                
                setTimeout(() => {
                    document.body.removeChild(suggestion);
                }, 10000);
            }
        });
    </script>
</body>
</html>
