// 管理后台JavaScript修复脚本
document.addEventListener('DOMContentLoaded', function() {
    console.log('Admin Dashboard JavaScript loaded');
    
    // 修复侧边栏菜单点击事件
    initializeSidebarEvents();
    
    // 修复通知图标点击事件
    initializeNotificationEvents();
    
    // 修复其他交互元素
    initializeOtherEvents();
});

// 初始化侧边栏事件
function initializeSidebarEvents() {
    // 获取所有侧边栏菜单项
    const sidebarItems = document.querySelectorAll('.sidebar-item, .nav-item, [data-page]');
    
    sidebarItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除其他项的活动状态
            sidebarItems.forEach(i => i.classList.remove('active'));
            
            // 添加当前项的活动状态
            this.classList.add('active');
            
            // 获取目标页面
            const targetPage = this.getAttribute('data-page') || 
                              this.getAttribute('onclick')?.match(/switchPage\('([^']+)'\)/)?.[1] ||
                              this.textContent.trim();
            
            if (targetPage) {
                switchPage(targetPage);
            }
        });
    });
    
    // 特殊处理一些菜单项
    const menuItems = [
        { selector: '[onclick*="switchPage(\'dashboard\')"]', page: 'dashboard' },
        { selector: '[onclick*="switchPage(\'user-management\')"]', page: 'user-management' },
        { selector: '[onclick*="switchPage(\'content-management\')"]', page: 'content-management' },
        { selector: '[onclick*="switchPage(\'system-settings\')"]', page: 'system-settings' },
        { selector: '[onclick*="switchPage(\'page-designer\')"]', page: 'page-designer' }
    ];
    
    menuItems.forEach(menuItem => {
        const elements = document.querySelectorAll(menuItem.selector);
        elements.forEach(element => {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                switchPage(menuItem.page);
            });
        });
    });
}

// 初始化通知事件
function initializeNotificationEvents() {
    // 通知图标点击事件
    const notificationIcon = document.querySelector('.notification-icon, .bell-icon, [class*="notification"]');
    if (notificationIcon) {
        notificationIcon.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 切换通知面板显示状态
            const notificationPanel = document.querySelector('.notification-panel, .notification-dropdown');
            if (notificationPanel) {
                notificationPanel.style.display = notificationPanel.style.display === 'none' ? 'block' : 'none';
            } else {
                // 如果没有通知面板，显示一个简单的通知
                showNotificationModal();
            }
        });
    }
    
    // 点击其他地方关闭通知面板
    document.addEventListener('click', function(e) {
        const notificationPanel = document.querySelector('.notification-panel, .notification-dropdown');
        if (notificationPanel && !e.target.closest('.notification-icon, .notification-panel')) {
            notificationPanel.style.display = 'none';
        }
    });
}

// 初始化其他事件
function initializeOtherEvents() {
    // 修复所有按钮点击事件
    const buttons = document.querySelectorAll('button, .btn, .card[onclick]');
    buttons.forEach(button => {
        if (button.onclick) {
            const originalOnclick = button.onclick;
            button.onclick = null;
            button.addEventListener('click', originalOnclick);
        }
    });
    
    // 修复下拉菜单
    const dropdowns = document.querySelectorAll('.dropdown-toggle, [data-toggle="dropdown"]');
    dropdowns.forEach(dropdown => {
        dropdown.addEventListener('click', function(e) {
            e.preventDefault();
            const menu = this.nextElementSibling || this.parentElement.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
            }
        });
    });
}

// 显示通知模态框
function showNotificationModal() {
    const modal = document.createElement('div');
    modal.className = 'notification-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 400px;
        width: 90%;
    `;
    
    content.innerHTML = `
        <h3>通知中心</h3>
        <p>暂无新通知</p>
        <button onclick="this.closest('.notification-modal').remove()" style="
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        ">关闭</button>
    `;
    
    modal.appendChild(content);
    document.body.appendChild(modal);
    
    // 点击背景关闭
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// 确保switchPage函数存在
if (typeof switchPage === 'undefined') {
    window.switchPage = function(page) {
        console.log('Switching to page:', page);
        
        // 隐藏所有页面内容
        const allPages = document.querySelectorAll('.page-content, .content-section, [id$="-page"]');
        allPages.forEach(p => p.style.display = 'none');
        
        // 显示目标页面
        const targetPage = document.getElementById(page + '-page') || 
                          document.getElementById(page) ||
                          document.querySelector(`[data-page="${page}"]`);
        
        if (targetPage) {
            targetPage.style.display = 'block';
        } else {
            // 如果找不到页面，显示默认内容
            console.log('Page not found:', page);
            alert('页面正在开发中...');
        }
    };
}

console.log('Admin Dashboard JavaScript fix applied successfully');
