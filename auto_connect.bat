@echo off
setlocal enabledelayedexpansion

echo ========================================
echo 比特熊智慧系统 - 自动SSH连接
echo ========================================
echo.

set SERVER_IP=*************
set USERNAME=root
set PASSWORD=ZbDX7%=]?H2(LAUz

echo 服务器信息:
echo IP地址: %SERVER_IP%
echo 用户名: %USERNAME%
echo 密码: %PASSWORD%
echo.

echo 正在测试网络连接...
ping -n 2 %SERVER_IP% >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 网络连接失败，请检查网络或服务器状态
    pause
    exit /b 1
)
echo ✓ 网络连接正常
echo.

echo 正在检测SSH客户端...

REM 检查Windows内置SSH
if exist "C:\Windows\System32\OpenSSH\ssh.exe" (
    echo ✓ 使用Windows内置SSH客户端
    goto :connect_windows_ssh
)

REM 检查Git SSH
if exist "C:\Program Files\Git\usr\bin\ssh.exe" (
    echo ✓ 使用Git SSH客户端
    goto :connect_git_ssh
)

REM 检查系统PATH中的SSH
ssh --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ 使用系统SSH客户端
    goto :connect_system_ssh
)

echo ❌ 未找到SSH客户端
echo.
echo 正在尝试安装Windows OpenSSH客户端...
powershell -Command "Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ OpenSSH客户端安装成功
    goto :connect_windows_ssh
) else (
    echo ❌ 自动安装失败，请手动安装SSH客户端
    echo.
    echo 手动安装方法:
    echo 1. 打开"设置" ^> "应用" ^> "可选功能"
    echo 2. 点击"添加功能"
    echo 3. 搜索并安装"OpenSSH客户端"
    pause
    exit /b 1
)

:connect_windows_ssh
echo.
echo 正在使用Windows SSH连接到服务器...
echo 连接命令: C:\Windows\System32\OpenSSH\ssh.exe %USERNAME%@%SERVER_IP%
echo.

REM 创建临时expect脚本
echo spawn C:\Windows\System32\OpenSSH\ssh.exe -o StrictHostKeyChecking=no %USERNAME%@%SERVER_IP% > temp_ssh.exp
echo expect "password:" >> temp_ssh.exp
echo send "%PASSWORD%\r" >> temp_ssh.exp
echo interact >> temp_ssh.exp

REM 如果有expect，使用expect
where expect >nul 2>&1
if %errorlevel% equ 0 (
    expect temp_ssh.exp
    del temp_ssh.exp
) else (
    REM 没有expect，直接连接（需要手动输入密码）
    echo 请在提示时输入密码: %PASSWORD%
    C:\Windows\System32\OpenSSH\ssh.exe -o StrictHostKeyChecking=no %USERNAME%@%SERVER_IP%
)
goto :end

:connect_git_ssh
echo.
echo 正在使用Git SSH连接到服务器...
echo 连接命令: "C:\Program Files\Git\usr\bin\ssh.exe" %USERNAME%@%SERVER_IP%
echo.
echo 请在提示时输入密码: %PASSWORD%
"C:\Program Files\Git\usr\bin\ssh.exe" -o StrictHostKeyChecking=no %USERNAME%@%SERVER_IP%
goto :end

:connect_system_ssh
echo.
echo 正在使用系统SSH连接到服务器...
echo 连接命令: ssh %USERNAME%@%SERVER_IP%
echo.
echo 请在提示时输入密码: %PASSWORD%
ssh -o StrictHostKeyChecking=no %USERNAME%@%SERVER_IP%
goto :end

:end
echo.
echo 连接已断开
pause
