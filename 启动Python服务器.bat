@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🐻 比特熊项目 - Python服务器启动
echo ========================================
echo.

:: 检查当前目录
echo 📁 当前目录: %CD%
echo.

:: 检查必要文件
if not exist "index.html" (
    echo ❌ 未找到 index.html 文件
    echo 💡 请确保在项目根目录中运行此脚本
    pause
    exit /b 1
)

if not exist "index-style.css" (
    echo ❌ 未找到 index-style.css 文件
    echo 💡 请确保在项目根目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目文件检查完成
echo.

:: 尝试启动Python服务器
echo 🚀 正在启动Python HTTP服务器...
echo.

:: 尝试Python 3
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 找到 Python
    python --version
    echo.
    echo 🌐 服务器将在以下地址运行:
    echo    http://localhost:8000
    echo.
    echo 📄 可用页面:
    echo    • 项目首页: http://localhost:8000/启动页面.html
    echo    • 静态版本: http://localhost:8000/index-standalone.html
    echo    • 标准版本: http://localhost:8000/index.html
    echo    • 项目演示: http://localhost:8000/项目演示.html
    echo.
    echo 💡 按 Ctrl+C 停止服务器
    echo 🌐 正在打开浏览器...
    echo.
    
    :: 延迟2秒后打开浏览器
    timeout /t 2 >nul
    start http://localhost:8000/启动页面.html
    
    :: 启动服务器
    python -m http.server 8000
    goto :end
)

:: 尝试Python3
python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 找到 Python3
    python3 --version
    echo.
    echo 🌐 服务器将在以下地址运行:
    echo    http://localhost:8000
    echo.
    echo 💡 按 Ctrl+C 停止服务器
    echo 🌐 正在打开浏览器...
    echo.
    
    timeout /t 2 >nul
    start http://localhost:8000/启动页面.html
    
    python3 -m http.server 8000
    goto :end
)

:: 尝试py命令
py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 找到 py 命令
    py --version
    echo.
    echo 🌐 服务器将在以下地址运行:
    echo    http://localhost:8000
    echo.
    echo 💡 按 Ctrl+C 停止服务器
    echo 🌐 正在打开浏览器...
    echo.
    
    timeout /t 2 >nul
    start http://localhost:8000/启动页面.html
    
    py -m http.server 8000
    goto :end
)

:: 没有找到Python
echo ❌ 未找到 Python 安装
echo.
echo 🔧 解决方案:
echo.
echo 方法1 - 安装Python:
echo   1. 访问 https://www.python.org/downloads/
echo   2. 下载并安装最新版本的Python
echo   3. 安装时勾选 "Add Python to PATH"
echo.
echo 方法2 - 使用静态版本:
echo   直接双击打开 index-standalone.html
echo.
echo 方法3 - 使用其他服务器:
echo   • 安装Node.js后运行: npx http-server
echo   • 安装PHP后运行: php -S localhost:8000
echo.

:end
echo.
echo 🎉 感谢使用比特熊项目!
pause
