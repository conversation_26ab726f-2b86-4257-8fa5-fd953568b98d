<?php
/**
 * 服务器数据库连接测试脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>服务器数据库连接测试</h1>";
echo "<hr>";

// 测试基本MySQL连接
echo "<h2>1. 测试MySQL基本连接</h2>";
try {
    $host = 'localhost';
    $username = 'root';
    $password = '309290133q';
    $port = 3306;
    
    $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "<p style='color: green;'>✓ MySQL连接成功</p>";
    
    // 获取MySQL版本
    $version = $pdo->query("SELECT VERSION() as version")->fetch();
    echo "<p>MySQL版本: " . $version['version'] . "</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ MySQL连接失败: " . $e->getMessage() . "</p>";
    exit;
}

// 测试数据库是否存在
echo "<h2>2. 检查数据库</h2>";
try {
    $database = 'bitbear_website';
    
    // 检查数据库是否存在
    $databases = $pdo->query("SHOW DATABASES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array($database, $databases)) {
        echo "<p style='color: green;'>✓ 数据库 '{$database}' 存在</p>";
    } else {
        echo "<p style='color: orange;'>! 数据库 '{$database}' 不存在，正在创建...</p>";
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✓ 数据库 '{$database}' 创建成功</p>";
    }
    
    // 连接到指定数据库
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, $options);
    echo "<p style='color: green;'>✓ 成功连接到数据库 '{$database}'</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ 数据库操作失败: " . $e->getMessage() . "</p>";
}

// 检查表结构
echo "<h2>3. 检查表结构</h2>";
try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>! 数据库中没有表，需要初始化数据库结构</p>";
        echo "<p>请执行以下命令初始化数据库：</p>";
        echo "<code>mysql -u root -p309290133q bitbear_website < database/init.sql</code>";
    } else {
        echo "<p style='color: green;'>✓ 数据库包含 " . count($tables) . " 个表：</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ 检查表结构失败: " . $e->getMessage() . "</p>";
}

// 测试配置文件
echo "<h2>4. 测试配置文件</h2>";
$configFile = __DIR__ . '/config/database_server.php';
if (file_exists($configFile)) {
    echo "<p style='color: green;'>✓ 服务器配置文件存在</p>";
    
    try {
        require_once $configFile;
        $db = DatabaseConfig::getInstance();
        $testResult = $db->testConnection();
        
        if ($testResult['status'] === 'success') {
            echo "<p style='color: green;'>✓ 配置文件测试成功</p>";
            echo "<p>当前时间: " . $testResult['data']['current_time'] . "</p>";
            
            // 获取数据库信息
            $dbInfo = $db->getDatabaseInfo();
            if (!isset($dbInfo['error'])) {
                echo "<h3>数据库信息：</h3>";
                echo "<ul>";
                echo "<li>数据库名: " . $dbInfo['database'] . "</li>";
                echo "<li>MySQL版本: " . $dbInfo['version'] . "</li>";
                echo "<li>字符集: " . $dbInfo['charset'] . "</li>";
                echo "<li>表数量: " . $dbInfo['tables_count'] . "</li>";
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>✗ 配置文件测试失败: " . $testResult['message'] . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 配置文件加载失败: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ 服务器配置文件不存在: {$configFile}</p>";
}

// 检查PHP环境
echo "<h2>5. PHP环境检查</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>PDO MySQL扩展: " . (extension_loaded('pdo_mysql') ? '✓ 已安装' : '✗ 未安装') . "</p>";
echo "<p>时区: " . date_default_timezone_get() . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 检查文件权限
echo "<h2>6. 文件权限检查</h2>";
$checkDirs = [
    __DIR__ . '/uploads',
    __DIR__ . '/uploads/avatars',
    __DIR__ . '/uploads/posts',
    __DIR__ . '/config'
];

foreach ($checkDirs as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? '可写' : '不可写';
        echo "<p>{$dir}: 权限 {$perms} ({$writable})</p>";
    } else {
        echo "<p style='color: orange;'>{$dir}: 目录不存在</p>";
    }
}

echo "<hr>";
echo "<h2>测试完成</h2>";
echo "<p>如果所有测试都通过，说明服务器环境配置正确。</p>";
echo "<p>如果有错误，请根据错误信息进行相应的配置调整。</p>";
?>
