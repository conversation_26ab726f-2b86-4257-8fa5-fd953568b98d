<?php
// 简单的调试API
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
header('Content-Type: application/json');

echo json_encode([
    'timestamp' => date('Y-m-d H:i:s'),
    'session_status' => [
        'admin_logged_in' => $_SESSION['admin_logged_in'] ?? false,
        'session_id' => session_id(),
        'all_session_data' => $_SESSION
    ],
    'request_info' => [
        'method' => $_SERVER['REQUEST_METHOD'],
        'get_params' => $_GET,
        'post_params' => $_POST,
        'action' => $_GET['action'] ?? 'none'
    ],
    'file_checks' => [
        'database_config_exists' => file_exists('config/database.php'),
        'comments_api_exists' => file_exists('api/comments-management.php')
    ]
]);
?>
