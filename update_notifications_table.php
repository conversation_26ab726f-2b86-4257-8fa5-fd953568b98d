<?php
/**
 * 更新通知表结构
 */

require_once 'config/database.php';

try {
    $db = db();
    $pdo = $db->getConnection();
    
    echo "正在更新通知表结构...<br>";
    
    // 检查是否已存在related_user_id字段
    $checkSql = "SHOW COLUMNS FROM notifications LIKE 'related_user_id'";
    $stmt = $pdo->prepare($checkSql);
    $stmt->execute();
    $exists = $stmt->fetch();
    
    if (!$exists) {
        // 添加related_user_id字段
        $alterSql = "ALTER TABLE notifications ADD COLUMN related_user_id INT NULL AFTER user_id";
        $pdo->exec($alterSql);
        echo "✅ 添加related_user_id字段成功<br>";
        
        // 添加外键约束
        $fkSql = "ALTER TABLE notifications ADD FOREIGN KEY (related_user_id) REFERENCES users(id) ON DELETE SET NULL";
        $pdo->exec($fkSql);
        echo "✅ 添加外键约束成功<br>";
    } else {
        echo "ℹ️ related_user_id字段已存在<br>";
    }
    
    // 更新通知类型枚举值
    $updateTypeSql = "ALTER TABLE notifications MODIFY COLUMN type ENUM('system', 'like', 'comment', 'reply', 'follow', 'mention', 'post', 'user_register', 'admin_login', 'system_update', 'server_warning', 'application_process') DEFAULT 'system'";
    $pdo->exec($updateTypeSql);
    echo "✅ 更新通知类型枚举值成功<br>";
    
    // 创建一些测试通知数据
    echo "<br>正在创建测试通知数据...<br>";
    
    // 获取一些用户ID用于测试
    $usersSql = "SELECT id, username FROM users LIMIT 3";
    $stmt = $pdo->prepare($usersSql);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) >= 2) {
        $user1 = $users[0];
        $user2 = $users[1];
        
        $testNotifications = [
            [
                'user_id' => $user1['id'],
                'related_user_id' => $user2['id'],
                'title' => '收到新的点赞',
                'content' => $user2['username'] . ' 点赞了您的帖子',
                'type' => 'like'
            ],
            [
                'user_id' => $user1['id'],
                'related_user_id' => $user2['id'],
                'title' => '收到新的评论',
                'content' => $user2['username'] . ' 评论了您的帖子',
                'type' => 'comment'
            ],
            [
                'user_id' => $user1['id'],
                'related_user_id' => $user2['id'],
                'title' => '新的关注者',
                'content' => $user2['username'] . ' 关注了您',
                'type' => 'follow'
            ],
            [
                'user_id' => $user1['id'],
                'related_user_id' => null,
                'title' => '系统维护通知',
                'content' => '系统将于今晚进行维护升级',
                'type' => 'system'
            ],
            [
                'user_id' => $user1['id'],
                'related_user_id' => $user2['id'],
                'title' => '回复了您的评论',
                'content' => $user2['username'] . ' 回复了您的评论',
                'type' => 'reply'
            ]
        ];
        
        $insertSql = "INSERT INTO notifications (user_id, related_user_id, title, content, type, is_read, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertSql);
        
        foreach ($testNotifications as $index => $notification) {
            $isRead = $index >= 3 ? 1 : 0; // 前3个未读，后面的已读
            $createdAt = date('Y-m-d H:i:s', time() - ($index * 1800)); // 每个通知间隔30分钟
            
            $stmt->execute([
                $notification['user_id'],
                $notification['related_user_id'],
                $notification['title'],
                $notification['content'],
                $notification['type'],
                $isRead,
                $createdAt
            ]);
        }
        
        echo "✅ 创建测试通知数据成功<br>";
        echo "已为用户 {$user1['username']} 创建了 " . count($testNotifications) . " 条测试通知<br>";
    }
    
    // 检查posts表是否有驳回相关字段
    echo "<br>正在检查posts表结构...<br>";

    $postColumns = $pdo->query("SHOW COLUMNS FROM posts")->fetchAll(PDO::FETCH_ASSOC);
    $hasRejectedBy = false;
    $hasRejectedAt = false;

    foreach ($postColumns as $column) {
        if ($column['Field'] === 'rejected_by') $hasRejectedBy = true;
        if ($column['Field'] === 'rejected_at') $hasRejectedAt = true;
    }

    if (!$hasRejectedBy) {
        $pdo->exec("ALTER TABLE posts ADD COLUMN rejected_by INT NULL AFTER status");
        $pdo->exec("ALTER TABLE posts ADD FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL");
        echo "✅ 添加rejected_by字段成功<br>";
    } else {
        echo "ℹ️ rejected_by字段已存在<br>";
    }

    if (!$hasRejectedAt) {
        $pdo->exec("ALTER TABLE posts ADD COLUMN rejected_at DATETIME NULL AFTER rejected_by");
        echo "✅ 添加rejected_at字段成功<br>";
    } else {
        echo "ℹ️ rejected_at字段已存在<br>";
    }

    echo "<br>✅ 数据库结构更新完成！<br>";
    echo "<a href='index.php'>返回首页测试通知功能</a><br>";
    echo "<a href='community-drafts.php'>测试草稿功能</a>";
    
} catch (Exception $e) {
    echo "❌ 更新失败: " . $e->getMessage() . "<br>";
    echo "错误详情: " . $e->getTraceAsString();
}
?>
