<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端导航测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        .test-title {
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
        }
        .test-description {
            color: #64748b;
            font-size: 14px;
        }
        .status-ok { color: #16a34a; }
        .status-warning { color: #ea580c; }
        .status-error { color: #dc2626; }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .btn:hover { background: #2563eb; }
        .device-simulator {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
        .mobile-frame {
            width: 375px;
            height: 600px;
            border: 8px solid #1f2937;
            border-radius: 25px;
            margin: 0 auto;
            overflow: hidden;
            position: relative;
            background: white;
        }
        .mobile-screen {
            width: 100%;
            height: 100%;
            border: none;
        }
        @media (max-width: 480px) {
            .mobile-frame {
                width: 100%;
                height: 400px;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🐻 移动端导航测试</h1>
            <p>测试比特熊智慧系统的移动端导航菜单修复</p>
        </div>

        <div class="test-item">
            <div class="test-title">✅ 修复内容</div>
            <div class="test-description">
                <ul>
                    <li>修复了移动端导航菜单的HTML结构，添加了正确的CSS类</li>
                    <li>优化了移动端菜单的样式，包括动画效果和视觉反馈</li>
                    <li>改进了汉堡菜单按钮的交互效果</li>
                    <li>添加了点击外部关闭菜单的功能</li>
                    <li>增强了下拉菜单在移动端的显示效果</li>
                    <li><strong>🆕 优化菜单高度</strong>：将菜单改为全屏显示，解决高度不足问题</li>
                    <li><strong>🆕 增加遮罩层</strong>：添加半透明遮罩，提升用户体验</li>
                    <li><strong>🆕 优化触摸体验</strong>：增大菜单项高度，便于手指点击</li>
                    <li><strong>✨ 毛玻璃效果</strong>：添加现代化的毛玻璃半透明背景效果</li>
                </ul>
            </div>
        </div>

        <div class="test-item">
            <div class="test-title">🔧 技术改进</div>
            <div class="test-description">
                <ul>
                    <li><strong>HTML结构</strong>: 为导航项添加了 <code>nav-item</code>, <code>dropdown</code>, <code>nav-link</code> 等CSS类</li>
                    <li><strong>CSS样式</strong>: 优化了移动端菜单的布局、动画和响应式设计</li>
                    <li><strong>JavaScript交互</strong>: 增强了菜单的交互逻辑，支持点击外部关闭</li>
                    <li><strong>用户体验</strong>: 添加了平滑的动画效果和视觉反馈</li>
                    <li><strong>毛玻璃效果</strong>: 使用 <code>backdrop-filter: blur()</code> 实现现代化的半透明毛玻璃背景</li>
                    <li><strong>暗色模式支持</strong>: 在暗色主题下也有相应的毛玻璃效果</li>
                </ul>
            </div>
        </div>

        <div class="test-item">
            <div class="test-title">📱 测试步骤</div>
            <div class="test-description">
                <ol>
                    <li>在移动设备或浏览器开发者工具的移动模式下访问主页</li>
                    <li>点击右上角的汉堡菜单按钮（三条横线）</li>
                    <li>验证导航菜单是否正确展开并显示所有菜单项</li>
                    <li>测试下拉菜单项的展开和收起功能</li>
                    <li>点击菜单外部区域，验证菜单是否自动关闭</li>
                </ol>
            </div>
        </div>

        <div class="device-simulator">
            <h3>📱 移动端预览</h3>
            <p>在下方的模拟器中测试移动端导航：</p>
            <div class="mobile-frame">
                <iframe src="index.php" class="mobile-screen"></iframe>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.php" class="btn">🏠 返回主页</a>
            <a href="system_status.php" class="btn">📊 系统状态</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e2e8f0; text-align: center; color: #64748b;">
            <h3>🧪 测试说明</h3>
            <p>如果您在移动设备上看到此页面，请直接测试上方的导航功能。</p>
            <p>如果在桌面浏览器上，请按 F12 打开开发者工具，切换到移动设备模式进行测试。</p>
            
            <div style="margin-top: 20px; padding: 15px; background: #f0fdf4; border: 1px solid #bbf7d0; border-radius: 8px;">
                <p><strong>✅ 预期结果：</strong></p>
                <ul style="text-align: left; display: inline-block;">
                    <li>汉堡菜单按钮在移动端正确显示</li>
                    <li>点击按钮时菜单平滑展开，占据全屏高度</li>
                    <li>菜单项正确显示，包括下拉子菜单，高度足够便于点击</li>
                    <li>点击外部区域或遮罩层时菜单自动关闭</li>
                    <li>菜单按钮有正确的动画效果（X形变换）</li>
                    <li>菜单展开时显示半透明遮罩层</li>
                    <li>支持ESC键关闭菜单</li>
                    <li>菜单展开时禁止页面滚动</li>
                    <li><strong>✨ 毛玻璃效果</strong>：菜单背景呈现现代化的半透明毛玻璃质感</li>
                    <li><strong>🌙 暗色模式</strong>：在暗色主题下也有相应的毛玻璃效果</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 检测设备类型
        function detectDevice() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            const deviceInfo = document.createElement('div');
            deviceInfo.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                background: ${isMobile ? '#16a34a' : '#3b82f6'};
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 1000;
            `;
            deviceInfo.textContent = `${isMobile ? '📱 移动端' : '🖥️ 桌面端'} (${width}px)`;
            document.body.appendChild(deviceInfo);
        }

        // 页面加载时检测设备
        window.addEventListener('load', detectDevice);
        window.addEventListener('resize', function() {
            const existing = document.querySelector('div[style*="position: fixed"]');
            if (existing) existing.remove();
            detectDevice();
        });
    </script>
</body>
</html>
