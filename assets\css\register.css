/* 用户注册和登录页面通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 背景渐变 */
.background-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(242deg, rgb(142, 34, 167) 0.01%, rgb(37, 99, 234) 97.15%);
    z-index: -1;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    padding: 0.75rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.brand-logo {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

/* 注册容器 */
.register-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rem 1.5rem 2rem;
}

.register-card {
    width: 100%;
    max-width: 480px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-title {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.register-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* 头像上传区域 */
.avatar-upload-section {
    text-align: center;
    margin-bottom: 2rem;
}

.avatar-preview {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-preview:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.5);
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 0.75rem;
    gap: 0.25rem;
}

.avatar-preview:hover .avatar-overlay {
    opacity: 1;
}

.avatar-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

/* 表单样式 */
.register-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    color: white;
    font-weight: 500;
    font-size: 0.875rem;
}

.input-container {
    position: relative;
}

.form-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.form-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    pointer-events: none;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
}

/* 错误提示 */
.field-error {
    color: #ff6b6b;
    font-size: 0.875rem;
    min-height: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 密码强度指示器 */
.password-strength {
    display: flex;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    flex: 1;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.strength-bar.active {
    background: #4ade80;
}

.strength-bar.medium {
    background: #fbbf24;
}

.strength-bar.weak {
    background: #f87171;
}

/* 复选框样式 */
.checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
    line-height: 1.5;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 0.125rem;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: #4ade80;
    border-color: #4ade80;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.terms-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: white;
}

/* 注册按钮 */
.register-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #4ade80, #22c55e);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 1rem;
}

.register-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(34, 197, 94, 0.3);
}

.register-button:active {
    transform: translateY(0);
}

.register-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.button-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 页脚 */
.register-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.login-prompt {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.login-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.login-link:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }
    
    .nav-links {
        gap: 1rem;
    }
    
    .nav-link {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .register-container {
        padding: 5rem 1rem 2rem;
    }
    
    .register-card {
        padding: 2rem 1.5rem;
    }
    
    .register-title {
        font-size: 1.75rem;
    }
    
    .form-input {
        padding: 0.875rem 0.875rem 0.875rem 2.75rem;
    }
    
    .input-icon {
        left: 0.875rem;
    }
    
    .password-toggle {
        right: 0.875rem;
    }
}

@media (max-width: 480px) {
    .brand-text {
        display: none;
    }
    
    .register-card {
        padding: 1.5rem 1rem;
    }
    
    .avatar-preview {
        width: 80px;
        height: 80px;
    }
}
