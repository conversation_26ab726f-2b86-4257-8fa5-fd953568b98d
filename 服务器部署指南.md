# 比特熊智慧系统服务器部署指南

## 服务器信息
- **IP地址**: *************
- **用户名**: root
- **密码**: ZbDX7%=]?H2(LAUz
- **项目目录**: /www/wwwroot/www.bitbear.top
- **数据库名**: bitbear_website
- **数据库密码**: 309290133q

## 1. 连接服务器

### 方法1: 使用PuTTY (推荐)
1. 下载并安装 PuTTY: https://www.putty.org/
2. 打开PuTTY
3. 在Host Name中输入: `*************`
4. Port: `22`
5. Connection type: `SSH`
6. 点击Open
7. 输入用户名: `root`
8. 输入密码: `ZbDX7%=]?H2(LAUz`

### 方法2: 使用Windows Terminal (如果已安装)
```bash
ssh root@*************
# 输入密码: ZbDX7%=]?H2(LAUz
```

## 2. 检查服务器环境

连接成功后，执行以下命令检查环境：

```bash
# 检查项目目录
ls -la /www/wwwroot/www.bitbear.top

# 检查PHP版本
php -v

# 检查MySQL服务
systemctl status mysql

# 检查Nginx服务
systemctl status nginx
```

## 3. 配置数据库

### 3.1 连接MySQL
```bash
mysql -u root -p
# 输入数据库密码: 309290133q
```

### 3.2 创建/检查数据库
```sql
-- 检查数据库是否存在
SHOW DATABASES;

-- 如果不存在，创建数据库
CREATE DATABASE IF NOT EXISTS bitbear_website CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE bitbear_website;

-- 检查表结构
SHOW TABLES;
```

## 4. 部署项目文件

### 4.1 检查项目文件
```bash
cd /www/wwwroot/www.bitbear.top
ls -la
```

### 4.2 设置文件权限
```bash
# 设置所有者为www-data
chown -R www-data:www-data /www/wwwroot/www.bitbear.top

# 设置目录权限
find /www/wwwroot/www.bitbear.top -type d -exec chmod 755 {} \;

# 设置文件权限
find /www/wwwroot/www.bitbear.top -type f -exec chmod 644 {} \;

# 设置uploads目录可写权限
chmod -R 777 /www/wwwroot/www.bitbear.top/uploads
```

## 5. 配置数据库连接

### 5.1 修改数据库配置文件
```bash
cd /www/wwwroot/www.bitbear.top
nano config/database.php
```

### 5.2 更新数据库配置
将数据库配置修改为：
```php
private $host = 'localhost';
private $username = 'root';
private $password = '309290133q';
private $database = 'bitbear_website';
private $charset = 'utf8mb4';
private $ports = [3306]; // 服务器通常使用3306端口
```

## 6. 导入数据库数据

### 6.1 创建数据库结构
```bash
cd /www/wwwroot/www.bitbear.top
mysql -u root -p309290133q bitbear_website < database/init.sql
```

### 6.2 导入其他表结构
```bash
# 导入社区表
mysql -u root -p309290133q bitbear_website < database/create_community_tables.sql

# 导入主页内容表
mysql -u root -p309290133q bitbear_website < database/homepage_content_tables.sql
```

## 7. 配置Web服务器

### 7.1 检查Nginx配置
```bash
# 检查Nginx配置文件
cat /etc/nginx/sites-available/www.bitbear.top

# 如果不存在，创建配置文件
nano /etc/nginx/sites-available/www.bitbear.top
```

### 7.2 Nginx配置示例
```nginx
server {
    listen 80;
    server_name www.bitbear.top *************;
    root /www/wwwroot/www.bitbear.top;
    index index.php index.html index.htm;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

### 7.3 启用站点并重启服务
```bash
# 创建软链接启用站点
ln -s /etc/nginx/sites-available/www.bitbear.top /etc/nginx/sites-enabled/

# 测试Nginx配置
nginx -t

# 重启Nginx
systemctl restart nginx

# 重启PHP-FPM
systemctl restart php8.1-fpm
```

## 8. 测试部署

### 8.1 检查网站访问
在浏览器中访问：
- http://*************
- http://www.bitbear.top (如果域名已解析)

### 8.2 检查数据库连接
```bash
cd /www/wwwroot/www.bitbear.top
php test_db_connection.php
```

## 9. 常见问题解决

### 9.1 权限问题
```bash
# 如果遇到权限问题，重新设置权限
chown -R www-data:www-data /www/wwwroot/www.bitbear.top
chmod -R 755 /www/wwwroot/www.bitbear.top
chmod -R 777 /www/wwwroot/www.bitbear.top/uploads
```

### 9.2 数据库连接问题
```bash
# 检查MySQL服务状态
systemctl status mysql

# 重启MySQL服务
systemctl restart mysql

# 检查数据库用户权限
mysql -u root -p309290133q -e "SELECT User, Host FROM mysql.user;"
```

### 9.3 PHP错误
```bash
# 检查PHP错误日志
tail -f /var/log/nginx/error.log
tail -f /var/log/php8.1-fpm.log
```

## 10. 数据迁移

如果需要从本地迁移数据到服务器：

### 10.1 导出本地数据
在本地执行：
```bash
# 导出本地数据库
mysqldump -u root -p bitbear_system > bitbear_local_data.sql
```

### 10.2 上传并导入数据
```bash
# 在服务器上导入数据
mysql -u root -p309290133q bitbear_website < bitbear_local_data.sql
```

## 完成部署

部署完成后，您应该能够：
1. 通过浏览器访问网站
2. 登录管理后台
3. 查看和管理内容
4. 正常使用所有功能

如果遇到任何问题，请检查错误日志并根据错误信息进行调试。
