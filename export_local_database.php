<?php
/**
 * 导出本地数据库数据到SQL文件
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "开始导出本地数据库数据...\n";

// 本地数据库配置
$localConfig = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'bitbear_system',
    'charset' => 'utf8mb4',
    'ports' => [3307, 3306]
];

// 尝试连接本地数据库
$pdo = null;
foreach ($localConfig['ports'] as $port) {
    try {
        $dsn = "mysql:host={$localConfig['host']};port={$port};dbname={$localConfig['database']};charset={$localConfig['charset']}";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ];
        
        $pdo = new PDO($dsn, $localConfig['username'], $localConfig['password'], $options);
        echo "✓ 成功连接到本地数据库 (端口: {$port})\n";
        break;
        
    } catch (PDOException $e) {
        echo "× 端口 {$port} 连接失败: " . $e->getMessage() . "\n";
        continue;
    }
}

if (!$pdo) {
    die("❌ 无法连接到本地数据库\n");
}

// 获取所有表
try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "✓ 找到 " . count($tables) . " 个表\n";
    
    if (empty($tables)) {
        die("❌ 数据库中没有表\n");
    }
    
} catch (PDOException $e) {
    die("❌ 获取表列表失败: " . $e->getMessage() . "\n");
}

// 创建导出文件
$exportFile = 'database_export_' . date('Y-m-d_H-i-s') . '.sql';
$fp = fopen($exportFile, 'w');

if (!$fp) {
    die("❌ 无法创建导出文件: {$exportFile}\n");
}

// 写入文件头
fwrite($fp, "-- 比特熊智慧系统数据库导出\n");
fwrite($fp, "-- 导出时间: " . date('Y-m-d H:i:s') . "\n");
fwrite($fp, "-- 数据库: {$localConfig['database']}\n\n");

fwrite($fp, "SET NAMES utf8mb4;\n");
fwrite($fp, "SET FOREIGN_KEY_CHECKS = 0;\n\n");

// 导出每个表的结构和数据
foreach ($tables as $table) {
    echo "正在导出表: {$table}\n";
    
    try {
        // 获取表结构
        $createTable = $pdo->query("SHOW CREATE TABLE `{$table}`")->fetch();
        fwrite($fp, "-- 表结构: {$table}\n");
        fwrite($fp, "DROP TABLE IF EXISTS `{$table}`;\n");
        fwrite($fp, $createTable['Create Table'] . ";\n\n");
        
        // 获取表数据
        $rows = $pdo->query("SELECT * FROM `{$table}`")->fetchAll();
        
        if (!empty($rows)) {
            fwrite($fp, "-- 表数据: {$table}\n");
            
            // 获取列名
            $columns = array_keys($rows[0]);
            $columnList = '`' . implode('`, `', $columns) . '`';
            
            // 分批插入数据
            $batchSize = 100;
            $batches = array_chunk($rows, $batchSize);
            
            foreach ($batches as $batch) {
                fwrite($fp, "INSERT INTO `{$table}` ({$columnList}) VALUES\n");
                
                $values = [];
                foreach ($batch as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = "'" . addslashes($value) . "'";
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                fwrite($fp, implode(",\n", $values) . ";\n\n");
            }
        }
        
    } catch (PDOException $e) {
        echo "× 导出表 {$table} 失败: " . $e->getMessage() . "\n";
        continue;
    }
}

fwrite($fp, "SET FOREIGN_KEY_CHECKS = 1;\n");
fclose($fp);

echo "✓ 数据库导出完成: {$exportFile}\n";
echo "文件大小: " . formatBytes(filesize($exportFile)) . "\n";

// 格式化文件大小
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

echo "\n下一步: 将此文件上传到服务器并导入到数据库\n";
?>
