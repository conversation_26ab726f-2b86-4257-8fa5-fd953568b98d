<?php
/**
 * 简化版注册测试页面
 * 专门用于测试云服务器环境下的注册功能
 */
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册测试 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .form-container {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .message {
            margin: 15px 0;
            padding: 12px;
            border-radius: 6px;
            display: none;
        }
        .message.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .message.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .debug-info {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #4facfe;
            text-decoration: none;
            margin: 0 10px;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 注册功能测试</h1>
            <p>测试云服务器环境下的用户注册</p>
        </div>
        
        <div class="form-container">
            <form id="testRegisterForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required 
                           placeholder="请输入用户名（3-20个字符）">
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email" required 
                           placeholder="请输入邮箱地址">
                </div>
                
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname" required 
                           placeholder="请输入昵称">
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required 
                           placeholder="请输入密码（至少6个字符）">
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" required 
                           placeholder="请再次输入密码">
                </div>
                
                <button type="submit" class="btn" id="submitBtn">
                    <span id="btnText">测试注册</span>
                    <span id="btnLoading" style="display: none;">注册中...</span>
                </button>
            </form>
            
            <div id="message" class="message"></div>
            
            <div class="links">
                <a href="register.php">正式注册页面</a>
                <a href="云服务器注册修复.php">修复工具</a>
                <a href="login.php">登录页面</a>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const btnText = document.getElementById('btnText');
            const btnLoading = document.getElementById('btnLoading');
            const messageDiv = document.getElementById('message');
            
            // 获取表单数据
            const formData = new FormData();
            formData.append('username', document.getElementById('username').value);
            formData.append('email', document.getElementById('email').value);
            formData.append('nickname', document.getElementById('nickname').value);
            formData.append('password', document.getElementById('password').value);
            formData.append('confirmPassword', document.getElementById('confirmPassword').value);
            
            // 显示加载状态
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
            messageDiv.style.display = 'none';
            
            // 发送请求
            fetch('api/register.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                
                // 显示结果
                messageDiv.style.display = 'block';
                
                if (data.success) {
                    messageDiv.className = 'message success';
                    messageDiv.innerHTML = `
                        <strong>✅ 注册成功！</strong><br>
                        ${data.message}<br>
                        用户ID: ${data.user_id || '未知'}
                    `;
                    
                    // 清空表单
                    document.getElementById('testRegisterForm').reset();
                    
                } else {
                    messageDiv.className = 'message error';
                    let errorHtml = `<strong>❌ 注册失败</strong><br>${data.message}`;
                    
                    // 显示具体错误信息
                    if (data.errors) {
                        errorHtml += '<br><br><strong>详细错误：</strong><br>';
                        for (const field in data.errors) {
                            errorHtml += `• ${data.errors[field]}<br>`;
                        }
                    }
                    
                    // 显示调试信息
                    if (data.debug_info) {
                        errorHtml += `
                            <div class="debug-info">
                                <strong>调试信息：</strong><br>
                                错误: ${data.debug_info.error}<br>
                                文件: ${data.debug_info.file}<br>
                                行号: ${data.debug_info.line}
                            </div>
                        `;
                    }
                    
                    messageDiv.innerHTML = errorHtml;
                }
            })
            .catch(error => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                btnText.style.display = 'inline';
                btnLoading.style.display = 'none';
                
                // 显示网络错误
                messageDiv.style.display = 'block';
                messageDiv.className = 'message error';
                messageDiv.innerHTML = `
                    <strong>❌ 网络错误</strong><br>
                    请检查网络连接或服务器状态<br>
                    <div class="debug-info">
                        错误详情: ${error.message}
                    </div>
                `;
            });
        });
        
        // 自动填充测试数据
        window.addEventListener('load', function() {
            const timestamp = Date.now();
            document.getElementById('username').value = 'testuser' + timestamp;
            document.getElementById('email').value = 'test' + timestamp + '@example.com';
            document.getElementById('nickname').value = '测试用户' + timestamp;
            document.getElementById('password').value = 'test123456';
            document.getElementById('confirmPassword').value = 'test123456';
        });
    </script>
</body>
</html>
