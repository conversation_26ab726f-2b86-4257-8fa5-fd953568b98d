<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>直接测试按钮功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-button { background: #28a745; font-size: 16px; padding: 15px 25px; }
        .results { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa; }
        .success { color: #28a745; font-weight: bold; }
        .error { color: #dc3545; font-weight: bold; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 直接测试按钮功能</h1>
        <p>这个页面将直接测试按钮点击是否工作</p>
        
        <h2>测试1: 基本按钮功能</h2>
        <button class="test-button" onclick="testBasic()">点击我测试基本功能</button>
        
        <h2>测试2: 模拟分类按钮</h2>
        <button class="test-button" onclick="console.log('按钮被点击了！'); window.showAddCategoryModal(); console.log('函数调用完成');">
            添加分类 (模拟)
        </button>
        
        <h2>测试3: 直接调用函数</h2>
        <button class="test-button" onclick="directCallTest()">直接调用showAddCategoryModal</button>
        
        <h2>测试4: 打开真实admin页面</h2>
        <button class="test-button" onclick="openRealAdminPage()">打开admin-dashboard.php</button>
        
        <div class="results" id="results">
            <div class="info">等待测试结果...</div>
        </div>
    </div>

    <script>
        // 全局错误处理
        window.addEventListener('error', function(e) {
            log(`❌ JavaScript错误: ${e.message}`, 'error');
            log(`📍 位置: ${e.filename}:${e.lineno}:${e.colno}`, 'error');
        });

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        function testBasic() {
            log('✅ 基本按钮功能正常工作！', 'success');
            log('🎉 onclick事件可以正常触发', 'success');
        }

        // 复制admin-dashboard.php中的函数
        window.showAddCategoryModal = function() {
            log('🚀 showAddCategoryModal函数被调用', 'success');
            alert('showAddCategoryModal函数工作正常！');
            
            // 创建简单的模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                background: rgba(0,0,0,0.7); display: flex; align-items: center; 
                justify-content: center; z-index: 1000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 30px; border-radius: 8px; max-width: 500px; width: 90%; text-align: center;">
                    <h2>🎉 成功！</h2>
                    <p>showAddCategoryModal函数正常工作！</p>
                    <p>这证明JavaScript代码没有问题。</p>
                    <button onclick="this.closest('div').parentElement.remove()" 
                            style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 15px;">
                        关闭
                    </button>
                </div>
            `;
            
            document.body.appendChild(modal);
            log('✅ 模态框已创建并显示', 'success');
        };

        function directCallTest() {
            log('📞 直接调用showAddCategoryModal函数...', 'info');
            try {
                window.showAddCategoryModal();
                log('✅ 直接调用成功', 'success');
            } catch (error) {
                log(`❌ 直接调用失败: ${error.message}`, 'error');
            }
        }

        function openRealAdminPage() {
            log('🔗 正在打开真实的admin-dashboard.php页面...', 'info');
            log('💡 请在新打开的页面中:', 'info');
            log('1. 打开浏览器开发者工具 (F12)', 'info');
            log('2. 切换到分类管理页面', 'info');
            log('3. 点击添加分类按钮', 'info');
            log('4. 查看控制台输出', 'info');
            
            // 打开新窗口
            const adminWindow = window.open('/admin-dashboard.php?t=' + Date.now(), 'adminDashboard', 'width=1200,height=800');
            
            if (adminWindow) {
                log('✅ admin页面已在新窗口中打开', 'success');
            } else {
                log('❌ 无法打开admin页面（可能被弹窗拦截）', 'error');
            }
        }

        // 页面加载时的初始化
        window.onload = function() {
            log('🎯 直接测试页面已加载', 'success');
            log('💡 请依次点击测试按钮', 'info');
            log('🔍 如果前3个测试都成功，说明JavaScript功能正常', 'info');
            log('📋 然后可以打开真实admin页面进行测试', 'info');
        };
    </script>
</body>
</html>
