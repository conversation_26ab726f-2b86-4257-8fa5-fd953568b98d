<!DOCTYPE html>
<html>
<head>
    <title>简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>分类功能测试结果</h1>
    <button onclick="testFunction()">测试功能</button>
    <div id="results"></div>
    
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }
        
        function testFunction() {
            log('开始测试...', 'info');
            
            // 测试基本功能
            try {
                log('✓ JavaScript 基本功能正常', 'success');
                
                // 测试事件处理
                const testBtn = document.createElement('button');
                testBtn.textContent = '测试按钮';
                testBtn.addEventListener('click', function() {
                    log('✓ 事件监听器工作正常', 'success');
                });
                
                // 模拟点击
                testBtn.click();
                
                // 测试表单处理
                const form = document.createElement('form');
                form.innerHTML = '<input name="test" value="测试值">';
                const formData = new FormData(form);
                if (formData.get('test') === '测试值') {
                    log('✓ 表单数据处理正常', 'success');
                } else {
                    log('✗ 表单数据处理失败', 'error');
                }
                
                log('所有测试完成！', 'success');
                
            } catch (error) {
                log('✗ 测试出错: ' + error.message, 'error');
            }
        }
        
        // 自动运行测试
        window.addEventListener('load', function() {
            setTimeout(testFunction, 500);
        });
    </script>
</body>
</html>
