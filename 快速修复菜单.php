<?php
/**
 * 快速修复菜单表结构
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔧 快速修复菜单表结构</h1>";

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查icon字段是否存在
    $columns = $db->query("SHOW COLUMNS FROM navbar_items")->fetchAll();
    $hasIconField = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'icon') {
            $hasIconField = true;
            break;
        }
    }
    
    if ($hasIconField) {
        echo "<p>✅ icon字段已存在，无需修复</p>";
    } else {
        echo "<p>⚠️ 缺少icon字段，正在修复...</p>";
        
        // 添加icon字段
        $db->exec("ALTER TABLE navbar_items ADD COLUMN icon VARCHAR(255) NULL AFTER parent_id");
        echo "<p>✅ icon字段添加成功</p>";
        
        // 添加索引
        try {
            $db->exec("ALTER TABLE navbar_items ADD INDEX idx_parent (parent_id)");
            echo "<p>✅ parent_id索引添加成功</p>";
        } catch (Exception $e) {
            echo "<p>ℹ️ parent_id索引可能已存在</p>";
        }
        
        try {
            $db->exec("ALTER TABLE navbar_items ADD INDEX idx_order (sort_order)");
            echo "<p>✅ sort_order索引添加成功</p>";
        } catch (Exception $e) {
            echo "<p>ℹ️ sort_order索引可能已存在</p>";
        }
    }
    
    // 测试菜单添加功能
    echo "<h2>🧪 测试菜单功能</h2>";
    
    $testName = '测试菜单_' . time();
    $stmt = $db->prepare("INSERT INTO navbar_items (name, url, type, parent_id, icon, visible, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
    $result = $stmt->execute([$testName, '/test.php', 'link', null, 'fas fa-test', true, 999]);
    
    if ($result) {
        echo "<p>✅ 菜单添加测试成功</p>";
        
        // 删除测试菜单
        $db->prepare("DELETE FROM navbar_items WHERE name = ?")->execute([$testName]);
        echo "<p>✅ 测试菜单清理完成</p>";
    } else {
        echo "<p>❌ 菜单添加测试失败</p>";
    }
    
    echo "<h2>🎉 修复完成</h2>";
    echo "<p>现在可以正常添加菜单项了！</p>";
    echo "<p><a href='admin-dashboard.php' style='color: #4facfe;'>返回管理后台</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 修复失败: " . $e->getMessage() . "</p>";
    echo "<p>请检查数据库连接或手动执行SQL修复脚本</p>";
}
?>
