# 📊 宝塔面板数据库导入说明

## 🎯 导入顺序（重要！）

请按照以下顺序导入SQL文件，确保数据库结构正确创建：

### 1️⃣ 第一步：基础表结构
**文件**: `init.sql`
**说明**: 创建用户、角色等基础表

### 2️⃣ 第二步：社区功能表
**文件**: `create_community_tables.sql`  
**说明**: 创建帖子、评论、分类等社区相关表

### 3️⃣ 第三步：主页内容表
**文件**: `homepage_content_tables.sql`
**说明**: 创建主页展示内容相关表

### 4️⃣ 第四步：O'Reilly区域表（可选）
**文件**: `create_oreilly_sections.sql`
**说明**: 创建O'Reilly风格展示区域表

## 📋 在宝塔面板中的操作步骤

### 步骤1：进入数据库管理
1. 登录宝塔面板
2. 点击左侧菜单 **"数据库"**
3. 找到 `bitbear_website` 数据库
4. 点击 **"管理"** 按钮进入phpMyAdmin

### 步骤2：导入SQL文件
1. 在phpMyAdmin中选择 `bitbear_website` 数据库
2. 点击顶部 **"导入"** 选项卡
3. 点击 **"选择文件"** 按钮
4. 按顺序上传并导入SQL文件

### 步骤3：验证导入结果
导入完成后，检查是否创建了以下表：

**基础表**:
- `users` - 用户表
- `user_roles` - 用户角色表
- `user_profiles` - 用户资料表
- `user_sessions` - 用户会话表

**社区表**:
- `posts` - 帖子表
- `post_categories` - 帖子分类表
- `comments` - 评论表
- `post_likes` - 点赞表

**主页内容表**:
- `homepage_hero` - 主页横幅表
- `homepage_experts` - 专家展示表
- `homepage_sections` - 页面区域表

## ⚠️ 常见问题解决

### 问题1：导入失败 - 表已存在
**解决方案**: 
- 先删除已存在的表，或
- 选择 **"DROP TABLE"** 选项重新导入

### 问题2：字符编码问题
**解决方案**:
- 确保数据库字符集为 `utf8mb4`
- 在导入时选择 `utf8mb4_unicode_ci` 排序规则

### 问题3：文件过大无法上传
**解决方案**:
- 在宝塔面板中调整PHP上传限制
- 或者分批导入SQL文件

### 问题4：权限错误
**解决方案**:
- 确保数据库用户有足够权限
- 检查数据库连接配置

## 🔧 手动创建数据库（备用方案）

如果自动导入失败，可以在phpMyAdmin中手动执行以下命令：

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS bitbear_website 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE bitbear_website;

-- 然后复制粘贴各个SQL文件的内容执行
```

## ✅ 导入完成检查

导入完成后，执行以下查询验证：

```sql
-- 检查表数量
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'bitbear_website';

-- 检查用户表
SELECT COUNT(*) as user_count FROM users;

-- 检查角色表
SELECT * FROM user_roles;
```

## 📞 需要帮助？

如果在导入过程中遇到问题：
1. 查看phpMyAdmin的错误信息
2. 检查宝塔面板的错误日志
3. 确认数据库配置是否正确

---
**导入成功后，就可以测试网站的数据库连接功能了！** 🎉
