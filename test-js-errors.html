<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript错误检测</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 4px; margin: 5px 0; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .results { margin-top: 20px; max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; border-radius: 4px; }
        iframe { width: 100%; height: 400px; border: 1px solid #ddd; border-radius: 4px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JavaScript错误检测工具</h1>
        <p>检测admin-dashboard.php中的JavaScript错误</p>
        
        <button onclick="loadAndCheckErrors()">🔍 检查JavaScript错误</button>
        <button onclick="testDirectCall()">📞 直接调用函数测试</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
        
        <div class="results" id="results"></div>
        
        <iframe id="testFrame" src="about:blank" style="display: none;"></iframe>
    </div>

    <script>
        let errorCount = 0;
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            errorCount = 0;
        }

        // 捕获JavaScript错误
        window.addEventListener('error', function(e) {
            errorCount++;
            log(`❌ JavaScript错误 #${errorCount}: ${e.message}`, 'error');
            log(`📍 文件: ${e.filename}:${e.lineno}:${e.colno}`, 'error');
            if (e.error && e.error.stack) {
                log(`📚 堆栈: ${e.error.stack}`, 'error');
            }
        });

        function loadAndCheckErrors() {
            log('🔍 开始检查admin-dashboard.php的JavaScript错误...', 'info');
            errorCount = 0;
            
            const iframe = document.getElementById('testFrame');
            iframe.style.display = 'block';
            
            // 监听iframe中的错误
            iframe.onload = function() {
                try {
                    const iframeWindow = iframe.contentWindow;
                    const iframeDoc = iframe.contentDocument;
                    
                    // 在iframe中捕获错误
                    iframeWindow.addEventListener('error', function(e) {
                        errorCount++;
                        log(`❌ iframe中的JavaScript错误 #${errorCount}: ${e.message}`, 'error');
                        log(`📍 位置: ${e.filename}:${e.lineno}:${e.colno}`, 'error');
                    });
                    
                    // 检查关键函数是否存在
                    setTimeout(() => {
                        log('🔍 检查关键函数...', 'info');
                        
                        if (typeof iframeWindow.showAddCategoryModal === 'function') {
                            log('✅ showAddCategoryModal函数存在', 'success');
                        } else {
                            log('❌ showAddCategoryModal函数不存在', 'error');
                        }
                        
                        if (typeof iframeWindow.addCategory === 'function') {
                            log('✅ addCategory函数存在', 'success');
                        } else {
                            log('❌ addCategory函数不存在', 'error');
                        }
                        
                        if (typeof iframeWindow.switchPage === 'function') {
                            log('✅ switchPage函数存在', 'success');
                            
                            // 尝试切换到分类管理页面
                            try {
                                iframeWindow.switchPage('categories-management');
                                log('✅ 成功切换到分类管理页面', 'success');
                                
                                // 检查按钮是否存在
                                setTimeout(() => {
                                    const addButton = iframeDoc.querySelector('button[onclick*="showAddCategoryModal"]');
                                    if (addButton) {
                                        log('✅ 找到添加分类按钮', 'success');
                                        log(`📝 按钮文本: "${addButton.textContent.trim()}"`, 'info');
                                        
                                        // 尝试点击按钮
                                        try {
                                            addButton.click();
                                            log('✅ 按钮点击成功', 'success');
                                            
                                            // 检查模态框
                                            setTimeout(() => {
                                                const modal = iframeDoc.querySelector('.modal-overlay');
                                                if (modal) {
                                                    log('🎉 模态框出现成功！', 'success');
                                                } else {
                                                    log('❌ 模态框未出现', 'error');
                                                }
                                            }, 500);
                                            
                                        } catch (clickError) {
                                            log(`❌ 点击按钮失败: ${clickError.message}`, 'error');
                                        }
                                    } else {
                                        log('❌ 找不到添加分类按钮', 'error');
                                    }
                                }, 1000);
                                
                            } catch (switchError) {
                                log(`❌ 切换页面失败: ${switchError.message}`, 'error');
                            }
                        } else {
                            log('❌ switchPage函数不存在', 'error');
                        }
                        
                        if (errorCount === 0) {
                            log('🎉 没有发现JavaScript错误！', 'success');
                        } else {
                            log(`⚠️ 发现 ${errorCount} 个JavaScript错误`, 'error');
                        }
                    }, 2000);
                    
                } catch (error) {
                    log(`❌ 访问iframe内容失败: ${error.message}`, 'error');
                }
            };
            
            iframe.onerror = function() {
                log('❌ iframe加载失败', 'error');
            };
            
            iframe.src = '/admin-dashboard.php';
        }

        function testDirectCall() {
            log('📞 测试直接调用函数...', 'info');
            
            // 创建一个新窗口来测试
            const testWindow = window.open('/admin-dashboard.php', 'testWindow', 'width=1200,height=800');
            
            testWindow.onload = function() {
                setTimeout(() => {
                    try {
                        // 切换到分类管理页面
                        if (typeof testWindow.switchPage === 'function') {
                            testWindow.switchPage('categories-management');
                            log('✅ 在新窗口中切换到分类管理页面', 'success');
                            
                            setTimeout(() => {
                                // 直接调用函数
                                if (typeof testWindow.showAddCategoryModal === 'function') {
                                    testWindow.showAddCategoryModal();
                                    log('✅ 在新窗口中调用showAddCategoryModal成功', 'success');
                                } else {
                                    log('❌ 新窗口中showAddCategoryModal函数不存在', 'error');
                                }
                            }, 1000);
                        } else {
                            log('❌ 新窗口中switchPage函数不存在', 'error');
                        }
                    } catch (error) {
                        log(`❌ 新窗口测试失败: ${error.message}`, 'error');
                    }
                }, 2000);
            };
        }

        // 页面加载时显示说明
        window.onload = function() {
            log('🎯 JavaScript错误检测工具已加载', 'info');
            log('💡 点击"检查JavaScript错误"开始诊断', 'info');
        };
    </script>
</body>
</html>
