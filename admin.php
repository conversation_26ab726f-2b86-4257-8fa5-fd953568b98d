<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家管理后台 - 比特熊</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            padding: 2rem 0;
            text-align: center;
        }
        
        .admin-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .admin-subtitle {
            opacity: 0.8;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .admin-content {
            padding: 3rem 0;
        }
        
        .experts-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .table-header {
            background: #f1f5f9;
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .table-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f8fafc;
            font-weight: 600;
            color: #475569;
        }
        
        .expert-avatar {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .add-form {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .status-message {
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 2rem;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">专家管理后台</h1>
            <p class="admin-subtitle">管理网站专家展示区域的内容</p>
        </div>
    </header>

    <main class="admin-content">
        <div class="container">
            <?php
            // 引入专家数据
            include 'experts-data.php';
            
            // 处理表单提交
            $message = '';
            $messageType = '';
            
            if ($_POST) {
                if (isset($_POST['action'])) {
                    switch ($_POST['action']) {
                        case 'add':
                            if (!empty($_POST['name']) && !empty($_POST['title'])) {
                                $image = !empty($_POST['image']) ? $_POST['image'] : 'image/default-avatar.svg';
                                addExpert($_POST['name'], $_POST['title'], $image);
                                $message = '专家添加成功！';
                                $messageType = 'success';
                            } else {
                                $message = '请填写专家姓名和职位！';
                                $messageType = 'error';
                            }
                            break;
                        case 'delete':
                            if (isset($_POST['id'])) {
                                if (deleteExpert($_POST['id'])) {
                                    $message = '专家删除成功！';
                                    $messageType = 'success';
                                } else {
                                    $message = '删除失败，专家不存在！';
                                    $messageType = 'error';
                                }
                            }
                            break;
                    }
                }
            }
            
            // 显示状态消息
            if ($message) {
                echo '<div class="status-message status-' . $messageType . '">' . $message . '</div>';
            }
            ?>

            <!-- 专家列表 -->
            <div class="experts-table">
                <div class="table-header">
                    <h2 class="table-title">当前专家列表</h2>
                </div>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>头像</th>
                            <th>姓名</th>
                            <th>职位</th>
                            <th>图片路径</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $experts = getExperts();
                        foreach ($experts as $expert) {
                            echo '<tr>';
                            echo '<td>' . $expert['id'] . '</td>';
                            echo '<td><img src="' . $expert['image'] . '" alt="' . $expert['name'] . '" class="expert-avatar"></td>';
                            echo '<td>' . htmlspecialchars($expert['name']) . '</td>';
                            echo '<td>' . htmlspecialchars($expert['title']) . '</td>';
                            echo '<td>' . htmlspecialchars($expert['image']) . '</td>';
                            echo '<td>';
                            echo '<div class="actions">';
                            echo '<form method="post" style="display: inline;">';
                            echo '<input type="hidden" name="action" value="delete">';
                            echo '<input type="hidden" name="id" value="' . $expert['id'] . '">';
                            echo '<button type="submit" class="btn btn-danger" onclick="return confirm(\'确定要删除这个专家吗？\')">删除</button>';
                            echo '</form>';
                            echo '</div>';
                            echo '</td>';
                            echo '</tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>

            <!-- 添加专家表单 -->
            <div class="add-form">
                <h2 style="margin-bottom: 1.5rem; color: #1e293b;">添加新专家</h2>
                <form method="post">
                    <input type="hidden" name="action" value="add">
                    
                    <div class="form-group">
                        <label class="form-label" for="name">专家姓名 *</label>
                        <input type="text" id="name" name="name" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="title">职位 *</label>
                        <input type="text" id="title" name="title" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="image">头像图片路径</label>
                        <input type="text" id="image" name="image" class="form-input" placeholder="留空使用默认头像">
                    </div>
                    
                    <button type="submit" class="btn btn-success">添加专家</button>
                </form>
            </div>

            <div style="margin-top: 2rem; text-align: center;">
                <a href="index.php" class="btn btn-primary">查看前台效果</a>
            </div>
        </div>
    </main>
</body>
</html>
