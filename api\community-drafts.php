<?php
/**
 * 社区草稿管理API
 */

session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';
require_once '../classes/Auth.php';

// 检查用户登录状态
$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

$userId = $currentUser['id'];

// 获取请求方法和参数
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    $db = db();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGet($pdo, $userId);
            break;
        case 'POST':
            handlePost($pdo, $userId, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => '不支持的请求方法']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 处理GET请求
 */
function handleGet($pdo, $userId) {
    $action = $_GET['action'] ?? 'list';
    
    if ($action === 'count') {
        // 获取草稿数量
        $sql = "SELECT COUNT(*) as count FROM posts WHERE user_id = ? AND (status = 'draft' OR status = 'rejected')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'count' => (int)$result['count']]);
    } else {
        // 获取草稿列表
        $limit = (int)($_GET['limit'] ?? 20);
        $offset = (int)($_GET['offset'] ?? 0);
        
        $sql = "SELECT p.*, pc.name as category_name, pc.color as category_color
                FROM posts p
                LEFT JOIN post_categories pc ON p.category_id = pc.id
                WHERE p.user_id = ? AND (p.status = 'draft' OR p.status = 'rejected')
                ORDER BY p.updated_at DESC
                LIMIT ? OFFSET ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$userId, $limit, $offset]);
        $drafts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'drafts' => $drafts]);
    }
}

/**
 * 处理POST请求
 */
function handlePost($pdo, $userId, $input) {
    $action = $input['action'] ?? '';
    
    if ($action === 'publish') {
        // 发布草稿
        $draftId = (int)($input['draft_id'] ?? 0);
        
        if ($draftId <= 0) {
            echo json_encode(['success' => false, 'message' => '无效的草稿ID']);
            return;
        }
        
        // 检查草稿是否属于当前用户
        $checkSql = "SELECT id, status FROM posts WHERE id = ? AND user_id = ?";
        $stmt = $pdo->prepare($checkSql);
        $stmt->execute([$draftId, $userId]);
        $draft = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$draft) {
            echo json_encode(['success' => false, 'message' => '草稿不存在或无权限']);
            return;
        }
        
        if ($draft['status'] !== 'draft' && $draft['status'] !== 'rejected') {
            echo json_encode(['success' => false, 'message' => '只能发布草稿状态的帖子']);
            return;
        }
        
        // 更新状态为待审核
        $updateSql = "UPDATE posts SET status = 'pending', updated_at = NOW() WHERE id = ?";
        $stmt = $pdo->prepare($updateSql);
        $stmt->execute([$draftId]);
        
        echo json_encode(['success' => true, 'message' => '草稿已提交审核']);
        
    } elseif ($action === 'delete') {
        // 删除草稿
        $draftId = (int)($input['draft_id'] ?? 0);
        
        if ($draftId <= 0) {
            echo json_encode(['success' => false, 'message' => '无效的草稿ID']);
            return;
        }
        
        // 检查草稿是否属于当前用户
        $checkSql = "SELECT id FROM posts WHERE id = ? AND user_id = ? AND (status = 'draft' OR status = 'rejected')";
        $stmt = $pdo->prepare($checkSql);
        $stmt->execute([$draftId, $userId]);
        $draft = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$draft) {
            echo json_encode(['success' => false, 'message' => '草稿不存在或无权限']);
            return;
        }
        
        // 删除草稿
        $deleteSql = "DELETE FROM posts WHERE id = ?";
        $stmt = $pdo->prepare($deleteSql);
        $stmt->execute([$draftId]);
        
        echo json_encode(['success' => true, 'message' => '草稿删除成功']);
        
    } elseif ($action === 'save_draft') {
        // 保存草稿
        $title = trim($input['title'] ?? '');
        $content = trim($input['content'] ?? '');
        $categoryId = (int)($input['category_id'] ?? 0);
        $postId = (int)($input['post_id'] ?? 0);
        
        if (empty($title) || empty($content)) {
            echo json_encode(['success' => false, 'message' => '标题和内容不能为空']);
            return;
        }
        
        if ($postId > 0) {
            // 更新现有草稿
            $updateSql = "UPDATE posts SET title = ?, content = ?, category_id = ?, updated_at = NOW() 
                         WHERE id = ? AND user_id = ? AND (status = 'draft' OR status = 'rejected')";
            $stmt = $pdo->prepare($updateSql);
            $stmt->execute([$title, $content, $categoryId, $postId, $userId]);
            
            if ($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => '草稿保存成功', 'post_id' => $postId]);
            } else {
                echo json_encode(['success' => false, 'message' => '草稿不存在或无权限']);
            }
        } else {
            // 创建新草稿
            $insertSql = "INSERT INTO posts (user_id, title, content, category_id, status, created_at, updated_at) 
                         VALUES (?, ?, ?, ?, 'draft', NOW(), NOW())";
            $stmt = $pdo->prepare($insertSql);
            $stmt->execute([$userId, $title, $content, $categoryId]);
            
            $newPostId = $pdo->lastInsertId();
            echo json_encode(['success' => true, 'message' => '草稿保存成功', 'post_id' => $newPostId]);
        }
        
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => '无效的操作']);
    }
}
?>
