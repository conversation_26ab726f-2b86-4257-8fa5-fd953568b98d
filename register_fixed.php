<?php
/**
 * 用户注册API - 修复版本
 * 解决lastInsertId()获取失败的问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 生产环境关闭显示错误

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只允许POST请求'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 包含数据库配置
    require_once 'config/database.php';
    
    // 获取数据库连接
    $db = DatabaseConfig::getInstance();
    
    // 获取表单数据
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $nickname = trim($_POST['nickname'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirmPassword'] ?? '';
    
    // 数据验证
    $errors = [];
    
    // 验证用户名
    if (empty($username)) {
        $errors['username'] = '请输入用户名';
    } elseif (strlen($username) < 3) {
        $errors['username'] = '用户名至少需要3个字符';
    } elseif (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        $errors['username'] = '用户名只能包含字母、数字、下划线和中文';
    } else {
        // 检查用户名是否已存在
        $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existingUser) {
            $errors['username'] = '用户名已存在';
        }
    }
    
    // 验证邮箱
    if (empty($email)) {
        $errors['email'] = '请输入邮箱地址';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = '请输入有效的邮箱地址';
    } else {
        // 检查邮箱是否已存在
        $existingEmail = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingEmail) {
            $errors['email'] = '邮箱地址已被注册';
        }
    }
    
    // 验证昵称
    if (empty($nickname)) {
        $errors['nickname'] = '请输入昵称';
    } elseif (strlen($nickname) < 2) {
        $errors['nickname'] = '昵称至少需要2个字符';
    }
    
    // 验证密码
    if (empty($password)) {
        $errors['password'] = '请输入密码';
    } elseif (strlen($password) < 6) {
        $errors['password'] = '密码至少需要6个字符';
    }
    
    // 验证确认密码
    if (empty($confirmPassword)) {
        $errors['confirmPassword'] = '请确认密码';
    } elseif ($password !== $confirmPassword) {
        $errors['confirmPassword'] = '两次输入的密码不一致';
    }
    
    // 如果有验证错误，返回错误信息
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => '数据验证失败',
            'errors' => $errors
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    // 处理头像上传
    $avatarUrl = 'assets/images/default-avatar.png'; // 默认头像
    
    // 开始数据库事务
    $db->getConnection()->beginTransaction();
    
    try {
        // 获取普通用户角色ID
        $userRole = $db->fetchOne("SELECT id FROM user_roles WHERE role_code = 'user'");
        $roleId = $userRole ? $userRole['id'] : 3; // 默认为3（普通用户）
        
        // 创建用户记录
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";

        $stmt = $db->getConnection()->prepare($sql);
        $result = $stmt->execute([
            $username,
            $email,
            $passwordHash,
            $nickname, // 使用昵称作为全名
            $roleId
        ]);

        if (!$result) {
            throw new Exception('创建用户失败');
        }

        // 获取插入的ID - 修复版本
        $userId = $db->getConnection()->lastInsertId();

        // 调试信息
        error_log("用户ID (lastInsertId): " . $userId);

        // 如果lastInsertId()失败，尝试查询最新插入的记录
        if (!$userId || $userId == 0) {
            error_log("lastInsertId失败，尝试查询最新记录");
            $latestUser = $db->fetchOne("SELECT id FROM users WHERE username = ? ORDER BY id DESC LIMIT 1", [$username]);
            if ($latestUser) {
                $userId = $latestUser['id'];
                error_log("通过查询获取用户ID: " . $userId);
            }
        }

        if (!$userId || $userId == 0) {
            throw new Exception('获取用户ID失败');
        }

        // 创建用户个人资料记录
        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";

        $profileStmt = $db->getConnection()->prepare($profileSql);
        $profileResult = $profileStmt->execute([
            $userId,
            $nickname,
            $avatarUrl
        ]);
        
        if (!$profileResult) {
            throw new Exception('创建用户资料失败');
        }
        
        // 提交事务
        $db->getConnection()->commit();
        
        // 记录注册日志
        error_log("新用户注册成功: ID={$userId}, 用户名={$username}, 邮箱={$email}");
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '注册成功！请登录您的账户',
            'user_id' => $userId
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        // 回滚事务
        $db->getConnection()->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    // 记录错误日志
    error_log("注册错误: " . $e->getMessage() . " 在文件 " . $e->getFile() . " 第 " . $e->getLine() . " 行");
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '注册过程中发生错误，请稍后重试',
        'debug_info' => [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
