<?php
/**
 * 比特熊组织领导者数据管理
 * 这个文件用于管理主页展示的领导者信息
 */

// 领导者数据数组
$leaders = [
    [
        'id' => 1,
        'name' => '张三',
        'title' => '技术总监',
        'avatar' => 'image/default-avatar.svg',
        'bio' => '拥有10年以上技术管理经验，专注于团队建设和技术创新。',
        'display_on_homepage' => true,
        'sort_order' => 1
    ],
    [
        'id' => 2,
        'name' => '李四',
        'title' => '产品经理',
        'avatar' => 'image/default-avatar.svg',
        'bio' => '资深产品经理，擅长用户体验设计和产品策略规划。',
        'display_on_homepage' => true,
        'sort_order' => 2
    ],
    [
        'id' => 3,
        'name' => '王五',
        'title' => '首席架构师',
        'avatar' => 'image/default-avatar.svg',
        'bio' => '系统架构专家，在大型分布式系统设计方面有丰富经验。',
        'display_on_homepage' => true,
        'sort_order' => 3
    ],
    [
        'id' => 4,
        'name' => '赵六',
        'title' => '运营总监',
        'avatar' => 'image/default-avatar.svg',
        'bio' => '负责平台运营和用户增长，具有丰富的社区运营经验。',
        'display_on_homepage' => true,
        'sort_order' => 4
    ],
    [
        'id' => 5,
        'name' => '钱七',
        'title' => '软件工程师',
        'avatar' => 'image/default-avatar.svg',
        'bio' => '全栈开发工程师，精通多种编程语言和开发框架。',
        'display_on_homepage' => true,
        'sort_order' => 5
    ],
    [
        'id' => 6,
        'name' => '孙八',
        'title' => 'Java开发者',
        'avatar' => 'image/default-avatar.svg',
        'bio' => 'Java技术专家，专注于后端服务开发和性能优化。',
        'display_on_homepage' => true,
        'sort_order' => 6
    ]
];

/**
 * 获取要在主页显示的领导者
 * @param int $limit 限制显示数量，默认为6
 * @return array 领导者数组
 */
function getHomepageLeaders($limit = 6) {
    global $leaders;
    
    // 筛选要在主页显示的领导者
    $homepageLeaders = array_filter($leaders, function($leader) {
        return $leader['display_on_homepage'] === true;
    });
    
    // 按排序顺序排序
    usort($homepageLeaders, function($a, $b) {
        return $a['sort_order'] - $b['sort_order'];
    });
    
    // 限制数量
    return array_slice($homepageLeaders, 0, $limit);
}

/**
 * 获取所有领导者
 * @return array 所有领导者数组
 */
function getAllLeaders() {
    global $leaders;
    return $leaders;
}

/**
 * 根据ID获取领导者信息
 * @param int $id 领导者ID
 * @return array|null 领导者信息或null
 */
function getLeaderById($id) {
    global $leaders;
    
    foreach ($leaders as $leader) {
        if ($leader['id'] == $id) {
            return $leader;
        }
    }
    
    return null;
}

/**
 * 添加新领导者
 * @param array $leaderData 领导者数据
 * @return bool 是否添加成功
 */
function addLeader($leaderData) {
    global $leaders;
    
    // 生成新的ID
    $maxId = 0;
    foreach ($leaders as $leader) {
        if ($leader['id'] > $maxId) {
            $maxId = $leader['id'];
        }
    }
    
    $leaderData['id'] = $maxId + 1;
    $leaders[] = $leaderData;
    
    // 在实际应用中，这里应该保存到数据库
    return true;
}

/**
 * 更新领导者信息
 * @param int $id 领导者ID
 * @param array $leaderData 更新的数据
 * @return bool 是否更新成功
 */
function updateLeader($id, $leaderData) {
    global $leaders;
    
    for ($i = 0; $i < count($leaders); $i++) {
        if ($leaders[$i]['id'] == $id) {
            $leaders[$i] = array_merge($leaders[$i], $leaderData);
            // 在实际应用中，这里应该保存到数据库
            return true;
        }
    }
    
    return false;
}

/**
 * 删除领导者
 * @param int $id 领导者ID
 * @return bool 是否删除成功
 */
function deleteLeader($id) {
    global $leaders;
    
    for ($i = 0; $i < count($leaders); $i++) {
        if ($leaders[$i]['id'] == $id) {
            array_splice($leaders, $i, 1);
            // 在实际应用中，这里应该从数据库删除
            return true;
        }
    }
    
    return false;
}

/**
 * 生成领导者HTML
 * @param array $leaders 领导者数组
 * @return string HTML字符串
 */
function generateLeadersHTML($leaders) {
    $html = '';
    
    foreach ($leaders as $leader) {
        $html .= '<div class="leader-card">';
        $html .= '<img src="' . htmlspecialchars($leader['avatar']) . '" alt="' . htmlspecialchars($leader['name']) . '" class="leader-avatar">';
        $html .= '<h4 class="leader-name">' . htmlspecialchars($leader['name']) . '</h4>';
        $html .= '<p class="leader-title">' . htmlspecialchars($leader['title']) . '</p>';
        $html .= '</div>';
    }
    
    return $html;
}

// 如果是AJAX请求，返回JSON数据
if (isset($_GET['ajax']) && $_GET['ajax'] === 'true') {
    header('Content-Type: application/json');
    
    $action = $_GET['action'] ?? 'get_homepage_leaders';
    
    switch ($action) {
        case 'get_homepage_leaders':
            $limit = intval($_GET['limit'] ?? 6);
            echo json_encode([
                'success' => true,
                'data' => getHomepageLeaders($limit)
            ]);
            break;
            
        case 'get_all_leaders':
            echo json_encode([
                'success' => true,
                'data' => getAllLeaders()
            ]);
            break;
            
        case 'get_leader':
            $id = intval($_GET['id'] ?? 0);
            $leader = getLeaderById($id);
            if ($leader) {
                echo json_encode([
                    'success' => true,
                    'data' => $leader
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'error' => '领导者不存在'
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => '无效的操作'
            ]);
    }
    exit;
}
?>
