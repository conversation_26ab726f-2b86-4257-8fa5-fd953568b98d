-- 比特熊智慧系统数据库初始化脚本（修正版）
-- 注意：请在宝塔面板中先选择 bitbear_website 数据库，然后导入此文件

-- 用户角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_code VARCHAR(20) NOT NULL UNIQUE,
    description TEXT,
    permissions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    avatar VARCHAR(255),
    role_id INT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    login_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户资料表
CREATE TABLE IF NOT EXISTS user_profiles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL UNIQUE,
    nickname VARCHAR(100),
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(200),
    avatar_url VARCHAR(500),
    cover_image VARCHAR(500),
    social_links TEXT,
    post_count INT DEFAULT 0,
    follower_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 用户活动日志表
CREATE TABLE IF NOT EXISTS user_activities (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    activity_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 插入默认角色数据
INSERT IGNORE INTO user_roles (role_name, role_code, description, permissions) VALUES
('超级管理员', 'super_admin', '拥有系统所有权限', 'all'),
('管理员', 'admin', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
('普通用户', 'user', '基础用户权限', 'dashboard,personal_settings');

-- 插入默认管理员用户（密码：admin123）
INSERT IGNORE INTO users (username, email, password_hash, full_name, role_id, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 1, 'active');

-- 为管理员创建用户资料
INSERT IGNORE INTO user_profiles (user_id, nickname, bio) VALUES
(1, '系统管理员', '比特熊智慧系统管理员账户');

-- 插入默认系统设置
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', '比特熊智慧系统', 'string', '网站名称'),
('site_description', '智慧学习，熊力无穷', 'string', '网站描述'),
('site_keywords', '比特熊,智慧系统,学习平台', 'string', '网站关键词'),
('maintenance_mode', '0', 'boolean', '维护模式'),
('user_registration', '1', 'boolean', '允许用户注册'),
('email_verification', '0', 'boolean', '邮箱验证'),
('max_upload_size', '10485760', 'integer', '最大上传文件大小（字节）'),
('allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx', 'string', '允许上传的文件类型');
