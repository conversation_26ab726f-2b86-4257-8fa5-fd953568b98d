<?php
require_once 'config/database.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin-login.php');
    exit;
}

$currentUser = $_SESSION['admin_user'] ?? 'Admin';

try {
    $db = DatabaseConfig::getInstance();
    
    // 获取基础统计数据
    $stats = [];
    
    // 用户统计
    $userStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users
        FROM users
    ");
    $stats['users'] = $userStats;
    
    // 帖子统计
    $postStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_posts,
            COUNT(CASE WHEN status = 'published' THEN 1 END) as published_posts,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_posts,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_posts_week,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_posts_month,
            AVG(view_count) as avg_views
        FROM posts
    ");
    $stats['posts'] = $postStats;
    
    // 评论统计
    $commentStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_comments,
            COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_comments,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_comments,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_comments,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_comments_week,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_comments_month
        FROM post_comments
    ");
    $stats['comments'] = $commentStats;
    
    // 举报统计
    $reportStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_reports,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_reports,
            COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_reports,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_reports_week
        FROM comment_reports
    ");
    $stats['reports'] = $reportStats;
    
    // 点赞统计
    $likeStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_likes,
            COUNT(CASE WHEN type = 'like' THEN 1 END) as likes,
            COUNT(CASE WHEN type = 'dislike' THEN 1 END) as dislikes
        FROM comment_likes
    ");
    $stats['likes'] = $likeStats;
    
    // 获取最活跃用户
    $activeUsers = $db->fetchAll("
        SELECT u.username, u.nickname, u.avatar_url,
               COUNT(DISTINCT p.id) as post_count,
               COUNT(DISTINCT c.id) as comment_count,
               (COUNT(DISTINCT p.id) + COUNT(DISTINCT c.id)) as total_activity
        FROM users u
        LEFT JOIN posts p ON u.id = p.user_id AND p.status = 'published'
        LEFT JOIN post_comments c ON u.id = c.user_id AND c.status = 'approved'
        GROUP BY u.id
        HAVING total_activity > 0
        ORDER BY total_activity DESC
        LIMIT 10
    ");
    
    // 获取热门帖子
    $popularPosts = $db->fetchAll("
        SELECT p.title, p.view_count, p.created_at, u.username, u.nickname,
               COUNT(c.id) as comment_count
        FROM posts p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN post_comments c ON p.id = c.post_id AND c.status = 'approved'
        WHERE p.status = 'published'
        GROUP BY p.id
        ORDER BY p.view_count DESC
        LIMIT 10
    ");
    
    // 获取最近举报
    $recentReports = $db->fetchAll("
        SELECT cr.*, c.content as comment_content, u.username as reporter_username,
               cu.username as comment_author, p.title as post_title
        FROM comment_reports cr
        LEFT JOIN post_comments c ON cr.comment_id = c.id
        LEFT JOIN users u ON cr.reporter_user_id = u.id
        LEFT JOIN users cu ON c.user_id = cu.id
        LEFT JOIN posts p ON c.post_id = p.id
        WHERE cr.status = 'pending'
        ORDER BY cr.created_at DESC
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $error = '获取统计数据失败：' . $e->getMessage();
    $stats = [
        'users' => ['total_users' => 0, 'new_users_week' => 0, 'new_users_month' => 0, 'active_users' => 0],
        'posts' => ['total_posts' => 0, 'published_posts' => 0, 'draft_posts' => 0, 'new_posts_week' => 0, 'new_posts_month' => 0, 'avg_views' => 0],
        'comments' => ['total_comments' => 0, 'approved_comments' => 0, 'pending_comments' => 0, 'rejected_comments' => 0, 'new_comments_week' => 0, 'new_comments_month' => 0],
        'reports' => ['total_reports' => 0, 'pending_reports' => 0, 'resolved_reports' => 0, 'new_reports_week' => 0],
        'likes' => ['total_likes' => 0, 'likes' => 0, 'dislikes' => 0]
    ];
    $activeUsers = [];
    $popularPosts = [];
    $recentReports = [];
}

function formatNumber($num) {
    if ($num >= 1000000) {
        return round($num / 1000000, 1) . 'M';
    } elseif ($num >= 1000) {
        return round($num / 1000, 1) . 'K';
    }
    return number_format($num);
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区统计 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #3b82f6;
        }
        
        .stat-card.users { border-left-color: #10b981; }
        .stat-card.posts { border-left-color: #f59e0b; }
        .stat-card.comments { border-left-color: #8b5cf6; }
        .stat-card.reports { border-left-color: #ef4444; }
        .stat-card.likes { border-left-color: #ec4899; }
        
        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }
        
        .stat-card.users .stat-icon { background: #10b981; }
        .stat-card.posts .stat-icon { background: #f59e0b; }
        .stat-card.comments .stat-icon { background: #8b5cf6; }
        .stat-card.reports .stat-icon { background: #ef4444; }
        .stat-card.likes .stat-icon { background: #ec4899; }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .stat-details {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #6b7280;
        }
        
        .stat-detail {
            display: flex;
            flex-direction: column;
        }
        
        .stat-detail-label {
            font-size: 12px;
            margin-bottom: 2px;
        }
        
        .stat-detail-value {
            font-weight: 600;
            color: #374151;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .content-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .user-item, .post-item, .report-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .user-item:last-child, .post-item:last-child, .report-item:last-child {
            border-bottom: none;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .user-info, .post-info, .report-info {
            flex: 1;
        }
        
        .user-name, .post-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        
        .user-stats, .post-stats, .report-meta {
            font-size: 14px;
            color: #6b7280;
        }
        
        .activity-score {
            background: #eff6ff;
            color: #3b82f6;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .report-type {
            background: #fef2f2;
            color: #dc2626;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="admin-header">
                <h1>社区统计</h1>
                <div class="admin-breadcrumb">
                    <a href="admin">管理后台</a>
                    <span>/</span>
                    <span>社区统计</span>
                </div>
            </div>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card users">
                    <div class="stat-header">
                        <span class="stat-title">用户统计</span>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo formatNumber($stats['users']['total_users']); ?></div>
                    <div class="stat-details">
                        <div class="stat-detail">
                            <span class="stat-detail-label">本周新增</span>
                            <span class="stat-detail-value"><?php echo $stats['users']['new_users_week']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">本月新增</span>
                            <span class="stat-detail-value"><?php echo $stats['users']['new_users_month']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">活跃用户</span>
                            <span class="stat-detail-value"><?php echo $stats['users']['active_users']; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card posts">
                    <div class="stat-header">
                        <span class="stat-title">帖子统计</span>
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo formatNumber($stats['posts']['total_posts']); ?></div>
                    <div class="stat-details">
                        <div class="stat-detail">
                            <span class="stat-detail-label">已发布</span>
                            <span class="stat-detail-value"><?php echo $stats['posts']['published_posts']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">草稿</span>
                            <span class="stat-detail-value"><?php echo $stats['posts']['draft_posts']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">平均浏览</span>
                            <span class="stat-detail-value"><?php echo number_format($stats['posts']['avg_views'] ?? 0, 0); ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card comments">
                    <div class="stat-header">
                        <span class="stat-title">评论统计</span>
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo formatNumber($stats['comments']['total_comments']); ?></div>
                    <div class="stat-details">
                        <div class="stat-detail">
                            <span class="stat-detail-label">已批准</span>
                            <span class="stat-detail-value"><?php echo $stats['comments']['approved_comments']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">待审核</span>
                            <span class="stat-detail-value"><?php echo $stats['comments']['pending_comments']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">已拒绝</span>
                            <span class="stat-detail-value"><?php echo $stats['comments']['rejected_comments']; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card reports">
                    <div class="stat-header">
                        <span class="stat-title">举报统计</span>
                        <div class="stat-icon">
                            <i class="fas fa-flag"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo formatNumber($stats['reports']['total_reports']); ?></div>
                    <div class="stat-details">
                        <div class="stat-detail">
                            <span class="stat-detail-label">待处理</span>
                            <span class="stat-detail-value"><?php echo $stats['reports']['pending_reports']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">已处理</span>
                            <span class="stat-detail-value"><?php echo $stats['reports']['resolved_reports']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">本周新增</span>
                            <span class="stat-detail-value"><?php echo $stats['reports']['new_reports_week']; ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card likes">
                    <div class="stat-header">
                        <span class="stat-title">互动统计</span>
                        <div class="stat-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo formatNumber($stats['likes']['total_likes']); ?></div>
                    <div class="stat-details">
                        <div class="stat-detail">
                            <span class="stat-detail-label">点赞</span>
                            <span class="stat-detail-value"><?php echo $stats['likes']['likes']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">踩</span>
                            <span class="stat-detail-value"><?php echo $stats['likes']['dislikes']; ?></span>
                        </div>
                        <div class="stat-detail">
                            <span class="stat-detail-label">比例</span>
                            <span class="stat-detail-value">
                                <?php 
                                $total = $stats['likes']['likes'] + $stats['likes']['dislikes'];
                                echo $total > 0 ? round(($stats['likes']['likes'] / $total) * 100, 1) . '%' : '0%';
                                ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 详细内容 -->
            <div class="content-grid">
                <!-- 最活跃用户 -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-star"></i>
                        最活跃用户
                    </h2>
                    <?php if (empty($activeUsers)): ?>
                        <p class="text-gray-500">暂无数据</p>
                    <?php else: ?>
                        <?php foreach ($activeUsers as $user): ?>
                            <div class="user-item">
                                <img src="<?php echo $user['avatar_url'] ?: 'assets/images/default-avatar.png'; ?>" 
                                     alt="头像" class="user-avatar">
                                <div class="user-info">
                                    <div class="user-name"><?php echo htmlspecialchars($user['nickname'] ?: $user['username']); ?></div>
                                    <div class="user-stats">
                                        <?php echo $user['post_count']; ?> 帖子 · <?php echo $user['comment_count']; ?> 评论
                                    </div>
                                </div>
                                <div class="activity-score"><?php echo $user['total_activity']; ?></div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- 热门帖子 -->
                <div class="content-section">
                    <h2 class="section-title">
                        <i class="fas fa-fire"></i>
                        热门帖子
                    </h2>
                    <?php if (empty($popularPosts)): ?>
                        <p class="text-gray-500">暂无数据</p>
                    <?php else: ?>
                        <?php foreach ($popularPosts as $post): ?>
                            <div class="post-item">
                                <div class="post-info">
                                    <div class="post-title"><?php echo htmlspecialchars($post['title']); ?></div>
                                    <div class="post-stats">
                                        <?php echo formatNumber($post['view_count']); ?> 浏览 · 
                                        <?php echo $post['comment_count']; ?> 评论 · 
                                        <?php echo htmlspecialchars($post['nickname'] ?: $post['username']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- 最近举报 -->
            <?php if (!empty($recentReports)): ?>
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    最近举报
                </h2>
                <?php foreach ($recentReports as $report): ?>
                    <div class="report-item">
                        <div class="report-info">
                            <div class="user-name">
                                <?php echo htmlspecialchars($report['reporter_username']); ?> 举报了 
                                <?php echo htmlspecialchars($report['comment_author']); ?> 的评论
                            </div>
                            <div class="report-meta">
                                <span class="report-type"><?php echo htmlspecialchars($report['report_type']); ?></span>
                                在帖子《<?php echo htmlspecialchars($report['post_title']); ?>》
                                · <?php echo date('m-d H:i', strtotime($report['created_at'])); ?>
                            </div>
                        </div>
                        <a href="admin-comments.php?search=<?php echo urlencode($report['comment_author']); ?>" 
                           class="btn btn-outline btn-small">查看</a>
                    </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </main>
    </div>
</body>
</html>
