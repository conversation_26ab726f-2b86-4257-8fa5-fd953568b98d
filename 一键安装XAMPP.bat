@echo off
chcp 65001 >nul
echo ========================================
echo    比特熊智慧系统 - XAMPP一键安装
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 需要管理员权限来安装XAMPP
    echo 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

cd /d "%~dp0"

echo 正在检查是否已安装XAMPP...
if exist "C:\xampp\xampp-control.exe" (
    echo [发现] XAMPP已安装在 C:\xampp
    echo.
    echo 正在启动XAMPP控制面板...
    start "" "C:\xampp\xampp-control.exe"
    echo.
    echo 请在XAMPP控制面板中：
    echo 1. 点击Apache的"Start"按钮
    echo 2. 点击MySQL的"Start"按钮
    echo 3. 等待服务启动完成（显示绿色）
    echo.
    echo 服务启动后，按任意键继续...
    pause >nul
    
    echo 正在启动后台服务器...
    start "" "http://localhost/比特熊智慧系统(v0.0.1)/admin/init-database.php"
    
    echo.
    echo 如果上面的链接无法访问，请：
    echo 1. 将项目文件夹复制到 C:\xampp\htdocs\
    echo 2. 访问 http://localhost/比特熊智慧系统(v0.0.1)/admin/init-database.php
    echo.
    pause
    exit /b 0
)

echo 正在下载XAMPP安装程序...
echo 下载地址: https://sourceforge.net/projects/xampp/files/XAMPP%20Windows/8.2.12/xampp-windows-x64-8.2.12-0-VS16-installer.exe

:: 创建临时下载目录
if not exist "%TEMP%\xampp_installer" mkdir "%TEMP%\xampp_installer"

echo.
echo 由于网络限制，将为您打开XAMPP官方下载页面
echo 请手动下载并安装XAMPP
echo.
start "" "https://www.apachefriends.org/zh_cn/download.html"

echo.
echo 下载完成后的安装步骤：
echo ========================================
echo 1. 运行下载的XAMPP安装程序
echo 2. 选择安装组件（保持默认即可）
echo 3. 安装路径选择 C:\xampp（推荐）
echo 4. 完成安装后启动XAMPP控制面板
echo 5. 启动Apache和MySQL服务
echo 6. 重新运行此脚本或直接访问后台
echo ========================================
echo.

echo 安装完成后，您可以通过以下方式访问后台：
echo.
echo 方法1 - 复制项目到htdocs：
echo 1. 将整个项目文件夹复制到 C:\xampp\htdocs\
echo 2. 访问 http://localhost/比特熊智慧系统(v0.0.1)/admin/init-database.php
echo.
echo 方法2 - 使用PHP内置服务器：
echo 1. 在项目目录运行: C:\xampp\php\php.exe -S localhost:8000
echo 2. 访问 http://localhost:8000/admin/init-database.php
echo.

pause
