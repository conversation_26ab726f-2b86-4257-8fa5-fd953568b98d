<?php
/**
 * 修复注册API的脚本
 */

// 读取原文件
$originalFile = '/www/wwwroot/www.bitbear.top/api/register.php';
$content = file_get_contents($originalFile);

// 要替换的旧代码
$oldCode = '        // 获取插入的ID - 修复版本
        $userId = $db->getConnection()->lastInsertId();

        // 调试信息
        error_log("用户ID (lastInsertId): " . $userId);

        // 如果lastInsertId()失败，尝试查询最新插入的记录
        if (!$userId || $userId == 0) {
            error_log("lastInsertId失败，尝试查询最新记录");
            $latestUser = $db->fetchOne("SELECT id FROM users WHERE username = ? ORDER BY id DESC LIMIT 1", [$username]);
            if ($latestUser) {
                $userId = $latestUser[\'id\'];
                error_log("通过查询获取用户ID: " . $userId);
            }
        }

        if (!$userId || $userId == 0) {
            throw new Exception(\'获取用户ID失败\');
        }';

// 新代码
$newCode = '        // 获取插入的ID - 修复版本
        $userId = $db->getConnection()->lastInsertId();

        // 调试信息
        error_log("用户ID (lastInsertId): " . $userId);

        // 如果lastInsertId()失败，尝试查询最新插入的记录
        if (!$userId || $userId == 0) {
            error_log("lastInsertId失败，尝试查询最新记录");
            $latestUser = $db->fetchOne("SELECT id FROM users WHERE username = ? ORDER BY id DESC LIMIT 1", [$username]);
            if ($latestUser) {
                $userId = $latestUser[\'id\'];
                error_log("通过查询获取用户ID: " . $userId);
            } else {
                // 如果还是找不到，尝试通过邮箱查询
                $latestUser = $db->fetchOne("SELECT id FROM users WHERE email = ? ORDER BY id DESC LIMIT 1", [$email]);
                if ($latestUser) {
                    $userId = $latestUser[\'id\'];
                    error_log("通过邮箱查询获取用户ID: " . $userId);
                }
            }
        }

        if (!$userId || $userId == 0) {
            // 最后尝试：查询刚刚插入的记录（通过时间戳）
            $recentUser = $db->fetchOne("SELECT id FROM users WHERE username = ? AND email = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MINUTE) ORDER BY id DESC LIMIT 1", [$username, $email]);
            if ($recentUser) {
                $userId = $recentUser[\'id\'];
                error_log("通过时间戳查询获取用户ID: " . $userId);
            } else {
                throw new Exception(\'获取用户ID失败\');
            }
        }';

// 执行替换
$newContent = str_replace($oldCode, $newCode, $content);

// 写入文件
if (file_put_contents($originalFile, $newContent)) {
    echo "修复成功！\n";
} else {
    echo "修复失败！\n";
}
?>
