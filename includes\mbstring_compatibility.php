<?php
/**
 * mbstring兼容函数
 * 当mbstring扩展不可用时的备用方案
 * 
 * 这个文件提供了mbstring扩展中常用函数的兼容实现
 * 主要用于处理UTF-8编码的多字节字符串（如中文）
 */

if (!function_exists('mb_substr')) {
    /**
     * 多字节字符串截取函数
     * 兼容mb_substr()函数
     * 
     * @param string $string 要截取的字符串
     * @param int $start 开始位置
     * @param int|null $length 截取长度
     * @param string $encoding 字符编码
     * @return string 截取后的字符串
     */
    function mb_substr($string, $start, $length = null, $encoding = 'UTF-8') {
        // 如果不是UTF-8编码，先转换
        if ($encoding !== 'UTF-8') {
            $string = iconv($encoding, 'UTF-8//IGNORE', $string);
        }
        
        // 处理空字符串
        if (empty($string)) {
            return '';
        }
        
        // 使用正则表达式分割UTF-8字符串
        $chars = preg_split('//u', $string, -1, PREG_SPLIT_NO_EMPTY);
        
        if ($chars === false) {
            // 如果正则分割失败，使用普通substr作为备用
            error_log("UTF-8字符串分割失败，使用普通substr: " . $string);
            return ($length === null) ? substr($string, $start) : substr($string, $start, $length);
        }
        
        $totalChars = count($chars);
        
        // 处理负数开始位置
        if ($start < 0) {
            $start = max(0, $totalChars + $start);
        }
        
        // 开始位置超出字符串长度
        if ($start >= $totalChars) {
            return '';
        }
        
        // 如果没有指定长度，返回从开始位置到结尾的所有字符
        if ($length === null) {
            $result = array_slice($chars, $start);
            return implode('', $result);
        }
        
        // 处理负数长度
        if ($length < 0) {
            $length = max(0, $totalChars - $start + $length);
        }
        
        // 截取指定长度的字符
        $result = array_slice($chars, $start, $length);
        return implode('', $result);
    }
}

if (!function_exists('mb_strlen')) {
    /**
     * 多字节字符串长度函数
     * 兼容mb_strlen()函数
     * 
     * @param string $string 要计算长度的字符串
     * @param string $encoding 字符编码
     * @return int 字符串长度
     */
    function mb_strlen($string, $encoding = 'UTF-8') {
        // 如果不是UTF-8编码，先转换
        if ($encoding !== 'UTF-8') {
            $string = iconv($encoding, 'UTF-8//IGNORE', $string);
        }
        
        // 处理空字符串
        if (empty($string)) {
            return 0;
        }
        
        // 使用正则表达式计算UTF-8字符数量
        $chars = preg_split('//u', $string, -1, PREG_SPLIT_NO_EMPTY);
        
        if ($chars === false) {
            // 如果正则分割失败，使用普通strlen作为备用
            error_log("UTF-8字符串长度计算失败，使用普通strlen: " . $string);
            return strlen($string);
        }
        
        return count($chars);
    }
}

if (!function_exists('mb_strpos')) {
    /**
     * 多字节字符串查找函数
     * 兼容mb_strpos()函数
     * 
     * @param string $haystack 被搜索的字符串
     * @param string $needle 搜索的字符串
     * @param int $offset 开始搜索的位置
     * @param string $encoding 字符编码
     * @return int|false 找到返回位置，未找到返回false
     */
    function mb_strpos($haystack, $needle, $offset = 0, $encoding = 'UTF-8') {
        // 如果不是UTF-8编码，先转换
        if ($encoding !== 'UTF-8') {
            $haystack = iconv($encoding, 'UTF-8//IGNORE', $haystack);
            $needle = iconv($encoding, 'UTF-8//IGNORE', $needle);
        }
        
        // 使用正则表达式分割字符串
        $chars = preg_split('//u', $haystack, -1, PREG_SPLIT_NO_EMPTY);
        $needleChars = preg_split('//u', $needle, -1, PREG_SPLIT_NO_EMPTY);
        
        if ($chars === false || $needleChars === false) {
            // 如果正则分割失败，使用普通strpos作为备用
            return strpos($haystack, $needle, $offset);
        }
        
        $haystackLen = count($chars);
        $needleLen = count($needleChars);
        
        // 从指定位置开始搜索
        for ($i = $offset; $i <= $haystackLen - $needleLen; $i++) {
            $match = true;
            for ($j = 0; $j < $needleLen; $j++) {
                if ($chars[$i + $j] !== $needleChars[$j]) {
                    $match = false;
                    break;
                }
            }
            if ($match) {
                return $i;
            }
        }
        
        return false;
    }
}

if (!function_exists('mb_convert_encoding')) {
    /**
     * 字符编码转换函数
     * 兼容mb_convert_encoding()函数
     * 
     * @param string $string 要转换的字符串
     * @param string $to_encoding 目标编码
     * @param string $from_encoding 源编码
     * @return string 转换后的字符串
     */
    function mb_convert_encoding($string, $to_encoding, $from_encoding = null) {
        // 如果iconv扩展可用，使用iconv进行转换
        if (function_exists('iconv')) {
            if ($from_encoding === null) {
                $from_encoding = 'UTF-8';
            }
            
            $result = iconv($from_encoding, $to_encoding . '//IGNORE', $string);
            return $result !== false ? $result : $string;
        }
        
        // 如果iconv不可用，只能返回原字符串
        error_log("字符编码转换失败：iconv扩展不可用");
        return $string;
    }
}

if (!function_exists('mb_detect_encoding')) {
    /**
     * 字符编码检测函数
     * 兼容mb_detect_encoding()函数
     * 
     * @param string $string 要检测的字符串
     * @param array|string $encoding_list 编码列表
     * @param bool $strict 是否严格模式
     * @return string|false 检测到的编码或false
     */
    function mb_detect_encoding($string, $encoding_list = null, $strict = false) {
        // 简单的编码检测逻辑
        if (empty($string)) {
            return 'UTF-8';
        }
        
        // 检查是否为有效的UTF-8
        if (mb_check_encoding($string, 'UTF-8')) {
            return 'UTF-8';
        }
        
        // 检查是否为GBK/GB2312
        if (function_exists('iconv')) {
            $converted = iconv('GBK', 'UTF-8//IGNORE', $string);
            if ($converted !== false && !empty($converted)) {
                return 'GBK';
            }
        }
        
        // 默认返回UTF-8
        return 'UTF-8';
    }
}

if (!function_exists('mb_check_encoding')) {
    /**
     * 检查字符串编码是否有效
     * 兼容mb_check_encoding()函数
     * 
     * @param string $string 要检查的字符串
     * @param string $encoding 编码类型
     * @return bool 编码是否有效
     */
    function mb_check_encoding($string, $encoding = 'UTF-8') {
        if ($encoding === 'UTF-8') {
            // 检查是否为有效的UTF-8编码
            return preg_match('//u', $string) === 1;
        }
        
        // 对于其他编码，简单返回true
        return true;
    }
}

// 记录兼容函数加载日志
error_log("mbstring兼容函数已加载");
?>
