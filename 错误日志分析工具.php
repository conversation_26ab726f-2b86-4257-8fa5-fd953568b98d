<?php
/**
 * 错误日志分析工具
 * 检查PHP错误日志、服务器日志，分析具体的错误信息
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>错误日志分析工具</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
    .section { margin: 20px 0; padding: 20px; border-radius: 8px; }
    .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .log-entry { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; margin: 5px 0; border-radius: 5px; font-family: monospace; font-size: 12px; }
    .log-error { border-left: 4px solid #dc3545; }
    .log-warning { border-left: 4px solid #ffc107; }
    .log-info { border-left: 4px solid #17a2b8; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    .btn:hover { background: #0056b3; }
    table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .expandable { cursor: pointer; background: #e9ecef; padding: 5px; border-radius: 3px; margin: 2px 0; }
    .expandable:hover { background: #dee2e6; }
    .expanded { display: block; }
    .collapsed { display: none; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🔍 错误日志分析工具</h1>";
echo "<p>检查PHP错误日志、服务器日志，分析具体的错误信息</p>";
echo "</div>";

// 1. PHP配置信息
echo "<div class='section info'>";
echo "<h3>⚙️ PHP错误配置信息</h3>";
echo "<table>";
echo "<tr><th>配置项</th><th>值</th><th>说明</th></tr>";

$errorConfigs = [
    'error_reporting' => [ini_get('error_reporting'), '错误报告级别'],
    'display_errors' => [ini_get('display_errors') ? '开启' : '关闭', '是否显示错误'],
    'log_errors' => [ini_get('log_errors') ? '开启' : '关闭', '是否记录错误日志'],
    'error_log' => [ini_get('error_log') ?: '默认位置', '错误日志文件位置'],
    'max_execution_time' => [ini_get('max_execution_time'), '最大执行时间(秒)'],
    'memory_limit' => [ini_get('memory_limit'), '内存限制'],
    'upload_max_filesize' => [ini_get('upload_max_filesize'), '上传文件大小限制'],
    'post_max_size' => [ini_get('post_max_size'), 'POST数据大小限制']
];

foreach ($errorConfigs as $key => $info) {
    echo "<tr><td>{$key}</td><td>{$info[0]}</td><td>{$info[1]}</td></tr>";
}
echo "</table>";
echo "</div>";

// 2. 检查最近的PHP错误
echo "<div class='section info'>";
echo "<h3>🚨 最近的PHP错误</h3>";

$lastError = error_get_last();
if ($lastError) {
    echo "<div class='log-entry log-error'>";
    echo "<strong>最后一个PHP错误:</strong><br>";
    echo "类型: " . $lastError['type'] . "<br>";
    echo "消息: " . htmlspecialchars($lastError['message']) . "<br>";
    echo "文件: " . $lastError['file'] . "<br>";
    echo "行号: " . $lastError['line'] . "<br>";
    echo "</div>";
} else {
    echo "<div class='success'>✅ 没有检测到最近的PHP错误</div>";
}
echo "</div>";

// 3. 检查错误日志文件
echo "<div class='section info'>";
echo "<h3>📄 错误日志文件分析</h3>";

$errorLogPath = ini_get('error_log');
$possibleLogPaths = [
    $errorLogPath,
    '/var/log/php_errors.log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    __DIR__ . '/error.log',
    __DIR__ . '/php_errors.log'
];

$foundLogs = [];

foreach ($possibleLogPaths as $logPath) {
    if ($logPath && file_exists($logPath) && is_readable($logPath)) {
        $foundLogs[] = $logPath;
    }
}

if (!empty($foundLogs)) {
    echo "<p>找到以下错误日志文件:</p>";
    echo "<ul>";
    foreach ($foundLogs as $logPath) {
        $size = filesize($logPath);
        $modified = date('Y-m-d H:i:s', filemtime($logPath));
        echo "<li><strong>{$logPath}</strong> (大小: " . number_format($size) . " 字节, 修改时间: {$modified})</li>";
    }
    echo "</ul>";
    
    // 分析最新的日志文件
    $latestLog = $foundLogs[0];
    echo "<h4>📋 分析最新日志文件: {$latestLog}</h4>";
    
    try {
        $logContent = file_get_contents($latestLog);
        $lines = explode("\n", $logContent);
        $recentLines = array_slice($lines, -50); // 最后50行
        
        echo "<p>显示最后50行日志:</p>";
        echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;'>";
        
        foreach ($recentLines as $line) {
            if (trim($line)) {
                $class = 'log-info';
                if (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false) {
                    $class = 'log-error';
                } elseif (stripos($line, 'warning') !== false) {
                    $class = 'log-warning';
                }
                
                echo "<div class='log-entry {$class}'>" . htmlspecialchars($line) . "</div>";
            }
        }
        echo "</div>";
        
        // 统计错误类型
        $errorCount = substr_count(strtolower($logContent), 'error');
        $warningCount = substr_count(strtolower($logContent), 'warning');
        $fatalCount = substr_count(strtolower($logContent), 'fatal');
        
        echo "<h4>📊 错误统计</h4>";
        echo "<table>";
        echo "<tr><th>错误类型</th><th>数量</th></tr>";
        echo "<tr><td>错误 (Error)</td><td>{$errorCount}</td></tr>";
        echo "<tr><td>警告 (Warning)</td><td>{$warningCount}</td></tr>";
        echo "<tr><td>致命错误 (Fatal)</td><td>{$fatalCount}</td></tr>";
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 读取日志文件失败: " . $e->getMessage() . "</div>";
    }
    
} else {
    echo "<div class='warning'>⚠️ 未找到可读的错误日志文件</div>";
    echo "<p>可能的原因:</p>";
    echo "<ul>";
    echo "<li>错误日志功能未启用</li>";
    echo "<li>日志文件路径配置错误</li>";
    echo "<li>文件权限不足</li>";
    echo "<li>日志文件不存在</li>";
    echo "</ul>";
}
echo "</div>";

// 4. 检查注册相关的错误
echo "<div class='section info'>";
echo "<h3>🔍 注册相关错误检查</h3>";

// 模拟一个注册请求来捕获错误
echo "<p>正在模拟注册请求以捕获可能的错误...</p>";

// 开启错误捕获
ob_start();
$oldErrorHandler = set_error_handler(function($severity, $message, $file, $line) {
    echo "<div class='log-entry log-error'>";
    echo "<strong>捕获到错误:</strong><br>";
    echo "级别: {$severity}<br>";
    echo "消息: " . htmlspecialchars($message) . "<br>";
    echo "文件: {$file}<br>";
    echo "行号: {$line}<br>";
    echo "</div>";
    return true;
});

try {
    // 检查关键文件是否存在
    $criticalFiles = [
        'api/register.php' => '注册API文件',
        'config/database.php' => '数据库配置文件',
        'classes/Auth.php' => '认证类文件'
    ];
    
    foreach ($criticalFiles as $file => $desc) {
        if (file_exists($file)) {
            echo "<div class='success'>✅ {$desc} 存在</div>";
            
            // 尝试包含文件检查语法错误
            try {
                $content = file_get_contents($file);
                if (php_check_syntax($file)) {
                    echo "<div class='success'>✅ {$desc} 语法正确</div>";
                } else {
                    echo "<div class='error'>❌ {$desc} 语法错误</div>";
                }
            } catch (Exception $e) {
                echo "<div class='error'>❌ 检查 {$desc} 时出错: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error'>❌ {$desc} 不存在: {$file}</div>";
        }
    }
    
    // 检查数据库连接
    echo "<h4>🗄️ 数据库连接检查</h4>";
    try {
        require_once 'config/database.php';
        $db = DatabaseConfig::getInstance();
        echo "<div class='success'>✅ 数据库连接成功</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 检查过程中出现异常: " . $e->getMessage() . "</div>";
} catch (Error $e) {
    echo "<div class='error'>❌ 检查过程中出现PHP错误: " . $e->getMessage() . "</div>";
}

// 恢复错误处理器
restore_error_handler();
$output = ob_get_clean();
echo $output;

echo "</div>";

// 5. 系统资源检查
echo "<div class='section info'>";
echo "<h3>💻 系统资源检查</h3>";

echo "<table>";
echo "<tr><th>资源项</th><th>当前值</th><th>状态</th></tr>";

$memoryUsage = memory_get_usage(true);
$memoryLimit = ini_get('memory_limit');
$memoryLimitBytes = return_bytes($memoryLimit);

echo "<tr><td>内存使用</td><td>" . formatBytes($memoryUsage) . " / {$memoryLimit}</td>";
echo "<td>" . ($memoryUsage < $memoryLimitBytes * 0.8 ? '✅ 正常' : '⚠️ 偏高') . "</td></tr>";

$diskFree = disk_free_space('.');
$diskTotal = disk_total_space('.');
$diskUsage = ($diskTotal - $diskFree) / $diskTotal * 100;

echo "<tr><td>磁盘使用</td><td>" . formatBytes($diskTotal - $diskFree) . " / " . formatBytes($diskTotal) . " (" . number_format($diskUsage, 1) . "%)</td>";
echo "<td>" . ($diskUsage < 90 ? '✅ 正常' : '⚠️ 偏高') . "</td></tr>";

$loadAvg = sys_getloadavg();
if ($loadAvg) {
    echo "<tr><td>系统负载</td><td>" . implode(', ', array_map(function($x) { return number_format($x, 2); }, $loadAvg)) . "</td>";
    echo "<td>" . ($loadAvg[0] < 2 ? '✅ 正常' : '⚠️ 偏高') . "</td></tr>";
}

echo "</table>";
echo "</div>";

// 辅助函数
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }
    return $val;
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='快速修复云服务器注册问题.php' class='btn'>快速修复</a>";
echo "<a href='环境检测验证.php' class='btn'>环境检测</a>";
echo "<a href='register.php' class='btn'>测试注册</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
