@echo off
title SSH连接到比特熊服务器

echo ========================================
echo 比特熊智慧系统 - SSH连接工具
echo ========================================
echo.
echo 服务器信息:
echo IP地址: *************
echo 用户名: root
echo 密码: ZbDX7%=]?H2(LAUz
echo.

echo 正在测试网络连接...
ping -n 2 ************* | find "TTL"
if %errorlevel% neq 0 (
    echo ❌ 网络连接失败
    pause
    exit /b 1
)
echo ✓ 网络连接正常
echo.

echo 正在尝试SSH连接...
echo.

REM 尝试使用plink
if exist "putty\plink.exe" (
    echo 使用plink连接...
    echo 如果提示接受主机密钥，请输入 y
    putty\plink.exe -ssh root@************* -pw "ZbDX7%=]?H2(LAUz"
    goto :end
)

REM 尝试使用Windows SSH
if exist "C:\Windows\System32\OpenSSH\ssh.exe" (
    echo 使用Windows SSH连接...
    echo 请输入密码: ZbDX7%=]?H2(LAUz
    C:\Windows\System32\OpenSSH\ssh.exe root@*************
    goto :end
)

REM 尝试使用Git SSH
if exist "C:\Program Files\Git\usr\bin\ssh.exe" (
    echo 使用Git SSH连接...
    echo 请输入密码: ZbDX7%=]?H2(LAUz
    "C:\Program Files\Git\usr\bin\ssh.exe" root@*************
    goto :end
)

echo ❌ 未找到SSH客户端
echo.
echo 请安装以下任一工具：
echo 1. Windows OpenSSH客户端
echo 2. Git for Windows
echo 3. PuTTY

:end
echo.
echo 连接已结束
pause
