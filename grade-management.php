<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8b5cf6;
            --secondary-color: #7c3aed;
            --accent-color: #a78bfa;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: var(--primary-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .controls-section {
            background: var(--primary-color);
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-controls label {
            color: #333;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .form-select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
        }

        .form-select:focus {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: #333;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
        }

        .form-select option {
            background: var(--primary-color);
            color: #333;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-left: auto;
        }

        .btn-primary {
            background: rgba(139, 92, 246, 0.8);
            border: 1px solid rgba(139, 92, 246, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(139, 92, 246, 0.9);
            border-color: rgba(139, 92, 246, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: var(--primary-color);
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #333;
            font-size: 20px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .grades-section {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .section-title {
            color: #333;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .grades-table {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            color: #333;
        }

        .table thead th {
            background: rgba(255, 255, 255, 0.15);
            border: none;
            color: #333;
            font-weight: 600;
            padding: 1rem;
        }

        .table tbody td {
            background: rgba(255, 255, 255, 0.05);
            border: none;
            color: #495057;
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table tbody tr:hover td {
            background: rgba(255, 255, 255, 0.1);
        }

        .grade-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .grade-excellent {
            background: rgba(16, 185, 129, 0.8);
            color: #333;
        }

        .grade-good {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
        }

        .grade-average {
            background: rgba(245, 158, 11, 0.8);
            color: #333;
        }

        .grade-poor {
            background: rgba(239, 68, 68, 0.8);
            color: #333;
        }

        .trend-section {
            background: var(--primary-color); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .trend-chart {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 2rem;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .filter-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .action-buttons {
                margin-left: 0;
                justify-content: center;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .table thead th,
            .table tbody td {
                padding: 0.75rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-chart-bar me-2"></i>
                成绩管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">成绩管理</h1>
            <p class="page-description">成绩录入存储、统计分析、GPA计算、成绩历史记录</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="filter-controls">
                <label>学期筛选：</label>
                <select class="form-select" style="width: auto;">
                    <option>2023-2024学年第一学期</option>
                    <option>2023-2024学年第二学期</option>
                    <option>2024-2025学年第一学期</option>
                </select>
                
                <label>课程类型：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部课程</option>
                    <option>必修课</option>
                    <option>选修课</option>
                    <option>实践课</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addGrade()">
                        <i class="fas fa-plus me-1"></i>
                        录入成绩
                    </button>
                    <button class="btn btn-primary" onclick="exportGrades()">
                        <i class="fas fa-download me-1"></i>
                        导出成绩单
                    </button>
                </div>
            </div>
        </div>

        <!-- 仪表盘内容 -->
        <div class="dashboard-container">
            <!-- 成绩统计概览 -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="stat-number">3.8</div>
                    <div class="stat-label">当前GPA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                    <div class="stat-number">18</div>
                    <div class="stat-label">已获学分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-number">85.6</div>
                    <div class="stat-label">平均分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number">+2.3</div>
                    <div class="stat-label">本学期提升</div>
                </div>
            </div>

            <!-- 成绩列表 -->
            <div class="grades-section">
                <h2 class="section-title">
                    <i class="fas fa-list"></i>
                    成绩记录
                </h2>
                <div class="grades-table">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>课程名称</th>
                                <th>学分</th>
                                <th>成绩</th>
                                <th>等级</th>
                                <th>绩点</th>
                                <th>考试时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>高等数学</td>
                                <td>4</td>
                                <td>92</td>
                                <td><span class="grade-badge grade-excellent">优秀</span></td>
                                <td>4.0</td>
                                <td>2024-01-15</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>数据结构</td>
                                <td>3</td>
                                <td>85</td>
                                <td><span class="grade-badge grade-good">良好</span></td>
                                <td>3.5</td>
                                <td>2024-01-12</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>英语听力</td>
                                <td>2</td>
                                <td>78</td>
                                <td><span class="grade-badge grade-average">中等</span></td>
                                <td>3.0</td>
                                <td>2024-01-10</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>计算机网络</td>
                                <td>3</td>
                                <td>88</td>
                                <td><span class="grade-badge grade-good">良好</span></td>
                                <td>3.7</td>
                                <td>2024-01-08</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">详情</button>
                                </td>
                            </tr>
                            <tr>
                                <td>操作系统</td>
                                <td>3</td>
                                <td>90</td>
                                <td><span class="grade-badge grade-excellent">优秀</span></td>
                                <td>3.9</td>
                                <td>2024-01-05</td>
                                <td>
                                    <button class="btn btn-sm btn-primary">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 成绩趋势分析 -->
            <div class="trend-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    成绩趋势分析
                </h2>
                <div class="trend-chart">
                    <i class="fas fa-chart-area me-2"></i>
                    成绩趋势图表功能正在开发中...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addGrade() {
            alert('录入成绩功能正在开发中...');
        }

        function exportGrades() {
            alert('导出成绩单功能正在开发中...');
        }
    </script>
</body>
</html>
