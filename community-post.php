<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// 检查用户是否登录
if (!$currentUser) {
    header('Location: admin-login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$error = '';
$success = '';
$isEdit = false;
$editPost = null;

// 检查是否是编辑模式
$editId = intval($_GET['edit'] ?? 0);
if ($editId > 0) {
    try {
        $db = db();
        $editPost = $db->fetchOne(
            "SELECT * FROM posts WHERE id = ? AND user_id = ? AND (status = 'draft' OR status = 'rejected')",
            [$editId, $currentUser['id']]
        );

        if ($editPost) {
            $isEdit = true;
        } else {
            $error = '草稿不存在或无权限编辑';
        }
    } catch (Exception $e) {
        $error = '加载草稿失败：' . $e->getMessage();
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = db();

        $title = trim($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $category_id = intval($_POST['category_id'] ?? 0);
        $excerpt = trim($_POST['excerpt'] ?? '');
        $featured_image = trim($_POST['featured_image'] ?? '');
        $action = $_POST['action'] ?? 'publish'; // publish, save_draft

        // 验证输入
        if (empty($title)) {
            throw new Exception('请输入帖子标题');
        }

        if (empty($content)) {
            throw new Exception('请输入帖子内容');
        }

        if (strlen($title) > 255) {
            throw new Exception('标题长度不能超过255个字符');
        }

        // 如果没有提供摘要，从内容中自动生成
        if (empty($excerpt)) {
            $excerpt = mb_substr(strip_tags($content), 0, 200);
        }

        // 获取用户IP
        $ip_address = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 确定帖子状态
        $status = ($action === 'save_draft') ? 'draft' : 'published';
        
        if ($isEdit && $editPost) {
            // 更新现有帖子
            $sql = "UPDATE posts SET title = ?, content = ?, excerpt = ?, featured_image = ?, category_id = ?, status = ?, updated_at = NOW()
                    WHERE id = ? AND user_id = ?";

            $db->execute($sql, [
                $title,
                $content,
                $excerpt,
                $featured_image ?: null,
                $category_id ?: null,
                $status,
                $editPost['id'],
                $currentUser['id']
            ]);

            $postId = $editPost['id'];

            if ($action === 'save_draft') {
                $success = '草稿保存成功！';
                // 重定向到草稿列表页面
                header('Location: community-drafts.php');
                exit;
            } else {
                $success = '帖子发布成功！';
                // 重定向到帖子详情页
                header('Location: community-post-detail.php?id=' . $postId);
                exit;
            }
        } else {
            // 插入新帖子
            $sql = "INSERT INTO posts (user_id, category_id, title, content, excerpt, featured_image, status,
                                       ip_address, user_agent, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            $params = [
                $currentUser['id'],
                $category_id ?: null,
                $title,
                $content,
                $excerpt,
                $featured_image ?: null,
                $status,
                $ip_address,
                $user_agent
            ];

            $db->execute($sql, $params);
            $postId = $db->lastInsertId();

            if ($action === 'save_draft') {
                $success = '草稿保存成功！';
                // 重定向到草稿列表页面
                header('Location: community-drafts.php');
                exit;
            } else {
                // 更新用户发帖数量（只有发布时才计数）
                $db->execute("UPDATE user_profiles SET post_count = post_count + 1 WHERE user_id = ?", [$currentUser['id']]);

                $success = '帖子发布成功！';

                // 重定向到帖子详情页
                header('Location: community-post-detail.php?id=' . $postId);
                exit;
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取分类列表
try {
    $db = db();
    $categories = $db->fetchAll("SELECT * FROM post_categories WHERE is_active = 1 ORDER BY sort_order");
} catch (Exception $e) {
    $categories = [];
}

if (!function_exists('escapeHtml')) {
    function escapeHtml($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $isEdit ? '编辑帖子' : '发布帖子'; ?> - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="assets/css/community-post.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- TinyMCE编辑器 - 使用开源版本 -->
    <script src="https://cdn.jsdelivr.net/npm/tinymce@6.8.2/tinymce.min.js"></script>

    <style>
        /* 编辑器容器样式 */
        .editor-container {
            position: relative;
        }

        /* 拖拽提示样式 */
        .drag-drop-hint {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(74, 144, 226, 0.1);
            border: 2px dashed #4a90e2;
            border-radius: 8px;
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
            color: #4a90e2;
            font-size: 16px;
            font-weight: 500;
        }

        .drag-drop-hint.active {
            display: flex;
        }

        .drag-drop-hint i {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .drag-drop-hint p {
            margin: 0;
            text-align: center;
        }

        /* TinyMCE编辑器拖拽样式 */
        .tox-edit-area.drag-over {
            border: 2px dashed #4a90e2 !important;
            background: rgba(74, 144, 226, 0.05) !important;
        }

        /* 图片上传按钮样式 */
        .tox-tbtn--enabled .tox-icon-image {
            color: #4a90e2;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                         alt="头像" class="user-avatar">
                    <span><?php echo escapeHtml($currentUser['username']); ?></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="post-form-container">
                <div class="form-header">
                    <h1 class="form-title">
                        <i class="fas fa-edit"></i>
                        <?php echo $isEdit ? '编辑帖子' : '发布新帖子'; ?>
                    </h1>
                    <p class="form-description"><?php echo $isEdit ? '修改您的帖子内容' : '分享你的想法，与社区成员交流讨论'; ?></p>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo escapeHtml($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo escapeHtml($success); ?>
                </div>
                <?php endif; ?>

                <form method="POST" class="post-form" id="postForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="title" class="form-label">
                                <i class="fas fa-heading"></i>
                                帖子标题 <span class="required">*</span>
                            </label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   class="form-input"
                                   placeholder="请输入一个吸引人的标题..."
                                   value="<?php echo escapeHtml($_POST['title'] ?? ($editPost['title'] ?? '')); ?>"
                                   maxlength="255"
                                   required>
                            <div class="form-help">标题应该简洁明了，能够准确概括帖子内容</div>
                        </div>

                        <div class="form-group">
                            <label for="category_id" class="form-label">
                                <i class="fas fa-tag"></i>
                                分类
                            </label>
                            <select id="category_id" name="category_id" class="form-select">
                                <option value="">选择分类（可选）</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['id']; ?>"
                                        <?php
                                        $selectedCategoryId = intval($_POST['category_id'] ?? ($editPost['category_id'] ?? 0));
                                        echo ($selectedCategoryId === intval($category['id'])) ? 'selected' : '';
                                        ?>>
                                    <?php if ($category['icon']): ?>
                                        <?php echo $category['icon']; ?> 
                                    <?php endif; ?>
                                    <?php echo escapeHtml($category['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label">
                            <i class="fas fa-align-left"></i>
                            帖子内容 <span class="required">*</span>
                        </label>
                        <div class="editor-container">
                            <div class="drag-drop-hint" id="dragDropHint">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>拖拽图片到此处快速上传，或点击工具栏中的图片按钮</p>
                            </div>
                            <textarea id="content"
                                      name="content"
                                      class="form-textarea"
                                      placeholder="在这里写下你的想法..."
                                      required><?php echo escapeHtml($_POST['content'] ?? ($editPost['content'] ?? '')); ?></textarea>
                        </div>
                        <div class="form-help">支持富文本编辑，可以插入图片、链接、代码等。支持拖拽上传图片！</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="excerpt" class="form-label">
                                <i class="fas fa-quote-left"></i>
                                帖子摘要
                            </label>
                            <textarea id="excerpt"
                                      name="excerpt"
                                      class="form-input"
                                      rows="3"
                                      placeholder="简要描述帖子内容（可选，如不填写将自动生成）"
                                      maxlength="500"><?php echo escapeHtml($_POST['excerpt'] ?? ($editPost['excerpt'] ?? '')); ?></textarea>
                            <div class="form-help">摘要将显示在帖子列表中，建议控制在200字以内</div>
                        </div>

                        <div class="form-group">
                            <label for="featured_image" class="form-label">
                                <i class="fas fa-image"></i>
                                特色图片
                            </label>
                            <input type="url"
                                   id="featured_image"
                                   name="featured_image"
                                   class="form-input"
                                   placeholder="图片URL（可选）"
                                   value="<?php echo escapeHtml($_POST['featured_image'] ?? ($editPost['featured_image'] ?? '')); ?>">
                            <div class="form-help">可以添加一张特色图片来吸引读者注意</div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" onclick="history.back()">
                            <i class="fas fa-arrow-left"></i>
                            取消
                        </button>

                        <button type="button" class="btn btn-secondary" id="previewBtn">
                            <i class="fas fa-eye"></i>
                            预览
                        </button>

                        <button type="button" class="btn btn-secondary" id="saveDraftBtn">
                            <i class="fas fa-save"></i>
                            保存草稿
                        </button>

                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-paper-plane"></i>
                            <?php echo $isEdit ? '更新帖子' : '发布帖子'; ?>
                        </button>
                    </div>

                    <!-- 隐藏字段用于标识操作类型 -->
                    <input type="hidden" name="action" id="actionInput" value="publish">
                </form>
            </div>
        </div>
    </main>

    <!-- 预览模态框 -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>帖子预览</h3>
                <button type="button" class="modal-close" onclick="closePreview()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" onclick="closePreview()">关闭预览</button>
            </div>
        </div>
    </div>

    <script>
        // 初始化TinyMCE编辑器
        tinymce.init({
            selector: '#content',
            height: 500,
            menubar: 'file edit view insert format tools table help',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
                'textcolor', 'colorpicker', 'textpattern', 'codesample', 'hr',
                'pagebreak', 'nonbreaking', 'template', 'paste', 'directionality', 'imagetools'
            ],
            toolbar1: 'undo redo | cut copy paste | bold italic underline strikethrough | ' +
                     'fontfamily fontsize | forecolor backcolor | removeformat',
            toolbar2: 'alignleft aligncenter alignright alignjustify | ' +
                     'bullist numlist outdent indent | blockquote hr pagebreak | ' +
                     'link unlink anchor | image customImageUpload media table emoticons charmap',
            toolbar3: 'subscript superscript | codesample | ltr rtl | ' +
                     'visualblocks code preview fullscreen help',

            // 字体设置
            font_family_formats:
                '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;' +
                '宋体=SimSun,serif;' +
                '黑体=SimHei,sans-serif;' +
                '楷体=KaiTi,serif;' +
                'Arial=arial,helvetica,sans-serif;' +
                'Times New Roman=times new roman,times,serif;' +
                'Courier New=courier new,courier,monospace',

            font_size_formats: '12px 14px 16px 18px 20px 24px 28px 32px 36px 48px 60px 72px 96px',

            // 颜色设置
            color_map: [
                "000000", "黑色",
                "993300", "深红色",
                "333300", "深黄色",
                "003300", "深绿色",
                "003366", "深青色",
                "000080", "深蓝色",
                "333399", "蓝色",
                "333333", "深灰色",
                "800000", "栗色",
                "FF6600", "橙色",
                "808000", "橄榄色",
                "008000", "绿色",
                "008080", "青色",
                "0000FF", "蓝色",
                "666699", "灰蓝色",
                "808080", "灰色",
                "FF0000", "红色",
                "FF9900", "琥珀色",
                "99CC00", "黄绿色",
                "339966", "海绿色",
                "33CCCC", "青绿色",
                "3366FF", "蓝色",
                "800080", "紫色",
                "999999", "中灰色",
                "FF00FF", "洋红色",
                "FFCC00", "金色",
                "FFFF00", "黄色",
                "00FF00", "酸橙色",
                "00FFFF", "水色",
                "00CCFF", "天蓝色",
                "993366", "红紫色",
                "C0C0C0", "银色",
                "FF99CC", "粉红色",
                "FFCC99", "桃色",
                "FFFF99", "浅黄色",
                "CCFFCC", "浅绿色",
                "CCFFFF", "浅青色",
                "99CCFF", "浅蓝色",
                "CC99FF", "淡紫色",
                "FFFFFF", "白色"
            ],

            // 图片上传设置
            images_upload_url: 'api/user-upload.php',
            images_upload_handler: function (blobInfo, success, failure) {
                uploadImageToServer(blobInfo.blob(), blobInfo.filename(), success, failure);
            },

            // 启用拖拽上传
            paste_data_images: true,
            images_reuse_filename: true,

            // 图片工具设置
            images_upload_credentials: true,
            automatic_uploads: true,
            imagetools_cors_hosts: ['localhost'],
            imagetools_toolbar: 'rotateleft rotateright | flipv fliph | editimage imageoptions',

            // 图片调整选项
            image_dimensions: true,
            image_class_list: [
                {title: '无样式', value: ''},
                {title: '响应式图片', value: 'img-responsive'},
                {title: '圆角图片', value: 'img-rounded'},
                {title: '圆形图片', value: 'img-circle'},
                {title: '缩略图', value: 'img-thumbnail'}
            ],

            // 媒体设置
            media_live_embeds: true,
            media_url_resolver: function (data, resolve) {
                if (data.url.indexOf('youtube.com') !== -1 || data.url.indexOf('youtu.be') !== -1) {
                    resolve({html: '<iframe src="' + data.url + '" width="560" height="315" frameborder="0" allowfullscreen></iframe>'});
                } else {
                    resolve({html: ''});
                }
            },

            content_style: `
                body {
                    font-family: Microsoft YaHei, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                }
                img { max-width: 100%; height: auto; }
                pre { background: #f4f4f4; padding: 10px; border-radius: 4px; overflow-x: auto; }
                blockquote {
                    border-left: 4px solid #ddd;
                    margin: 0;
                    padding: 0 15px;
                    color: #777;
                    font-style: italic;
                }
            `,

            branding: false,
            promotion: false,
            language: 'zh_CN',

            setup: function (editor) {
                editor.on('change', function () {
                    editor.save();
                });

                // 自定义图片上传按钮
                editor.ui.registry.addButton('customImageUpload', {
                    icon: 'image',
                    tooltip: '上传本地图片',
                    onAction: function () {
                        // 创建文件选择器
                        var input = document.createElement('input');
                        input.setAttribute('type', 'file');
                        input.setAttribute('accept', 'image/*');
                        input.setAttribute('multiple', 'true');
                        input.style.display = 'none';
                        document.body.appendChild(input);

                        input.onchange = function() {
                            var files = Array.from(this.files);
                            files.forEach(function(file) {
                                if (file.type.startsWith('image/')) {
                                    uploadImageToServer(file, file.name, function(url) {
                                        editor.insertContent(`<img src="${url}" alt="${file.name}" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                                    }, function(error) {
                                        alert('图片上传失败: ' + error);
                                    });
                                }
                            });
                            document.body.removeChild(input);
                        };

                        input.click();
                    }
                });

                // 自定义文件上传按钮
                editor.ui.registry.addButton('customUpload', {
                    text: '上传文件',
                    onAction: function () {
                        // 触发文件上传
                        var input = document.createElement('input');
                        input.setAttribute('type', 'file');
                        input.setAttribute('accept', 'image/*,audio/*,video/*,.pdf,.doc,.docx,.txt');
                        input.click();

                        input.onchange = function() {
                            var file = this.files[0];
                            if (file) {
                                uploadFile(file, editor);
                            }
                        };
                    }
                });
            }
        });

        // 图片上传到服务器的通用函数
        function uploadImageToServer(blob, filename, success, failure) {
            const formData = new FormData();
            formData.append('file', blob, filename);

            fetch('api/user-upload.php', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    success(data.location);
                } else {
                    failure(data.error || '图片上传失败');
                }
            })
            .catch(error => {
                console.error('图片上传错误:', error);
                failure('网络错误，请重试');
            });
        }

        // 文件上传功能
        function uploadFile(file, editor) {
            const formData = new FormData();
            formData.append('file', file);

            // 显示上传进度
            const progressHtml = '<div class="upload-progress">正在上传文件...</div>';
            editor.insertContent(progressHtml);

            fetch('api/user-upload.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // 移除进度提示
                editor.setContent(editor.getContent().replace(progressHtml, ''));

                if (data.success) {
                    const fileUrl = data.location;
                    const fileName = file.name;
                    const fileType = file.type;

                    let insertHtml = '';

                    if (fileType.startsWith('image/')) {
                        insertHtml = `<img src="${fileUrl}" alt="${fileName}" style="max-width: 100%; height: auto;">`;
                    } else if (fileType.startsWith('audio/')) {
                        insertHtml = `<audio controls><source src="${fileUrl}" type="${fileType}">您的浏览器不支持音频播放。</audio>`;
                    } else if (fileType.startsWith('video/')) {
                        insertHtml = `<video controls style="max-width: 100%; height: auto;"><source src="${fileUrl}" type="${fileType}">您的浏览器不支持视频播放。</video>`;
                    } else {
                        insertHtml = `<a href="${fileUrl}" target="_blank" class="file-link"><i class="fas fa-file"></i> ${fileName}</a>`;
                    }

                    editor.insertContent(insertHtml);
                } else {
                    alert('文件上传失败：' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                // 移除进度提示
                editor.setContent(editor.getContent().replace(progressHtml, ''));
                console.error('上传错误:', error);
                alert('文件上传失败，请重试');
            });
        }

        // 拖拽上传功能
        function initDragDropUpload() {
            const editorContainer = document.querySelector('.editor-container');
            const dragDropHint = document.getElementById('dragDropHint');
            let dragCounter = 0;

            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                editorContainer.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // 拖拽进入
            editorContainer.addEventListener('dragenter', function(e) {
                dragCounter++;
                if (e.dataTransfer.types.includes('Files')) {
                    dragDropHint.classList.add('active');
                }
            });

            // 拖拽离开
            editorContainer.addEventListener('dragleave', function(e) {
                dragCounter--;
                if (dragCounter === 0) {
                    dragDropHint.classList.remove('active');
                }
            });

            // 拖拽悬停
            editorContainer.addEventListener('dragover', function(e) {
                if (e.dataTransfer.types.includes('Files')) {
                    dragDropHint.classList.add('active');
                }
            });

            // 文件放置
            editorContainer.addEventListener('drop', function(e) {
                dragCounter = 0;
                dragDropHint.classList.remove('active');

                const files = Array.from(e.dataTransfer.files);
                const imageFiles = files.filter(file => file.type.startsWith('image/'));

                if (imageFiles.length > 0) {
                    imageFiles.forEach(file => {
                        uploadImageToServer(file, file.name, function(url) {
                            // 获取TinyMCE编辑器实例
                            const editor = tinymce.get('content');
                            if (editor) {
                                editor.insertContent(`<img src="${url}" alt="${file.name}" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                            }
                        }, function(error) {
                            alert('图片上传失败: ' + error);
                        });
                    });
                } else if (files.length > 0) {
                    alert('请拖拽图片文件！');
                }
            });
        }

        // 粘贴上传功能
        function initPasteUpload() {
            document.addEventListener('paste', function(e) {
                const items = Array.from(e.clipboardData.items);
                const imageItems = items.filter(item => item.type.startsWith('image/'));

                if (imageItems.length > 0) {
                    e.preventDefault();

                    imageItems.forEach(item => {
                        const file = item.getAsFile();
                        if (file) {
                            uploadImageToServer(file, 'pasted-image-' + Date.now() + '.png', function(url) {
                                const editor = tinymce.get('content');
                                if (editor) {
                                    editor.insertContent(`<img src="${url}" alt="粘贴的图片" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                                }
                            }, function(error) {
                                alert('图片上传失败: ' + error);
                            });
                        }
                    });
                }
            });
        }

        // 预览功能
        document.getElementById('previewBtn').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const content = tinymce.get('content').getContent();
            
            if (!title.trim()) {
                alert('请先输入标题');
                return;
            }
            
            if (!content.trim()) {
                alert('请先输入内容');
                return;
            }
            
            const previewHtml = `
                <div class="preview-post">
                    <h1 class="preview-title">${title}</h1>
                    <div class="preview-content">${content}</div>
                </div>
            `;
            
            document.getElementById('previewContent').innerHTML = previewHtml;
            document.getElementById('previewModal').style.display = 'flex';
        });

        function closePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // 保存草稿功能
        document.getElementById('saveDraftBtn').addEventListener('click', function() {
            const title = document.getElementById('title').value.trim();
            const content = tinymce.get('content').getContent().trim();

            if (!title) {
                alert('请输入帖子标题');
                return;
            }

            if (!content) {
                alert('请输入帖子内容');
                return;
            }

            // 设置操作类型为保存草稿
            document.getElementById('actionInput').value = 'save_draft';

            // 显示保存状态
            const saveDraftBtn = document.getElementById('saveDraftBtn');
            const originalText = saveDraftBtn.innerHTML;
            saveDraftBtn.disabled = true;
            saveDraftBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';

            // 提交表单
            document.getElementById('postForm').submit();
        });

        // 表单提交前验证
        document.getElementById('postForm').addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const content = tinymce.get('content').getContent().trim();

            if (!title) {
                e.preventDefault();
                alert('请输入帖子标题');
                return;
            }

            if (!content) {
                e.preventDefault();
                alert('请输入帖子内容');
                return;
            }

            // 显示提交状态
            const submitBtn = document.getElementById('submitBtn');
            const actionType = document.getElementById('actionInput').value;

            if (actionType === 'save_draft') {
                // 草稿保存状态已在上面处理
                return;
            }

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发布中...';
        });

        // 点击模态框背景关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化拖拽上传功能
            initDragDropUpload();

            // 初始化粘贴上传功能
            initPasteUpload();

            // 等待TinyMCE加载完成后隐藏拖拽提示
            const checkEditor = setInterval(function() {
                if (tinymce.get('content')) {
                    clearInterval(checkEditor);
                    // 编辑器加载完成后隐藏拖拽提示
                    setTimeout(function() {
                        const dragDropHint = document.getElementById('dragDropHint');
                        if (dragDropHint) {
                            dragDropHint.style.display = 'none';
                        }
                    }, 1000);
                }
            }, 100);
        });
    </script>
</body>
</html>
