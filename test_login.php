<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .result { margin-top: 10px; }
    </style>
</head>
<body>
    <h1>登录功能测试</h1>
    
    <div class="test-section">
        <h2>快速登录测试</h2>
        <p>使用默认管理员账号测试登录功能</p>
        
        <div>
            <label>用户名: </label>
            <input type="text" id="testUsername" value="admin" readonly>
        </div>
        <div>
            <label>密码: </label>
            <input type="password" id="testPassword" value="admin123" readonly>
        </div>
        <div>
            <label>用户类型: </label>
            <select id="testUserType">
                <option value="super">超级管理员</option>
                <option value="admin">普通管理员</option>
                <option value="vip">特殊用户</option>
            </select>
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="checkSession()">检查会话状态</button>
        <button onclick="logout()">退出登录</button>
        
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>自定义登录测试</h2>
        <p>输入自定义账号信息进行测试</p>
        
        <div>
            <label>用户名: </label>
            <input type="text" id="customUsername" placeholder="输入用户名">
        </div>
        <div>
            <label>密码: </label>
            <input type="password" id="customPassword" placeholder="输入密码">
        </div>
        <div>
            <label>用户类型: </label>
            <select id="customUserType">
                <option value="super">超级管理员</option>
                <option value="admin">普通管理员</option>
                <option value="vip">特殊用户</option>
            </select>
        </div>
        
        <button onclick="testCustomLogin()">自定义登录测试</button>
        
        <div id="customResult" class="result"></div>
    </div>

    <script>
        // 测试默认登录
        async function testLogin() {
            const result = document.getElementById('loginResult');
            result.innerHTML = '<p class="info">正在测试登录...</p>';
            
            const formData = new FormData();
            formData.append('username', document.getElementById('testUsername').value);
            formData.append('password', document.getElementById('testPassword').value);
            formData.append('user_type', document.getElementById('testUserType').value);
            
            try {
                const response = await fetch('admin-auth.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 登录测试成功！</p>
                        <p>消息: ${data.message}</p>
                        <p>跳转地址: ${data.redirect}</p>
                        <p><a href="${data.redirect}" target="_blank">打开管理后台</a></p>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 登录失败: ${data.message}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        // 测试自定义登录
        async function testCustomLogin() {
            const result = document.getElementById('customResult');
            result.innerHTML = '<p class="info">正在测试自定义登录...</p>';
            
            const username = document.getElementById('customUsername').value;
            const password = document.getElementById('customPassword').value;
            const userType = document.getElementById('customUserType').value;
            
            if (!username || !password) {
                result.innerHTML = '<p class="error">❌ 请输入用户名和密码</p>';
                return;
            }
            
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            formData.append('user_type', userType);
            
            try {
                const response = await fetch('admin-auth.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 自定义登录成功！</p>
                        <p>消息: ${data.message}</p>
                        <p>跳转地址: ${data.redirect}</p>
                        <p><a href="${data.redirect}" target="_blank">打开管理后台</a></p>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 登录失败: ${data.message}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        // 检查会话状态
        async function checkSession() {
            const result = document.getElementById('loginResult');
            result.innerHTML = '<p class="info">正在检查会话状态...</p>';
            
            try {
                const response = await fetch('admin-dashboard.php');
                
                if (response.redirected && response.url.includes('admin-login.php')) {
                    result.innerHTML = '<p class="error">❌ 未登录，会话已过期</p>';
                } else if (response.ok) {
                    result.innerHTML = '<p class="success">✅ 会话有效，已登录状态</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ 检查失败，状态码: ' + response.status + '</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ 检查失败: ' + error.message + '</p>';
            }
        }

        // 退出登录
        function logout() {
            const result = document.getElementById('loginResult');
            result.innerHTML = '<p class="info">正在退出登录...</p>';
            
            // 清除会话（这里简单地跳转到登录页面）
            window.location.href = 'admin-login.php';
        }

        // 页面加载时显示提示信息
        window.onload = function() {
            console.log('登录测试页面已加载');
            console.log('默认测试账号: admin / admin123');
            console.log('可用的测试账号:');
            console.log('- 超级管理员: admin/admin123, superadmin/super123');
            console.log('- 普通管理员: manager/manager123, editor/editor123');
            console.log('- 特殊用户: vip1/vip123, premium/premium123');
        };
    </script>
</body>
</html>
