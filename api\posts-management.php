<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

try {
    $db = db();
    $pdo = $db->getConnection();
} catch(Exception $e) {
    echo json_encode(['success' => false, 'error' => '数据库连接失败: ' . $e->getMessage()]);
    exit;
}

$action = $_GET['action'] ?? '';

switch($action) {
    case 'stats':
        getPostsStats($pdo);
        break;
    case 'list':
        getPostsList($pdo);
        break;
    case 'update_status':
        updatePostStatus($pdo);
        break;
    case 'reject':
        rejectPost($pdo);
        break;
    case 'delete':
        deletePost($pdo);
        break;
    case 'pin':
        pinPost($pdo);
        break;
    default:
        echo json_encode(['success' => false, 'error' => '无效的操作']);
}

function getPostsStats($pdo) {
    try {

        // 获取统计数据 - 兼容不同的字段名
        $totalPosts = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
        $categoriesCount = $pdo->query("SELECT COUNT(*) FROM post_categories")->fetchColumn();

        // 检查status字段的值（可能是rejected或hidden）
        $violationPosts = $pdo->query("SELECT COUNT(*) FROM posts WHERE status IN ('rejected', 'hidden', 'deleted')")->fetchColumn();

        // 兼容MySQL和SQLite的日期函数
        $driver = $pdo->getAttribute(PDO::ATTR_DRIVER_NAME);
        if ($driver === 'mysql') {
            $yesterdayPosts = $pdo->query("SELECT COUNT(*) FROM posts WHERE DATE(created_at) = DATE(NOW() - INTERVAL 1 DAY)")->fetchColumn();
        } else {
            $yesterdayPosts = $pdo->query("SELECT COUNT(*) FROM posts WHERE DATE(created_at) = DATE('now', '-1 day')")->fetchColumn();
        }

        echo json_encode([
            'success' => true,
            'data' => [
                'totalPosts' => $totalPosts,
                'categoriesCount' => $categoriesCount,
                'violationPosts' => $violationPosts,
                'yesterdayPosts' => $yesterdayPosts
            ]
        ]);
    } catch(PDOException $e) {
        error_log("获取统计数据失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => '获取统计数据失败: ' . $e->getMessage()]);
    } catch(Exception $e) {
        error_log("获取统计数据异常: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => '获取统计数据异常: ' . $e->getMessage()]);
    }
}

function getPostsList($pdo) {
    try {


        $page = intval($_GET['page'] ?? 1);
        $limit = 10;
        $offset = ($page - 1) * $limit;
        
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? 'all';
        $category = $_GET['category'] ?? 'all';
        
        // 构建查询条件
        $whereConditions = [];
        $params = [];
        
        if (!empty($search)) {
            $whereConditions[] = "(title LIKE ? OR content LIKE ? OR author_name LIKE ?)";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
        }
        
        if ($status !== 'all') {
            $whereConditions[] = "status = ?";
            $params[] = $status;
        }
        
        if ($category !== 'all') {
            $whereConditions[] = "category_name = ?";
            $params[] = $category;
        }
        
        $whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);
        
        // 获取总数
        $countSql = "SELECT COUNT(*) FROM posts $whereClause";
        $countStmt = $pdo->prepare($countSql);
        $countStmt->execute($params);
        $totalCount = $countStmt->fetchColumn();
        
        // 获取帖子列表 - 使用JOIN查询获取用户名和分类名
        $sql = "SELECT p.id, p.title, p.user_id,
                       COALESCE(u.username, u.full_name, '未知用户') as author_name,
                       COALESCE(c.name, '未分类') as category_name,
                       p.status,
                       COALESCE(p.view_count, 0) as views,
                       COALESCE(p.is_pinned, 0) as is_pinned,
                       p.created_at,
                       p.rejection_reason,
                       p.rejected_at,
                       COALESCE(ru.username, ru.full_name, '') as rejected_by_name
                FROM posts p
                LEFT JOIN users u ON p.user_id = u.id
                LEFT JOIN post_categories c ON p.category_id = c.id
                LEFT JOIN users ru ON p.rejected_by = ru.id
                $whereClause
                ORDER BY p.is_pinned DESC, p.created_at DESC
                LIMIT $limit OFFSET $offset";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);


        
        // 格式化数据
        foreach ($posts as &$post) {
            $post['created_at'] = date('Y-m-d H:i', strtotime($post['created_at']));
        }
        
        $totalPages = ceil($totalCount / $limit);
        
        echo json_encode([
            'success' => true,
            'data' => [
                'posts' => $posts,
                'pagination' => [
                    'currentPage' => $page,
                    'totalPages' => $totalPages,
                    'totalCount' => $totalCount,
                    'limit' => $limit
                ]
            ]
        ]);
    } catch(PDOException $e) {
        error_log("获取帖子列表失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => '获取帖子列表失败: ' . $e->getMessage()]);
    } catch(Exception $e) {
        error_log("获取帖子列表异常: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => '获取帖子列表异常: ' . $e->getMessage()]);
    }
}

function updatePostStatus($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $postId = $input['postId'] ?? 0;
        $status = $input['status'] ?? '';
        
        if (!$postId || !$status) {
            echo json_encode(['success' => false, 'error' => '参数不完整']);
            return;
        }
        
        $stmt = $pdo->prepare("UPDATE posts SET status = ? WHERE id = ?");
        $result = $stmt->execute([$status, $postId]);
        
        if ($result) {
            echo json_encode(['success' => true, 'message' => '状态更新成功']);
        } else {
            echo json_encode(['success' => false, 'error' => '状态更新失败']);
        }
    } catch(PDOException $e) {
        echo json_encode(['success' => false, 'error' => '更新状态失败: ' . $e->getMessage()]);
    }
}

function rejectPost($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $postId = $input['postId'] ?? 0;
        $reason = $input['reason'] ?? '';
        $adminId = $input['adminId'] ?? 1; // 管理员ID，应该从session获取

        if (!$postId || !$reason) {
            echo json_encode(['success' => false, 'error' => '参数不完整']);
            return;
        }

        // 获取帖子信息
        $postStmt = $pdo->prepare("SELECT user_id, title FROM posts WHERE id = ?");
        $postStmt->execute([$postId]);
        $post = $postStmt->fetch(PDO::FETCH_ASSOC);

        if (!$post) {
            echo json_encode(['success' => false, 'error' => '帖子不存在']);
            return;
        }

        // 更新帖子状态
        $stmt = $pdo->prepare("UPDATE posts SET status = 'rejected', rejection_reason = ?, rejected_at = NOW(), rejected_by = ? WHERE id = ?");
        $result = $stmt->execute([$reason, $adminId, $postId]);

        if ($result) {
            // 发送通知给用户
            $notificationStmt = $pdo->prepare("INSERT INTO notifications (user_id, title, content, type, created_at) VALUES (?, ?, ?, 'system', NOW())");
            $notificationTitle = "您的帖子被打回";
            $notificationContent = "管理员打回了您的帖子《{$post['title']}》，原因：{$reason}。请尝试修改后再次发布。";
            $notificationStmt->execute([$post['user_id'], $notificationTitle, $notificationContent]);

            echo json_encode(['success' => true, 'message' => '帖子已打回']);
        } else {
            echo json_encode(['success' => false, 'error' => '打回失败']);
        }
    } catch(PDOException $e) {
        echo json_encode(['success' => false, 'error' => '打回帖子失败: ' . $e->getMessage()]);
    }
}

function deletePost($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $postId = $input['postId'] ?? 0;

        if (!$postId) {
            echo json_encode(['success' => false, 'error' => '参数不完整']);
            return;
        }

        // 软删除帖子
        $stmt = $pdo->prepare("UPDATE posts SET status = 'deleted' WHERE id = ?");
        $result = $stmt->execute([$postId]);

        if ($result) {
            echo json_encode(['success' => true, 'message' => '帖子已删除']);
        } else {
            echo json_encode(['success' => false, 'error' => '删除失败']);
        }
    } catch(PDOException $e) {
        echo json_encode(['success' => false, 'error' => '删除帖子失败: ' . $e->getMessage()]);
    }
}

function pinPost($pdo) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $postId = $input['postId'] ?? 0;
        $isPinned = $input['isPinned'] ?? false;

        if (!$postId) {
            echo json_encode(['success' => false, 'error' => '参数不完整']);
            return;
        }

        $stmt = $pdo->prepare("UPDATE posts SET is_pinned = ? WHERE id = ?");
        $result = $stmt->execute([$isPinned ? 1 : 0, $postId]);

        if ($result) {
            $message = $isPinned ? '帖子已置顶' : '帖子已取消置顶';
            echo json_encode(['success' => true, 'message' => $message]);
        } else {
            echo json_encode(['success' => false, 'error' => '操作失败']);
        }
    } catch(PDOException $e) {
        echo json_encode(['success' => false, 'error' => '置顶操作失败: ' . $e->getMessage()]);
    }
}
?>
