<?php
/**
 * 简单的MySQL连接测试
 */

echo "MySQL连接测试\n";
echo "================\n";

$host = 'localhost';
$username = 'root';
$password = '';
$ports = [3306, 3307];

foreach ($ports as $port) {
    echo "\n测试端口 {$port}:\n";
    
    try {
        $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✅ 连接成功！\n";
        
        // 获取版本
        $version = $pdo->query('SELECT VERSION() as v')->fetch();
        echo "📋 MySQL版本: {$version['v']}\n";
        
        // 创建数据库
        $dbName = 'bitbear_system';
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ 数据库 '{$dbName}' 创建成功\n";
        
        // 连接到数据库
        $dsn = "mysql:host={$host};port={$port};dbname={$dbName};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✅ 连接到数据库成功\n";
        echo "🎉 端口 {$port} 可以正常使用！\n";
        
        // 保存工作配置
        file_put_contents('db_config.txt', "工作端口: {$port}\n主机: {$host}\n用户名: {$username}\n密码: " . (empty($password) ? '(空)' : $password));
        
        break;
        
    } catch (PDOException $e) {
        echo "❌ 连接失败: " . $e->getMessage() . "\n";
    }
}

echo "\n测试完成！\n";
?>
