<?php
// 直接使用PDO连接数据库
$host = 'localhost';
$port = '3307';
$dbname = 'bitbear_system';
$username = 'root';
$password = '123456';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>检查分类表数据</h2>";

    // 查询所有分类
    $stmt = $pdo->prepare("SELECT * FROM post_categories ORDER BY id DESC LIMIT 10");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($categories)) {
        echo "<p>暂无分类数据</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>名称</th><th>Slug</th><th>描述</th><th>颜色</th><th>图标</th><th>排序</th><th>状态</th><th>创建时间</th></tr>";
        
        foreach ($categories as $category) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($category['id']) . "</td>";
            echo "<td>" . htmlspecialchars($category['name']) . "</td>";
            echo "<td>" . htmlspecialchars($category['slug']) . "</td>";
            echo "<td>" . htmlspecialchars($category['description']) . "</td>";
            echo "<td>" . htmlspecialchars($category['color']) . "</td>";
            echo "<td>" . htmlspecialchars($category['icon']) . "</td>";
            echo "<td>" . htmlspecialchars($category['sort_order']) . "</td>";
            echo "<td>" . ($category['is_active'] ? '激活' : '禁用') . "</td>";
            echo "<td>" . htmlspecialchars($category['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<br><p>总分类数量: " . count($categories) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
