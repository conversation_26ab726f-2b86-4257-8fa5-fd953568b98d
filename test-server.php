<?php
echo "<h1>🎉 比特熊智慧系统本地服务器运行成功！</h1>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP版本: " . phpversion() . "</p>";
echo "<p>服务器信息: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

echo "<h2>📋 可用页面:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>🏠 主页 (index.php)</a></li>";
echo "<li><a href='index-standalone.html'>🌟 静态主页 (index-standalone.html)</a></li>";
echo "<li><a href='admin-login.php'>🔐 管理后台登录 (admin-login.php)</a></li>";
echo "<li><a href='admin-dashboard.php'>📊 管理后台 (admin-dashboard.php)</a></li>";
echo "<li><a href='community.php'>👥 社区页面 (community.php)</a></li>";
echo "</ul>";

echo "<h2>🔧 数据库状态:</h2>";
try {
    require_once 'config/database.php';
    $db = db();
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 测试查询
    $result = $db->query("SELECT COUNT(*) as count FROM users");
    $userCount = $result->fetch()['count'] ?? 0;
    echo "<p>用户数量: {$userCount}</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<h2>🚀 快速启动:</h2>";
echo "<p>服务器已在以下端口运行:</p>";
echo "<ul>";
echo "<li>http://localhost:8000</li>";
echo "<li>http://localhost:8002</li>";
echo "</ul>";
?>
