<?php
/**
 * 快速修复云服务器注册问题
 * 自动检测并修复常见的注册问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>快速修复云服务器注册问题</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
    .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
    .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #e0e0e0; }
    .success { color: green; background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #28a745; }
    .error { color: red; background: #ffe8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #dc3545; }
    .info { color: blue; background: #e8f0ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #17a2b8; }
    .warning { color: orange; background: #fff8e8; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #ffc107; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 6px; margin: 5px; transition: background 0.3s; }
    .btn:hover { background: #0056b3; }
    .btn.success { background: #28a745; }
    .btn.success:hover { background: #1e7e34; }
    .step { margin: 20px 0; }
</style></head><body>";

echo "<div class='container'>";
echo "<div class='header'>";
echo "<h1>🚀 快速修复云服务器注册问题</h1>";
echo "<p>自动检测并修复常见的注册问题</p>";
echo "</div>";

$fixedIssues = [];
$errors = [];

// 1. 检查并修复数据库连接
echo "<div class='step'>";
echo "<h3>🔧 步骤1: 检查数据库连接</h3>";

try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    
    echo "<div class='success'>✅ 数据库连接正常</div>";
    
    // 测试查询
    $result = $connection->query("SELECT 1")->fetch();
    if ($result) {
        echo "<div class='success'>✅ 数据库查询测试通过</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</div>";
    $errors[] = "数据库连接失败";
}
echo "</div>";

// 2. 检查并创建必要的表
if (isset($db)) {
    echo "<div class='step'>";
    echo "<h3>🔧 步骤2: 检查并创建必要的表</h3>";
    
    try {
        // 检查user_roles表
        $result = $db->query("SHOW TABLES LIKE 'user_roles'")->fetch();
        if (!$result) {
            echo "<div class='warning'>⚠️ user_roles表不存在，正在创建...</div>";
            
            $db->exec("
                CREATE TABLE user_roles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    role_code VARCHAR(50) NOT NULL UNIQUE,
                    role_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    permissions TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            // 插入默认角色
            $db->exec("
                INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
            ");
            
            echo "<div class='success'>✅ user_roles表创建成功</div>";
            $fixedIssues[] = "创建user_roles表";
        } else {
            echo "<div class='success'>✅ user_roles表已存在</div>";
            
            // 检查是否有数据
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM user_roles")['count'];
            if ($count == 0) {
                echo "<div class='warning'>⚠️ user_roles表为空，正在插入默认数据...</div>";
                $db->exec("
                    INSERT INTO user_roles (role_code, role_name, description, permissions) VALUES
                    ('super_admin', '超级管理员', '拥有系统所有权限', 'all'),
                    ('admin', '管理员', '拥有大部分管理权限', 'dashboard,page_designer,content_management'),
                    ('user', '普通用户', '基础用户权限', 'dashboard,personal_settings')
                ");
                echo "<div class='success'>✅ 默认角色数据插入成功</div>";
                $fixedIssues[] = "插入默认角色数据";
            }
        }
        
        // 检查users表
        $result = $db->query("SHOW TABLES LIKE 'users'")->fetch();
        if (!$result) {
            echo "<div class='warning'>⚠️ users表不存在，正在创建...</div>";
            
            $db->exec("
                CREATE TABLE users (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    email VARCHAR(100) NOT NULL UNIQUE,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    avatar VARCHAR(255),
                    role_id INT DEFAULT 3,
                    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                    last_login TIMESTAMP NULL,
                    login_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE SET NULL
                )
            ");
            
            echo "<div class='success'>✅ users表创建成功</div>";
            $fixedIssues[] = "创建users表";
        } else {
            echo "<div class='success'>✅ users表已存在</div>";
        }
        
        // 检查user_profiles表
        $result = $db->query("SHOW TABLES LIKE 'user_profiles'")->fetch();
        if (!$result) {
            echo "<div class='warning'>⚠️ user_profiles表不存在，正在创建...</div>";
            
            $db->exec("
                CREATE TABLE user_profiles (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    user_id INT NOT NULL,
                    nickname VARCHAR(100),
                    bio TEXT,
                    signature VARCHAR(500),
                    avatar_url VARCHAR(255),
                    location VARCHAR(100),
                    website VARCHAR(255),
                    social_links JSON,
                    post_count INT DEFAULT 0,
                    comment_count INT DEFAULT 0,
                    like_received_count INT DEFAULT 0,
                    reputation_score INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_id (user_id)
                )
            ");
            
            echo "<div class='success'>✅ user_profiles表创建成功</div>";
            $fixedIssues[] = "创建user_profiles表";
        } else {
            echo "<div class='success'>✅ user_profiles表已存在</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ 表结构检查/创建失败: " . $e->getMessage() . "</div>";
        $errors[] = "表结构问题";
    }
    echo "</div>";
}

// 3. 检查并创建上传目录
echo "<div class='step'>";
echo "<h3>🔧 步骤3: 检查并创建上传目录</h3>";

$uploadDirs = [
    'uploads/',
    'uploads/avatars/',
    'uploads/temp/'
];

foreach ($uploadDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<div class='success'>✅ 创建目录: {$dir}</div>";
            $fixedIssues[] = "创建目录 {$dir}";
        } else {
            echo "<div class='error'>❌ 创建目录失败: {$dir}</div>";
            $errors[] = "目录创建失败: {$dir}";
        }
    } else {
        echo "<div class='success'>✅ 目录已存在: {$dir}</div>";
    }
    
    // 设置目录权限
    if (is_dir($dir)) {
        chmod($dir, 0755);
    }
}
echo "</div>";

// 4. 测试注册功能
if (isset($db) && empty($errors)) {
    echo "<div class='step'>";
    echo "<h3>🔧 步骤4: 测试注册功能</h3>";
    
    try {
        $testData = [
            'username' => 'quicktest_' . time(),
            'email' => 'quicktest_' . time() . '@example.com',
            'nickname' => '快速测试用户',
            'password' => 'test123456'
        ];
        
        echo "<div class='info'>正在测试注册功能...</div>";
        
        // 开始事务
        $db->exec("BEGIN");
        
        // 获取普通用户角色ID
        $userRole = $db->fetchOne("SELECT id FROM user_roles WHERE role_code = 'user'");
        $roleId = $userRole ? $userRole['id'] : 3;
        
        // 创建用户记录
        $passwordHash = password_hash($testData['password'], PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
        
        $stmt = $db->getConnection()->prepare($sql);
        $result = $stmt->execute([
            $testData['username'],
            $testData['email'],
            $passwordHash,
            $testData['nickname'],
            $roleId
        ]);
        
        if ($result) {
            $userId = $db->getConnection()->lastInsertId();
            echo "<div class='success'>✅ 用户创建成功，ID: {$userId}</div>";
            
            // 创建用户资料
            $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                           VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
            
            $profileStmt = $db->getConnection()->prepare($profileSql);
            $profileResult = $profileStmt->execute([
                $userId,
                $testData['nickname'],
                'assets/images/default-avatar.png'
            ]);
            
            if ($profileResult) {
                echo "<div class='success'>✅ 用户资料创建成功</div>";
                
                // 清理测试数据
                $db->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                $db->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
                echo "<div class='success'>✅ 测试数据清理完成</div>";
                
                echo "<div class='success'><strong>🎉 注册功能测试完全成功！</strong></div>";
                $fixedIssues[] = "注册功能测试通过";
                
            } else {
                echo "<div class='error'>❌ 用户资料创建失败</div>";
                $errors[] = "用户资料创建失败";
            }
        } else {
            echo "<div class='error'>❌ 用户创建失败</div>";
            $errors[] = "用户创建失败";
        }
        
        // 提交事务
        $db->exec("COMMIT");
        
    } catch (Exception $e) {
        $db->exec("ROLLBACK");
        echo "<div class='error'>❌ 注册功能测试失败: " . $e->getMessage() . "</div>";
        $errors[] = "注册功能测试失败";
    }
    echo "</div>";
}

// 5. 显示修复总结
echo "<div class='step'>";
if (!empty($fixedIssues)) {
    echo "<div class='success'>";
    echo "<h3>🎯 修复总结</h3>";
    echo "<p>已完成以下修复操作：</p>";
    echo "<ul>";
    foreach ($fixedIssues as $issue) {
        echo "<li>{$issue}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div class='error'>";
    echo "<h3>⚠️ 仍存在的问题</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>{$error}</li>";
    }
    echo "</ul>";
    echo "<p>请手动检查这些问题或联系技术支持。</p>";
    echo "</div>";
} else {
    echo "<div class='success'>";
    echo "<h3>🎉 修复完成</h3>";
    echo "<p><strong>所有检查项目都已通过，注册功能应该可以正常使用了！</strong></p>";
    echo "</div>";
}
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' class='btn success'>立即测试注册</a>";
echo "<a href='test_register_api_direct.php' class='btn'>API测试</a>";
echo "<a href='云服务器注册问题诊断增强版.php' class='btn'>详细诊断</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
