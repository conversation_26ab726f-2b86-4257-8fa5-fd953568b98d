<?php
/**
 * 快速诊断脚本 - 一键检测注册问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 输出JSON格式的诊断结果
header('Content-Type: application/json; charset=utf-8');

$diagnosis = [
    'success' => true,
    'environment' => 'unknown',
    'database' => ['status' => 'unknown'],
    'tables' => [],
    'permissions' => [],
    'test_registration' => ['status' => 'unknown'],
    'recommendations' => []
];

try {
    // 1. 环境检测
    $isCloudServer = (strpos($_SERVER['SERVER_NAME'] ?? '', 'bitbear.top') !== false ||
                     strpos($_SERVER['HTTP_HOST'] ?? '', 'bitbear.top') !== false ||
                     ($_SERVER['SERVER_ADDR'] ?? '') === '*************');
    
    $diagnosis['environment'] = $isCloudServer ? 'cloud_server' : 'local_development';
    
    // 2. 数据库连接测试
    try {
        require_once 'config/database.php';
        $dbConfig = DatabaseConfig::getInstance();
        $db = $dbConfig->getConnection();
        
        $diagnosis['database'] = [
            'status' => 'connected',
            'info' => $db->query("SELECT DATABASE() as db_name, VERSION() as version")->fetch()
        ];
        
        // 3. 检查必要的表
        $requiredTables = ['user_roles', 'users', 'user_profiles'];
        $existingTables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($requiredTables as $table) {
            $exists = in_array($table, $existingTables);
            $diagnosis['tables'][$table] = [
                'exists' => $exists,
                'status' => $exists ? 'ok' : 'missing'
            ];
            
            if ($exists) {
                // 检查表结构
                $columns = $db->query("SHOW COLUMNS FROM {$table}")->fetchAll(PDO::FETCH_COLUMN);
                $diagnosis['tables'][$table]['columns'] = count($columns);
                
                // 检查数据
                $count = $db->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
                $diagnosis['tables'][$table]['records'] = $count;
            }
        }
        
        // 4. 检查用户角色数据
        if (in_array('user_roles', $existingTables)) {
            $roleCount = $db->query("SELECT COUNT(*) FROM user_roles")->fetchColumn();
            if ($roleCount == 0) {
                $diagnosis['recommendations'][] = '用户角色表为空，需要初始化角色数据';
            }
        }
        
        // 5. 测试注册流程
        try {
            $testUsername = 'diagtest_' . time();
            $testEmail = 'diagtest_' . time() . '@example.com';
            
            $db->exec("BEGIN");
            
            // 获取用户角色ID
            $userRole = $db->query("SELECT id FROM user_roles WHERE role_code = 'user'")->fetch();
            $roleId = $userRole ? $userRole['id'] : 3;
            
            // 测试插入用户
            $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                    VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";
            
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([
                $testUsername,
                $testEmail,
                password_hash('test123', PASSWORD_DEFAULT),
                '诊断测试用户',
                $roleId
            ]);
            
            if ($result) {
                $userId = $db->lastInsertId();
                
                // 测试插入用户资料
                if (in_array('user_profiles', $existingTables)) {
                    $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                                   VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
                    
                    $profileStmt = $db->prepare($profileSql);
                    $profileResult = $profileStmt->execute([
                        $userId,
                        '诊断测试用户',
                        'assets/images/default-avatar.png'
                    ]);
                    
                    if ($profileResult) {
                        $diagnosis['test_registration'] = [
                            'status' => 'success',
                            'message' => '注册流程测试成功'
                        ];
                    } else {
                        $diagnosis['test_registration'] = [
                            'status' => 'profile_failed',
                            'message' => '用户创建成功但资料创建失败'
                        ];
                    }
                } else {
                    $diagnosis['test_registration'] = [
                        'status' => 'partial_success',
                        'message' => '用户创建成功但缺少用户资料表'
                    ];
                }
                
                // 清理测试数据
                $db->prepare("DELETE FROM user_profiles WHERE user_id = ?")->execute([$userId]);
                $db->prepare("DELETE FROM users WHERE id = ?")->execute([$userId]);
            }
            
            $db->exec("COMMIT");
            
        } catch (Exception $e) {
            $db->exec("ROLLBACK");
            $diagnosis['test_registration'] = [
                'status' => 'failed',
                'message' => $e->getMessage(),
                'error_type' => 'database_error'
            ];
        }
        
    } catch (Exception $e) {
        $diagnosis['database'] = [
            'status' => 'failed',
            'error' => $e->getMessage()
        ];
        $diagnosis['success'] = false;
    }
    
    // 6. 检查目录权限
    $directories = ['uploads/', 'uploads/avatars/', 'uploads/temp/'];
    foreach ($directories as $dir) {
        $status = 'missing';
        if (is_dir($dir)) {
            $status = is_writable($dir) ? 'writable' : 'not_writable';
        }
        $diagnosis['permissions'][$dir] = $status;
    }
    
    // 7. 生成建议
    if ($diagnosis['database']['status'] === 'failed') {
        $diagnosis['recommendations'][] = '数据库连接失败，请检查数据库配置';
    }
    
    foreach ($diagnosis['tables'] as $table => $info) {
        if (!$info['exists']) {
            $diagnosis['recommendations'][] = "缺少数据表: {$table}，需要运行数据库初始化";
        }
    }
    
    foreach ($diagnosis['permissions'] as $dir => $status) {
        if ($status === 'missing') {
            $diagnosis['recommendations'][] = "缺少目录: {$dir}，需要创建";
        } elseif ($status === 'not_writable') {
            $diagnosis['recommendations'][] = "目录不可写: {$dir}，需要修改权限";
        }
    }
    
    if ($diagnosis['test_registration']['status'] === 'failed') {
        $diagnosis['recommendations'][] = '注册流程测试失败: ' . $diagnosis['test_registration']['message'];
    }
    
    if (empty($diagnosis['recommendations'])) {
        $diagnosis['recommendations'][] = '系统检查正常，注册功能应该可以正常工作';
    }
    
} catch (Exception $e) {
    $diagnosis['success'] = false;
    $diagnosis['error'] = $e->getMessage();
    $diagnosis['recommendations'][] = '诊断过程出错: ' . $e->getMessage();
}

echo json_encode($diagnosis, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
