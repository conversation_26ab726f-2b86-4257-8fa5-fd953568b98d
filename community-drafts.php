<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'includes/time_helper.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

if (!$currentUser) {
    header('Location: login.php');
    exit;
}

// 获取分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    $db = db();
    
    // 获取草稿和被驳回的帖子
    $sql = "SELECT p.*, pc.name as category_name, pc.color as category_color,
                   u.username as rejected_by_username
            FROM posts p
            LEFT JOIN post_categories pc ON p.category_id = pc.id
            LEFT JOIN users u ON p.rejected_by = u.id
            WHERE p.user_id = ? AND (p.status = 'draft' OR p.status = 'rejected')
            ORDER BY p.updated_at DESC
            LIMIT ? OFFSET ?";

    $drafts = $db->fetchAll($sql, [$currentUser['id'], $limit, $offset]);
    
    // 获取总数
    $countSql = "SELECT COUNT(*) as total FROM posts WHERE user_id = ? AND (status = 'draft' OR status = 'rejected')";
    $totalDrafts = $db->fetchOne($countSql, [$currentUser['id']])['total'];
    $totalPages = ceil($totalDrafts / $limit);
    
    // 获取分类列表
    $categories = $db->fetchAll("SELECT * FROM post_categories WHERE is_active = 1 ORDER BY sort_order");
    
} catch (Exception $e) {
    error_log("草稿页面错误: " . $e->getMessage());
    $drafts = [];
    $totalDrafts = 0;
    $totalPages = 1;
    $categories = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的草稿 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .draft-status {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .draft-status.draft {
            background: #fef3c7;
            color: #d97706;
        }
        
        .draft-status.rejected {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .rejection-reason {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            font-size: 14px;
            color: #991b1b;
        }
        
        .rejection-reason-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .rejection-text {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .rejection-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #6b7280;
        }

        .rejected-by, .rejected-time {
            display: flex;
            align-items: center;
        }
        
        .draft-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d1d5db;
        }
        
        .empty-state h3 {
            margin-bottom: 8px;
            color: #374151;
        }

        /* 页面头部美化 */
        .page-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .header-info h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header-info p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        /* 用户信息区域美化 */
        .user-profile {
            position: relative;
        }

        /* 导航用户区域对齐 */
        .nav-user {
            display: flex;
            align-items: center;
        }

        /* 用户卡片样式 */
        .user-profile-card {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            margin-top: -2px; /* 微调向上移动 */
        }

        .user-profile-card:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .user-avatar-small {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .user-name-small {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            white-space: nowrap;
        }



        /* 草稿卡片美化 */
        .post-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15) !important;
        }

        .post-title {
            color: #1f2937 !important;
            font-weight: 600;
        }

        .post-content {
            color: #4b5563 !important;
            line-height: 1.6;
        }

        .post-meta {
            color: #6b7280 !important;
        }

        /* 按钮美化 */
        .btn {
            transition: all 0.2s ease;
            border-radius: 8px;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-outline {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* 空状态美化 */
        .empty-state {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 60px 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d1d5db;
            opacity: 0.7;
        }

        /* 导航栏图标样式 */
        .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .header-info h1 {
                font-size: 2rem;
            }

            .header-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="user-profile.php" class="nav-link">
                    <i class="fas fa-user"></i> 个人资料
                </a>
                <a href="community.php" class="nav-link">
                    <i class="fas fa-comments"></i> 社区
                </a>
                <a href="notifications.php" class="nav-link">
                    <i class="fas fa-bell"></i> 通知中心
                </a>
                <?php if ($auth->hasRole('admin') || $auth->hasRole('super_admin')): ?>
                    <a href="admin" class="nav-link">
                        <i class="fas fa-cog"></i> 管理后台
                    </a>
                <?php endif; ?>
            </div>
            
            <div class="nav-user">
                <?php if ($currentUser): ?>
                    <div class="user-profile-card">
                        <img src="<?php echo htmlspecialchars($currentUser['avatar'] ?? 'assets/images/default-avatar.png'); ?>"
                             alt="用户头像" class="user-avatar-small">
                        <span class="user-name-small"><?php echo htmlspecialchars($currentUser['nickname'] ?? $currentUser['username']); ?></span>
                    </div>
                <?php else: ?>
                    <div class="auth-buttons">
                        <a href="login.php" class="btn btn-outline">登录</a>
                        <a href="register.php" class="btn btn-primary">注册</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-info">
                    <h1>我的草稿</h1>
                    <p>管理您的草稿帖子和被驳回的帖子</p>
                </div>
                
                <div class="header-actions">
                    <a href="community.php" class="btn btn-outline btn-large">
                        <i class="fas fa-arrow-left"></i> 返回社区
                    </a>
                    <a href="community-post.php" class="btn btn-primary btn-large">
                        <i class="fas fa-plus"></i> 发布新帖
                    </a>
                </div>
            </div>
        </div>

        <!-- 草稿列表 -->
        <div class="posts-container">
            <?php if (empty($drafts)): ?>
                <div class="empty-state">
                    <i class="fas fa-file-alt"></i>
                    <h3>暂无草稿</h3>
                    <p>您还没有保存任何草稿或被驳回的帖子</p>
                    <a href="community-post.php" class="btn btn-primary" style="margin-top: 16px;">
                        <i class="fas fa-plus"></i> 创建新帖
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($drafts as $draft): ?>
                    <article class="post-card">
                        <div class="post-header">
                            <div class="post-meta">
                                <span class="draft-status <?php echo $draft['status']; ?>">
                                    <i class="fas <?php echo $draft['status'] === 'draft' ? 'fa-file-alt' : 'fa-times-circle'; ?>"></i>
                                    <?php echo $draft['status'] === 'draft' ? '草稿' : '已驳回'; ?>
                                </span>
                                
                                <?php if ($draft['category_name']): ?>
                                    <span class="category-tag" style="background-color: <?php echo htmlspecialchars($draft['category_color'] ?? '#6b7280'); ?>">
                                        <?php echo htmlspecialchars($draft['category_name']); ?>
                                    </span>
                                <?php endif; ?>
                                
                                <span class="post-time">
                                    <?php echo $draft['status'] === 'draft' ? '保存于' : '驳回于'; ?>
                                    <?php echo timeAgo($draft['updated_at']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="post-content">
                            <h2 class="post-title">
                                <?php echo htmlspecialchars($draft['title']); ?>
                            </h2>
                            
                            <div class="post-excerpt">
                                <?php echo htmlspecialchars(mb_substr(strip_tags($draft['content']), 0, 200)) . '...'; ?>
                            </div>
                            
                            <?php if ($draft['status'] === 'rejected'): ?>
                                <div class="rejection-reason">
                                    <div class="rejection-reason-label">驳回信息：</div>
                                    <?php if (!empty($draft['rejection_reason'])): ?>
                                        <div class="rejection-text"><?php echo htmlspecialchars($draft['rejection_reason']); ?></div>
                                    <?php endif; ?>
                                    <div class="rejection-meta">
                                        <?php if (!empty($draft['rejected_by_username'])): ?>
                                            <span class="rejected-by">驳回者：<?php echo htmlspecialchars($draft['rejected_by_username']); ?></span>
                                        <?php endif; ?>
                                        <?php if (!empty($draft['rejected_at'])): ?>
                                            <span class="rejected-time">驳回时间：<?php echo date('Y-m-d H:i', strtotime($draft['rejected_at'])); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="draft-actions">
                                <a href="community-post.php?edit=<?php echo $draft['id']; ?>" class="btn btn-primary btn-small">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                
                                <?php if ($draft['status'] === 'draft'): ?>
                                    <button onclick="publishDraft(<?php echo $draft['id']; ?>)" class="btn btn-success btn-small">
                                        <i class="fas fa-paper-plane"></i> 发布
                                    </button>
                                <?php endif; ?>
                                
                                <button onclick="deleteDraft(<?php echo $draft['id']; ?>)" class="btn btn-danger btn-small">
                                    <i class="fas fa-trash"></i> 删除
                                </button>
                            </div>
                        </div>
                    </article>
                <?php endforeach; ?>
                
                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1; ?>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </a>
                        <?php endif; ?>
                        
                        <span class="pagination-info">
                            第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页
                        </span>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="?page=<?php echo $page + 1; ?>" class="pagination-btn">
                                下一页 <i class="fas fa-chevron-right"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 发布草稿
        function publishDraft(draftId) {
            if (confirm('确定要发布这篇草稿吗？')) {
                fetch('api/community-drafts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'publish',
                        draft_id: draftId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('草稿发布成功！');
                        location.reload();
                    } else {
                        alert('发布失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('发布失败:', error);
                    alert('发布失败，请稍后重试');
                });
            }
        }
        
        // 删除草稿
        function deleteDraft(draftId) {
            if (confirm('确定要删除这篇草稿吗？此操作不可恢复。')) {
                fetch('api/community-drafts.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        draft_id: draftId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('草稿删除成功！');
                        location.reload();
                    } else {
                        alert('删除失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('删除失败:', error);
                    alert('删除失败，请稍后重试');
                });
            }
        }
        
        // 加载草稿数量
        function loadDraftCount() {
            fetch('api/community-drafts.php?action=count')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.count > 0) {
                        const countElement = document.getElementById('draftCount');
                        if (countElement) {
                            countElement.textContent = data.count;
                            countElement.style.display = 'flex';
                        }
                    }
                })
                .catch(error => {
                    console.error('加载草稿数量失败:', error);
                });
        }
        
        // 页面加载完成后加载草稿数量
        document.addEventListener('DOMContentLoaded', function() {
            loadDraftCount();
        });
    </script>
</body>
</html>
