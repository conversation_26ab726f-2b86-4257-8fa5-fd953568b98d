<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单按钮测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .results { margin-top: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; min-height: 200px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单按钮测试</h1>
        
        <h2>测试1: 基本onclick功能</h2>
        <button onclick="testBasicClick()">测试基本点击</button>
        
        <h2>测试2: 模拟admin-dashboard按钮</h2>
        <button onclick="window.showAddCategoryModal()">模拟添加分类按钮</button>
        
        <h2>测试3: 直接在admin页面执行</h2>
        <button onclick="executeInAdminPage()">在admin页面执行代码</button>
        
        <div class="results" id="results"></div>
    </div>

    <script>
        function log(message, type = '') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
        }

        function testBasicClick() {
            log('✅ 基本点击功能正常工作！', 'success');
        }

        // 复制admin-dashboard.php中的函数定义
        window.showAddCategoryModal = function() {
            log('🚀 showAddCategoryModal函数被调用', 'success');
            
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                background: rgba(0,0,0,0.5); display: flex; align-items: center; 
                justify-content: center; z-index: 1000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 500px; width: 90%;">
                    <h3>添加分类</h3>
                    <p>✅ 模态框功能正常工作！</p>
                    <button onclick="this.closest('div').parentElement.remove()" 
                            style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            `;
            
            document.body.appendChild(modal);
            log('✅ 模态框已创建并显示', 'success');
        };

        function executeInAdminPage() {
            log('🔗 正在打开admin-dashboard.php...', '');
            
            // 打开admin页面
            const adminWindow = window.open('/admin-dashboard.php', 'adminTest', 'width=1200,height=800');
            
            adminWindow.onload = function() {
                setTimeout(() => {
                    try {
                        log('📋 尝试在admin页面中执行代码...', '');
                        
                        // 在admin页面的控制台中执行代码
                        adminWindow.eval(`
                            console.log('🧪 从外部注入的测试代码');
                            
                            // 检查函数是否存在
                            console.log('showAddCategoryModal类型:', typeof window.showAddCategoryModal);
                            console.log('addCategory类型:', typeof window.addCategory);
                            console.log('switchPage类型:', typeof window.switchPage);
                            
                            // 切换到分类管理页面
                            if (typeof window.switchPage === 'function') {
                                window.switchPage('categories-management');
                                console.log('✅ 已切换到分类管理页面');
                                
                                setTimeout(() => {
                                    // 查找按钮
                                    const btn = document.querySelector('button[onclick*="showAddCategoryModal"]');
                                    if (btn) {
                                        console.log('✅ 找到按钮:', btn);
                                        console.log('按钮onclick:', btn.getAttribute('onclick'));
                                        
                                        // 直接调用函数
                                        if (typeof window.showAddCategoryModal === 'function') {
                                            console.log('🚀 直接调用showAddCategoryModal...');
                                            window.showAddCategoryModal();
                                        } else {
                                            console.error('❌ showAddCategoryModal函数不存在');
                                        }
                                    } else {
                                        console.error('❌ 找不到按钮');
                                    }
                                }, 1000);
                            } else {
                                console.error('❌ switchPage函数不存在');
                            }
                        `);
                        
                        log('✅ 代码已在admin页面中执行，请查看admin页面的控制台', 'success');
                        
                    } catch (error) {
                        log(`❌ 在admin页面执行代码失败: ${error.message}`, 'error');
                    }
                }, 3000);
            };
        }

        // 页面加载时显示说明
        window.onload = function() {
            log('🎯 简单按钮测试工具已加载');
            log('💡 请依次测试各个按钮功能');
        };
    </script>
</body>
</html>
