# Local Network Environment Diagnosis Tool
# Comprehensive check for device-specific network issues

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Local Network Environment Diagnosis" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$serverIP = "*************"
$serverPort = 8888
$targetPath = "/tencentcloud"
$fullUrl = "http://$serverIP`:$serverPort$targetPath"

# 1. Basic network connectivity check
Write-Host "1. Basic network connectivity check..." -ForegroundColor Yellow
Write-Host ""

# Ping test
Write-Host "Ping test:" -ForegroundColor Cyan
try {
    $pingResults = Test-Connection -ComputerName $serverIP -Count 4 -ErrorAction Stop
    $successCount = ($pingResults | Where-Object { $_.StatusCode -eq 0 }).Count
    $avgTime = ($pingResults | Where-Object { $_.StatusCode -eq 0 } | Measure-Object -Property ResponseTime -Average).Average

    Write-Host "  Success rate: $successCount/4 ($([math]::Round($successCount/4*100, 1))%)" -ForegroundColor $(if($successCount -eq 4){"Green"}elseif($successCount -gt 0){"Yellow"}else{"Red"})
    if ($avgTime) {
        Write-Host "  Average latency: $([math]::Round($avgTime, 1))ms" -ForegroundColor $(if($avgTime -lt 100){"Green"}elseif($avgTime -lt 300){"Yellow"}else{"Red"})
    }
} catch {
    Write-Host "  Ping failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Port connectivity test
Write-Host ""
Write-Host "Port connectivity test:" -ForegroundColor Cyan
try {
    $portTest = Test-NetConnection -ComputerName $serverIP -Port $serverPort -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host "  Port $serverPort is reachable" -ForegroundColor Green
        Write-Host "  Connection time: $($portTest.ResponseTime)ms" -ForegroundColor Green
    } else {
        Write-Host "  Port $serverPort is not reachable" -ForegroundColor Red
    }
} catch {
    Write-Host "  Port test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 2. Network adapters and routing check
Write-Host "2. Network adapters and routing check..." -ForegroundColor Yellow
Write-Host ""

# Active network adapters
Write-Host "Active network adapters:" -ForegroundColor Cyan
$adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
foreach ($adapter in $adapters) {
    $ipConfig = Get-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($ipConfig) {
        Write-Host "  $($adapter.Name): $($ipConfig.IPAddress)" -ForegroundColor White
        Write-Host "    Description: $($adapter.InterfaceDescription)" -ForegroundColor Gray
        Write-Host "    Link speed: $($adapter.LinkSpeed)" -ForegroundColor Gray

        # Check if virtual adapter
        if ($adapter.InterfaceDescription -like "*VMware*" -or $adapter.InterfaceDescription -like "*Virtual*") {
            Write-Host "    Virtual network adapter" -ForegroundColor Yellow
        }
    }
}

# Default gateway
Write-Host ""
Write-Host "Default gateway:" -ForegroundColor Cyan
$defaultRoutes = Get-NetRoute -DestinationPrefix "0.0.0.0/0" | Sort-Object RouteMetric
foreach ($route in $defaultRoutes) {
    $adapter = Get-NetAdapter -InterfaceIndex $route.InterfaceIndex -ErrorAction SilentlyContinue
    if ($adapter) {
        Write-Host "  Gateway: $($route.NextHop) (via $($adapter.Name), metric: $($route.RouteMetric))" -ForegroundColor White
    }
}

Write-Host ""

# 3. DNS configuration check
Write-Host "3. DNS configuration check..." -ForegroundColor Yellow
Write-Host ""

# Current DNS servers
Write-Host "DNS server configuration:" -ForegroundColor Cyan
$dnsServers = Get-DnsClientServerAddress | Where-Object { $_.AddressFamily -eq 2 -and $_.ServerAddresses.Count -gt 0 }
foreach ($dns in $dnsServers) {
    $adapter = Get-NetAdapter -InterfaceIndex $dns.InterfaceIndex -ErrorAction SilentlyContinue
    if ($adapter -and $adapter.Status -eq "Up") {
        Write-Host "  $($adapter.Name): $($dns.ServerAddresses -join ', ')" -ForegroundColor White
    }
}

# DNS resolution test
Write-Host ""
Write-Host "DNS resolution test:" -ForegroundColor Cyan
$testDomains = @("www.baidu.com", "www.google.com", "github.com")
foreach ($domain in $testDomains) {
    try {
        $startTime = Get-Date
        $result = Resolve-DnsName -Name $domain -ErrorAction Stop
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  $domain`: Success $([math]::Round($duration, 1))ms" -ForegroundColor Green
    } catch {
        Write-Host "  $domain`: Resolution failed" -ForegroundColor Red
    }
}

Write-Host ""

# 4. Firewall and security software check
Write-Host "4. Firewall and security software check..." -ForegroundColor Yellow
Write-Host ""

# Windows Firewall status
Write-Host "Windows Firewall status:" -ForegroundColor Cyan
$firewallProfiles = Get-NetFirewallProfile
foreach ($profile in $firewallProfiles) {
    $status = if ($profile.Enabled) { "Enabled" } else { "Disabled" }
    $color = if ($profile.Enabled) { "Yellow" } else { "Green" }
    Write-Host "  $($profile.Name): $status" -ForegroundColor $color
}

Write-Host ""

# 5. Proxy settings check
Write-Host "5. Proxy settings check..." -ForegroundColor Yellow
Write-Host ""

# System proxy settings
Write-Host "System proxy settings:" -ForegroundColor Cyan
try {
    $proxySettings = Get-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings" -ErrorAction Stop
    if ($proxySettings.ProxyEnable -eq 1) {
        Write-Host "  Proxy enabled: $($proxySettings.ProxyServer)" -ForegroundColor Yellow
        Write-Host "  Proxy override: $($proxySettings.ProxyOverride)" -ForegroundColor Gray
    } else {
        Write-Host "  No system proxy enabled" -ForegroundColor Green
    }
} catch {
    Write-Host "  Cannot read proxy settings" -ForegroundColor Red
}

# 环境变量代理
Write-Host ""
Write-Host "环境变量代理:" -ForegroundColor Cyan
$proxyVars = @("HTTP_PROXY", "HTTPS_PROXY", "http_proxy", "https_proxy")
$foundProxy = $false
foreach ($var in $proxyVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ($value) {
        Write-Host "  $var`: $value" -ForegroundColor Yellow
        $foundProxy = $true
    }
}
if (-not $foundProxy) {
    Write-Host "  ✅ 未发现环境变量代理" -ForegroundColor Green
}

Write-Host ""

# 6. HTTP请求详细测试
Write-Host "6. HTTP请求详细测试..." -ForegroundColor Yellow
Write-Host ""

# 不同方法的HTTP测试
$testMethods = @(
    @{Name="PowerShell Invoke-WebRequest"; Method="PowerShell"}
    @{Name="System.Net.WebClient"; Method="WebClient"}
    @{Name="System.Net.HttpClient"; Method="HttpClient"}
)

foreach ($test in $testMethods) {
    Write-Host "$($test.Name):" -ForegroundColor Cyan
    try {
        $startTime = Get-Date
        
        switch ($test.Method) {
            "PowerShell" {
                $response = Invoke-WebRequest -Uri $fullUrl -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
                $statusCode = $response.StatusCode
                $contentLength = $response.Content.Length
            }
            "WebClient" {
                $webClient = New-Object System.Net.WebClient
                $webClient.Proxy = [System.Net.WebRequest]::DefaultWebProxy
                $webClient.Proxy.Credentials = [System.Net.CredentialCache]::DefaultNetworkCredentials
                $content = $webClient.DownloadString($fullUrl)
                $statusCode = 200
                $contentLength = $content.Length
                $webClient.Dispose()
            }
            "HttpClient" {
                $httpClient = New-Object System.Net.Http.HttpClient
                $httpClient.Timeout = [TimeSpan]::FromSeconds(10)
                $response = $httpClient.GetAsync($fullUrl).Result
                $statusCode = [int]$response.StatusCode
                $content = $response.Content.ReadAsStringAsync().Result
                $contentLength = $content.Length
                $httpClient.Dispose()
            }
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  ✅ 状态码: $statusCode, 内容长度: $contentLength, 耗时: $([math]::Round($duration, 1))ms" -ForegroundColor Green
        
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        Write-Host "  ❌ 失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "  耗时: $([math]::Round($duration, 1))ms" -ForegroundColor Gray
    }
}

Write-Host ""

# 7. 网络路由跟踪
Write-Host "7. 网络路由跟踪..." -ForegroundColor Yellow
Write-Host ""

Write-Host "路由跟踪到 $serverIP`:" -ForegroundColor Cyan
try {
    $traceResult = Test-NetConnection -ComputerName $serverIP -TraceRoute -WarningAction SilentlyContinue
    if ($traceResult.TraceRoute) {
        for ($i = 0; $i -lt $traceResult.TraceRoute.Count; $i++) {
            $hop = $traceResult.TraceRoute[$i]
            Write-Host "  跳 $($i+1): $hop" -ForegroundColor White
            
            # 检查是否有异常跳点
            if ($hop -like "192.168.*" -and $i -gt 2) {
                Write-Host "    ⚠️  可能的内网路由问题" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host "  ❌ 路由跟踪失败" -ForegroundColor Red
    }
} catch {
    Write-Host "  ❌ 路由跟踪错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 8. 生成诊断报告
Write-Host "8. 生成诊断报告..." -ForegroundColor Yellow
Write-Host ""

$reportPath = "network-diagnosis-report.txt"
$reportContent = @"
网络诊断报告
生成时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
目标地址: $fullUrl

=== 基础信息 ===
计算机名: $env:COMPUTERNAME
用户名: $env:USERNAME
操作系统: $((Get-WmiObject Win32_OperatingSystem).Caption)
网络适配器数量: $($adapters.Count)

=== 网络配置 ===
"@

# 添加网络适配器信息到报告
foreach ($adapter in $adapters) {
    $ipConfig = Get-NetIPAddress -InterfaceIndex $adapter.InterfaceIndex -AddressFamily IPv4 -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($ipConfig) {
        $reportContent += "`n适配器: $($adapter.Name) - IP: $($ipConfig.IPAddress)"
    }
}

$reportContent | Out-File -FilePath $reportPath -Encoding UTF8
Write-Host "✅ 诊断报告已保存: $reportPath" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "诊断完成" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
