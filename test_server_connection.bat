@echo off
echo ========================================
echo 比特熊智慧系统 - 服务器连接测试
echo ========================================
echo.

echo 服务器信息:
echo IP地址: *************
echo 用户名: root
echo 密码: ZbDX7%=]?H2(LAUz
echo.

echo 正在测试网络连接...
ping -n 4 *************

echo.
echo 正在测试SSH端口(22)...
telnet ************* 22

echo.
echo ========================================
echo 手动连接指南:
echo ========================================
echo.
echo 如果网络连接正常，您可以使用以下方法连接:
echo.
echo 方法1 - 使用Windows内置SSH (推荐):
echo   1. 按Win+R，输入cmd，回车
echo   2. 输入: ssh root@*************
echo   3. 输入密码: ZbDX7%=]?H2(LAUz
echo.
echo 方法2 - 使用Git Bash:
echo   1. 右键桌面，选择"Git Bash Here"
echo   2. 输入: ssh root@*************
echo   3. 输入密码: ZbDX7%=]?H2(LAUz
echo.
echo 方法3 - 使用PuTTY:
echo   1. 下载PuTTY: https://www.putty.org/
echo   2. 运行PuTTY，输入IP: *************
echo   3. 点击Open，输入用户名和密码
echo.

pause
