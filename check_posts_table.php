<?php
require_once 'config/database.php';

try {
    $db = db();
    
    echo "=== 检查posts表结构 ===\n";
    $result = $db->fetchAll('DESCRIBE posts');
    foreach ($result as $row) {
        echo "{$row['Field']} - {$row['Type']} - Key: {$row['Key']} - Extra: {$row['Extra']}\n";
    }
    
    echo "\n=== 检查AUTO_INCREMENT状态 ===\n";
    $status = $db->fetchOne("SHOW TABLE STATUS LIKE 'posts'");
    echo "Auto_increment: " . ($status['Auto_increment'] ?? 'NULL') . "\n";
    
    echo "\n=== 修复AUTO_INCREMENT ===\n";
    $db->query("ALTER TABLE posts AUTO_INCREMENT = 1");
    echo "✓ AUTO_INCREMENT已重置为1\n";
    
    // 再次测试插入
    echo "\n=== 测试插入帖子 ===\n";
    $sql = "INSERT INTO posts (user_id, title, content, status, created_at) VALUES (1, '测试帖子', '测试内容', 'published', NOW())";
    $db->execute($sql);
    $postId = $db->lastInsertId();
    echo "✓ 帖子插入成功，ID: {$postId}\n";
    
    // 验证
    $post = $db->fetchOne("SELECT id, title FROM posts WHERE id = ?", [$postId]);
    if ($post) {
        echo "✓ 验证成功: ID {$post['id']}, 标题: {$post['title']}\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
