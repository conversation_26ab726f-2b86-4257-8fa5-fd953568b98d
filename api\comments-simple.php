<?php
// 简化版评论管理API
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../config/database.php';

session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => '未授权访问']);
    exit;
}

header('Content-Type: application/json');

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    $action = $_GET['action'] ?? '';

    switch ($action) {
        case 'stats':
            handleStats($db);
            break;
        case 'list':
            handleList($db);
            break;
        default:
            echo json_encode(['success' => false, 'error' => '无效的操作']);
    }
} catch (Exception $e) {
    error_log("评论API错误: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => '服务器内部错误: ' . $e->getMessage()]);
}

function handleStats($db) {
    try {
        // 检查comments表是否存在
        $stmt = $db->query("SHOW TABLES LIKE 'comments'");
        if ($stmt->rowCount() == 0) {
            echo json_encode([
                'success' => true,
                'stats' => [
                    'total' => 0,
                    'published' => 0,
                    'hidden' => 0,
                    'reported' => 0
                ]
            ]);
            return;
        }
        
        // 简单的统计查询
        $sql = "SELECT COUNT(*) as total FROM comments";
        $stmt = $db->query($sql);
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 尝试按状态统计
        try {
            $sql = "SELECT 
                        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                        SUM(CASE WHEN status = 'hidden' THEN 1 ELSE 0 END) as hidden
                    FROM comments";
            $stmt = $db->query($sql);
            $statusStats = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            // 如果没有status字段，使用默认值
            $statusStats = ['published' => $total, 'hidden' => 0];
        }
        
        echo json_encode([
            'success' => true,
            'stats' => [
                'total' => intval($total),
                'published' => intval($statusStats['published'] ?? $total),
                'hidden' => intval($statusStats['hidden'] ?? 0),
                'reported' => 0 // 暂时设为0
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception("统计查询失败: " . $e->getMessage());
    }
}

function handleList($db) {
    try {
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = 10;
        $offset = ($page - 1) * $perPage;
        
        // 检查comments表是否存在
        $stmt = $db->query("SHOW TABLES LIKE 'comments'");
        if ($stmt->rowCount() == 0) {
            echo json_encode([
                'success' => true,
                'comments' => [],
                'pagination' => [
                    'currentPage' => 1,
                    'totalPages' => 0,
                    'totalRecords' => 0,
                    'perPage' => $perPage
                ]
            ]);
            return;
        }
        
        // 获取总数
        $countStmt = $db->query("SELECT COUNT(*) as total FROM comments");
        $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        // 获取评论列表（简化版，不JOIN其他表）
        $sql = "SELECT * FROM comments ORDER BY created_at DESC LIMIT $perPage OFFSET $offset";
        $stmt = $db->query($sql);
        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 格式化评论数据
        $formattedComments = [];
        foreach ($comments as $comment) {
            $formattedComments[] = [
                'id' => $comment['id'],
                'content' => $comment['content'] ?? '',
                'status' => $comment['status'] ?? 'published',
                'created_at' => $comment['created_at'] ?? '',
                'user_id' => $comment['user_id'] ?? 0,
                'post_id' => $comment['post_id'] ?? 0,
                'username' => '用户' . ($comment['user_id'] ?? 0), // 简化显示
                'post_title' => '文章' . ($comment['post_id'] ?? 0) // 简化显示
            ];
        }
        
        echo json_encode([
            'success' => true,
            'comments' => $formattedComments,
            'pagination' => [
                'currentPage' => $page,
                'totalPages' => ceil($totalRecords / $perPage),
                'totalRecords' => intval($totalRecords),
                'perPage' => $perPage
            ]
        ]);
        
    } catch (Exception $e) {
        throw new Exception("列表查询失败: " . $e->getMessage());
    }
}
?>
