<?php
require_once 'config/database.php';
require_once 'classes/Auth.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin-login.php');
    exit;
}

$currentUser = $_SESSION['admin_user'] ?? 'Admin';

// 处理管理员操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $commentId = intval($_POST['comment_id'] ?? 0);
    
    try {
        $db = DatabaseConfig::getInstance();
        
        if ($action === 'delete' && $commentId > 0) {
            // 删除评论
            $db->execute("UPDATE comments SET status = 'deleted', updated_at = NOW() WHERE id = ?", [$commentId]);
            $success = '评论已删除';

        } elseif ($action === 'reject' && $commentId > 0) {
            // 打回评论
            $rejectReason = trim($_POST['reject_reason'] ?? '');
            $db->execute("UPDATE comments SET status = 'hidden', updated_at = NOW() WHERE id = ?", [$commentId]);

            // 创建通知给用户
            $comment = $db->fetchOne("SELECT user_id, post_id FROM comments WHERE id = ?", [$commentId]);
            if ($comment) {
                $db->execute("INSERT INTO notifications (user_id, type, title, content, created_at) VALUES (?, 'comment_rejected', '评论被打回', ?, NOW())",
                            [$comment['user_id'], "您的评论因违规被管理员打回。原因：" . $rejectReason]);
            }

            $success = '评论已打回，用户将收到通知';

        } elseif ($action === 'approve' && $commentId > 0) {
            // 批准评论
            $db->execute("UPDATE comments SET status = 'published', updated_at = NOW() WHERE id = ?", [$commentId]);
            $success = '评论已批准';
        }
        
    } catch (Exception $e) {
        $error = '操作失败：' . $e->getMessage();
    }
}

// 分页参数
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// 筛选参数
$status = $_GET['status'] ?? 'all';
$search = trim($_GET['search'] ?? '');

// 构建查询条件
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "c.status = ?";
    $params[] = $status;
}

if (!empty($search)) {
    $whereConditions[] = "(c.content LIKE ? OR u.username LIKE ? OR p.title LIKE ?)";
    $searchTerm = '%' . $search . '%';
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = empty($whereConditions) ? '' : 'WHERE ' . implode(' AND ', $whereConditions);

try {
    $db = DatabaseConfig::getInstance();
    
    // 获取总数
    $totalSql = "SELECT COUNT(*) as total
                 FROM comments c
                 LEFT JOIN users u ON c.user_id = u.id
                 LEFT JOIN posts p ON c.post_id = p.id
                 $whereClause";
    $totalResult = $db->fetchOne($totalSql, $params);
    $total = $totalResult['total'];
    $totalPages = ceil($total / $limit);

    // 获取评论列表
    $sql = "SELECT c.*, u.username, u.full_name,
                   COALESCE(up.nickname, u.full_name, u.username) as nickname,
                   p.title as post_title,
                   (SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = c.id AND type = 'like') as like_count,
                   (SELECT COUNT(*) FROM likes WHERE target_type = 'comment' AND target_id = c.id AND type = 'dislike') as dislike_count,
                   (SELECT COUNT(*) FROM reports WHERE target_type = 'comment' AND target_id = c.id AND status = 'pending') as report_count
            FROM comments c
            LEFT JOIN users u ON c.user_id = u.id
            LEFT JOIN user_profiles up ON u.id = up.user_id
            LEFT JOIN posts p ON c.post_id = p.id
            $whereClause
            ORDER BY c.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    $comments = $db->fetchAll($sql, $params);
    
} catch (Exception $e) {
    $error = '获取评论列表失败：' . $e->getMessage();
    $comments = [];
    $total = 0;
    $totalPages = 0;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评论管理 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .comment-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .comment-user {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .comment-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .comment-status.published { background: #dcfce7; color: #16a34a; }
        .comment-status.pending { background: #fef3c7; color: #d97706; }
        .comment-status.hidden { background: #fee2e2; color: #dc2626; }
        .comment-status.deleted { background: #f3f4f6; color: #6b7280; }
        
        .comment-content {
            margin: 12px 0;
            line-height: 1.6;
        }
        
        .comment-meta {
            display: flex;
            gap: 16px;
            font-size: 14px;
            color: #6b7280;
            margin: 12px 0;
        }
        
        .comment-stats {
            display: flex;
            gap: 16px;
            margin: 12px 0;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
        }
        
        .comment-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .reject-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .reject-modal-content {
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
        }
        
        .reject-modal h3 {
            margin-bottom: 16px;
        }
        
        .reject-modal textarea {
            width: 100%;
            height: 100px;
            margin-bottom: 16px;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            resize: vertical;
        }
        
        .reject-modal-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/admin-sidebar.php'; ?>
        
        <main class="admin-main">
            <div class="admin-header">
                <h1>评论管理</h1>
                <div class="admin-breadcrumb">
                    <a href="admin">管理后台</a>
                    <span>/</span>
                    <span>评论管理</span>
                </div>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <!-- 筛选器 -->
            <div class="filters">
                <form method="GET" class="filter-row">
                    <div class="filter-group">
                        <label>状态：</label>
                        <select name="status" onchange="this.form.submit()">
                            <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>全部</option>
                            <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>已发布</option>
                            <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>待审核</option>
                            <option value="hidden" <?php echo $status === 'hidden' ? 'selected' : ''; ?>>已隐藏</option>
                            <option value="deleted" <?php echo $status === 'deleted' ? 'selected' : ''; ?>>已删除</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>搜索：</label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="搜索评论内容、用户名或帖子标题">
                        <button type="submit" class="btn btn-primary btn-small">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 评论列表 -->
            <div class="comments-list">
                <?php if (empty($comments)): ?>
                    <div class="empty-state">
                        <i class="fas fa-comments"></i>
                        <h3>暂无评论</h3>
                        <p>没有找到符合条件的评论</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($comments as $comment): ?>
                        <div class="comment-card">
                            <div class="comment-header">
                                <div class="comment-user">
                                    <strong><?php echo htmlspecialchars($comment['nickname'] ?: $comment['username']); ?></strong>
                                    <span class="comment-status <?php echo $comment['status']; ?>">
                                        <?php
                                        $statusMap = [
                                            'published' => '已发布',
                                            'pending' => '待审核',
                                            'hidden' => '已隐藏',
                                            'deleted' => '已删除'
                                        ];
                                        echo $statusMap[$comment['status']] ?? $comment['status'];
                                        ?>
                                    </span>
                                </div>
                                <span class="comment-date"><?php echo date('Y-m-d H:i', strtotime($comment['created_at'])); ?></span>
                            </div>
                            
                            <div class="comment-content">
                                <?php echo nl2br(htmlspecialchars($comment['content'])); ?>
                            </div>
                            
                            <div class="comment-meta">
                                <span><i class="fas fa-file-alt"></i> 帖子：
                                    <a href="community-post-detail.php?id=<?php echo $comment['post_id']; ?>" target="_blank">
                                        <?php echo htmlspecialchars($comment['post_title']); ?>
                                    </a>
                                </span>
                                <?php if ($comment['parent_id']): ?>
                                    <span><i class="fas fa-reply"></i> 回复评论</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="comment-stats">
                                <div class="stat-item">
                                    <i class="fas fa-thumbs-up text-green-600"></i>
                                    <span><?php echo $comment['like_count']; ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-thumbs-down text-red-600"></i>
                                    <span><?php echo $comment['dislike_count']; ?></span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-flag text-orange-600"></i>
                                    <span><?php echo $comment['report_count']; ?> 举报</span>
                                </div>
                            </div>
                            

                            
                            <div class="comment-actions">
                                <a href="community-post-detail.php?id=<?php echo $comment['post_id']; ?>#comment-<?php echo $comment['id']; ?>" 
                                   target="_blank" class="btn btn-outline btn-small">
                                    <i class="fas fa-eye"></i> 查看位置
                                </a>
                                
                                <?php if ($comment['status'] === 'pending'): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="approve">
                                        <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                        <button type="submit" class="btn btn-success btn-small">
                                            <i class="fas fa-check"></i> 批准
                                        </button>
                                    </form>
                                <?php endif; ?>

                                <?php if ($comment['status'] === 'published'): ?>
                                    <button onclick="showRejectModal(<?php echo $comment['id']; ?>)" class="btn btn-warning btn-small">
                                        <i class="fas fa-eye-slash"></i> 隐藏
                                    </button>
                                <?php endif; ?>

                                <?php if ($comment['status'] === 'hidden'): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="approve">
                                        <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                        <button type="submit" class="btn btn-success btn-small">
                                            <i class="fas fa-eye"></i> 恢复显示
                                        </button>
                                    </form>
                                <?php endif; ?>
                                
                                <?php if ($comment['status'] !== 'deleted'): ?>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('确定要删除这条评论吗？')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="comment_id" value="<?php echo $comment['id']; ?>">
                                        <button type="submit" class="btn btn-danger btn-small">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>" 
                           class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </a>
                    <?php endif; ?>
                    
                    <span class="pagination-info">
                        第 <?php echo $page; ?> 页，共 <?php echo $totalPages; ?> 页（共 <?php echo $total; ?> 条）
                    </span>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&status=<?php echo urlencode($status); ?>&search=<?php echo urlencode($search); ?>" 
                           class="pagination-btn">
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </main>
    </div>
    
    <!-- 隐藏评论模态框 -->
    <div id="rejectModal" class="reject-modal">
        <div class="reject-modal-content">
            <h3>隐藏评论</h3>
            <form method="POST" id="rejectForm">
                <input type="hidden" name="action" value="reject">
                <input type="hidden" name="comment_id" id="rejectCommentId">

                <label for="rejectReason">隐藏原因：</label>
                <textarea name="reject_reason" id="rejectReason" placeholder="请输入隐藏原因，用户将收到此通知" required></textarea>

                <div class="reject-modal-actions">
                    <button type="button" onclick="hideRejectModal()" class="btn btn-outline">取消</button>
                    <button type="submit" class="btn btn-warning">确认隐藏</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        function showRejectModal(commentId) {
            document.getElementById('rejectCommentId').value = commentId;
            document.getElementById('rejectModal').style.display = 'flex';
        }
        
        function hideRejectModal() {
            document.getElementById('rejectModal').style.display = 'none';
            document.getElementById('rejectReason').value = '';
        }
        
        // 点击模态框外部关闭
        document.getElementById('rejectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRejectModal();
            }
        });
    </script>
</body>
</html>
