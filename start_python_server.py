#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特熊项目 - Python HTTP服务器启动脚本
支持静态文件服务和基本的路由功能
"""

import http.server
import socketserver
import os
import sys
import webbrowser
import threading
import time
from urllib.parse import urlparse, parse_qs

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # 默认页面重定向
        if path == '/':
            self.send_response(302)
            self.send_header('Location', '/启动页面.html')
            self.end_headers()
            return
        
        # 处理PHP文件（简单模拟）
        if path.endswith('.php'):
            self.serve_php_file(path)
            return
        
        # 处理静态文件
        super().do_GET()
    
    def serve_php_file(self, path):
        """简单的PHP文件处理"""
        file_path = path.lstrip('/')
        
        if not os.path.exists(file_path):
            self.send_error(404, f"File not found: {path}")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的PHP处理 - 替换专家数据
            if 'experts-data.php' in content:
                experts_html = self.generate_experts_html()
                # 替换PHP代码块
                import re
                pattern = r'<\?php[\s\S]*?\?>'
                content = re.sub(pattern, experts_html, content)
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Error processing PHP file: {str(e)}")
    
    def generate_experts_html(self):
        """生成专家HTML内容"""
        experts = [
            {'name': 'Arianne Dee', 'title': 'Software developer', 'delay': 0},
            {'name': 'Sari Greene', 'title': 'Cybersecurity practitioner', 'delay': 100},
            {'name': 'Bruno Gonçalves', 'title': 'Senior data scientist', 'delay': 200},
            {'name': 'Neal Ford', 'title': 'Software architect', 'delay': 300},
            {'name': 'Kelsey Hightower', 'title': 'Software engineer', 'delay': 400},
            {'name': 'Ken Kousen', 'title': 'Java Champion', 'delay': 500}
        ]
        
        html = ''
        for expert in experts:
            html += f'''
                <div class="expert-card" data-aos="fade-up" data-aos-delay="{expert['delay']}">
                    <div class="expert-image-container">
                        <img src="image/default-avatar.svg" alt="{expert['name']} - {expert['title']}" class="expert-image">
                        <div class="expert-overlay"></div>
                    </div>
                    <div class="expert-info">
                        <h4 class="expert-name">{expert['name']}</h4>
                        <p class="expert-title">{expert['title']}</p>
                    </div>
                </div>
            '''
        
        return html
    
    def log_message(self, format, *args):
        """自定义日志消息"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def find_free_port(start_port=8000, max_attempts=10):
    """查找可用端口"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    return None

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 浏览器已打开: {url}")
        except Exception as e:
            print(f"❌ 无法打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def main():
    """主函数"""
    print("=" * 50)
    print("🐻 比特熊项目 - Python HTTP服务器")
    print("=" * 50)
    print()
    
    # 检查项目文件
    required_files = ['index.html', 'index-style.css', '启动页面.html']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print()
        print("💡 请确保在项目根目录中运行此脚本")
        return
    
    # 查找可用端口
    port = find_free_port()
    if not port:
        print("❌ 无法找到可用端口 (8000-8009)")
        return
    
    # 启动服务器
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            server_url = f"http://localhost:{port}"
            
            print(f"✅ 服务器启动成功!")
            print(f"🌐 访问地址: {server_url}")
            print()
            print("📄 可用页面:")
            print(f"   • 项目首页: {server_url}/")
            print(f"   • 静态版本: {server_url}/index-standalone.html")
            print(f"   • 标准版本: {server_url}/index.html")
            print(f"   • PHP版本: {server_url}/index.php")
            print(f"   • 管理后台: {server_url}/admin.php")
            print(f"   • 项目演示: {server_url}/项目演示.html")
            print()
            print("💡 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 延迟打开浏览器
            open_browser_delayed(server_url)
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
