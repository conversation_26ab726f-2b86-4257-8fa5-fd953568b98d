<?php
// 直接测试API功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// 确保管理员已登录
$_SESSION['admin_logged_in'] = true;

echo "<h2>API直接测试</h2>";

// 测试1: 包含API文件
echo "<h3>1. 包含API文件测试</h3>";
try {
    // 模拟GET参数
    $_GET['action'] = 'stats';
    
    // 捕获输出
    ob_start();
    include 'api/comments-management.php';
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✓ API文件包含成功</p>";
    echo "<p><strong>输出:</strong></p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API文件包含失败: " . $e->getMessage() . "</p>";
}

// 测试2: 直接测试数据库连接
echo "<h3>2. 数据库连接测试</h3>";
try {
    require_once 'config/database.php';
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 测试查询
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM comments");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>评论总数: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
}

// 测试3: 手动调用API函数
echo "<h3>3. 手动调用API函数测试</h3>";
try {
    require_once 'config/database.php';
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    // 手动调用统计函数
    $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN status = 'hidden' THEN 1 ELSE 0 END) as hidden
            FROM comments";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✓ 统计查询成功</p>";
    echo "<p><strong>统计结果:</strong></p>";
    echo "<pre>" . json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 统计查询失败: " . $e->getMessage() . "</p>";
}
?>
