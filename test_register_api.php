<?php
// 直接测试注册API
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>注册API直接测试</h1>";

// 模拟POST数据
$_POST = [
    'username' => 'testuser' . time(),
    'email' => 'test' . time() . '@example.com',
    'nickname' => '测试用户',
    'password' => 'test123456',
    'confirmPassword' => 'test123456'
];

echo "<h2>测试数据:</h2>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h2>API响应:</h2>";

// 捕获输出
ob_start();

try {
    // 包含注册API
    include 'api/register.php';
} catch (Exception $e) {
    echo "包含API文件时出错: " . $e->getMessage();
}

$output = ob_get_clean();

echo "<pre>" . htmlspecialchars($output) . "</pre>";

// 尝试解析JSON响应
if ($output) {
    $response = json_decode($output, true);
    if ($response) {
        echo "<h2>解析后的响应:</h2>";
        echo "<pre>" . print_r($response, true) . "</pre>";
        
        if (isset($response['success'])) {
            if ($response['success']) {
                echo "<p style='color: green; font-size: 18px;'>✓ 注册成功！</p>";
            } else {
                echo "<p style='color: red; font-size: 18px;'>✗ 注册失败: " . $response['message'] . "</p>";
                
                if (isset($response['debug_info'])) {
                    echo "<h3>调试信息:</h3>";
                    echo "<pre>" . print_r($response['debug_info'], true) . "</pre>";
                }
                
                if (isset($response['errors'])) {
                    echo "<h3>字段错误:</h3>";
                    echo "<pre>" . print_r($response['errors'], true) . "</pre>";
                }
            }
        }
    } else {
        echo "<p style='color: red;'>无法解析JSON响应</p>";
    }
}
?>
