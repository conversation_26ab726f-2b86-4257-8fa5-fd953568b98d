<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>领导者管理 - 比特熊管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #1e293b;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #64748b;
        }
        
        .actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }
        
        .btn-secondary:hover {
            background: #cbd5e1;
        }
        
        .leaders-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .leader-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .leader-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .leader-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid #e2e8f0;
        }
        
        .leader-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #1e293b;
        }
        
        .leader-title {
            color: #64748b;
            margin-bottom: 10px;
        }
        
        .leader-bio {
            font-size: 0.9rem;
            color: #64748b;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .leader-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-inactive {
            background: #fef2f2;
            color: #991b1b;
        }
        
        .leader-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 0.9rem;
        }
        
        .btn-edit {
            background: #f59e0b;
            color: white;
        }
        
        .btn-edit:hover {
            background: #d97706;
        }
        
        .btn-delete {
            background: #ef4444;
            color: white;
        }
        
        .btn-delete:hover {
            background: #dc2626;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 12px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-checkbox {
            width: auto;
            margin-right: 8px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
        }
        
        @media (max-width: 768px) {
            .leaders-grid {
                grid-template-columns: 1fr;
            }
            
            .actions {
                flex-direction: column;
            }
            
            .modal-content {
                margin: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>领导者管理</h1>
            <p>管理在主页展示的组织领导者信息</p>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="openAddModal()">添加新领导者</button>
            <button class="btn btn-secondary" onclick="refreshLeaders()">刷新数据</button>
            <a href="index.html" class="btn btn-secondary">返回主页</a>
        </div>
        
        <div class="leaders-grid" id="leadersGrid">
            <!-- 领导者卡片将通过JavaScript动态加载 -->
        </div>
    </div>
    
    <!-- 添加/编辑领导者模态框 -->
    <div class="modal" id="leaderModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">添加新领导者</h3>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            
            <form id="leaderForm">
                <input type="hidden" id="leaderId" name="id">
                
                <div class="form-group">
                    <label class="form-label" for="leaderName">姓名</label>
                    <input type="text" class="form-input" id="leaderName" name="name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="leaderTitle">职位</label>
                    <input type="text" class="form-input" id="leaderTitle" name="title" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="leaderAvatar">头像URL</label>
                    <input type="text" class="form-input" id="leaderAvatar" name="avatar" placeholder="image/default-avatar.svg">
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="leaderBio">个人简介</label>
                    <textarea class="form-input form-textarea" id="leaderBio" name="bio" placeholder="请输入个人简介..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="sortOrder">排序顺序</label>
                    <input type="number" class="form-input" id="sortOrder" name="sort_order" min="1" value="1">
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" class="form-checkbox" id="displayOnHomepage" name="display_on_homepage" checked>
                        <label class="form-label" for="displayOnHomepage">在主页显示</label>
                    </div>
                </div>
                
                <div class="actions">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 模拟数据（在实际应用中应该从PHP API获取）
        let leaders = [
            {
                id: 1,
                name: '张三',
                title: '技术总监',
                avatar: 'image/default-avatar.svg',
                bio: '拥有10年以上技术管理经验，专注于团队建设和技术创新。',
                display_on_homepage: true,
                sort_order: 1
            },
            {
                id: 2,
                name: '李四',
                title: '产品经理',
                avatar: 'image/default-avatar.svg',
                bio: '资深产品经理，擅长用户体验设计和产品策略规划。',
                display_on_homepage: true,
                sort_order: 2
            },
            {
                id: 3,
                name: '王五',
                title: '首席架构师',
                avatar: 'image/default-avatar.svg',
                bio: '系统架构专家，在大型分布式系统设计方面有丰富经验。',
                display_on_homepage: true,
                sort_order: 3
            }
        ];
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadLeaders();
        });
        
        // 加载领导者数据
        function loadLeaders() {
            const grid = document.getElementById('leadersGrid');
            grid.innerHTML = '';
            
            leaders.forEach(leader => {
                const card = createLeaderCard(leader);
                grid.appendChild(card);
            });
        }
        
        // 创建领导者卡片
        function createLeaderCard(leader) {
            const card = document.createElement('div');
            card.className = 'leader-card';
            
            card.innerHTML = `
                <img src="${leader.avatar}" alt="${leader.name}" class="leader-avatar">
                <h4 class="leader-name">${leader.name}</h4>
                <p class="leader-title">${leader.title}</p>
                <p class="leader-bio">${leader.bio}</p>
                <span class="leader-status ${leader.display_on_homepage ? 'status-active' : 'status-inactive'}">
                    ${leader.display_on_homepage ? '主页显示' : '不显示'}
                </span>
                <div class="leader-actions">
                    <button class="btn btn-edit btn-small" onclick="editLeader(${leader.id})">编辑</button>
                    <button class="btn btn-delete btn-small" onclick="deleteLeader(${leader.id})">删除</button>
                </div>
            `;
            
            return card;
        }
        
        // 打开添加模态框
        function openAddModal() {
            document.getElementById('modalTitle').textContent = '添加新领导者';
            document.getElementById('leaderForm').reset();
            document.getElementById('leaderId').value = '';
            document.getElementById('displayOnHomepage').checked = true;
            document.getElementById('leaderModal').style.display = 'block';
        }
        
        // 编辑领导者
        function editLeader(id) {
            const leader = leaders.find(l => l.id === id);
            if (!leader) return;
            
            document.getElementById('modalTitle').textContent = '编辑领导者';
            document.getElementById('leaderId').value = leader.id;
            document.getElementById('leaderName').value = leader.name;
            document.getElementById('leaderTitle').value = leader.title;
            document.getElementById('leaderAvatar').value = leader.avatar;
            document.getElementById('leaderBio').value = leader.bio;
            document.getElementById('sortOrder').value = leader.sort_order;
            document.getElementById('displayOnHomepage').checked = leader.display_on_homepage;
            
            document.getElementById('leaderModal').style.display = 'block';
        }
        
        // 删除领导者
        function deleteLeader(id) {
            if (confirm('确定要删除这个领导者吗？')) {
                leaders = leaders.filter(l => l.id !== id);
                loadLeaders();
                alert('删除成功！');
            }
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('leaderModal').style.display = 'none';
        }
        
        // 刷新数据
        function refreshLeaders() {
            loadLeaders();
            alert('数据已刷新！');
        }
        
        // 表单提交处理
        document.getElementById('leaderForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const leaderData = {
                name: formData.get('name'),
                title: formData.get('title'),
                avatar: formData.get('avatar') || 'image/default-avatar.svg',
                bio: formData.get('bio'),
                sort_order: parseInt(formData.get('sort_order')),
                display_on_homepage: formData.get('display_on_homepage') === 'on'
            };
            
            const id = formData.get('id');
            
            if (id) {
                // 编辑现有领导者
                const index = leaders.findIndex(l => l.id === parseInt(id));
                if (index !== -1) {
                    leaders[index] = { ...leaders[index], ...leaderData };
                }
            } else {
                // 添加新领导者
                const maxId = Math.max(...leaders.map(l => l.id), 0);
                leaderData.id = maxId + 1;
                leaders.push(leaderData);
            }
            
            loadLeaders();
            closeModal();
            alert('保存成功！');
        });
        
        // 点击模态框外部关闭
        document.getElementById('leaderModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
