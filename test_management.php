<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 10px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
        .nav-buttons { margin-bottom: 20px; }
        .nav-buttons button { background: #28a745; }
        .nav-buttons button:hover { background: #218838; }
    </style>
</head>
<body>
    <h1>比特熊智慧系统 - 管理功能测试</h1>
    
    <div class="nav-buttons">
        <button onclick="window.open('admin-dashboard.php', '_blank')">打开管理后台</button>
        <button onclick="window.open('test_all_apis.php', '_blank')">API测试页面</button>
        <button onclick="window.open('test_login.php', '_blank')">登录测试页面</button>
    </div>
    
    <div class="test-section">
        <h2>用户管理API测试</h2>
        <button onclick="testGetUsers()">获取用户列表</button>
        <button onclick="testCreateUser()">创建测试用户</button>
        <button onclick="testUpdateUser()">更新用户信息</button>
        <div id="userResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>公告管理API测试</h2>
        <button onclick="testGetAnnouncements()">获取公告列表</button>
        <button onclick="testCreateAnnouncement()">创建测试公告</button>
        <button onclick="testUpdateAnnouncement()">更新公告</button>
        <div id="announcementResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>数据库表结构测试</h2>
        <button onclick="testDatabaseTables()">检查数据库表</button>
        <button onclick="testSampleData()">插入示例数据</button>
        <div id="databaseResult" class="result"></div>
    </div>

    <script>
        // 用户管理测试
        async function testGetUsers() {
            const result = document.getElementById('userResult');
            result.innerHTML = '<p class="info">正在获取用户列表...</p>';
            
            try {
                const response = await fetch('api/users.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 用户列表获取成功</p>
                        <p>用户数量: ${data.data.users.length}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 获取失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        async function testCreateUser() {
            const result = document.getElementById('userResult');
            result.innerHTML = '<p class="info">正在创建测试用户...</p>';
            
            const userData = {
                username: 'testuser_' + Date.now(),
                email: 'test_' + Date.now() + '@example.com',
                full_name: '测试用户',
                role_id: 3, // 普通用户角色
                password: 'test123'
            };
            
            try {
                const response = await fetch('api/users.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 用户创建成功</p>
                        <p>用户ID: ${data.data.id}</p>
                        <p>用户名: ${userData.username}</p>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 创建失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        async function testUpdateUser() {
            const result = document.getElementById('userResult');
            result.innerHTML = '<p class="info">正在更新用户信息...</p>';
            
            const updateData = {
                id: 1, // 更新管理员用户
                full_name: '系统管理员 (已更新)',
                status: 'active'
            };
            
            try {
                const response = await fetch('api/users.php', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `<p class="success">✅ 用户更新成功: ${data.message}</p>`;
                } else {
                    result.innerHTML = `<p class="error">❌ 更新失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        // 公告管理测试
        async function testGetAnnouncements() {
            const result = document.getElementById('announcementResult');
            result.innerHTML = '<p class="info">正在获取公告列表...</p>';
            
            try {
                const response = await fetch('api/announcements.php');
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 公告列表获取成功</p>
                        <p>公告数量: ${data.data.announcements.length}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 获取失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        async function testCreateAnnouncement() {
            const result = document.getElementById('announcementResult');
            result.innerHTML = '<p class="info">正在创建测试公告...</p>';
            
            const announcementData = {
                title: '测试公告 - ' + new Date().toLocaleString(),
                content: '这是一个通过API创建的测试公告，用于验证公告管理功能是否正常工作。',
                type: 'normal',
                is_pinned: false
            };
            
            try {
                const response = await fetch('api/announcements.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(announcementData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <p class="success">✅ 公告创建成功</p>
                        <p>公告ID: ${data.data.id}</p>
                        <p>标题: ${announcementData.title}</p>
                    `;
                } else {
                    result.innerHTML = `<p class="error">❌ 创建失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        async function testUpdateAnnouncement() {
            const result = document.getElementById('announcementResult');
            result.innerHTML = '<p class="info">正在更新公告...</p>';
            
            const updateData = {
                id: 1, // 更新第一个公告
                title: '系统维护通知 (已更新)',
                type: 'urgent',
                is_pinned: true
            };
            
            try {
                const response = await fetch('api/announcements.php', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updateData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `<p class="success">✅ 公告更新成功: ${data.message}</p>`;
                } else {
                    result.innerHTML = `<p class="error">❌ 更新失败: ${data.error}</p>`;
                }
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 请求失败: ${error.message}</p>`;
            }
        }

        // 数据库测试
        async function testDatabaseTables() {
            const result = document.getElementById('databaseResult');
            result.innerHTML = '<p class="info">正在检查数据库表结构...</p>';
            
            try {
                // 通过访问API来间接测试数据库表
                const tests = [
                    { name: '用户表', url: 'api/users.php' },
                    { name: '公告表', url: 'api/announcements.php' },
                    { name: '通知表', url: 'api/notifications.php' },
                    { name: '日程表', url: 'api/events.php' },
                    { name: '活动表', url: 'api/activities.php' }
                ];
                
                let results = [];
                
                for (const test of tests) {
                    try {
                        const response = await fetch(test.url);
                        const data = await response.json();
                        results.push(`✅ ${test.name}: 正常`);
                    } catch (error) {
                        results.push(`❌ ${test.name}: 错误 - ${error.message}`);
                    }
                }
                
                result.innerHTML = `
                    <p class="success">数据库表检查完成</p>
                    <pre>${results.join('\n')}</pre>
                `;
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 检查失败: ${error.message}</p>`;
            }
        }

        async function testSampleData() {
            const result = document.getElementById('databaseResult');
            result.innerHTML = '<p class="info">正在插入示例数据...</p>';
            
            try {
                // 创建示例用户
                await fetch('api/users.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'demo_user',
                        email: '<EMAIL>',
                        full_name: '演示用户',
                        role_id: 3,
                        password: 'demo123'
                    })
                });
                
                // 创建示例公告
                await fetch('api/announcements.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title: '欢迎使用比特熊智慧系统',
                        content: '感谢您使用我们的系统，这里有丰富的功能等待您探索。',
                        type: 'normal'
                    })
                });
                
                result.innerHTML = '<p class="success">✅ 示例数据插入成功</p>';
            } catch (error) {
                result.innerHTML = `<p class="error">❌ 插入失败: ${error.message}</p>`;
            }
        }

        // 页面加载时显示提示
        window.onload = function() {
            console.log('管理功能测试页面已加载');
            console.log('可以测试以下功能:');
            console.log('1. 用户管理 - 增删改查用户');
            console.log('2. 公告管理 - 发布和管理公告');
            console.log('3. 数据库表 - 检查表结构和数据');
        };
    </script>
</body>
</html>
