# 比特熊智慧系统 - SSH连接脚本
param(
    [string]$ServerIP = "*************",
    [string]$Username = "root",
    [string]$Password = "ZbDX7%=]?H2(LAUz"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "比特熊智慧系统 - SSH连接工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "服务器信息:" -ForegroundColor Yellow
Write-Host "IP地址: $ServerIP" -ForegroundColor White
Write-Host "用户名: $Username" -ForegroundColor White
Write-Host "密码: $Password" -ForegroundColor Red
Write-Host ""

# 测试网络连接
Write-Host "正在测试网络连接..." -ForegroundColor Yellow
$pingResult = Test-Connection -ComputerName $ServerIP -Count 2 -Quiet
if ($pingResult) {
    Write-Host "✓ 网络连接正常" -ForegroundColor Green
} else {
    Write-Host "✗ 网络连接失败" -ForegroundColor Red
    Write-Host "请检查网络连接或服务器状态" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "正在检测SSH客户端..." -ForegroundColor Yellow

# 检查Windows内置SSH
$windowsSSH = "C:\Windows\System32\OpenSSH\ssh.exe"
if (Test-Path $windowsSSH) {
    Write-Host "✓ 找到Windows内置SSH客户端" -ForegroundColor Green
    Write-Host "正在连接到服务器..." -ForegroundColor Yellow
    Write-Host "请在提示时输入密码: $Password" -ForegroundColor Red
    Write-Host ""
    
    # 使用Windows SSH连接
    & $windowsSSH -o StrictHostKeyChecking=no $Username@$ServerIP
    exit
}

# 检查Git SSH
$gitSSH = "C:\Program Files\Git\usr\bin\ssh.exe"
if (Test-Path $gitSSH) {
    Write-Host "✓ 找到Git SSH客户端" -ForegroundColor Green
    Write-Host "正在连接到服务器..." -ForegroundColor Yellow
    Write-Host "请在提示时输入密码: $Password" -ForegroundColor Red
    Write-Host ""
    
    # 使用Git SSH连接
    & $gitSSH -o StrictHostKeyChecking=no $Username@$ServerIP
    exit
}

# 检查系统PATH中的SSH
try {
    $sshVersion = & ssh -V 2>&1
    Write-Host "✓ 找到系统SSH客户端" -ForegroundColor Green
    Write-Host "正在连接到服务器..." -ForegroundColor Yellow
    Write-Host "请在提示时输入密码: $Password" -ForegroundColor Red
    Write-Host ""
    
    # 使用系统SSH连接
    & ssh -o StrictHostKeyChecking=no $Username@$ServerIP
    exit
} catch {
    # SSH不在PATH中
}

# 没有找到SSH客户端
Write-Host "❌ 未找到SSH客户端" -ForegroundColor Red
Write-Host ""
Write-Host "请安装以下任一SSH客户端：" -ForegroundColor Yellow
Write-Host ""
Write-Host "1. Windows OpenSSH客户端 (推荐):" -ForegroundColor Cyan
Write-Host "   - 打开'设置' > '应用' > '可选功能'" -ForegroundColor White
Write-Host "   - 点击'添加功能'" -ForegroundColor White
Write-Host "   - 搜索并安装'OpenSSH客户端'" -ForegroundColor White
Write-Host ""
Write-Host "2. Git for Windows:" -ForegroundColor Cyan
Write-Host "   - 访问 https://git-scm.com/download/win" -ForegroundColor White
Write-Host "   - 下载并安装Git for Windows" -ForegroundColor White
Write-Host ""
Write-Host "3. PuTTY:" -ForegroundColor Cyan
Write-Host "   - 访问 https://www.putty.org/" -ForegroundColor White
Write-Host "   - 下载并安装PuTTY" -ForegroundColor White
Write-Host ""

Write-Host "安装完成后，请重新运行此脚本" -ForegroundColor Yellow
Write-Host ""
Write-Host "或者您可以手动连接：" -ForegroundColor Yellow
Write-Host "ssh $Username@$ServerIP" -ForegroundColor White
Write-Host ""

Read-Host "按回车键退出"
