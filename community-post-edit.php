<?php
session_start();
require_once 'config/database.php';
require_once 'classes/Auth.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// 检查用户是否登录
if (!$currentUser) {
    header('Location: admin-login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$error = '';
$success = '';
$postId = intval($_GET['id'] ?? 0);

if (!$postId) {
    header('Location: community.php');
    exit;
}

// 获取帖子信息
try {
    $db = db();
    $post = $db->query("SELECT * FROM posts WHERE id = ?", [$postId])->fetch();
    
    if (!$post) {
        throw new Exception('帖子不存在');
    }
    
    // 检查权限：只有作者或管理员可以编辑
    if ($post['user_id'] != $currentUser['id'] && !in_array($currentUser['role_code'] ?? '', ['admin', 'super_admin'])) {
        throw new Exception('您没有权限编辑此帖子');
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$error) {
    try {
        $title = trim($_POST['title'] ?? '');
        $content = $_POST['content'] ?? '';
        $category_id = intval($_POST['category_id'] ?? 0);
        $excerpt = trim($_POST['excerpt'] ?? '');
        $featured_image = trim($_POST['featured_image'] ?? '');
        
        // 验证输入
        if (empty($title)) {
            throw new Exception('请输入帖子标题');
        }
        
        if (empty($content)) {
            throw new Exception('请输入帖子内容');
        }
        
        if (strlen($title) > 255) {
            throw new Exception('标题长度不能超过255个字符');
        }
        
        // 如果没有提供摘要，从内容中自动生成
        if (empty($excerpt)) {
            $excerpt = mb_substr(strip_tags($content), 0, 200);
        }
        
        // 更新帖子
        $sql = "UPDATE posts SET 
                title = ?, 
                content = ?, 
                excerpt = ?, 
                category_id = ?, 
                featured_image = ?,
                updated_at = datetime('now')
                WHERE id = ?";
        
        $db->execute($sql, [
            $title,
            $content, 
            $excerpt,
            $category_id,
            $featured_image,
            $postId
        ]);
        
        $success = '帖子更新成功！';
        
        // 重新获取更新后的帖子信息
        $post = $db->query("SELECT * FROM posts WHERE id = ?", [$postId])->fetch();
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取分类列表
try {
    $db = db();
    $categories = $db->query("SELECT * FROM categories ORDER BY name")->fetchAll();
} catch (Exception $e) {
    $categories = [];
}

if (!function_exists('escapeHtml')) {
    function escapeHtml($text) {
        return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑帖子 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="assets/css/community.css">
    <link rel="stylesheet" href="assets/css/community-post.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- TinyMCE编辑器 - 使用开源版本 -->
    <script src="https://cdn.jsdelivr.net/npm/tinymce@6.8.2/tinymce.min.js"></script>
    
    <style>
        /* 编辑器容器样式 */
        .editor-container {
            position: relative;
        }

        /* 拖拽提示样式 */
        .drag-drop-hint {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(74, 144, 226, 0.1);
            border: 2px dashed #4a90e2;
            border-radius: 8px;
            display: none;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            z-index: 1000;
            color: #4a90e2;
            font-size: 16px;
            font-weight: 500;
        }

        .drag-drop-hint.active {
            display: flex;
        }

        .drag-drop-hint i {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .drag-drop-hint p {
            margin: 0;
            text-align: center;
        }

        /* TinyMCE编辑器拖拽样式 */
        .tox-edit-area.drag-over {
            border: 2px dashed #4a90e2 !important;
            background: rgba(74, 144, 226, 0.05) !important;
        }
        
        /* 图片上传按钮样式 */
        .tox-tbtn--enabled .tox-icon-image {
            color: #4a90e2;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="index.php">
                    <img src="assets/images/logo.png" alt="比特熊智慧系统" class="logo">
                    <span>比特熊智慧系统</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="index.php" class="nav-link">首页</a>
                <a href="community.php" class="nav-link">社区</a>
                <a href="#" class="nav-link">课程</a>
                <a href="#" class="nav-link">文档</a>
            </div>
            
            <div class="nav-actions">
                <div class="user-menu">
                    <img src="<?php echo $currentUser['avatar'] ?? 'assets/images/default-avatar.png'; ?>" 
                         alt="头像" class="user-avatar">
                    <span><?php echo escapeHtml($currentUser['username']); ?></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="post-form-container">
                <div class="form-header">
                    <h1 class="form-title">
                        <i class="fas fa-edit"></i>
                        编辑帖子
                    </h1>
                    <p class="form-description">修改您的帖子内容</p>
                </div>

                <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo escapeHtml($error); ?>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo escapeHtml($success); ?>
                    <a href="community-post-detail.php?id=<?php echo $postId; ?>" class="alert-link">查看帖子</a>
                </div>
                <?php endif; ?>

                <?php if (!$error && $post): ?>
                <form method="POST" class="post-form" id="postForm">
                    <div class="form-group">
                        <label for="title" class="form-label">
                            <i class="fas fa-heading"></i>
                            帖子标题 *
                        </label>
                        <input type="text" 
                               id="title" 
                               name="title" 
                               class="form-input" 
                               placeholder="请输入帖子标题..." 
                               value="<?php echo escapeHtml($post['title']); ?>"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="category_id" class="form-label">
                            <i class="fas fa-folder"></i>
                            分类
                        </label>
                        <select id="category_id" name="category_id" class="form-select">
                            <option value="0">选择分类</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo $category['id'] == $post['category_id'] ? 'selected' : ''; ?>>
                                <?php echo escapeHtml($category['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label">
                            <i class="fas fa-edit"></i>
                            帖子内容 *
                        </label>
                        <div class="editor-container">
                            <div class="drag-drop-hint" id="dragDropHint">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>拖拽图片到此处快速上传，或点击工具栏中的图片按钮</p>
                            </div>
                            <textarea id="content" name="content" class="form-textarea" placeholder="在这里写下你的想法..." required><?php echo escapeHtml($post['content']); ?></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="excerpt" class="form-label">
                            <i class="fas fa-align-left"></i>
                            帖子摘要
                        </label>
                        <textarea id="excerpt" 
                                  name="excerpt" 
                                  class="form-textarea" 
                                  rows="3" 
                                  placeholder="简要描述帖子内容（可选，留空将自动生成）"><?php echo escapeHtml($post['excerpt']); ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="featured_image" class="form-label">
                            <i class="fas fa-image"></i>
                            特色图片
                        </label>
                        <input type="url" 
                               id="featured_image" 
                               name="featured_image" 
                               class="form-input" 
                               placeholder="输入图片URL（可选）"
                               value="<?php echo escapeHtml($post['featured_image']); ?>">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            保存修改
                        </button>
                        <button type="button" class="btn btn-secondary" id="previewBtn">
                            <i class="fas fa-eye"></i>
                            预览
                        </button>
                        <a href="community-post-detail.php?id=<?php echo $postId; ?>" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            取消
                        </a>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- 预览模态框 -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>帖子预览</h2>
                <span class="close" onclick="closePreview()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
        </div>
    </div>

    <script>
        // 初始化TinyMCE编辑器
        tinymce.init({
            selector: '#content',
            height: 500,
            menubar: 'file edit view insert format tools table help',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'emoticons',
                'textcolor', 'colorpicker', 'textpattern', 'codesample', 'hr',
                'pagebreak', 'nonbreaking', 'template', 'paste', 'directionality', 'imagetools'
            ],
            toolbar1: 'undo redo | cut copy paste | bold italic underline strikethrough | ' +
                     'fontfamily fontsize | forecolor backcolor | removeformat',
            toolbar2: 'alignleft aligncenter alignright alignjustify | ' +
                     'bullist numlist outdent indent | blockquote hr pagebreak | ' +
                     'link unlink anchor | image customImageUpload media table emoticons charmap',
            toolbar3: 'subscript superscript | codesample | ltr rtl | ' +
                     'visualblocks code preview fullscreen help',

            // 字体设置
            font_family_formats:
                '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;' +
                '宋体=SimSun,serif;' +
                '黑体=SimHei,sans-serif;' +
                '楷体=KaiTi,serif;' +
                'Arial=arial,helvetica,sans-serif;' +
                'Times New Roman=times new roman,times,serif;' +
                'Courier New=courier new,courier,monospace',

            font_size_formats: '12px 14px 16px 18px 20px 24px 28px 32px 36px 48px 60px 72px 96px',

            // 颜色设置
            color_map: [
                "000000", "黑色",
                "993300", "深红色",
                "333300", "深黄色",
                "003300", "深绿色",
                "003366", "深青色",
                "000080", "深蓝色",
                "333399", "蓝色",
                "333333", "深灰色",
                "800000", "栗色",
                "FF6600", "橙色",
                "808000", "橄榄色",
                "008000", "绿色",
                "008080", "青色",
                "0000FF", "蓝色",
                "666699", "灰蓝色",
                "808080", "灰色",
                "FF0000", "红色",
                "FF9900", "琥珀色",
                "99CC00", "黄绿色",
                "339966", "海绿色",
                "33CCCC", "绿松石色",
                "3366FF", "皇家蓝",
                "800080", "紫色",
                "999999", "中灰色",
                "FF00FF", "洋红色",
                "FFCC00", "金色",
                "FFFF00", "黄色",
                "00FF00", "酸橙色",
                "00FFFF", "水色",
                "00CCFF", "天蓝色",
                "993366", "红紫色",
                "C0C0C0", "银色",
                "FF99CC", "粉红色",
                "FFCC99", "桃色",
                "FFFF99", "浅黄色",
                "CCFFCC", "浅绿色",
                "CCFFFF", "浅青色",
                "99CCFF", "浅蓝色",
                "CC99FF", "淡紫色",
                "FFFFFF", "白色"
            ],

            // 图片上传设置
            images_upload_url: 'api/user-upload.php',
            images_upload_handler: function (blobInfo, success, failure) {
                uploadImageToServer(blobInfo.blob(), blobInfo.filename(), success, failure);
            },

            // 启用拖拽上传
            paste_data_images: true,
            images_reuse_filename: true,

            // 图片工具设置
            images_upload_credentials: true,
            automatic_uploads: true,
            imagetools_cors_hosts: ['localhost'],
            imagetools_toolbar: 'rotateleft rotateright | flipv fliph | editimage imageoptions',

            // 图片调整选项
            image_dimensions: true,
            image_class_list: [
                {title: '无样式', value: ''},
                {title: '响应式图片', value: 'img-responsive'},
                {title: '圆角图片', value: 'img-rounded'},
                {title: '圆形图片', value: 'img-circle'},
                {title: '缩略图', value: 'img-thumbnail'}
            ],

            // 媒体设置
            media_live_embeds: true,
            media_url_resolver: function (data, resolve) {
                if (data.url.indexOf('youtube.com') !== -1 || data.url.indexOf('youtu.be') !== -1) {
                    resolve({html: '<iframe src="' + data.url + '" width="560" height="315" frameborder="0" allowfullscreen></iframe>'});
                } else {
                    resolve({html: ''});
                }
            },

            content_style: `
                body {
                    font-family: Microsoft YaHei, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                }
                img { max-width: 100%; height: auto; }
                .img-responsive { max-width: 100%; height: auto; }
                .img-rounded { border-radius: 8px; }
                .img-circle { border-radius: 50%; }
                .img-thumbnail { border: 1px solid #ddd; padding: 4px; border-radius: 4px; }
            `,

            setup: function(editor) {
                // 自定义图片上传按钮
                editor.ui.registry.addButton('customImageUpload', {
                    icon: 'image',
                    tooltip: '上传本地图片',
                    onAction: function () {
                        // 创建文件选择器，支持多选
                        var input = document.createElement('input');
                        input.setAttribute('type', 'file');
                        input.setAttribute('accept', 'image/*');
                        input.setAttribute('multiple', 'true');

                        input.click();

                        input.onchange = function() {
                            var files = Array.from(this.files);
                            files.forEach(function(file) {
                                if (file.type.startsWith('image/')) {
                                    uploadImageToServer(file, file.name, function(url) {
                                        editor.insertContent(`<img src="${url}" alt="${file.name}" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                                    }, function(error) {
                                        alert('图片上传失败: ' + error);
                                    });
                                }
                            });
                        };
                    }
                });
            }
        });

        // 图片上传到服务器的通用函数
        function uploadImageToServer(file, filename, successCallback, errorCallback) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('filename', filename);

            fetch('api/user-upload.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    successCallback(data.url);
                } else {
                    errorCallback(data.message || '上传失败');
                }
            })
            .catch(error => {
                console.error('上传错误:', error);
                errorCallback('文件上传失败，请重试');
            });
        }

        // 拖拽上传功能
        function initDragDropUpload() {
            const editorContainer = document.querySelector('.editor-container');
            const dragDropHint = document.getElementById('dragDropHint');
            let dragCounter = 0;

            // 防止默认拖拽行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                editorContainer.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            // 拖拽进入
            editorContainer.addEventListener('dragenter', function(e) {
                dragCounter++;
                if (e.dataTransfer.types.includes('Files')) {
                    dragDropHint.classList.add('active');
                }
            });

            // 拖拽离开
            editorContainer.addEventListener('dragleave', function(e) {
                dragCounter--;
                if (dragCounter === 0) {
                    dragDropHint.classList.remove('active');
                }
            });

            // 拖拽悬停
            editorContainer.addEventListener('dragover', function(e) {
                if (e.dataTransfer.types.includes('Files')) {
                    dragDropHint.classList.add('active');
                }
            });

            // 文件放置
            editorContainer.addEventListener('drop', function(e) {
                dragCounter = 0;
                dragDropHint.classList.remove('active');

                const files = Array.from(e.dataTransfer.files);
                const imageFiles = files.filter(file => file.type.startsWith('image/'));

                if (imageFiles.length > 0) {
                    imageFiles.forEach(file => {
                        uploadImageToServer(file, file.name, function(url) {
                            // 获取TinyMCE编辑器实例
                            const editor = tinymce.get('content');
                            if (editor) {
                                editor.insertContent(`<img src="${url}" alt="${file.name}" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                            }
                        }, function(error) {
                            alert('图片上传失败: ' + error);
                        });
                    });
                } else if (files.length > 0) {
                    alert('请拖拽图片文件！');
                }
            });
        }

        // 粘贴上传功能
        function initPasteUpload() {
            document.addEventListener('paste', function(e) {
                const items = Array.from(e.clipboardData.items);
                const imageItems = items.filter(item => item.type.startsWith('image/'));

                if (imageItems.length > 0) {
                    e.preventDefault();

                    imageItems.forEach(item => {
                        const file = item.getAsFile();
                        if (file) {
                            uploadImageToServer(file, 'pasted-image-' + Date.now() + '.png', function(url) {
                                const editor = tinymce.get('content');
                                if (editor) {
                                    editor.insertContent(`<img src="${url}" alt="粘贴的图片" style="max-width: 100%; height: auto; cursor: pointer;" data-mce-resize="true">`);
                                }
                            }, function(error) {
                                alert('图片上传失败: ' + error);
                            });
                        }
                    });
                }
            });
        }

        // 预览功能
        document.getElementById('previewBtn').addEventListener('click', function() {
            const title = document.getElementById('title').value;
            const content = tinymce.get('content').getContent();

            if (!title.trim()) {
                alert('请输入帖子标题');
                return;
            }

            if (!content.trim()) {
                alert('请输入帖子内容');
                return;
            }

            // 显示预览
            document.getElementById('previewContent').innerHTML = `
                <h1 style="margin-bottom: 20px; color: #333;">${title}</h1>
                <div style="line-height: 1.6; color: #555;">${content}</div>
            `;

            document.getElementById('previewModal').style.display = 'block';
        });

        function closePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化拖拽上传功能
            initDragDropUpload();

            // 初始化粘贴上传功能
            initPasteUpload();

            // 等待TinyMCE加载完成后隐藏拖拽提示
            const checkEditor = setInterval(function() {
                if (tinymce.get('content')) {
                    clearInterval(checkEditor);
                    // 编辑器加载完成后隐藏拖拽提示
                    setTimeout(function() {
                        const dragDropHint = document.getElementById('dragDropHint');
                        if (dragDropHint) {
                            dragDropHint.style.display = 'none';
                        }
                    }, 1000);
                }
            }, 100);
        });
    </script>
</body>
</html>
