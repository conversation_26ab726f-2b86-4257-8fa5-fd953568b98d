<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>毛玻璃效果演示 - 比特熊智慧系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 背景装饰元素 */
        .bg-decoration {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .bg-circle:nth-child(1) {
            width: 200px;
            height: 200px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .bg-circle:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .bg-circle:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 2rem;
            color: white;
            transition: all 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
        }

        .demo-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .demo-card p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .glass-preview {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .mobile-nav-demo {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 2rem;
            margin: 2rem 0;
            color: white;
        }

        .nav-item-demo {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.5rem 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-item-demo:hover {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            transform: translateX(10px);
        }

        .test-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .test-button:hover {
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            transform: translateY(-2px);
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #e2e8f0;
            overflow-x: auto;
            margin: 1rem 0;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }

        /* 暗色模式支持 */
        @media (prefers-color-scheme: dark) {
            .demo-card {
                background: rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(25px);
                -webkit-backdrop-filter: blur(25px);
                border: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .demo-card:hover {
                background: rgba(0, 0, 0, 0.4);
            }
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>✨ 毛玻璃效果演示</h1>
            <p>比特熊智慧系统移动端导航的现代化视觉效果</p>
        </div>

        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎨 毛玻璃技术</h3>
                <p>使用CSS的 <code>backdrop-filter: blur()</code> 属性实现现代化的毛玻璃半透明效果，让界面更具层次感和现代感。</p>
                <div class="glass-preview">
                    <p>这是毛玻璃效果的预览</p>
                </div>
            </div>

            <div class="demo-card">
                <h3>📱 移动端优化</h3>
                <p>专门为移动端导航菜单设计的毛玻璃效果，在保持美观的同时确保良好的可读性和用户体验。</p>
                <div class="code-block">
background: rgba(255, 255, 255, 0.85);
backdrop-filter: blur(20px);
-webkit-backdrop-filter: blur(20px);
                </div>
            </div>

            <div class="demo-card">
                <h3>🌙 暗色模式支持</h3>
                <p>在暗色主题下自动切换到适合的毛玻璃效果，确保在不同主题下都有最佳的视觉体验。</p>
                <div class="code-block">
background: rgba(15, 23, 42, 0.85);
backdrop-filter: blur(25px);
                </div>
            </div>
        </div>

        <div class="mobile-nav-demo">
            <h3>📱 移动端导航演示</h3>
            <p>以下是移动端导航菜单项的毛玻璃效果演示：</p>
            
            <div class="nav-item-demo">
                <strong>首页</strong> - 主导航项
            </div>
            <div class="nav-item-demo">
                <strong>组织</strong> - 带下拉菜单的导航项
            </div>
            <div class="nav-item-demo">
                <strong>服务</strong> - 普通导航项
            </div>
            <div class="nav-item-demo">
                <strong>新闻</strong> - 普通导航项
            </div>
        </div>

        <div style="text-align: center; margin: 3rem 0;">
            <h3 style="color: white; margin-bottom: 1rem;">🧪 测试实际效果</h3>
            <a href="test_mobile_nav.php" class="test-button">测试移动端导航</a>
            <a href="index.php" class="test-button">返回主页</a>
            <a href="mobile_nav_test.html" class="test-button">查看测试报告</a>
        </div>

        <div class="demo-card" style="margin-top: 2rem;">
            <h3>💡 技术说明</h3>
            <p><strong>兼容性：</strong>毛玻璃效果在现代浏览器中有良好支持，包括Safari、Chrome、Firefox等。</p>
            <p><strong>性能：</strong>使用硬件加速的CSS属性，确保流畅的动画效果。</p>
            <p><strong>降级：</strong>在不支持的浏览器中会自动降级为普通的半透明效果。</p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // 显示浏览器支持信息
            const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(10px)') || 
                                         CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
            
            if (!supportsBackdropFilter) {
                const warning = document.createElement('div');
                warning.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(255, 193, 7, 0.9);
                    color: #000;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 1000;
                    max-width: 300px;
                `;
                warning.innerHTML = '⚠️ 您的浏览器可能不完全支持毛玻璃效果，建议使用最新版本的Chrome、Safari或Firefox。';
                document.body.appendChild(warning);
            }
        });
    </script>
</body>
</html>
