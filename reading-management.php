<?php
session_start();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>读书管理 - 学务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #06b6d4;
            --secondary-color: #0891b2;
            --accent-color: #22d3ee;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }

        * {
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .container-fluid {
            padding-left: 0 !important;
            padding-right: 0 !important;
            max-width: 100vw !important;
            width: 100vw !important;
        }

        body {
            background: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: var(--primary-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            width: 100vw;
            margin: 0;
            padding-left: 0;
            padding-right: 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-container {
            padding: 0;
            width: 100vw;
            height: 100vh;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin: 0;
        }

        .page-header { background: #f8f9fa; border-radius: 0; padding: 0.75rem; margin-bottom: 0; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); border-bottom: 1px solid #e9ecef; flex-shrink: 0; width: 100vw; }

        .page-title { margin: 0; color: #333; font-weight: 600; }

        .page-description { margin: 0.5rem 0 0 0; color: #666; }

        .stats-section {
            background: white;
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .reading-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .controls-section {
            background: white;
            border-radius: 0;
            padding: 0.75rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex-shrink: 0;
            width: 100vw;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .filter-controls label {
            color: #333;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .form-select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #333;
            font-weight: 500;
        }

        .form-select:focus {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: #333;
            box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
        }

        .form-select option {
            background: var(--primary-color);
            color: #333;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            margin-left: auto;
        }

        .btn-primary {
            background: rgba(6, 182, 212, 0.8);
            border: 1px solid rgba(6, 182, 212, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: rgba(6, 182, 212, 0.9);
            border-color: rgba(6, 182, 212, 1);
        }

        .btn-success {
            background: rgba(16, 185, 129, 0.8);
            border: 1px solid rgba(16, 185, 129, 0.9);
            color: #333;
            font-weight: 500;
        }

        .btn-success:hover {
            background: rgba(16, 185, 129, 0.9);
        }

        .dashboard-container {
            background: white;
            border-radius: 0;
            padding: 1rem;
            margin-bottom: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: none;
            flex: 1;
            overflow: auto;
            min-height: 0;
            width: 100vw;
        }

        .books-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .book-card {
            background: white; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .book-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .book-card.reading::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: white;
        }

        .book-card.completed::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: white;
        }

        .book-card.planned::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: white;
        }

        .book-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .book-info {
            flex: 1;
        }

        .book-title {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .book-author {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .book-category {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        .book-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-reading {
            background: rgba(16, 185, 129, 0.8);
            color: #333;
        }

        .status-completed {
            background: rgba(59, 130, 246, 0.8);
            color: #333;
        }

        .status-planned {
            background: rgba(245, 158, 11, 0.8);
            color: #333;
        }

        .book-progress {
            margin-bottom: 1rem;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-label {
            color: #495057;
            font-size: 0.9rem;
        }

        .progress-percentage {
            color: #333;
            font-weight: 600;
        }

        .progress {
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-bar {
            background: white;
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .book-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-size: 0.8rem;
        }

        .book-meta-item {
            text-align: center;
        }

        .book-meta-label {
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 0.25rem;
        }

        .book-meta-value {
            color: #333;
            font-weight: 600;
        }

        .book-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }

        @media (max-width: 768px) {
            .reading-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .books-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .filter-controls {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .action-buttons {
                margin-left: 0;
                justify-content: center;
            }
            
            .dashboard-container {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="student-affairs.php">
                <i class="fas fa-book-open me-2"></i>
                读书管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="student-affairs.php">
                    <i class="fas fa-arrow-left me-1"></i>
                    返回学务管理
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">读书管理</h1>
            <p class="page-description">阅读清单管理、进度跟踪、读书笔记、阅读统计分析</p>
        </div>

        <!-- 阅读统计 -->
        <div class="stats-section">
            <div class="reading-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">本年度已读</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">正在阅读</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">计划阅读</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2.5</div>
                    <div class="stat-label">月均阅读(本)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">156</div>
                    <div class="stat-label">阅读时长(小时)</div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="controls-section">
            <div class="filter-controls">
                <label>状态筛选：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部书籍</option>
                    <option>正在阅读</option>
                    <option>已完成</option>
                    <option>计划阅读</option>
                </select>
                
                <label>分类筛选：</label>
                <select class="form-select" style="width: auto;">
                    <option>全部分类</option>
                    <option>技术类</option>
                    <option>文学类</option>
                    <option>历史类</option>
                    <option>科学类</option>
                </select>
                
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addBook()">
                        <i class="fas fa-plus me-1"></i>
                        添加书籍
                    </button>
                    <button class="btn btn-primary" onclick="exportReadingList()">
                        <i class="fas fa-download me-1"></i>
                        导出书单
                    </button>
                </div>
            </div>
        </div>

        <!-- 书籍列表 -->
        <div class="dashboard-container">
            <div class="books-grid">
                <div class="book-card reading">
                    <div class="book-header">
                        <div class="book-info">
                            <div class="book-title">算法导论</div>
                            <div class="book-author">Thomas H. Cormen</div>
                            <div class="book-category">技术类 · 计算机科学</div>
                        </div>
                        <div class="book-status status-reading">正在阅读</div>
                    </div>
                    <div class="book-progress">
                        <div class="progress-header">
                            <div class="progress-label">阅读进度</div>
                            <div class="progress-percentage">45%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 45%"></div>
                        </div>
                    </div>
                    <div class="book-meta">
                        <div class="book-meta-item">
                            <div class="book-meta-label">总页数</div>
                            <div class="book-meta-value">1312</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">已读页数</div>
                            <div class="book-meta-value">590</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">开始时间</div>
                            <div class="book-meta-value">2024-01-01</div>
                        </div>
                    </div>
                    <div class="book-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">写笔记</button>
                    </div>
                </div>

                <div class="book-card completed">
                    <div class="book-header">
                        <div class="book-info">
                            <div class="book-title">人类简史</div>
                            <div class="book-author">尤瓦尔·赫拉利</div>
                            <div class="book-category">历史类 · 人文社科</div>
                        </div>
                        <div class="book-status status-completed">已完成</div>
                    </div>
                    <div class="book-progress">
                        <div class="progress-header">
                            <div class="progress-label">阅读进度</div>
                            <div class="progress-percentage">100%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="book-meta">
                        <div class="book-meta-item">
                            <div class="book-meta-label">总页数</div>
                            <div class="book-meta-value">440</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">阅读天数</div>
                            <div class="book-meta-value">15</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">完成时间</div>
                            <div class="book-meta-value">2023-12-20</div>
                        </div>
                    </div>
                    <div class="book-actions">
                        <button class="btn btn-primary btn-sm">查看笔记</button>
                        <button class="btn btn-success btn-sm">写书评</button>
                    </div>
                </div>

                <div class="book-card reading">
                    <div class="book-header">
                        <div class="book-info">
                            <div class="book-title">深度学习</div>
                            <div class="book-author">Ian Goodfellow</div>
                            <div class="book-category">技术类 · 人工智能</div>
                        </div>
                        <div class="book-status status-reading">正在阅读</div>
                    </div>
                    <div class="book-progress">
                        <div class="progress-header">
                            <div class="progress-label">阅读进度</div>
                            <div class="progress-percentage">25%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 25%"></div>
                        </div>
                    </div>
                    <div class="book-meta">
                        <div class="book-meta-item">
                            <div class="book-meta-label">总页数</div>
                            <div class="book-meta-value">775</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">已读页数</div>
                            <div class="book-meta-value">194</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">开始时间</div>
                            <div class="book-meta-value">2024-01-10</div>
                        </div>
                    </div>
                    <div class="book-actions">
                        <button class="btn btn-primary btn-sm">更新进度</button>
                        <button class="btn btn-success btn-sm">写笔记</button>
                    </div>
                </div>

                <div class="book-card planned">
                    <div class="book-header">
                        <div class="book-info">
                            <div class="book-title">三体</div>
                            <div class="book-author">刘慈欣</div>
                            <div class="book-category">文学类 · 科幻小说</div>
                        </div>
                        <div class="book-status status-planned">计划阅读</div>
                    </div>
                    <div class="book-progress">
                        <div class="progress-header">
                            <div class="progress-label">阅读进度</div>
                            <div class="progress-percentage">0%</div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="book-meta">
                        <div class="book-meta-item">
                            <div class="book-meta-label">总页数</div>
                            <div class="book-meta-value">302</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">计划开始</div>
                            <div class="book-meta-value">2024-02-01</div>
                        </div>
                        <div class="book-meta-item">
                            <div class="book-meta-label">预计天数</div>
                            <div class="book-meta-value">10</div>
                        </div>
                    </div>
                    <div class="book-actions">
                        <button class="btn btn-success btn-sm">开始阅读</button>
                        <button class="btn btn-primary btn-sm">编辑计划</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addBook() {
            alert('添加书籍功能正在开发中...');
        }

        function exportReadingList() {
            alert('导出书单功能正在开发中...');
        }
    </script>
</body>
</html>
