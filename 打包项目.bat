@echo off
chcp 65001 >nul
echo =========================================
echo 比特熊智慧系统 - 项目打包工具
echo =========================================
echo.

set PROJECT_NAME=bitbear_system
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set PACKAGE_NAME=%PROJECT_NAME%_%TIMESTAMP%.zip

echo 📦 准备打包项目...
echo 包名: %PACKAGE_NAME%
echo.

:: 检查是否安装了7zip或WinRAR
set COMPRESS_TOOL=
if exist "C:\Program Files\7-Zip\7z.exe" (
    set COMPRESS_TOOL="C:\Program Files\7-Zip\7z.exe"
    echo ✓ 找到7-Zip压缩工具
) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    set COMPRESS_TOOL="C:\Program Files (x86)\7-Zip\7z.exe"
    echo ✓ 找到7-Zip压缩工具
) else if exist "C:\Program Files\WinRAR\WinRAR.exe" (
    set COMPRESS_TOOL="C:\Program Files\WinRAR\WinRAR.exe"
    echo ✓ 找到WinRAR压缩工具
) else (
    echo ❌ 未找到压缩工具，请安装7-Zip或WinRAR
    echo.
    echo 下载地址:
    echo 7-Zip: https://www.7-zip.org/
    echo WinRAR: https://www.winrar.com/
    pause
    exit /b 1
)

echo.
echo 📋 打包内容:
echo   ✓ 所有PHP文件
echo   ✓ 配置文件
echo   ✓ 数据库文件
echo   ✓ 静态资源
echo   ✓ 上传目录
echo.
echo ❌ 排除内容:
echo   - .git 目录
echo   - node_modules 目录
echo   - 临时文件
echo   - 日志文件
echo   - 开发工具
echo.

:: 创建临时排除列表文件
echo .git\ > exclude_list.txt
echo .gitignore >> exclude_list.txt
echo node_modules\ >> exclude_list.txt
echo *.log >> exclude_list.txt
echo *.tmp >> exclude_list.txt
echo *.bak >> exclude_list.txt
echo .DS_Store >> exclude_list.txt
echo Thumbs.db >> exclude_list.txt
echo putty\ >> exclude_list.txt
echo 打包项目.bat >> exclude_list.txt
echo deploy_to_server.bat >> exclude_list.txt
echo connect-to-server.bat >> exclude_list.txt
echo auto_deploy_to_server.sh >> exclude_list.txt
echo test_fix.php >> exclude_list.txt
echo %PACKAGE_NAME% >> exclude_list.txt

echo 🚀 开始打包...

:: 使用7-Zip打包
if "%COMPRESS_TOOL%"=="C:\Program Files\7-Zip\7z.exe" (
    %COMPRESS_TOOL% a -tzip "%PACKAGE_NAME%" * -x@exclude_list.txt
) else if "%COMPRESS_TOOL%"=="C:\Program Files (x86)\7-Zip\7z.exe" (
    %COMPRESS_TOOL% a -tzip "%PACKAGE_NAME%" * -x@exclude_list.txt
) else (
    :: 使用WinRAR打包
    %COMPRESS_TOOL% a -afzip -ep1 "%PACKAGE_NAME%" * -x@exclude_list.txt
)

:: 清理临时文件
del exclude_list.txt

if exist "%PACKAGE_NAME%" (
    echo.
    echo ✅ 打包完成！
    echo.
    echo 📦 包文件: %PACKAGE_NAME%
    
    :: 获取文件大小
    for %%A in ("%PACKAGE_NAME%") do (
        set SIZE=%%~zA
        set /a SIZE_MB=!SIZE!/1024/1024
    )
    
    echo 📊 文件大小: %SIZE% 字节
    echo.
    echo 📋 上传步骤:
    echo   1. 访问宝塔面板: http://*************:8888
    echo   2. 进入文件管理器
    echo   3. 导航到: /www/wwwroot/www.bitbear.top
    echo   4. 上传 %PACKAGE_NAME%
    echo   5. 右键解压文件
    echo   6. 设置文件权限
    echo.
    
    set /p OPEN_FOLDER="是否打开文件夹查看打包结果？(Y/N): "
    if /i "%OPEN_FOLDER%"=="Y" (
        explorer .
    )
    
) else (
    echo.
    echo ❌ 打包失败！
    echo 请检查压缩工具是否正常工作
)

echo.
echo 按任意键退出...
pause >nul
