<?php
/**
 * 修复菜单表结构 - 添加缺失的icon字段
 */

require_once 'config/database.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复菜单表结构 - 比特熊智慧系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ddd;
        }
        .step.success {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .step.info {
            background: #f0f9ff;
            border-left-color: #3b82f6;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 修复菜单表结构</h1>
            <p>检查并修复navbar_items表的icon字段问题</p>
        </div>
        
        <div class="content">
            <?php
            
            try {
                $dbConfig = DatabaseConfig::getInstance();
                $db = $dbConfig->getConnection();
                
                echo "<div class='step success'>";
                echo "<h3>✅ 数据库连接成功</h3>";
                echo "<p>已成功连接到数据库</p>";
                echo "</div>";
                
                // 1. 检查表是否存在
                echo "<div class='step info'>";
                echo "<h3>🔍 检查navbar_items表</h3>";
                
                $tableExists = false;
                try {
                    $result = $db->query("SHOW TABLES LIKE 'navbar_items'")->fetch();
                    $tableExists = !empty($result);
                    
                    if ($tableExists) {
                        echo "<p>✅ navbar_items表存在</p>";
                    } else {
                        echo "<p>❌ navbar_items表不存在</p>";
                    }
                } catch (Exception $e) {
                    echo "<p>❌ 检查表时出错: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                // 2. 检查表结构
                if ($tableExists) {
                    echo "<div class='step info'>";
                    echo "<h3>🔍 检查表结构</h3>";
                    
                    $columns = $db->query("SHOW COLUMNS FROM navbar_items")->fetchAll();
                    $hasIconField = false;
                    
                    echo "<div class='code-block'>";
                    echo "当前表结构:\n";
                    foreach ($columns as $column) {
                        echo "- " . $column['Field'] . " (" . $column['Type'] . ")\n";
                        if ($column['Field'] === 'icon') {
                            $hasIconField = true;
                        }
                    }
                    echo "</div>";
                    
                    if ($hasIconField) {
                        echo "<p>✅ icon字段已存在</p>";
                    } else {
                        echo "<p>❌ 缺少icon字段</p>";
                    }
                    echo "</div>";
                    
                    // 3. 修复表结构
                    if (!$hasIconField) {
                        echo "<div class='step info'>";
                        echo "<h3>🔧 修复表结构</h3>";
                        
                        try {
                            // 添加icon字段
                            $db->exec("ALTER TABLE navbar_items ADD COLUMN icon VARCHAR(255) NULL AFTER parent_id");
                            echo "<p>✅ 成功添加icon字段</p>";
                            
                            // 验证修复结果
                            $newColumns = $db->query("SHOW COLUMNS FROM navbar_items")->fetchAll();
                            $iconFieldAdded = false;
                            foreach ($newColumns as $column) {
                                if ($column['Field'] === 'icon') {
                                    $iconFieldAdded = true;
                                    break;
                                }
                            }
                            
                            if ($iconFieldAdded) {
                                echo "<p>✅ 字段添加验证成功</p>";
                            } else {
                                echo "<p>❌ 字段添加验证失败</p>";
                            }
                            
                        } catch (Exception $e) {
                            echo "<p>❌ 添加字段失败: " . $e->getMessage() . "</p>";
                        }
                        echo "</div>";
                    }
                } else {
                    // 4. 创建完整的表
                    echo "<div class='step info'>";
                    echo "<h3>🔧 创建navbar_items表</h3>";
                    
                    try {
                        $createTableSQL = "
                        CREATE TABLE navbar_items (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(100) NOT NULL,
                            url VARCHAR(255) NOT NULL,
                            type ENUM('link', 'dropdown', 'submenu') DEFAULT 'link',
                            parent_id INT NULL,
                            icon VARCHAR(255) NULL,
                            visible BOOLEAN DEFAULT TRUE,
                            sort_order INT DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (parent_id) REFERENCES navbar_items(id) ON DELETE CASCADE,
                            INDEX idx_parent (parent_id),
                            INDEX idx_order (sort_order)
                        )";
                        
                        $db->exec($createTableSQL);
                        echo "<p>✅ navbar_items表创建成功</p>";
                        
                        // 插入默认数据
                        $defaultData = [
                            ['首页', '/index.php', 'link', null, 'fas fa-home', 1],
                            ['关于我们', '/about.php', 'link', null, 'fas fa-info-circle', 2],
                            ['服务', '/services.php', 'link', null, 'fas fa-cogs', 3],
                            ['联系我们', '/contact.php', 'link', null, 'fas fa-envelope', 4]
                        ];
                        
                        $stmt = $db->prepare("INSERT INTO navbar_items (name, url, type, parent_id, icon, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                        foreach ($defaultData as $item) {
                            $stmt->execute($item);
                        }
                        
                        echo "<p>✅ 默认菜单数据插入成功</p>";
                        
                    } catch (Exception $e) {
                        echo "<p>❌ 创建表失败: " . $e->getMessage() . "</p>";
                    }
                    echo "</div>";
                }
                
                // 5. 测试菜单功能
                echo "<div class='step info'>";
                echo "<h3>🧪 测试菜单功能</h3>";
                
                try {
                    // 测试插入一个菜单项
                    $testName = '测试菜单_' . time();
                    $stmt = $db->prepare("INSERT INTO navbar_items (name, url, type, parent_id, icon, visible, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([$testName, '/test.php', 'link', null, 'fas fa-test', true, 999]);
                    
                    echo "<p>✅ 菜单插入测试成功</p>";
                    
                    // 删除测试菜单
                    $db->prepare("DELETE FROM navbar_items WHERE name = ?")->execute([$testName]);
                    echo "<p>✅ 测试菜单清理完成</p>";
                    
                } catch (Exception $e) {
                    echo "<p>❌ 菜单功能测试失败: " . $e->getMessage() . "</p>";
                }
                echo "</div>";
                
                echo "<div class='step success'>";
                echo "<h3>🎉 修复完成</h3>";
                echo "<p>navbar_items表结构已修复，现在可以正常添加菜单项了！</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='step error'>";
                echo "<h3>❌ 修复失败</h3>";
                echo "<p>错误信息: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
            
            ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="admin-dashboard.php" class="btn">返回管理后台</a>
                <a href="云服务器兼容性检查.php" class="btn">兼容性检查</a>
            </div>
        </div>
    </div>
</body>
</html>
