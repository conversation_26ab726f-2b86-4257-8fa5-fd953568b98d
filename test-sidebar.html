<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏测试</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            padding: 1rem;
        }
        
        .nav-item {
            margin-bottom: 0.5rem;
        }
        
        .nav-item.has-submenu .nav-link {
            position: relative;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .nav-text {
            margin-left: 0.75rem;
            flex: 1;
        }
        
        .nav-arrow {
            margin-left: auto;
            transition: transform 0.3s ease;
        }
        
        .nav-item.has-submenu.expanded .nav-arrow {
            transform: rotate(90deg);
        }
        
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.1);
            margin-left: 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
        }
        
        .nav-item.has-submenu.expanded .submenu {
            max-height: 200px;
        }
        
        .submenu-item {
            padding: 0;
        }
        
        .submenu-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 6px;
            margin: 0.25rem;
        }
        
        .submenu-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .test-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="nav-item has-submenu" id="testNavItem">
            <a href="#" class="nav-link" data-page="community-management">
                <div class="nav-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 11.5A8.38 8.38 0 0 1 14.5 21A8.38 8.38 0 0 1 3 14.5A8.38 8.38 0 0 1 9.5 3A8.38 8.38 0 0 1 21 11.5Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <span class="nav-text">社区管理</span>
                <div class="nav-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </a>
            <div class="submenu">
                <div class="submenu-item">
                    <a href="#" class="submenu-link" data-page="posts-management">
                        <span>帖子管理</span>
                    </a>
                </div>
                <div class="submenu-item">
                    <a href="#" class="submenu-link" data-page="categories-management">
                        <span>分类管理</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-info">
        <h3>侧边栏测试</h3>
        <p>点击"社区管理"应该展开子菜单</p>
        <div id="testResults"></div>
        <button onclick="manualTest()">手动测试</button>
    </div>

    <script>
        function initializeNavigation() {
            console.log('初始化导航功能...');
            
            document.addEventListener('click', function(e) {
                console.log('点击事件触发:', e.target);
                
                const navLink = e.target.closest('.nav-link');
                const submenuLink = e.target.closest('.submenu-link');

                if (submenuLink) {
                    e.preventDefault();
                    console.log('点击了子菜单项:', submenuLink);
                    const pageId = submenuLink.getAttribute('data-page');
                    console.log('页面ID:', pageId);
                } else if (navLink) {
                    e.preventDefault();
                    console.log('点击了导航链接:', navLink);
                    
                    const navItem = navLink.closest('.nav-item');
                    console.log('导航项:', navItem);

                    // 处理有子菜单的项目
                    if (navItem.classList.contains('has-submenu')) {
                        console.log('这是一个有子菜单的项目，切换展开状态');
                        navItem.classList.toggle('expanded');
                        
                        const isExpanded = navItem.classList.contains('expanded');
                        console.log('展开状态:', isExpanded);
                        
                        document.getElementById('testResults').innerHTML = 
                            `<p>菜单${isExpanded ? '已展开' : '已收起'}</p>`;
                        return;
                    }

                    const pageId = navLink.getAttribute('data-page');
                    if (pageId) {
                        console.log('切换到页面:', pageId);
                    }
                }
            });
        }
        
        function manualTest() {
            const navItem = document.getElementById('testNavItem');
            const navLink = navItem.querySelector('.nav-link');
            
            console.log('手动测试开始...');
            console.log('导航项:', navItem);
            console.log('导航链接:', navLink);
            
            // 模拟点击
            navLink.click();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，初始化导航...');
            initializeNavigation();
            
            document.getElementById('testResults').innerHTML = 
                '<p>导航功能已初始化，请点击"社区管理"测试</p>';
        });
    </script>
</body>
</html>
