<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

echo "<h2>数据库表检查</h2>";

try {
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";
    
    // 检查所有表
    echo "<h3>1. 所有表列表</h3>";
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: red;'>✗ 没有找到任何表</p>";
        echo "<p>需要运行数据库初始化脚本</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
    // 检查comments表结构
    if (in_array('comments', $tables)) {
        echo "<h3>2. comments表结构</h3>";
        $stmt = $db->query("DESCRIBE comments");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查comments表数据
        echo "<h3>3. comments表数据示例</h3>";
        $stmt = $db->query("SELECT * FROM comments LIMIT 5");
        $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($comments)) {
            echo "<p>comments表为空</p>";
        } else {
            echo "<pre>" . json_encode($comments, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        }
    } else {
        echo "<h3>2. comments表不存在</h3>";
        echo "<p style='color: red;'>✗ comments表不存在，需要创建</p>";
    }
    
    // 检查其他相关表
    $relatedTables = ['users', 'posts', 'user_profiles', 'comment_reports'];
    echo "<h3>4. 相关表检查</h3>";
    foreach ($relatedTables as $table) {
        if (in_array($table, $tables)) {
            echo "<p style='color: green;'>✓ $table 表存在</p>";
        } else {
            echo "<p style='color: orange;'>⚠ $table 表不存在</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 错误: " . $e->getMessage() . "</p>";
}
?>
