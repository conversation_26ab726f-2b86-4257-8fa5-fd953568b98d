<?php
// 测试SQLite数据库连接脚本
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>SQLite数据库连接测试</h2>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>\n";

// 检查数据库目录
$db_dir = __DIR__ . '/database';
echo "<h3>目录和文件检查：</h3>\n";
echo "<p class='info'>数据库目录: " . $db_dir . "</p>\n";
echo "<p class='info'>目录存在: " . (is_dir($db_dir) ? "是" : "否") . "</p>\n";
echo "<p class='info'>目录可写: " . (is_writable($db_dir) ? "是" : "否") . "</p>\n";

// 检查数据库文件
$sqlite_path = $db_dir . '/bitbear_system.sqlite';
echo "<p class='info'>数据库文件: " . $sqlite_path . "</p>\n";
echo "<p class='info'>文件存在: " . (file_exists($sqlite_path) ? "是" : "否") . "</p>\n";

if (file_exists($sqlite_path)) {
    echo "<p class='info'>文件可读: " . (is_readable($sqlite_path) ? "是" : "否") . "</p>\n";
    echo "<p class='info'>文件可写: " . (is_writable($sqlite_path) ? "是" : "否") . "</p>\n";
    echo "<p class='info'>文件大小: " . filesize($sqlite_path) . " 字节</p>\n";
}

echo "<h3>SQLite连接测试：</h3>\n";

// 尝试创建数据库连接
try {
    // 确保目录存在
    if (!is_dir($db_dir)) {
        mkdir($db_dir, 0755, true);
        echo "<p class='success'>创建数据库目录成功</p>\n";
    }
    
    // 如果数据库文件不存在，创建一个空文件
    if (!file_exists($sqlite_path)) {
        touch($sqlite_path);
        chmod($sqlite_path, 0644);
        echo "<p class='success'>创建数据库文件成功</p>\n";
    }

    $dsn = "sqlite:" . $sqlite_path;
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 30,
    ];

    $pdo = new PDO($dsn, null, null, $options);
    echo "<p class='success'>✅ SQLite连接成功!</p>\n";
    
    // 设置SQLite特定的配置
    $pdo->exec("PRAGMA foreign_keys = ON");
    $pdo->exec("PRAGMA journal_mode = WAL");
    $pdo->exec("PRAGMA synchronous = NORMAL");
    echo "<p class='success'>SQLite配置设置成功</p>\n";
    
    // 检查现有表
    $tables = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'")->fetchAll(PDO::FETCH_COLUMN);
    if (!empty($tables)) {
        echo "<p class='info'>现有表: " . implode(', ', $tables) . "</p>\n";
    } else {
        echo "<p class='info'>数据库为空，需要初始化</p>\n";
    }
    
    // 测试创建一个简单的表
    $pdo->exec("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)");
    echo "<p class='success'>测试表创建成功</p>\n";
    
    // 测试插入数据
    $pdo->exec("INSERT OR IGNORE INTO test_table (name) VALUES ('test')");
    echo "<p class='success'>测试数据插入成功</p>\n";
    
    // 测试查询数据
    $stmt = $pdo->query("SELECT * FROM test_table");
    $results = $stmt->fetchAll();
    echo "<p class='success'>查询到 " . count($results) . " 条记录</p>\n";
    
    // 清理测试表
    $pdo->exec("DROP TABLE IF EXISTS test_table");
    echo "<p class='success'>测试表清理成功</p>\n";
    
    echo "<p class='success'>✅ 数据库连接测试完成，一切正常!</p>\n";
    
} catch (PDOException $e) {
    echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>\n";
    echo "<p class='error'>错误代码: " . $e->getCode() . "</p>\n";
} catch (Exception $e) {
    echo "<p class='error'>❌ 其他错误: " . $e->getMessage() . "</p>\n";
}

// 测试包含数据库配置类
echo "<h3>数据库配置类测试：</h3>\n";
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    $connection = $db->getConnection();
    echo "<p class='success'>✅ 数据库配置类连接成功!</p>\n";

    // 检查连接的数据库类型
    $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
    echo "<p class='info'>使用的数据库驱动: " . $driver . "</p>\n";

} catch (Exception $e) {
    echo "<p class='error'>❌ 数据库配置类连接失败: " . $e->getMessage() . "</p>\n";
}
