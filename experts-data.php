<?php
// 专家数据配置文件
// 这个文件包含了所有专家的信息，可以通过后台管理系统进行修改

$experts = [
    [
        'id' => 1,
        'name' => 'Arianne Dee',
        'title' => 'Software developer',
        'image' => 'image/default-avatar.svg',
        'alt' => '<PERSON><PERSON> - Software developer'
    ],
    [
        'id' => 2,
        'name' => '<PERSON><PERSON>',
        'title' => 'Cybersecurity practitioner',
        'image' => 'image/default-avatar.svg',
        'alt' => '<PERSON><PERSON> - Cybersecurity practitioner'
    ],
    [
        'id' => 3,
        'name' => '<PERSON>',
        'title' => 'Senior data scientist',
        'image' => 'image/default-avatar.svg',
        'alt' => '<PERSON> - Senior data scientist'
    ],
    [
        'id' => 4,
        'name' => '<PERSON>',
        'title' => 'Software architect',
        'image' => 'image/default-avatar.svg',
        'alt' => '<PERSON> - Software architect'
    ],
    [
        'id' => 5,
        'name' => '<PERSON> Hightower',
        'title' => 'Software engineer',
        'image' => 'image/default-avatar.svg',
        'alt' => '<PERSON> Hightower - Software engineer'
    ],
    [
        'id' => 6,
        'name' => '<PERSON>n',
        'title' => 'Java Champion',
        'image' => 'image/default-avatar.svg',
        'alt' => 'Ken Kousen - Java Champion'
    ]
];

// 获取专家数据的函数
function getExperts() {
    global $experts;
    return $experts;
}

// 获取单个专家数据的函数
function getExpert($id) {
    global $experts;
    foreach ($experts as $expert) {
        if ($expert['id'] == $id) {
            return $expert;
        }
    }
    return null;
}

// 添加新专家的函数（用于后台管理）
function addExpert($name, $title, $image, $alt = '') {
    global $experts;
    $newId = count($experts) + 1;
    $experts[] = [
        'id' => $newId,
        'name' => $name,
        'title' => $title,
        'image' => $image,
        'alt' => $alt ?: $name . ' - ' . $title
    ];
    return $newId;
}

// 更新专家信息的函数（用于后台管理）
function updateExpert($id, $name, $title, $image, $alt = '') {
    global $experts;
    for ($i = 0; $i < count($experts); $i++) {
        if ($experts[$i]['id'] == $id) {
            $experts[$i]['name'] = $name;
            $experts[$i]['title'] = $title;
            $experts[$i]['image'] = $image;
            $experts[$i]['alt'] = $alt ?: $name . ' - ' . $title;
            return true;
        }
    }
    return false;
}

// 删除专家的函数（用于后台管理）
function deleteExpert($id) {
    global $experts;
    for ($i = 0; $i < count($experts); $i++) {
        if ($experts[$i]['id'] == $id) {
            array_splice($experts, $i, 1);
            return true;
        }
    }
    return false;
}
?>
