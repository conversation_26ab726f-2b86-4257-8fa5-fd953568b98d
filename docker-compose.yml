version: '3.8'

services:
  web:
    build: .
    ports:
      - "80:80"
    volumes:
      - ./uploads:/var/www/html/uploads
    environment:
      - DB_HOST=db
      - DB_NAME=wisdom_system
      - DB_USER=root
      - DB_PASS=password
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: wisdom_system
    volumes:
      - db_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d

volumes:
  db_data:
