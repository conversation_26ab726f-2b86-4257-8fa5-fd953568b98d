<?php
/**
 * 移动端导航测试页面
 * 专门用于测试移动端导航菜单的功能
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端导航测试 - 比特熊智慧系统</title>
    <link rel="stylesheet" href="index-style.css?v=<?php echo time(); ?>">
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem;
            color: #0c4a6e;
        }
        .test-info h3 {
            margin: 0 0 0.5rem 0;
            color: #0369a1;
        }
        .main-content {
            padding: 2rem 1rem;
            max-width: 800px;
            margin: 0 auto;
        }
        .feature-list {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .feature-item:last-child {
            border-bottom: none;
        }
        .feature-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }
        .demo-section {
            background: #fefce8;
            border: 1px solid #facc15;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="center">
            <img src="image/bit.png" width="130px" height="60px" alt="比特熊Logo">
            
            <ul class="list-left" id="dynamicNavbar">
                <?php
                // 包含导航栏组件并获取数据
                require_once 'components/navbar.php';
                $navbarData = getNavbarData();

                // 渲染导航菜单项
                foreach ($navbarData as $item) {
                    $hasChildren = !empty($item['children']);
                    $itemClass = $hasChildren ? 'nav-item dropdown' : 'nav-item';
                    
                    if ($hasChildren) {
                        echo '<li class="' . $itemClass . '">';
                        echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle">';
                        echo htmlspecialchars($item['name']);
                        echo '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
                        echo '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>';
                        echo '</svg>';
                        echo '</a>';
                        echo '<ul class="dropdown-menu">';
                        foreach ($item['children'] as $child) {
                            echo '<li><a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">' . htmlspecialchars($child['name']) . '</a></li>';
                        }
                        echo '</ul>';
                        echo '</li>';
                    } else {
                        echo '<li class="' . $itemClass . '">';
                        echo '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">' . htmlspecialchars($item['name']) . '</a>';
                        echo '</li>';
                    }
                }
                ?>
            </ul>

            <div class="list-right">
                <div class="mobile-search-btn-container">
                    <button class="search-btn mobile-search-btn" aria-label="搜索">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
                
                <div class="loading">
                    <a href="admin-login.php">登录</a>
                </div>
                <div class="zhuche">
                    <a href="register.php">注册</a>
                </div>
            </div>

            <!-- 移动端菜单按钮 -->
            <button class="mobile-menu-btn" aria-label="打开菜单">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
        
        <!-- 移动端菜单遮罩层 -->
        <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>
    </header>

    <div class="main-content">
        <div class="test-info">
            <h3>📱 移动端导航测试页面</h3>
            <p>此页面专门用于测试移动端导航菜单的优化效果。请在移动设备或浏览器的移动模式下测试。</p>
        </div>

        <div class="feature-list">
            <h3>🎯 测试要点</h3>
            <div class="feature-item">
                <span class="feature-icon">📏</span>
                <span>菜单高度：现在占据全屏高度，不再被压缩</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">👆</span>
                <span>触摸友好：菜单项高度增加，便于手指点击</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🎭</span>
                <span>遮罩层：展开时显示半透明背景遮罩</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔄</span>
                <span>动画效果：平滑的展开/收起动画</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">⌨️</span>
                <span>键盘支持：ESC键关闭菜单</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">🚫</span>
                <span>滚动锁定：菜单展开时禁止页面滚动</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✨</span>
                <span>毛玻璃效果：现代化的半透明背景，支持暗色模式</span>
            </div>
        </div>

        <div class="demo-section">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li>点击右上角的汉堡菜单按钮（三条横线）</li>
                <li>观察菜单是否全屏展开，高度是否足够</li>
                <li>测试菜单项是否容易点击</li>
                <li>测试下拉菜单的展开功能</li>
                <li>点击遮罩层或按ESC键关闭菜单</li>
                <li>验证菜单关闭时的动画效果</li>
            </ol>
        </div>

        <div style="text-align: center; margin: 2rem 0;">
            <a href="index.php" style="display: inline-block; padding: 12px 24px; background: #3b82f6; color: white; text-decoration: none; border-radius: 6px; margin: 0 0.5rem;">返回主页</a>
            <a href="mobile_nav_test.html" style="display: inline-block; padding: 12px 24px; background: #10b981; color: white; text-decoration: none; border-radius: 6px; margin: 0 0.5rem;">查看测试报告</a>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化所有功能
        document.addEventListener('DOMContentLoaded', function() {
            initMobileMenu();
            initDropdownMenus();
            
            // 显示当前屏幕尺寸
            showScreenInfo();
        });

        // 移动端导航菜单切换功能
        function initMobileMenu() {
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navMenu = document.querySelector('.list-left');
            const overlay = document.querySelector('.mobile-menu-overlay');

            if (mobileMenuBtn && navMenu) {
                mobileMenuBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const isActive = navMenu.classList.toggle('active');
                    this.classList.toggle('active');
                    if (overlay) {
                        overlay.classList.toggle('active', isActive);
                    }
                    document.body.style.overflow = isActive ? 'hidden' : '';
                });

                if (overlay) {
                    overlay.addEventListener('click', function() {
                        closeMenu();
                    });
                }

                function closeMenu() {
                    navMenu.classList.remove('active');
                    mobileMenuBtn.classList.remove('active');
                    if (overlay) {
                        overlay.classList.remove('active');
                    }
                    document.body.style.overflow = '';
                }

                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                        closeMenu();
                    }
                });

                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        closeMenu();
                    }
                    showScreenInfo();
                });
            }
        }

        // 下拉菜单功能
        function initDropdownMenus() {
            const dropdownItems = document.querySelectorAll('.nav-item.dropdown');

            function isMobile() {
                return window.innerWidth <= 768;
            }

            dropdownItems.forEach(item => {
                const dropdownToggle = item.querySelector('.dropdown-toggle');
                const dropdownMenu = item.querySelector('.dropdown-menu');
                const dropdownArrow = item.querySelector('.dropdown-arrow');

                if (dropdownToggle && dropdownMenu && dropdownArrow) {
                    dropdownToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        
                        dropdownItems.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.querySelector('.dropdown-menu').classList.remove('show');
                                otherItem.querySelector('.dropdown-arrow').classList.remove('rotated');
                            }
                        });

                        dropdownMenu.classList.toggle('show');
                        dropdownArrow.classList.toggle('rotated');
                    });
                }
            });
        }

        // 显示屏幕信息
        function showScreenInfo() {
            const existing = document.getElementById('screen-info');
            if (existing) existing.remove();

            const info = document.createElement('div');
            info.id = 'screen-info';
            info.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 10000;
                font-family: monospace;
            `;
            
            const width = window.innerWidth;
            const height = window.innerHeight;
            const isMobile = width <= 768;
            
            info.textContent = `${width}×${height} ${isMobile ? '📱' : '🖥️'}`;
            document.body.appendChild(info);
        }
    </script>
</body>
</html>
