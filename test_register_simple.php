<?php
/**
 * 简化的注册测试脚本
 * 直接测试注册API功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 模拟POST请求数据
$_POST = [
    'username' => 'testuser' . time(),
    'email' => 'test' . time() . '@example.com',
    'nickname' => '测试用户' . time(),
    'password' => 'test123456',
    'confirmPassword' => 'test123456'
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>注册API测试</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".code { background: #f8f9fa; border: 1px solid #e9ecef; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }";
echo "</style>";
echo "</head>";
echo "<body>";
echo "<div class='container'>";
echo "<h1>🧪 注册API测试</h1>";

echo "<div class='info'>";
echo "<h3>测试数据:</h3>";
echo "<div class='code'>" . json_encode($_POST, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</div>";
echo "</div>";

echo "<div class='info'>";
echo "<h3>测试结果:</h3>";
echo "</div>";

// 开始缓冲输出
ob_start();

try {
    // 包含注册API
    require_once 'config/database.php';
    
    echo "<div class='success'>✅ 数据库配置加载成功</div>";
    
    // 检查数据库连接
    try {
        $db = DatabaseConfig::getInstance();
        $db->query("SELECT 1");
        echo "<div class='success'>✅ 数据库连接成功</div>";
    } catch (Exception $dbError) {
        echo "<div class='error'>❌ 数据库连接失败: " . $dbError->getMessage() . "</div>";
        throw $dbError;
    }

    // 获取表单数据
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $nickname = trim($_POST['nickname'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirmPassword'] ?? '';
    
    echo "<div class='info'>📝 表单数据验证通过</div>";
    
    // 数据验证
    $errors = [];
    
    // 验证用户名
    if (empty($username)) {
        $errors['username'] = '请输入用户名';
    } elseif (strlen($username) < 3) {
        $errors['username'] = '用户名至少需要3个字符';
    } elseif (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        $errors['username'] = '用户名只能包含字母、数字、下划线和中文';
    } else {
        // 检查用户名是否已存在
        $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
        if ($existingUser) {
            $errors['username'] = '用户名已存在';
        }
    }
    
    // 验证邮箱
    if (empty($email)) {
        $errors['email'] = '请输入邮箱地址';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = '请输入有效的邮箱地址';
    } else {
        // 检查邮箱是否已存在
        $existingEmail = $db->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
        if ($existingEmail) {
            $errors['email'] = '邮箱地址已被注册';
        }
    }
    
    // 验证昵称
    if (empty($nickname)) {
        $errors['nickname'] = '请输入昵称';
    } elseif (strlen($nickname) < 2) {
        $errors['nickname'] = '昵称至少需要2个字符';
    }
    
    // 验证密码
    if (empty($password)) {
        $errors['password'] = '请输入密码';
    } elseif (strlen($password) < 6) {
        $errors['password'] = '密码至少需要6个字符';
    }
    
    // 验证确认密码
    if (empty($confirmPassword)) {
        $errors['confirmPassword'] = '请确认密码';
    } elseif ($password !== $confirmPassword) {
        $errors['confirmPassword'] = '两次输入的密码不一致';
    }
    
    // 如果有验证错误，显示错误信息
    if (!empty($errors)) {
        echo "<div class='error'>";
        echo "<h4>❌ 数据验证失败:</h4>";
        foreach ($errors as $field => $error) {
            echo "<p>{$field}: {$error}</p>";
        }
        echo "</div>";
        throw new Exception('数据验证失败');
    }
    
    echo "<div class='success'>✅ 数据验证通过</div>";
    
    // 处理头像上传
    $avatarUrl = 'assets/images/default-avatar.png'; // 默认头像
    echo "<div class='info'>📷 使用默认头像: {$avatarUrl}</div>";
    
    // 开始数据库事务
    $db->getConnection()->beginTransaction();
    echo "<div class='info'>🔄 开始数据库事务</div>";
    
    try {
        // 获取普通用户角色ID
        $userRole = $db->fetchOne("SELECT id FROM user_roles WHERE role_code = 'user'");
        $roleId = $userRole ? $userRole['id'] : 3; // 默认为3（普通用户）
        echo "<div class='info'>👤 用户角色ID: {$roleId}</div>";
        
        // 创建用户记录
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        $sql = "INSERT INTO users (username, email, password_hash, full_name, role_id, status, created_at)
                VALUES (?, ?, ?, ?, ?, 'active', CURRENT_TIMESTAMP)";

        $stmt = $db->getConnection()->prepare($sql);
        $result = $stmt->execute([
            $username,
            $email,
            $passwordHash,
            $nickname, // 使用昵称作为全名
            $roleId
        ]);

        if (!$result) {
            throw new Exception('创建用户失败');
        }

        // 获取插入的ID
        $userId = $db->getConnection()->lastInsertId();
        echo "<div class='success'>✅ 用户创建成功，ID: {$userId}</div>";

        if (!$userId || $userId == 0) {
            throw new Exception('获取用户ID失败');
        }

        // 创建用户个人资料记录
        $profileSql = "INSERT INTO user_profiles (user_id, nickname, avatar_url, created_at)
                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)";

        $profileStmt = $db->getConnection()->prepare($profileSql);
        $profileResult = $profileStmt->execute([
            $userId,
            $nickname,
            $avatarUrl
        ]);
        
        if (!$profileResult) {
            throw new Exception('创建用户资料失败');
        }
        
        echo "<div class='success'>✅ 用户资料创建成功</div>";
        
        // 提交事务
        $db->getConnection()->commit();
        echo "<div class='success'>✅ 数据库事务提交成功</div>";
        
        // 记录注册日志
        error_log("新用户注册成功: ID={$userId}, 用户名={$username}, 邮箱={$email}");
        
        echo "<div class='success'>";
        echo "<h3>🎉 注册测试完全成功！</h3>";
        echo "<p>用户ID: {$userId}</p>";
        echo "<p>用户名: {$username}</p>";
        echo "<p>邮箱: {$email}</p>";
        echo "<p>昵称: {$nickname}</p>";
        echo "</div>";
        
        // 显示JSON响应格式
        $response = [
            'success' => true,
            'message' => '注册成功！请登录您的账户',
            'user_id' => $userId
        ];
        
        echo "<div class='info'>";
        echo "<h4>API响应格式:</h4>";
        echo "<div class='code'>" . json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        // 回滚事务
        $db->getConnection()->rollback();
        echo "<div class='error'>❌ 事务回滚: " . $e->getMessage() . "</div>";
        throw $e;
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h4>❌ 注册测试失败</h4>";
    echo "<p>错误信息: " . $e->getMessage() . "</p>";
    echo "<p>错误文件: " . basename($e->getFile()) . "</p>";
    echo "<p>错误行号: " . $e->getLine() . "</p>";
    echo "</div>";
    
    // 显示错误响应格式
    $errorResponse = [
        'success' => false,
        'message' => '注册过程中发生错误，请稍后重试',
        'debug_info' => [
            'error' => $e->getMessage(),
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ];
    
    echo "<div class='error'>";
    echo "<h4>API错误响应格式:</h4>";
    echo "<div class='code'>" . json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</div>";
    echo "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='register.php' style='display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>测试注册页面</a>";
echo "<a href='fix_server_register.php' style='display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>运行修复脚本</a>";
echo "<a href='debug_register_server.php' style='display: inline-block; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; margin: 5px;'>详细诊断</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
