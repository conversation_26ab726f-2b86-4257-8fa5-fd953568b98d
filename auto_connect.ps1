# 比特熊智慧系统 - 自动SSH连接脚本
param(
    [string]$ServerIP = "*************",
    [string]$Username = "root",
    [string]$Password = "ZbDX7%=]?H2(LAUz"
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "比特熊智慧系统 - 自动SSH连接" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "服务器信息:" -ForegroundColor Yellow
Write-Host "IP地址: $ServerIP" -ForegroundColor White
Write-Host "用户名: $Username" -ForegroundColor White
Write-Host "密码: $Password" -ForegroundColor Red
Write-Host ""

# 测试网络连接
Write-Host "正在测试网络连接..." -ForegroundColor Yellow
try {
    $pingResult = Test-Connection -ComputerName $ServerIP -Count 2 -Quiet -ErrorAction Stop
    if ($pingResult) {
        Write-Host "✓ 网络连接正常" -ForegroundColor Green
    } else {
        throw "Ping失败"
    }
} catch {
    Write-Host "❌ 网络连接失败，请检查网络或服务器状态" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "正在检测SSH客户端..." -ForegroundColor Yellow

# 定义SSH客户端路径
$sshClients = @(
    @{Name="Windows内置SSH"; Path="C:\Windows\System32\OpenSSH\ssh.exe"},
    @{Name="Git SSH"; Path="C:\Program Files\Git\usr\bin\ssh.exe"},
    @{Name="Git SSH (x86)"; Path="C:\Program Files (x86)\Git\usr\bin\ssh.exe"}
)

$sshPath = $null
$sshName = $null

# 检查SSH客户端
foreach ($client in $sshClients) {
    if (Test-Path $client.Path) {
        $sshPath = $client.Path
        $sshName = $client.Name
        Write-Host "✓ 找到 $($client.Name)" -ForegroundColor Green
        break
    }
}

# 如果没找到，尝试系统PATH中的SSH
if (-not $sshPath) {
    try {
        $null = Get-Command ssh -ErrorAction Stop
        $sshPath = "ssh"
        $sshName = "系统SSH"
        Write-Host "✓ 找到系统SSH客户端" -ForegroundColor Green
    } catch {
        Write-Host "❌ 未找到SSH客户端" -ForegroundColor Red
        
        # 尝试安装Windows OpenSSH
        Write-Host "正在尝试安装Windows OpenSSH客户端..." -ForegroundColor Yellow
        try {
            $capability = Get-WindowsCapability -Online | Where-Object Name -like "OpenSSH.Client*"
            if ($capability.State -ne "Installed") {
                Add-WindowsCapability -Online -Name $capability.Name -ErrorAction Stop
                Write-Host "✓ OpenSSH客户端安装成功" -ForegroundColor Green
                $sshPath = "C:\Windows\System32\OpenSSH\ssh.exe"
                $sshName = "Windows内置SSH"
            }
        } catch {
            Write-Host "❌ 自动安装失败，需要管理员权限" -ForegroundColor Red
            Write-Host ""
            Write-Host "请手动安装SSH客户端：" -ForegroundColor Yellow
            Write-Host "1. 以管理员身份运行PowerShell" -ForegroundColor White
            Write-Host "2. 执行: Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******" -ForegroundColor White
            Write-Host "或者下载Git for Windows: https://git-scm.com/download/win" -ForegroundColor White
            Read-Host "按回车键退出"
            exit 1
        }
    }
}

Write-Host ""
Write-Host "正在使用 $sshName 连接到服务器..." -ForegroundColor Yellow
Write-Host "连接命令: $sshPath $Username@$ServerIP" -ForegroundColor Gray
Write-Host ""

# 创建自动输入密码的脚本
$expectScript = @"
#!/usr/bin/expect -f
set timeout 30
spawn $sshPath -o StrictHostKeyChecking=no $Username@$ServerIP
expect {
    "Are you sure you want to continue connecting" {
        send "yes\r"
        expect "password:"
        send "$Password\r"
    }
    "password:" {
        send "$Password\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}
interact
"@

# 保存expect脚本
$expectFile = Join-Path $env:TEMP "ssh_auto_connect.exp"
$expectScript | Out-File -FilePath $expectFile -Encoding ASCII

# 检查是否有expect命令
try {
    $null = Get-Command expect -ErrorAction Stop
    Write-Host "使用expect自动输入密码..." -ForegroundColor Green
    & expect $expectFile
} catch {
    # 没有expect，尝试使用plink（如果可用）
    try {
        $null = Get-Command plink -ErrorAction Stop
        Write-Host "使用plink自动连接..." -ForegroundColor Green
        & plink -ssh -batch -pw $Password $Username@$ServerIP
    } catch {
        # 使用普通SSH连接（需要手动输入密码）
        Write-Host "自动密码输入不可用，请手动输入密码" -ForegroundColor Yellow
        Write-Host "密码: $Password" -ForegroundColor Red
        Write-Host ""
        
        # 启动SSH连接
        if ($sshPath -eq "ssh") {
            & ssh -o StrictHostKeyChecking=no $Username@$ServerIP
        } else {
            & $sshPath -o StrictHostKeyChecking=no $Username@$ServerIP
        }
    }
}

# 清理临时文件
if (Test-Path $expectFile) {
    Remove-Item $expectFile -Force
}

Write-Host ""
Write-Host "连接已断开" -ForegroundColor Yellow
Read-Host "按回车键退出"
