#!/bin/bash

# 服务器信息
SERVER_IP="*************"
SERVER_USER="root"
SERVER_PASSWORD="ZbDX7%=]?H2(LAUz"
REMOTE_PATH="/www/wwwroot/比特熊组织网站项目(v0.0.1)"
LOCAL_PATH="."

echo "开始部署比特熊智慧系统到服务器..."

# 1. 创建临时的SSH配置
echo "配置SSH连接..."
export SSHPASS="$SERVER_PASSWORD"

# 2. 测试SSH连接
echo "测试SSH连接..."
sshpass -e ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 $SERVER_USER@$SERVER_IP "echo 'SSH连接成功'"

if [ $? -ne 0 ]; then
    echo "SSH连接失败，尝试手动连接..."
    echo "请手动执行以下命令："
    echo "ssh root@*************"
    echo "密码: ZbDX7%=]?H2(LAUz"
    exit 1
fi

# 3. 创建远程目录
echo "创建远程目录..."
sshpass -e ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "mkdir -p '$REMOTE_PATH'"

# 4. 使用scp上传文件
echo "正在上传文件到服务器..."
sshpass -e scp -r -o StrictHostKeyChecking=no \
    --exclude='.git' --exclude='node_modules' --exclude='*.log' \
    $LOCAL_PATH/* $SERVER_USER@$SERVER_IP:"$REMOTE_PATH/"

# 5. 连接服务器执行部署命令
echo "连接服务器执行部署命令..."
sshpass -e ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP << 'EOF'
    cd "/www/wwwroot/比特熊组织网站项目(v0.0.1)"
    
    # 设置文件权限
    chmod -R 755 .
    chmod -R 777 uploads/ 2>/dev/null || mkdir -p uploads && chmod -R 777 uploads/
    
    # 检查MySQL是否运行
    if ! systemctl is-active --quiet mysql; then
        echo "启动MySQL服务..."
        systemctl start mysql
    fi
    
    # 创建数据库
    echo "创建数据库..."
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS bitbear_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || echo "数据库可能已存在"
    
    # 导入数据库结构
    if [ -f "database/init.sql" ]; then
        echo "导入数据库结构..."
        mysql -u root bitbear_system < database/init.sql
    fi
    
    # 检查Apache/Nginx配置
    if systemctl is-active --quiet apache2; then
        echo "重启Apache服务..."
        systemctl reload apache2
    elif systemctl is-active --quiet nginx; then
        echo "重启Nginx服务..."
        systemctl reload nginx
    fi
    
    echo "服务器部署完成！"
EOF

echo "部署完成！"
