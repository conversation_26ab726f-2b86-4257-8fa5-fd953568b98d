@echo off
echo ========================================
echo 腾讯云服务器SSH连接工具
echo ========================================
echo.
echo 服务器信息:
echo   IP地址: *************
echo   用户名: root
echo   密码: ZbDX7%=]?H2(LAUz
echo   操作系统: CentOS 7.8 64bit
echo.

REM 检查SSH客户端
echo 正在检查SSH客户端...
echo.

REM 方法1: Windows内置SSH
if exist "C:\Windows\System32\OpenSSH\ssh.exe" (
    echo ✓ 找到Windows内置SSH客户端
    echo.
    echo 连接命令: ssh root@*************
    echo 密码: ZbDX7%=]?H2(LAUz
    echo.
    echo 注意: 输入密码时不会显示字符，这是正常的安全特性
    echo.
    pause
    echo 正在连接到服务器...
    C:\Windows\System32\OpenSSH\ssh.exe root@*************
    goto :end
)

REM 方法2: Git SSH
if exist "C:\Program Files\Git\usr\bin\ssh.exe" (
    echo ✓ 找到Git SSH客户端
    echo.
    echo 连接命令: ssh root@*************
    echo 密码: ZbDX7%=]?H2(LAUz
    echo.
    echo 注意: 输入密码时不会显示字符，这是正常的安全特性
    echo.
    pause
    echo 正在连接到服务器...
    "C:\Program Files\Git\usr\bin\ssh.exe" root@*************
    goto :end
)

REM 方法3: 系统PATH中的SSH
ssh --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ 找到系统SSH客户端
    echo.
    echo 连接命令: ssh root@*************
    echo 密码: ZbDX7%=]?H2(LAUz
    echo.
    echo 注意: 输入密码时不会显示字符，这是正常的安全特性
    echo.
    pause
    echo 正在连接到服务器...
    ssh root@*************
    goto :end
)

REM 没有找到SSH客户端
echo ❌ 未找到SSH客户端
echo.
echo 请安装以下任一SSH客户端：
echo.
echo 1. Windows OpenSSH客户端:
echo    - 打开"设置" ^> "应用" ^> "可选功能"
echo    - 点击"添加功能"
echo    - 搜索并安装"OpenSSH客户端"
echo.
echo 2. Git for Windows:
echo    - 访问 https://git-scm.com/download/win
echo    - 下载并安装Git for Windows
echo.
echo 3. PuTTY:
echo    - 访问 https://www.putty.org/
echo    - 下载并安装PuTTY
echo    - 使用以下信息连接:
echo      主机名: *************
echo      端口: 22
echo      用户名: root
echo      密码: ZbDX7%=]?H2(LAUz
echo.

:end
echo.
echo ========================================
echo 连接后常用命令:
echo ========================================
echo   ls -la                    # 列出文件
echo   pwd                       # 显示当前目录
echo   cd /var/www               # 切换到网站目录
echo   systemctl status httpd    # 检查Apache状态
echo   systemctl status nginx    # 检查Nginx状态
echo   netstat -tlnp             # 显示监听端口
echo   ps aux                    # 显示运行进程
echo   df -h                     # 显示磁盘使用情况
echo   free -h                   # 显示内存使用情况
echo   exit                      # 断开连接
echo.
pause
