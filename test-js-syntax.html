<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法测试</title>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="test-results"></div>
    <button onclick="testSwitchPage()">测试switchPage函数</button>
    <button onclick="testNavigation()">测试导航功能</button>

    <script>
        console.log('🚀 开始JavaScript语法测试...');
        
        // 测试基本的switchPage函数
        window.switchPage = function(pageId) {
            console.log('✅ switchPage函数被调用，pageId:', pageId);
            document.getElementById('test-results').innerHTML += `<p>switchPage(${pageId}) 调用成功</p>`;
        };
        
        function testSwitchPage() {
            console.log('🧪 测试switchPage函数...');
            try {
                window.switchPage('test-page');
                console.log('✅ switchPage测试成功');
            } catch (error) {
                console.error('❌ switchPage测试失败:', error);
            }
        }
        
        function testNavigation() {
            console.log('🧪 测试导航功能...');
            try {
                // 模拟点击导航
                const event = { preventDefault: () => {} };
                console.log('✅ 导航测试成功');
                document.getElementById('test-results').innerHTML += `<p>导航测试成功</p>`;
            } catch (error) {
                console.error('❌ 导航测试失败:', error);
            }
        }
        
        console.log('✅ JavaScript语法测试完成');
        console.log('switchPage函数类型:', typeof window.switchPage);
    </script>
</body>
</html>
