<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>比特熊智慧系统 - 轻量版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        /* 简化的导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 2rem;
            margin-bottom: 3rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            flex-wrap: wrap;
        }
        
        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: white;
        }
        
        /* 主要内容 */
        .hero {
            text-align: center;
            padding: 4rem 0;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .hero p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 3rem;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }
        
        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        /* 功能卡片 */
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 4rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .feature-description {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 0.875rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-ok { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-error { color: #ef4444; }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .navbar {
                flex-direction: column;
                text-align: center;
            }
            
            .nav-menu {
                justify-content: center;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .status-indicator {
                bottom: 10px;
                right: 10px;
                left: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 简化的导航栏 -->
        <nav class="navbar">
            <div class="logo">🐻 比特熊智慧系统</div>
            <ul class="nav-menu">
                <?php
                // 尝试加载动态导航，失败则使用静态导航
                try {
                    require_once 'components/navbar.php';
                    $navbarData = getNavbarData();
                    
                    foreach ($navbarData as $item) {
                        if (empty($item['children'])) {
                            echo '<li><a href="' . htmlspecialchars($item['url']) . '" class="nav-link">' . htmlspecialchars($item['name']) . '</a></li>';
                        } else {
                            echo '<li><a href="' . htmlspecialchars($item['url']) . '" class="nav-link">' . htmlspecialchars($item['name']) . '</a></li>';
                        }
                    }
                } catch (Exception $e) {
                    // 静态导航作为后备
                    echo '<li><a href="#" class="nav-link">首页</a></li>';
                    echo '<li><a href="#" class="nav-link">关于</a></li>';
                    echo '<li><a href="#" class="nav-link">服务</a></li>';
                    echo '<li><a href="#" class="nav-link">联系</a></li>';
                }
                ?>
                <li><a href="admin-dashboard.php" class="nav-link">管理后台</a></li>
            </ul>
        </nav>
        
        <!-- 主要内容 -->
        <div class="hero">
            <h1>比特熊智慧系统</h1>
            <p>现代化的智能管理平台，让工作更高效</p>
            
            <div class="buttons">
                <a href="index.php" class="btn btn-primary">
                    🏠 完整版首页
                </a>
                <a href="admin-dashboard.php" class="btn btn-secondary">
                    ⚙️ 管理后台
                </a>
                <a href="diagnostic.php" class="btn btn-secondary">
                    🔍 系统诊断
                </a>
            </div>
        </div>
        
        <!-- 功能特色 -->
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🧭</div>
                <div class="feature-title">动态导航栏</div>
                <div class="feature-description">
                    支持在后台自定义导航栏结构，包括下拉菜单和子菜单项。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">用户管理</div>
                <div class="feature-description">
                    完整的用户管理系统，支持用户注册、登录、权限控制。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">数据统计</div>
                <div class="feature-description">
                    实时数据统计和可视化图表，帮助了解系统运行状况。
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚙️</div>
                <div class="feature-title">系统设置</div>
                <div class="feature-description">
                    完善的系统配置功能，支持主题切换、参数设置等。
                </div>
            </div>
        </div>
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicator">
        <div class="status-item">
            <span>系统状态:</span>
            <span class="status-ok">✅ 正常</span>
        </div>
        <div class="status-item">
            <span>数据库:</span>
            <span id="dbStatus" class="status-ok">检查中...</span>
        </div>
        <div class="status-item">
            <span>加载时间:</span>
            <span id="loadTime" class="status-ok">计算中...</span>
        </div>
    </div>
    
    <script>
        // 记录页面加载时间
        const startTime = performance.now();
        
        // 检查数据库状态
        async function checkDatabaseStatus() {
            try {
                const response = await fetch('api/navbar.php');
                const data = await response.json();
                
                const dbStatus = document.getElementById('dbStatus');
                if (data.success) {
                    dbStatus.textContent = '✅ 已连接';
                    dbStatus.className = 'status-ok';
                } else {
                    dbStatus.textContent = '❌ 连接失败';
                    dbStatus.className = 'status-error';
                }
            } catch (error) {
                const dbStatus = document.getElementById('dbStatus');
                dbStatus.textContent = '⚠️ 检查失败';
                dbStatus.className = 'status-warning';
            }
        }
        
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = performance.now() - startTime;
            const loadTimeElement = document.getElementById('loadTime');
            
            loadTimeElement.textContent = `${loadTime.toFixed(0)}ms`;
            
            if (loadTime < 100) {
                loadTimeElement.className = 'status-ok';
            } else if (loadTime < 500) {
                loadTimeElement.className = 'status-warning';
            } else {
                loadTimeElement.className = 'status-error';
            }
            
            // 检查数据库状态
            checkDatabaseStatus();
            
            console.log('🐻 轻量版首页加载完成');
            console.log(`⚡ 加载时间: ${loadTime.toFixed(2)}ms`);
        });
        
        // 添加点击效果
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // 创建点击波纹效果
                const ripple = document.createElement('span');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
