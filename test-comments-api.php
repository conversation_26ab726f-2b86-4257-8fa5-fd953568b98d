<?php
// 测试评论管理API
session_start();

echo "<h2>评论管理API测试</h2>";

// 检查会话状态
echo "<h3>1. 会话状态检查</h3>";
echo "<pre>";
echo "Session ID: " . session_id() . "\n";
echo "Admin logged in: " . (isset($_SESSION['admin_logged_in']) ? ($_SESSION['admin_logged_in'] ? 'true' : 'false') : 'not set') . "\n";
echo "Session data: " . print_r($_SESSION, true);
echo "</pre>";

// 测试数据库连接
echo "<h3>2. 数据库连接测试</h3>";
try {
    require_once 'config/database.php';
    $dbConfig = DatabaseConfig::getInstance();
    $db = $dbConfig->getConnection();
    echo "<p style='color: green;'>✓ 数据库连接成功</p>";

    // 检查评论表是否存在
    $stmt = $db->prepare("SHOW TABLES LIKE 'comments'");
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ comments表存在</p>";

        // 检查评论数量
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM comments");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>评论总数: " . $result['count'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ comments表不存在</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
}

// 测试API调用
echo "<h3>3. API调用测试</h3>";

if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    echo "<p style='color: green;'>✓ 管理员已登录，测试API调用...</p>";
    
    // 测试统计API
    echo "<h4>统计API测试:</h4>";
    $url = 'http://localhost:8000/api/comments-management.php?action=stats';
    echo "<p>请求URL: <a href='$url' target='_blank'>$url</a></p>";
    
    // 测试列表API
    echo "<h4>列表API测试:</h4>";
    $url = 'http://localhost:8000/api/comments-management.php?action=list&page=1';
    echo "<p>请求URL: <a href='$url' target='_blank'>$url</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ 管理员未登录，请先登录</p>";
    echo "<p><a href='admin-login.php'>点击这里登录</a></p>";
}

echo "<h3>4. 文件路径检查</h3>";
$files_to_check = [
    'api/comments-management.php',
    'config/database.php',
    'community-management.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file 存在</p>";
    } else {
        echo "<p style='color: red;'>✗ $file 不存在</p>";
    }
}
?>
