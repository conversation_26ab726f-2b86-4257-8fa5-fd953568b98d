# 比特熊智慧系统完整服务器配置命令

# ===== 连接服务器 =====
ssh root@*************
# 密码: ZbDX7%=]?H2(LAUz

# ===== 连接成功后执行以下命令 =====

# 1. 系统更新
apt update && apt upgrade -y

# 2. 安装基础软件
apt install -y nginx git curl wget unzip nano

# 3. 创建网站目录
mkdir -p /var/www/bitbear
cd /var/www/bitbear

# 4. 配置Nginx
cat > /etc/nginx/sites-available/bitbear << 'EOF'
server {
    listen 80;
    server_name _;
    root /var/www/bitbear;
    index index.html index.htm;

    location / {
        try_files $uri $uri/ =404;
    }

    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 5. 启用站点
ln -sf /etc/nginx/sites-available/bitbear /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 6. 启动服务
systemctl start nginx
systemctl enable nginx
nginx -t && systemctl restart nginx

# 7. 配置防火墙
ufw allow 22
ufw allow 80
ufw allow 443
ufw --force enable

# 8. 设置权限
chown -R www-data:www-data /var/www/bitbear
chmod -R 755 /var/www/bitbear

# ===== 上传文件方法 =====
# 方法1: 使用scp从本地上传
# scp -r * root@*************:/var/www/bitbear/

# 方法2: 在服务器上创建文件
# nano /var/www/bitbear/index.html
# 然后复制粘贴本地文件内容

# ===== 验证部署 =====
# 检查文件
ls -la /var/www/bitbear/

# 检查Nginx状态
systemctl status nginx

# 测试网站
curl http://localhost

# ===== 安全配置 =====
# 更改root密码
passwd

# 创建新用户
adduser bitbear
usermod -aG sudo bitbear

# 配置SSH密钥（推荐）
# ssh-keygen -t rsa -b 4096
# 然后将公钥添加到 ~/.ssh/authorized_keys
