<?php
/**
 * 服务器数据库配置文件
 * 专为服务器环境配置
 */

class DatabaseConfig {
    private static $instance = null;
    private $connection = null;
    
    // 服务器数据库配置
    private $host = 'localhost';
    private $username = 'root';
    private $password = '309290133q';
    private $database = 'bitbear_website';
    private $charset = 'utf8mb4';
    private $port = 3306; // 服务器标准MySQL端口
    
    private function __construct() {
        // 设置时区为中国标准时间
        date_default_timezone_set('Asia/Shanghai');
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $dsn = "mysql:host={$this->host};port={$this->port};charset={$this->charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::ATTR_TIMEOUT => 5,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::MYSQL_ATTR_FOUND_ROWS => true
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);

            // 检查数据库是否存在，不存在则创建
            $this->createDatabaseIfNotExists();

            // 重新连接到指定数据库
            $dsn = "mysql:host={$this->host};port={$this->port};dbname={$this->database};charset={$this->charset}";
            $this->connection = new PDO($dsn, $this->username, $this->password, $options);

            error_log("服务器MySQL数据库连接成功 - 端口: {$this->port}");

        } catch (PDOException $e) {
            error_log("服务器数据库连接失败: " . $e->getMessage());
            throw new Exception("无法连接到服务器数据库: " . $e->getMessage());
        }
    }
    
    private function createDatabaseIfNotExists() {
        try {
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->database}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci";
            $this->connection->exec($sql);
            error_log("数据库 {$this->database} 检查/创建完成");
        } catch (PDOException $e) {
            error_log("创建数据库失败: " . $e->getMessage());
        }
    }

    public function getConnection() {
        // 检查连接是否仍然有效
        if ($this->connection === null) {
            $this->connect();
        }
        
        try {
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            // 连接已断开，重新连接
            $this->connect();
        }
        
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("数据库查询错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    // 初始化数据库表结构
    public function initializeDatabase() {
        $sqlFile = __DIR__ . '/../database/init.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('数据库初始化文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $this->getConnection()->exec($statement);
                } catch (PDOException $e) {
                    // 忽略已存在的表等错误
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        error_log("执行SQL语句失败: " . $e->getMessage());
                    }
                }
            }
        }
    }

    // 检查数据库连接状态
    public function testConnection() {
        try {
            $result = $this->fetchOne("SELECT 1 as test, NOW() as current_time");
            return [
                'status' => 'success',
                'message' => '数据库连接正常',
                'data' => $result
            ];
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => '数据库连接失败: ' . $e->getMessage()
            ];
        }
    }

    // 获取数据库信息
    public function getDatabaseInfo() {
        try {
            $version = $this->fetchOne("SELECT VERSION() as version");
            $charset = $this->fetchOne("SELECT @@character_set_database as charset");
            $tables = $this->fetchAll("SHOW TABLES");
            
            return [
                'database' => $this->database,
                'version' => $version['version'],
                'charset' => $charset['charset'],
                'tables_count' => count($tables),
                'tables' => array_column($tables, 'Tables_in_' . $this->database)
            ];
        } catch (Exception $e) {
            return [
                'error' => '获取数据库信息失败: ' . $e->getMessage()
            ];
        }
    }
}

// 便捷函数
function db() {
    return DatabaseConfig::getInstance();
}
?>
