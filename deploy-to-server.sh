#!/bin/bash

# 比特熊智慧系统服务器部署脚本
# 使用方法: ./deploy-to-server.sh

set -e  # 遇到错误立即退出

# 配置变量
SERVER_IP="*************"
SERVER_USER="root"  # 建议创建专用用户
PROJECT_NAME="比特熊组织网站项目(v0.0.1)"
REMOTE_PATH="/www/wwwroot/${PROJECT_NAME}"
DB_NAME="wisdom_system"
DB_USER="wisdom_user"
DB_PASS="$(openssl rand -base64 32)"  # 生成随机密码

echo "=== 比特熊智慧系统部署开始 ==="

# 1. 检查本地环境
echo "检查本地环境..."
if ! command -v rsync &> /dev/null; then
    echo "错误: 需要安装 rsync"
    exit 1
fi

if ! command -v mysql &> /dev/null; then
    echo "错误: 需要安装 mysql 客户端"
    exit 1
fi

# 2. 创建部署包
echo "创建部署包..."
tar -czf deploy-package.tar.gz \
    --exclude='.git' \
    --exclude='node_modules' \
    --exclude='*.log' \
    --exclude='deploy-package.tar.gz' \
    .

# 3. 上传文件到服务器
echo "上传文件到服务器..."
scp deploy-package.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/

# 4. 在服务器上执行部署
echo "在服务器上执行部署..."
ssh ${SERVER_USER}@${SERVER_IP} << EOF
    set -e
    
    echo "创建项目目录..."
    mkdir -p "${REMOTE_PATH}"
    cd "${REMOTE_PATH}"
    
    echo "解压项目文件..."
    tar -xzf /tmp/deploy-package.tar.gz
    rm /tmp/deploy-package.tar.gz
    
    echo "设置文件权限..."
    chown -R www-data:www-data .
    chmod -R 755 .
    chmod -R 777 uploads/ 2>/dev/null || mkdir -p uploads && chmod 777 uploads
    
    echo "安装PHP依赖..."
    if [ -f composer.json ]; then
        composer install --no-dev --optimize-autoloader
    fi
    
    echo "创建配置文件..."
    if [ ! -f config/config.php ]; then
        cp config/config.example.php config/config.php
        sed -i "s/your_username/${DB_USER}/g" config/config.php
        sed -i "s/your_password/${DB_PASS}/g" config/config.php
        sed -i "s/wisdom_system/${DB_NAME}/g" config/config.php
    fi
    
    echo "服务器端文件部署完成"
EOF

echo "=== 文件上传完成 ==="
echo "数据库密码: ${DB_PASS}"
echo "请保存此密码！"

# 清理本地临时文件
rm -f deploy-package.tar.gz

echo "=== 部署脚本执行完成 ==="
echo "接下来请执行数据库部署脚本"
