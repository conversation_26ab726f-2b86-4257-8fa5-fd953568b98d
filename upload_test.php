<?php
// PHP文件上传测试脚本
// 创建时间: <?php echo date('Y-m-d H:i:s'); ?>

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-form {
            border: 2px dashed #ccc;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            border-radius: 10px;
        }
        .upload-form:hover {
            border-color: #007cba;
            background-color: #f9f9f9;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
            max-width: 400px;
        }
        input[type="submit"] {
            background-color: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        input[type="submit"]:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            margin-bottom: 20px;
        }
        .file-info {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 文件上传测试工具</h1>
        
        <div class="info result">
            <strong>服务器信息:</strong><br>
            PHP版本: <?php echo PHP_VERSION; ?><br>
            服务器时间: <?php echo date('Y-m-d H:i:s'); ?><br>
            上传目录: <?php echo __DIR__; ?><br>
            最大上传大小: <?php echo ini_get('upload_max_filesize'); ?><br>
            POST最大大小: <?php echo ini_get('post_max_size'); ?>
        </div>

        <?php
        // 处理文件上传
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['upload_file'])) {
            $upload_dir = __DIR__ . '/uploads/';
            
            // 创建上传目录
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file = $_FILES['upload_file'];
            $file_name = $file['name'];
            $file_tmp = $file['tmp_name'];
            $file_size = $file['size'];
            $file_error = $file['error'];
            
            echo '<div class="result">';
            
            if ($file_error === UPLOAD_ERR_OK) {
                // 生成安全的文件名
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                $safe_name = date('YmdHis') . '_' . uniqid() . '.' . $file_ext;
                $target_path = $upload_dir . $safe_name;
                
                if (move_uploaded_file($file_tmp, $target_path)) {
                    echo '<div class="success">';
                    echo '<h3>✅ 文件上传成功!</h3>';
                    echo '<strong>原文件名:</strong> ' . htmlspecialchars($file_name) . '<br>';
                    echo '<strong>保存文件名:</strong> ' . htmlspecialchars($safe_name) . '<br>';
                    echo '<strong>文件大小:</strong> ' . number_format($file_size / 1024, 2) . ' KB<br>';
                    echo '<strong>文件类型:</strong> ' . htmlspecialchars($file_ext) . '<br>';
                    echo '<strong>保存路径:</strong> ' . htmlspecialchars($target_path) . '<br>';
                    echo '<strong>上传时间:</strong> ' . date('Y-m-d H:i:s');
                    
                    // 如果是图片，显示预览
                    $image_exts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
                    if (in_array($file_ext, $image_exts)) {
                        echo '<br><br><strong>图片预览:</strong><br>';
                        echo '<img src="uploads/' . htmlspecialchars($safe_name) . '" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; margin-top: 10px;">';
                    }
                    echo '</div>';
                } else {
                    echo '<div class="error">';
                    echo '<h3>❌ 文件移动失败!</h3>';
                    echo '无法将文件移动到目标目录。请检查目录权限。';
                    echo '</div>';
                }
            } else {
                echo '<div class="error">';
                echo '<h3>❌ 上传错误!</h3>';
                switch ($file_error) {
                    case UPLOAD_ERR_INI_SIZE:
                        echo '文件大小超过了 php.ini 中 upload_max_filesize 的限制。';
                        break;
                    case UPLOAD_ERR_FORM_SIZE:
                        echo '文件大小超过了表单中 MAX_FILE_SIZE 的限制。';
                        break;
                    case UPLOAD_ERR_PARTIAL:
                        echo '文件只有部分被上传。';
                        break;
                    case UPLOAD_ERR_NO_FILE:
                        echo '没有文件被上传。';
                        break;
                    case UPLOAD_ERR_NO_TMP_DIR:
                        echo '找不到临时文件夹。';
                        break;
                    case UPLOAD_ERR_CANT_WRITE:
                        echo '文件写入失败。';
                        break;
                    default:
                        echo '未知上传错误。错误代码: ' . $file_error;
                }
                echo '</div>';
            }
            echo '</div>';
        }
        ?>

        <form method="post" enctype="multipart/form-data" class="upload-form">
            <h3>📁 选择要上传的文件</h3>
            <p>支持各种文件类型，建议文件大小不超过 <?php echo ini_get('upload_max_filesize'); ?></p>
            
            <input type="file" name="upload_file" required>
            <br>
            <input type="submit" value="🚀 开始上传">
        </form>

        <div class="file-info result">
            <strong>📋 使用说明:</strong><br>
            1. 选择要上传的文件<br>
            2. 点击"开始上传"按钮<br>
            3. 文件将保存到 uploads/ 目录下<br>
            4. 上传成功后会显示详细信息<br>
            5. 图片文件会显示预览
        </div>

        <?php
        // 显示已上传的文件列表
        $upload_dir = __DIR__ . '/uploads/';
        if (is_dir($upload_dir)) {
            $files = array_diff(scandir($upload_dir), array('.', '..'));
            if (!empty($files)) {
                echo '<div class="info result">';
                echo '<h3>📂 已上传的文件:</h3>';
                echo '<ul>';
                foreach ($files as $file) {
                    $file_path = $upload_dir . $file;
                    $file_size = filesize($file_path);
                    $file_time = date('Y-m-d H:i:s', filemtime($file_path));
                    echo '<li>';
                    echo '<strong>' . htmlspecialchars($file) . '</strong> ';
                    echo '(' . number_format($file_size / 1024, 2) . ' KB) ';
                    echo '- ' . $file_time;
                    echo '</li>';
                }
                echo '</ul>';
                echo '</div>';
            }
        }
        ?>
    </div>
</body>
</html>
