<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快捷入口测试</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        /* 管理模块快捷入口样式 */
        .management-shortcuts {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .shortcuts-header {
            margin-bottom: 1.5rem;
        }

        .shortcuts-header h3 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .shortcuts-subtitle {
            margin: 0;
            color: #64748b;
            font-size: 0.875rem;
        }

        .shortcuts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .shortcut-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .shortcut-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .shortcut-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }

        .shortcut-card:hover::before {
            transform: scaleX(1);
        }

        .shortcut-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .shortcut-icon.announcement {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .shortcut-icon.community {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .shortcut-icon.mailbox {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .shortcut-content {
            flex: 1;
        }

        .shortcut-content h4 {
            margin: 0 0 0.5rem 0;
            color: #1e293b;
            font-size: 1.125rem;
            font-weight: 600;
        }

        .shortcut-content p {
            margin: 0 0 0.75rem 0;
            color: #64748b;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .shortcut-stats {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .stat-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #e2e8f0;
            color: #475569;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .stat-badge.important {
            background: #fee2e2;
            color: #dc2626;
        }

        .stat-badge.pending {
            background: #fef3c7;
            color: #d97706;
        }

        .shortcut-arrow {
            color: #94a3b8;
            transition: all 0.3s ease;
        }

        .shortcut-card:hover .shortcut-arrow {
            color: #3b82f6;
            transform: translateX(4px);
        }

        .demo-info {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border-left: 4px solid #3b82f6;
            max-width: 1200px;
            margin: 0 auto 2rem auto;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.2s ease;
            margin: 0.5rem;
        }

        .btn:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="demo-info">
        <h1>🎯 管理模块快捷入口测试</h1>
        <p>这是独立的快捷入口测试页面，用于验证样式和功能是否正常。</p>
        <a href="admin-dashboard.php" class="btn">返回管理后台</a>
        <a href="test_management.php" class="btn">管理功能测试</a>
    </div>

    <!-- 管理模块快捷入口 -->
    <div class="management-shortcuts">
        <div class="shortcuts-header">
            <h3 class="card-title">管理模块</h3>
            <p class="shortcuts-subtitle">快速访问常用管理功能</p>
        </div>
        
        <div class="shortcuts-grid">
            <!-- 公告栏入口 -->
            <div class="shortcut-card" onclick="alert('跳转到公告栏管理')">
                <div class="shortcut-icon announcement">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="shortcut-content">
                    <h4>公告栏</h4>
                    <p>发布和管理系统公告</p>
                    <div class="shortcut-stats">
                        <span class="stat-badge">3 条公告</span>
                        <span class="stat-badge important">1 条重要</span>
                    </div>
                </div>
                <div class="shortcut-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
            
            <!-- 社区管理入口 -->
            <div class="shortcut-card" onclick="alert('跳转到社区管理')">
                <div class="shortcut-icon community">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15A2 2 0 0 1 19 17H7L4 20V5A2 2 0 0 1 6 3H19A2 2 0 0 1 21 5Z" stroke="currentColor" stroke-width="2"/>
                        <path d="M13 8H7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        <path d="M17 12H7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    </svg>
                </div>
                <div class="shortcut-content">
                    <h4>社区管理</h4>
                    <p>管理用户帖子和互动</p>
                    <div class="shortcut-stats">
                        <span class="stat-badge">156 个帖子</span>
                        <span class="stat-badge pending">8 待审核</span>
                    </div>
                </div>
                <div class="shortcut-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
            
            <!-- 站内邮箱入口 -->
            <div class="shortcut-card" onclick="alert('跳转到站内邮箱')">
                <div class="shortcut-icon mailbox">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2"/>
                        <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
                <div class="shortcut-content">
                    <h4>站内邮箱</h4>
                    <p>发送和接收站内消息</p>
                    <div class="shortcut-stats">
                        <span class="stat-badge">5 条未读</span>
                        <span class="stat-badge">12 条消息</span>
                    </div>
                </div>
                <div class="shortcut-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-info">
        <h2>✅ 功能验证</h2>
        <p><strong>如果您能看到上面的三个管理模块卡片，说明样式正常。</strong></p>
        <p>每个卡片都应该有：</p>
        <ul>
            <li>🎨 独特的颜色主题（橙色、绿色、蓝色）</li>
            <li>📊 统计数据徽章</li>
            <li>🖱️ 悬停动画效果</li>
            <li>➡️ 右侧箭头图标</li>
        </ul>
        <p>点击任意卡片应该显示相应的提示信息。</p>
    </div>

    <script>
        console.log('快捷入口测试页面已加载');
        console.log('如果看到三个管理模块卡片，说明样式正常工作');
    </script>
</body>
</html>
