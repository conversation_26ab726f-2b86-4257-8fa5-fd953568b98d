<?php
/**
 * 直接测试注册API
 * 绕过前端，直接测试后端注册功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>注册API直接测试</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    .btn:hover { background: #0056b3; }
</style></head><body>";

echo "<div class='container'>";
echo "<h1>🧪 注册API直接测试</h1>";

// 测试数据
$testData = [
    'username' => 'directtest_' . time(),
    'email' => 'directtest_' . time() . '@example.com',
    'nickname' => '直接测试用户',
    'password' => 'test123456',
    'confirmPassword' => 'test123456'
];

echo "<div class='info'>";
echo "<h3>📋 测试数据</h3>";
echo "<pre>" . json_encode($testData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
echo "</div>";

// 1. 检查API文件是否存在
echo "<div class='info'>";
echo "<h3>📁 API文件检查</h3>";
if (file_exists('api/register.php')) {
    echo "<p>✅ api/register.php 文件存在</p>";
    echo "<p>文件大小: " . filesize('api/register.php') . " 字节</p>";
    echo "<p>文件权限: " . substr(sprintf('%o', fileperms('api/register.php')), -4) . "</p>";
} else {
    echo "<p>❌ api/register.php 文件不存在</p>";
}
echo "</div>";

// 2. 模拟POST请求
echo "<div class='info'>";
echo "<h3>🔄 模拟POST请求测试</h3>";

// 保存原始的$_POST和$_SERVER
$originalPost = $_POST;
$originalServer = $_SERVER;

// 设置模拟的POST数据
$_POST = $testData;
$_SERVER['REQUEST_METHOD'] = 'POST';

// 开始输出缓冲
ob_start();

try {
    // 包含注册API文件
    include 'api/register.php';
    
    // 获取输出
    $output = ob_get_contents();
    
    echo "<p>✅ API执行完成</p>";
    echo "<h4>API响应:</h4>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    // 尝试解析JSON响应
    $responseData = json_decode($output, true);
    if ($responseData) {
        echo "<h4>解析后的响应:</h4>";
        echo "<pre>" . json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($responseData['success'])) {
            if ($responseData['success']) {
                echo "<div class='success'>✅ 注册成功！</div>";
                
                // 如果注册成功，清理测试数据
                if (isset($responseData['user_id'])) {
                    try {
                        require_once 'config/database.php';
                        $db = DatabaseConfig::getInstance();
                        
                        $userId = $responseData['user_id'];
                        $db->query("DELETE FROM user_profiles WHERE user_id = ?", [$userId]);
                        $db->query("DELETE FROM users WHERE id = ?", [$userId]);
                        
                        echo "<div class='info'>✅ 测试数据已清理</div>";
                    } catch (Exception $e) {
                        echo "<div class='warning'>⚠️ 清理测试数据失败: " . $e->getMessage() . "</div>";
                    }
                }
            } else {
                echo "<div class='error'>❌ 注册失败: " . ($responseData['message'] ?? '未知错误') . "</div>";
                
                if (isset($responseData['errors'])) {
                    echo "<h4>详细错误:</h4>";
                    echo "<pre>" . json_encode($responseData['errors'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "</pre>";
                }
            }
        }
    } else {
        echo "<div class='error'>❌ 无法解析API响应为JSON</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ API执行出错: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    echo "<div class='error'>❌ PHP错误: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// 结束输出缓冲
ob_end_clean();

// 恢复原始的$_POST和$_SERVER
$_POST = $originalPost;
$_SERVER = $originalServer;

echo "</div>";

// 3. 数据库连接测试
echo "<div class='info'>";
echo "<h3>🗄️ 数据库连接测试</h3>";

try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查关键表
    $tables = ['users', 'user_roles', 'user_profiles'];
    foreach ($tables as $table) {
        try {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}")['count'];
            echo "<p>✅ {$table} 表存在，包含 {$count} 条记录</p>";
        } catch (Exception $e) {
            echo "<p>❌ {$table} 表检查失败: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "</div>";

// 4. 环境信息
echo "<div class='info'>";
echo "<h3>🌐 环境信息</h3>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>服务器: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='register.php' class='btn'>测试注册页面</a>";
echo "<a href='云服务器注册问题诊断增强版.php' class='btn'>详细诊断</a>";
echo "<a href='index.php' class='btn'>返回首页</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
