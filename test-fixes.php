<?php
/**
 * 测试修复效果的页面
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修复测试</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { color: red; background: #ffe8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { color: blue; background: #e8f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .warning { color: orange; background: #fff8e8; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .test-section { border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }
    .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
    .test-button:hover { background: #005a87; }
</style></head><body>";

echo "<h1>系统修复测试</h1>";

// 测试1：认证初始化
echo "<div class='test-section'>";
echo "<h2>测试1：认证系统初始化</h2>";
try {
    require_once 'includes/auth-init.php';
    
    echo "<div class='success'>✓ 认证初始化成功</div>";
    
    // 检查变量是否正确初始化
    $checks = [
        'currentUser' => isset($currentUser),
        'isAuthenticated' => isset($isAuthenticated),
        'auth' => isset($auth)
    ];
    
    foreach ($checks as $var => $exists) {
        if ($exists) {
            echo "<div class='success'>✓ \${$var} 变量已正确初始化</div>";
        } else {
            echo "<div class='error'>✗ \${$var} 变量未初始化</div>";
        }
    }
    
    // 显示当前状态
    echo "<div class='info'>登录状态: " . ($isAuthenticated ? '已登录' : '未登录') . "</div>";
    if ($currentUser) {
        echo "<div class='info'>当前用户: " . ($currentUser['username'] ?? '未知') . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 认证初始化失败: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 测试2：导航栏组件
echo "<div class='test-section'>";
echo "<h2>测试2：导航栏组件</h2>";
try {
    require_once 'components/navbar.php';
    $navbarData = getNavbarData();
    
    if (is_array($navbarData) && !empty($navbarData)) {
        echo "<div class='success'>✓ 导航栏数据加载成功，共 " . count($navbarData) . " 个项目</div>";
        
        // 渲染导航栏HTML测试
        $navbarHtml = renderNavbar($navbarData);
        if (!empty($navbarHtml)) {
            echo "<div class='success'>✓ 导航栏HTML渲染成功</div>";
        } else {
            echo "<div class='warning'>⚠ 导航栏HTML渲染为空</div>";
        }
    } else {
        echo "<div class='warning'>⚠ 导航栏数据为空，使用默认导航</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 导航栏组件测试失败: " . $e->getMessage() . "</div>";
}
echo "</div>";

// 测试3：API错误处理
echo "<div class='test-section'>";
echo "<h2>测试3：API错误处理</h2>";
echo "<div class='info'>测试登录和注册API的错误处理机制</div>";

echo "<button class='test-button' onclick='testLoginAPI()'>测试登录API</button>";
echo "<button class='test-button' onclick='testRegisterAPI()'>测试注册API</button>";

echo "<div id='apiTestResults'></div>";
echo "</div>";

// 测试4：数据库连接状态
echo "<div class='test-section'>";
echo "<h2>测试4：数据库连接状态</h2>";
try {
    require_once 'config/database.php';
    $db = DatabaseConfig::getInstance();
    
    $connection = $db->getConnection();
    $driver = $connection->getAttribute(PDO::ATTR_DRIVER_NAME);
    
    echo "<div class='success'>✓ 数据库连接成功</div>";
    echo "<div class='info'>数据库类型: " . strtoupper($driver) . "</div>";
    
    // 测试基本查询
    $result = $db->query("SELECT 1 as test");
    $row = $result->fetch();
    if ($row && $row['test'] == 1) {
        echo "<div class='success'>✓ 数据库查询测试成功</div>";
    }
    
    // 检查关键表
    $tables = [];
    if ($driver === 'mysql') {
        $result = $db->query("SHOW TABLES");
        while ($row = $result->fetch()) {
            $tables[] = array_values($row)[0];
        }
    } elseif ($driver === 'sqlite') {
        $result = $db->query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
        while ($row = $result->fetch()) {
            $tables[] = $row['name'];
        }
    }
    
    $requiredTables = ['users', 'user_roles', 'navbar_items'];
    foreach ($requiredTables as $table) {
        if (in_array($table, $tables)) {
            echo "<div class='success'>✓ 表 '{$table}' 存在</div>";
        } else {
            echo "<div class='warning'>⚠ 表 '{$table}' 不存在</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='error'>✗ 数据库连接失败: " . $e->getMessage() . "</div>";
    echo "<div class='info'>这可能是正常的，系统会自动使用SQLite作为备用数据库</div>";
}
echo "</div>";

// JavaScript测试代码
echo "<script>
function testLoginAPI() {
    const resultsDiv = document.getElementById('apiTestResults');
    resultsDiv.innerHTML = '<div class=\"info\">正在测试登录API...</div>';
    
    // 测试空数据提交
    fetch('api/login.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'username=&password='
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false && data.errors) {
            resultsDiv.innerHTML += '<div class=\"success\">✓ 登录API验证错误处理正常</div>';
        } else {
            resultsDiv.innerHTML += '<div class=\"warning\">⚠ 登录API验证可能有问题</div>';
        }
        
        // 测试无效用户名
        return fetch('api/login.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'username=nonexistentuser&password=wrongpassword'
        });
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false && data.message) {
            resultsDiv.innerHTML += '<div class=\"success\">✓ 登录API错误处理正常</div>';
        } else {
            resultsDiv.innerHTML += '<div class=\"warning\">⚠ 登录API错误处理可能有问题</div>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML += '<div class=\"error\">✗ 登录API测试失败: ' + error.message + '</div>';
    });
}

function testRegisterAPI() {
    const resultsDiv = document.getElementById('apiTestResults');
    resultsDiv.innerHTML = '<div class=\"info\">正在测试注册API...</div>';
    
    // 测试空数据提交
    fetch('api/register.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'username=&email=&password='
    })
    .then(response => response.json())
    .then(data => {
        if (data.success === false && (data.errors || data.message)) {
            resultsDiv.innerHTML += '<div class=\"success\">✓ 注册API验证错误处理正常</div>';
        } else {
            resultsDiv.innerHTML += '<div class=\"warning\">⚠ 注册API验证可能有问题</div>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML += '<div class=\"error\">✗ 注册API测试失败: ' + error.message + '</div>';
    });
}
</script>";

echo "<hr>";
echo "<div class='info'>";
echo "<h3>测试完成后，您可以：</h3>";
echo "<ul>";
echo "<li><a href='index.php'>访问首页</a> - 检查是否还有JavaScript错误</li>";
echo "<li><a href='login.php'>测试登录页面</a> - 尝试登录功能</li>";
echo "<li><a href='register.php'>测试注册页面</a> - 尝试注册功能</li>";
echo "<li><a href='admin-dashboard.php'>访问管理后台</a> - 检查后台功能</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
